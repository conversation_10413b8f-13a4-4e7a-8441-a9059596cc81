# Sistema Mútuo de Avaliação entre Influenciadores e Restaurantes

Este documento descreve a implementação do sistema mútuo de avaliação entre influenciadores e restaurantes no crIAdores.

## Visão Geral

O sistema permite que influenciadores e restaurantes avaliem uns aos outros após participarem de campanhas juntos. As avaliações incluem uma classificação de 1 a 5 estrelas e um comentário opcional.

## Componentes Principais

### 1. Componentes de UI
- **StarRating**: Componente reutilizável para exibir e coletar avaliações em estrelas
- **RatingForm**: Formulário para enviar avaliações
- **RatingDisplay**: Componente para exibir avaliações individuais
- **RatingsSummary**: Componente para exibir resumo de avaliações
- **RatingStatsCard**: Card para o dashboard que mostra estatísticas de avaliação

### 2. Popups de Avaliação
- **InfluencerRatingPopup**: Popup para avaliar influenciadores
- **RestaurantRatingPopup**: Popup para avaliar restaurantes
- **AllRatingsPopup**: Popup para visualizar todas as avaliações

### 3. Integração com Dashboards
- Card de avaliações no dashboard do influenciador
- Card de avaliações no dashboard do restaurante
- Botões de avaliação nos cards de campanha
- Botões de avaliação nos cards de influenciadores

### 4. Backend e Banco de Dados
- Tabela `ratings` para armazenar as avaliações
- Views para facilitar a consulta de avaliações agregadas
- Gatilhos para atualizar automaticamente as médias de avaliação
- Endpoints de API para gerenciar as avaliações

## Configuração do Banco de Dados

Para configurar o banco de dados, execute o script SQL em `src/db/setup_ratings_system.sql` no seu banco de dados Supabase. Este script cria:

1. A tabela `ratings` para armazenar as avaliações
2. Índices para melhorar a performance
3. Views para facilitar a consulta de avaliações agregadas
4. Funções e gatilhos para atualizar automaticamente as médias de avaliação

## Fluxo de Avaliação

1. Um usuário (influenciador ou restaurante) participa de uma campanha
2. Após a campanha, o usuário pode avaliar o outro participante (restaurante ou influenciador)
3. A avaliação é armazenada no banco de dados
4. A média de avaliação do usuário avaliado é atualizada automaticamente
5. As avaliações são exibidas nos dashboards e popups

## Uso dos Componentes

### StarRating

```tsx
<StarRating
  value={3.5}
  onChange={(rating) => console.log(rating)}
  size="md"
  readOnly={false}
  count={5}
/>
```

### RatingForm

```tsx
<RatingForm
  campaignInfluencerId="campaign-influencer-id"
  rateeId="ratee-id"
  rateeName="Nome do Avaliado"
  onSuccess={() => console.log('Avaliação enviada com sucesso')}
  onCancel={() => console.log('Avaliação cancelada')}
  existingRating={{ rating: 4, comment: 'Comentário existente' }}
/>
```

### RatingDisplay

```tsx
<RatingDisplay
  rating={4}
  comment="Ótima parceria! Restaurante muito receptivo."
  raterName="Nome do Avaliador"
  date="2023-06-15T10:30:00Z"
/>
```

### RatingsSummary

```tsx
<RatingsSummary
  averageRating={4.2}
  totalRatings={15}
  ratingDistribution={{ 5: 8, 4: 5, 3: 2, 2: 0, 1: 0 }}
/>
```

### RatingStatsCard

```tsx
<RatingStatsCard
  userId="user-id"
  userRole="influencer"
  onViewAllClick={() => console.log('Ver todas as avaliações')}
/>
```

## API de Avaliação

### GET /api/v1/ratings

Obtém avaliações com base nos parâmetros fornecidos.

Parâmetros:
- `userId`: ID do usuário (opcional, padrão: usuário atual)
- `type`: Tipo de avaliação ('received' ou 'given', padrão: 'received')
- `campaignId`: ID da campanha (opcional)

### POST /api/v1/ratings

Cria ou atualiza uma avaliação.

Corpo da requisição:
```json
{
  "campaignInfluencerId": "campaign-influencer-id",
  "rateeId": "ratee-id",
  "rating": 5,
  "comment": "Comentário opcional"
}
```

## Considerações Futuras

1. **Notificações**: Implementar notificações para informar os usuários quando receberem uma nova avaliação
2. **Filtros Avançados**: Adicionar filtros avançados para as avaliações (por data, por campanha, etc.)
3. **Relatórios**: Gerar relatórios de avaliação para ajudar os usuários a melhorar seu desempenho
4. **Moderação**: Implementar um sistema de moderação para evitar avaliações inadequadas
5. **Integração com Redes Sociais**: Permitir que os usuários compartilhem suas avaliações nas redes sociais
