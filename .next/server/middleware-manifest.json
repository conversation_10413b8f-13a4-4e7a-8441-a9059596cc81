{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|login|registro|_vercel|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|login|registro|_vercel|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "GVLW7eajiwDt5zYYP_oAM", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3uLTFfLmBBMUnXxy59PC1+Ldy4mQyyLVyk5cAgnOxnE=", "__NEXT_PREVIEW_MODE_ID": "43b17ce8ae2ae1319dcb1d94e270416e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3a6861251c98da67b8a23e401e69861cc8af09f302d31b19134ce9ead37bd8eb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0f3bc3caa675c60f3ee9a3631fc2326ca1bd9393d753e1b8d2a28e386f41c8bf"}}}, "functions": {}, "sortedMiddleware": ["/"]}