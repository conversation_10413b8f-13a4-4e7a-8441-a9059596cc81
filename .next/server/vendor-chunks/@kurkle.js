"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@kurkle";
exports.ids = ["vendor-chunks/@kurkle"];
exports.modules = {

/***/ "(ssr)/./node_modules/@kurkle/color/dist/color.esm.js":
/*!******************************************************!*\
  !*** ./node_modules/@kurkle/color/dist/color.esm.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* binding */ Color),\n/* harmony export */   b2n: () => (/* binding */ b2n),\n/* harmony export */   b2p: () => (/* binding */ b2p),\n/* harmony export */   \"default\": () => (/* binding */ index_esm),\n/* harmony export */   hexParse: () => (/* binding */ hexParse),\n/* harmony export */   hexString: () => (/* binding */ hexString),\n/* harmony export */   hsl2rgb: () => (/* binding */ hsl2rgb),\n/* harmony export */   hslString: () => (/* binding */ hslString),\n/* harmony export */   hsv2rgb: () => (/* binding */ hsv2rgb),\n/* harmony export */   hueParse: () => (/* binding */ hueParse),\n/* harmony export */   hwb2rgb: () => (/* binding */ hwb2rgb),\n/* harmony export */   lim: () => (/* binding */ lim),\n/* harmony export */   n2b: () => (/* binding */ n2b),\n/* harmony export */   n2p: () => (/* binding */ n2p),\n/* harmony export */   nameParse: () => (/* binding */ nameParse),\n/* harmony export */   p2b: () => (/* binding */ p2b),\n/* harmony export */   rgb2hsl: () => (/* binding */ rgb2hsl),\n/* harmony export */   rgbParse: () => (/* binding */ rgbParse),\n/* harmony export */   rgbString: () => (/* binding */ rgbString),\n/* harmony export */   rotate: () => (/* binding */ rotate),\n/* harmony export */   round: () => (/* binding */ round)\n/* harmony export */ });\n/*!\n * @kurkle/color v0.3.4\n * https://github.com/kurkle/color#readme\n * (c) 2024 Jukka Kurkela\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n\nconst map$1 = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? (map$1[str[7]] << 4 | map$1[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\n\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\n\nconst map = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names$1 = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tVScEd: 'ba55d3',\n\tVpurpN: '9370db',\n\tVsHgYF: '3cb371',\n\tVUXe: '7b68ee',\n\tVsprRggYF: 'fa9a',\n\tVQe: '48d1cc',\n\tVviTetYd: 'c71585',\n\tmidnightXe: '191970',\n\tmRtcYam: 'f5fffa',\n\tmistyPse: 'ffe4e1',\n\tmoccasR: 'ffe4b5',\n\tnavajowEte: 'ffdead',\n\tnavy: '80',\n\tTdlace: 'fdf5e6',\n\tTive: '808000',\n\tTivedBb: '6b8e23',\n\tSange: 'ffa500',\n\tSangeYd: 'ff4500',\n\tScEd: 'da70d6',\n\tpOegTMnPd: 'eee8aa',\n\tpOegYF: '98fb98',\n\tpOeQe: 'afeeee',\n\tpOeviTetYd: 'db7093',\n\tpapayawEp: 'ffefd5',\n\tpHKpuff: 'ffdab9',\n\tperu: 'cd853f',\n\tpRk: 'ffc0cb',\n\tplum: 'dda0dd',\n\tpowMrXe: 'b0e0e6',\n\tpurpN: '800080',\n\tYbeccapurpN: '663399',\n\tYd: 'ff0000',\n\tPsybrown: 'bc8f8f',\n\tPyOXe: '4169e1',\n\tsaddNbPwn: '8b4513',\n\tsOmon: 'fa8072',\n\tsandybPwn: 'f4a460',\n\tsHgYF: '2e8b57',\n\tsHshell: 'fff5ee',\n\tsiFna: 'a0522d',\n\tsilver: 'c0c0c0',\n\tskyXe: '87ceeb',\n\tUXe: '6a5acd',\n\tUWay: '708090',\n\tUgYy: '708090',\n\tsnow: 'fffafa',\n\tsprRggYF: 'ff7f',\n\tstAlXe: '4682b4',\n\ttan: 'd2b48c',\n\tteO: '8080',\n\ttEstN: 'd8bfd8',\n\ttomato: 'ff6347',\n\tQe: '40e0d0',\n\tviTet: 'ee82ee',\n\tJHt: 'f5deb3',\n\twEte: 'ffffff',\n\twEtesmoke: 'f5f5f5',\n\tLw: 'ffff00',\n\tLwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\n\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\n\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\n\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\n\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\n\nfunction index_esm(input) {\n  return new Color(input);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@kurkle/color/dist/color.esm.js\n");

/***/ })

};
;