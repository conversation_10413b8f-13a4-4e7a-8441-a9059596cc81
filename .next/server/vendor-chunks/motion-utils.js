"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/motion-utils";
exports.ids = ["vendor-chunks/motion-utils"];
exports.modules = {

/***/ "(ssr)/./node_modules/motion-utils/dist/es/array.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/array.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUniqueItem: () => (/* binding */ addUniqueItem),\n/* harmony export */   moveItem: () => (/* binding */ moveItem),\n/* harmony export */   removeItem: () => (/* binding */ removeItem)\n/* harmony export */ });\nfunction addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvYXJyYXkubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUrQyIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9hcnJheS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYWRkVW5pcXVlSXRlbShhcnIsIGl0ZW0pIHtcbiAgICBpZiAoYXJyLmluZGV4T2YoaXRlbSkgPT09IC0xKVxuICAgICAgICBhcnIucHVzaChpdGVtKTtcbn1cbmZ1bmN0aW9uIHJlbW92ZUl0ZW0oYXJyLCBpdGVtKSB7XG4gICAgY29uc3QgaW5kZXggPSBhcnIuaW5kZXhPZihpdGVtKTtcbiAgICBpZiAoaW5kZXggPiAtMSlcbiAgICAgICAgYXJyLnNwbGljZShpbmRleCwgMSk7XG59XG4vLyBBZGFwdGVkIGZyb20gYXJyYXktbW92ZVxuZnVuY3Rpb24gbW92ZUl0ZW0oWy4uLmFycl0sIGZyb21JbmRleCwgdG9JbmRleCkge1xuICAgIGNvbnN0IHN0YXJ0SW5kZXggPSBmcm9tSW5kZXggPCAwID8gYXJyLmxlbmd0aCArIGZyb21JbmRleCA6IGZyb21JbmRleDtcbiAgICBpZiAoc3RhcnRJbmRleCA+PSAwICYmIHN0YXJ0SW5kZXggPCBhcnIubGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IGVuZEluZGV4ID0gdG9JbmRleCA8IDAgPyBhcnIubGVuZ3RoICsgdG9JbmRleCA6IHRvSW5kZXg7XG4gICAgICAgIGNvbnN0IFtpdGVtXSA9IGFyci5zcGxpY2UoZnJvbUluZGV4LCAxKTtcbiAgICAgICAgYXJyLnNwbGljZShlbmRJbmRleCwgMCwgaXRlbSk7XG4gICAgfVxuICAgIHJldHVybiBhcnI7XG59XG5cbmV4cG9ydCB7IGFkZFVuaXF1ZUl0ZW0sIG1vdmVJdGVtLCByZW1vdmVJdGVtIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/array.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/errors.mjs":
/*!******************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/errors.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant),\n/* harmony export */   warning: () => (/* binding */ warning)\n/* harmony export */ });\nlet warning = () => { };\nlet invariant = () => { };\nif (true) {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvZXJyb3JzLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQSxJQUFJLElBQXFDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvbm9kZV9tb2R1bGVzL21vdGlvbi11dGlscy9kaXN0L2VzL2Vycm9ycy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IHdhcm5pbmcgPSAoKSA9PiB7IH07XG5sZXQgaW52YXJpYW50ID0gKCkgPT4geyB9O1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHdhcm5pbmcgPSAoY2hlY2ssIG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKCFjaGVjayAmJiB0eXBlb2YgY29uc29sZSAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKG1lc3NhZ2UpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBpbnZhcmlhbnQgPSAoY2hlY2ssIG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKCFjaGVjaykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpO1xuICAgICAgICB9XG4gICAgfTtcbn1cblxuZXhwb3J0IHsgaW52YXJpYW50LCB3YXJuaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/errors.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/global-config.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/global-config.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionGlobalConfig: () => (/* binding */ MotionGlobalConfig)\n/* harmony export */ });\nconst MotionGlobalConfig = {\n    skipAnimations: false,\n    useManualTiming: false,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvZ2xvYmFsLWNvbmZpZy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4QiIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9nbG9iYWwtY29uZmlnLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBNb3Rpb25HbG9iYWxDb25maWcgPSB7XG4gICAgc2tpcEFuaW1hdGlvbnM6IGZhbHNlLFxuICAgIHVzZU1hbnVhbFRpbWluZzogZmFsc2UsXG59O1xuXG5leHBvcnQgeyBNb3Rpb25HbG9iYWxDb25maWcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/global-config.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/memo.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/memo.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memo: () => (/* binding */ memo)\n/* harmony export */ });\n/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvbWVtby5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvbWVtby5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyojX19OT19TSURFX0VGRkVDVFNfXyovXG5mdW5jdGlvbiBtZW1vKGNhbGxiYWNrKSB7XG4gICAgbGV0IHJlc3VsdDtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBpZiAocmVzdWx0ID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICByZXN1bHQgPSBjYWxsYmFjaygpO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH07XG59XG5cbmV4cG9ydCB7IG1lbW8gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/memo.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/noop.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/noop.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: () => (/* binding */ noop)\n/* harmony export */ });\n/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvbm9vcC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRWdCIiwic291cmNlcyI6WyIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvbm9kZV9tb2R1bGVzL21vdGlvbi11dGlscy9kaXN0L2VzL25vb3AubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qI19fTk9fU0lERV9FRkZFQ1RTX18qL1xuY29uc3Qgbm9vcCA9IChhbnkpID0+IGFueTtcblxuZXhwb3J0IHsgbm9vcCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/noop.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/progress.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/progress.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   progress: () => (/* binding */ progress)\n/* harmony export */ });\n/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvcHJvZ3Jlc3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFb0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvcHJvZ3Jlc3MubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG4gIFByb2dyZXNzIHdpdGhpbiBnaXZlbiByYW5nZVxuXG4gIEdpdmVuIGEgbG93ZXIgbGltaXQgYW5kIGFuIHVwcGVyIGxpbWl0LCB3ZSByZXR1cm4gdGhlIHByb2dyZXNzXG4gIChleHByZXNzZWQgYXMgYSBudW1iZXIgMC0xKSByZXByZXNlbnRlZCBieSB0aGUgZ2l2ZW4gdmFsdWUsIGFuZFxuICBsaW1pdCB0aGF0IHByb2dyZXNzIHRvIHdpdGhpbiAwLTEuXG5cbiAgQHBhcmFtIFtudW1iZXJdOiBMb3dlciBsaW1pdFxuICBAcGFyYW0gW251bWJlcl06IFVwcGVyIGxpbWl0XG4gIEBwYXJhbSBbbnVtYmVyXTogVmFsdWUgdG8gZmluZCBwcm9ncmVzcyB3aXRoaW4gZ2l2ZW4gcmFuZ2VcbiAgQHJldHVybiBbbnVtYmVyXTogUHJvZ3Jlc3Mgb2YgdmFsdWUgd2l0aGluIHJhbmdlIGFzIGV4cHJlc3NlZCAwLTFcbiovXG4vKiNfX05PX1NJREVfRUZGRUNUU19fKi9cbmNvbnN0IHByb2dyZXNzID0gKGZyb20sIHRvLCB2YWx1ZSkgPT4ge1xuICAgIGNvbnN0IHRvRnJvbURpZmZlcmVuY2UgPSB0byAtIGZyb207XG4gICAgcmV0dXJuIHRvRnJvbURpZmZlcmVuY2UgPT09IDAgPyAxIDogKHZhbHVlIC0gZnJvbSkgLyB0b0Zyb21EaWZmZXJlbmNlO1xufTtcblxuZXhwb3J0IHsgcHJvZ3Jlc3MgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/progress.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/subscription-manager.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/subscription-manager.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SubscriptionManager: () => (/* binding */ SubscriptionManager)\n/* harmony export */ });\n/* harmony import */ var _array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/array.mjs\");\n\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        (0,_array_mjs__WEBPACK_IMPORTED_MODULE_0__.addUniqueItem)(this.subscriptions, handler);\n        return () => (0,_array_mjs__WEBPACK_IMPORTED_MODULE_0__.removeItem)(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/subscription-manager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/time-conversion.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/time-conversion.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   millisecondsToSeconds: () => (/* binding */ millisecondsToSeconds),\n/* harmony export */   secondsToMilliseconds: () => (/* binding */ secondsToMilliseconds)\n/* harmony export */ });\n/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvdGltZS1jb252ZXJzaW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3RCIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy90aW1lLWNvbnZlcnNpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29udmVydHMgc2Vjb25kcyB0byBtaWxsaXNlY29uZHNcbiAqXG4gKiBAcGFyYW0gc2Vjb25kcyAtIFRpbWUgaW4gc2Vjb25kcy5cbiAqIEByZXR1cm4gbWlsbGlzZWNvbmRzIC0gQ29udmVydGVkIHRpbWUgaW4gbWlsbGlzZWNvbmRzLlxuICovXG4vKiNfX05PX1NJREVfRUZGRUNUU19fKi9cbmNvbnN0IHNlY29uZHNUb01pbGxpc2Vjb25kcyA9IChzZWNvbmRzKSA9PiBzZWNvbmRzICogMTAwMDtcbi8qI19fTk9fU0lERV9FRkZFQ1RTX18qL1xuY29uc3QgbWlsbGlzZWNvbmRzVG9TZWNvbmRzID0gKG1pbGxpc2Vjb25kcykgPT4gbWlsbGlzZWNvbmRzIC8gMTAwMDtcblxuZXhwb3J0IHsgbWlsbGlzZWNvbmRzVG9TZWNvbmRzLCBzZWNvbmRzVG9NaWxsaXNlY29uZHMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/time-conversion.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/velocity-per-second.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/velocity-per-second.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   velocityPerSecond: () => (/* binding */ velocityPerSecond)\n/* harmony export */ });\n/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvdmVsb2NpdHktcGVyLXNlY29uZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU2QiIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy92ZWxvY2l0eS1wZXItc2Vjb25kLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICBDb252ZXJ0IHZlbG9jaXR5IGludG8gdmVsb2NpdHkgcGVyIHNlY29uZFxuXG4gIEBwYXJhbSBbbnVtYmVyXTogVW5pdCBwZXIgZnJhbWVcbiAgQHBhcmFtIFtudW1iZXJdOiBGcmFtZSBkdXJhdGlvbiBpbiBtc1xuKi9cbmZ1bmN0aW9uIHZlbG9jaXR5UGVyU2Vjb25kKHZlbG9jaXR5LCBmcmFtZUR1cmF0aW9uKSB7XG4gICAgcmV0dXJuIGZyYW1lRHVyYXRpb24gPyB2ZWxvY2l0eSAqICgxMDAwIC8gZnJhbWVEdXJhdGlvbikgOiAwO1xufVxuXG5leHBvcnQgeyB2ZWxvY2l0eVBlclNlY29uZCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/velocity-per-second.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/warn-once.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/warn-once.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasWarned: () => (/* binding */ hasWarned),\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nconst warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvd2Fybi1vbmNlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFK0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvd2Fybi1vbmNlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB3YXJuZWQgPSBuZXcgU2V0KCk7XG5mdW5jdGlvbiBoYXNXYXJuZWQobWVzc2FnZSkge1xuICAgIHJldHVybiB3YXJuZWQuaGFzKG1lc3NhZ2UpO1xufVxuZnVuY3Rpb24gd2Fybk9uY2UoY29uZGl0aW9uLCBtZXNzYWdlLCBlbGVtZW50KSB7XG4gICAgaWYgKGNvbmRpdGlvbiB8fCB3YXJuZWQuaGFzKG1lc3NhZ2UpKVxuICAgICAgICByZXR1cm47XG4gICAgY29uc29sZS53YXJuKG1lc3NhZ2UpO1xuICAgIGlmIChlbGVtZW50KVxuICAgICAgICBjb25zb2xlLndhcm4oZWxlbWVudCk7XG4gICAgd2FybmVkLmFkZChtZXNzYWdlKTtcbn1cblxuZXhwb3J0IHsgaGFzV2FybmVkLCB3YXJuT25jZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/warn-once.mjs\n");

/***/ })

};
;