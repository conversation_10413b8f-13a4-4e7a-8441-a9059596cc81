{"/api/admin/add-client/route": "app/api/admin/add-client/route.js", "/api/admin/add-client-public/route": "app/api/admin/add-client-public/route.js", "/api/admin/campaigns/[campaign_id]/available-dates/route": "app/api/admin/campaigns/[campaign_id]/available-dates/route.js", "/api/admin/criadores/[id]/route": "app/api/admin/criadores/[id]/route.js", "/api/admin/criadores/route": "app/api/admin/criadores/route.js", "/api/admin/influencers/route": "app/api/admin/influencers/route.js", "/api/admin/influencers/[id]/route": "app/api/admin/influencers/[id]/route.js", "/api/admin/negocios/[id]/route": "app/api/admin/negocios/[id]/route.js", "/api/admin/negocios/route": "app/api/admin/negocios/route.js", "/api/admin/negocios/create-test/route": "app/api/admin/negocios/create-test/route.js", "/api/auth/instagram/route": "app/api/auth/instagram/route.js", "/api/auth/tiktok/callback/route": "app/api/auth/tiktok/callback/route.js", "/api/create-test-data/route": "app/api/create-test-data/route.js", "/api/digital-presence/sync/route": "app/api/digital-presence/sync/route.js", "/api/instagram-callback/route": "app/api/instagram-callback/route.js", "/api/instagram-webhook/route": "app/api/instagram-webhook/route.js", "/api/instagram/webhook/route": "app/api/instagram/webhook/route.js", "/api/invite/create/route": "app/api/invite/create/route.js", "/api/invite/accept/route": "app/api/invite/accept/route.js", "/api/invite/accept-simple/route": "app/api/invite/accept-simple/route.js", "/api/placeholder/svg/route": "app/api/placeholder/svg/route.js", "/api/invite/validate/route": "app/api/invite/validate/route.js", "/api/setup/create-user/route": "app/api/setup/create-user/route.js", "/api/setup/create-influencer/route": "app/api/setup/create-influencer/route.js", "/api/setup/add-gustavucaliani/route": "app/api/setup/add-gustavucaliani/route.js", "/api/setup/simple-add-client/route": "app/api/setup/simple-add-client/route.js", "/api/test-supabase/route": "app/api/test-supabase/route.js", "/api/test/check-instagram-connection/route": "app/api/test/check-instagram-connection/route.js", "/api/test/create-influencer/route": "app/api/test/create-influencer/route.js", "/api/test/instagram/connect/route": "app/api/test/instagram/connect/route.js", "/api/test/instagram/callback/route": "app/api/test/instagram/callback/route.js", "/api/test/instagram/simulate-connection/route": "app/api/test/instagram/simulate-connection/route.js", "/api/test/create-simple-user/route": "app/api/test/create-simple-user/route.js", "/api/test/instagram/simulate-post/route": "app/api/test/instagram/simulate-post/route.js", "/api/test/route": "app/api/test/route.js", "/api/test/instagram/simulate-webhook/route": "app/api/test/instagram/simulate-webhook/route.js", "/api/test/simulate-instagram-connection-v2/route": "app/api/test/simulate-instagram-connection-v2/route.js", "/api/test/simulate-instagram-connection-v3/route": "app/api/test/simulate-instagram-connection-v3/route.js", "/api/test/simulate-instagram-post/route": "app/api/test/simulate-instagram-post/route.js", "/api/test/simulate-instagram-connection/route": "app/api/test/simulate-instagram-connection/route.js", "/api/test/simulate-instagram-webhook/route": "app/api/test/simulate-instagram-webhook/route.js", "/api/test/sync-instagram-posts/route": "app/api/test/sync-instagram-posts/route.js", "/api/test/utils/status/route": "app/api/test/utils/status/route.js", "/api/v1/campaigns/content/feedback/route": "app/api/v1/campaigns/content/feedback/route.js", "/api/v1/campaigns/content/revision/route": "app/api/v1/campaigns/content/revision/route.js", "/api/v1/campaigns/content/review/route": "app/api/v1/campaigns/content/review/route.js", "/api/v1/campaigns/content/submit/route": "app/api/v1/campaigns/content/submit/route.js", "/api/test/webhook-test/route": "app/api/test/webhook-test/route.js", "/api/v1/campaigns/schedule/available-dates/route": "app/api/v1/campaigns/schedule/available-dates/route.js", "/api/v1/campaigns/leaderboard/route": "app/api/v1/campaigns/leaderboard/route.js", "/api/v1/cron/process-notifications/route": "app/api/v1/cron/process-notifications/route.js", "/api/v1/campaigns/schedule/cancel/route": "app/api/v1/campaigns/schedule/cancel/route.js", "/api/v1/campaigns/schedule/book/route": "app/api/v1/campaigns/schedule/book/route.js", "/api/v1/cron/weekly-reports/route": "app/api/v1/cron/weekly-reports/route.js", "/api/v1/health/route": "app/api/v1/health/route.js", "/api/v1/instagram/connect/route": "app/api/v1/instagram/connect/route.js", "/api/v1/instagram/callback/route": "app/api/v1/instagram/callback/route.js", "/api/v1/notifications/settings/route": "app/api/v1/notifications/settings/route.js", "/api/v1/instagram/sync/route": "app/api/v1/instagram/sync/route.js", "/api/v1/instagram/webhook/route": "app/api/v1/instagram/webhook/route.js", "/api/v1/ratings/route": "app/api/v1/ratings/route.js", "/api/v1/posts/metrics/update/route": "app/api/v1/posts/metrics/update/route.js", "/api/v1/schedule/cancel/route": "app/api/v1/schedule/cancel/route.js", "/api/v1/notifications/test/route": "app/api/v1/notifications/test/route.js", "/api/v1/schedule/criador/route": "app/api/v1/schedule/criador/route.js", "/api/v1/schedule/complete/route": "app/api/v1/schedule/complete/route.js", "/api/v1/schedule/campaign/[id]/route": "app/api/v1/schedule/campaign/[id]/route.js", "/api/v1/schedule/influencer/route": "app/api/v1/schedule/influencer/route.js", "/api/v1/tasks/[id]/comments/route": "app/api/v1/tasks/[id]/comments/route.js", "/api/v1/tasks/[id]/route": "app/api/v1/tasks/[id]/route.js", "/api/v1/tasks/route": "app/api/v1/tasks/route.js", "/api/v1/whatsapp/mock-verify/route": "app/api/v1/whatsapp/mock-verify/route.js", "/api/v1/whatsapp/send-template/route": "app/api/v1/whatsapp/send-template/route.js", "/api/v1/whatsapp/send/route": "app/api/v1/whatsapp/send/route.js", "/api/v1/tasks/[id]/complete/route": "app/api/v1/tasks/[id]/complete/route.js", "/api/v1/whatsapp/webhook/route": "app/api/v1/whatsapp/webhook/route.js", "/api/whatsapp/webhook/route": "app/api/whatsapp/webhook/route.js", "/api/v1/whatsapp/test/route": "app/api/v1/whatsapp/test/route.js", "/api/v1/whatsapp/verify/route": "app/api/v1/whatsapp/verify/route.js", "/instagram-webhook/route": "app/instagram-webhook/route.js", "/webhook/route": "app/webhook/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/admin/campaigns/create-test/route": "app/api/admin/campaigns/create-test/route.js", "/api/admin/campaigns/create/route": "app/api/admin/campaigns/create/route.js", "/api/admin/campaigns/[campaign_id]/[id]/route": "app/api/admin/campaigns/[campaign_id]/[id]/route.js", "/api/admin/campaigns/[campaign_id]/route": "app/api/admin/campaigns/[campaign_id]/route.js", "/api/admin/create-tables/route": "app/api/admin/create-tables/route.js", "/api/admin/campaigns/update/route": "app/api/admin/campaigns/update/route.js", "/api/admin/campaigns/route": "app/api/admin/campaigns/route.js", "/api/auth/callback/route": "app/api/auth/callback/route.js", "/api/auth/instagram/callback/route": "app/api/auth/instagram/callback/route.js", "/_not-found/page": "app/_not-found/page.js", "/admin-test/page": "app/admin-test/page.js", "/admin/whatsapp/page": "app/admin/whatsapp/page.js", "/admin/test-api/page": "app/admin/test-api/page.js", "/criador/configuracoes/page": "app/criador/configuracoes/page.js", "/dev/instagram-test/page": "app/dev/instagram-test/page.js", "/dev/test-data/page": "app/dev/test-data/page.js", "/login-simple/page": "app/login-simple/page.js", "/redirect/page": "app/redirect/page.js", "/exemplos-popups/page": "app/exemplos-popups/page.js", "/popups-demo/page": "app/popups-demo/page.js", "/direct-test/page": "app/direct-test/page.js", "/simple-test/page": "app/simple-test/page.js", "/page": "app/page.js", "/test/create-simple-user/page": "app/test/create-simple-user/page.js", "/whatsapp-api-test/page": "app/whatsapp-api-test/page.js", "/test/create-influencer/page": "app/test/create-influencer/page.js", "/whatsapp-dashboard/page": "app/whatsapp-dashboard/page.js", "/whatsapp-messages/page": "app/whatsapp-messages/page.js", "/whatsapp-mock-test/page": "app/whatsapp-mock-test/page.js", "/whatsapp-test/page": "app/whatsapp-test/page.js", "/(auth)/change-password/page": "app/(auth)/change-password/page.js", "/(auth)/registro/page": "app/(auth)/registro/page.js", "/(dashboard)/exemplos/popups-arrastaveis/page": "app/(dashboard)/exemplos/popups-arrastaveis/page.js", "/(auth)/logout/page": "app/(auth)/logout/page.js", "/(dashboard)/exemplos/popups-aninhados/page": "app/(dashboard)/exemplos/popups-aninhados/page.js", "/(dashboard)/exemplos/popups-otimizados/page": "app/(dashboard)/exemplos/popups-otimizados/page.js", "/(auth)/convite/page": "app/(auth)/convite/page.js", "/(dashboard)/exemplos/popups-padronizados/page": "app/(dashboard)/exemplos/popups-padronizados/page.js", "/(dashboard)/exemplos/popups/page": "app/(dashboard)/exemplos/popups/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(dashboard)/design-options/page": "app/(dashboard)/design-options/page.js", "/(dashboard)/plano-novo/page": "app/(dashboard)/plano-novo/page.js", "/(dashboard)/exemplos/tema/page": "app/(dashboard)/exemplos/tema/page.js", "/(dashboard)/exemplos/popups-dinamicos/page": "app/(dashboard)/exemplos/popups-dinamicos/page.js", "/(dashboard)/restaurante-novo/page": "app/(dashboard)/restaurante-novo/page.js", "/(dashboard)/plano/page": "app/(dashboard)/plano/page.js", "/(public)/privacy-policy/page": "app/(public)/privacy-policy/page.js", "/(public)/politica-de-privacidade/page": "app/(public)/politica-de-privacidade/page.js", "/(test)/setup/create-influencer/page": "app/(test)/setup/create-influencer/page.js", "/(test)/setup/add-user/page": "app/(test)/setup/add-user/page.js", "/(public)/privacy/page": "app/(public)/privacy/page.js", "/(test)/setup/page": "app/(test)/setup/page.js", "/(test)/setup/create-user/page": "app/(test)/setup/create-user/page.js", "/(test)/demo/instagram-integration/page": "app/(test)/demo/instagram-integration/page.js", "/(test)/setup/check-config/page": "app/(test)/setup/check-config/page.js", "/(test)/setup/client-side-add/page": "app/(test)/setup/client-side-add/page.js", "/(test)/setup/simple-add-client/page": "app/(test)/setup/simple-add-client/page.js", "/(test)/webhook-test/page": "app/(test)/webhook-test/page.js", "/(test)/setup/minimal-signup/page": "app/(test)/setup/minimal-signup/page.js", "/(test)/test-supabase/page": "app/(test)/test-supabase/page.js", "/(test)/setup/test-flow/page": "app/(test)/setup/test-flow/page.js", "/(test)/setup/test-accept/page": "app/(test)/setup/test-accept/page.js", "/(test)/setup/simple-test/page": "app/(test)/setup/simple-test/page.js", "/dashboard/page": "app/dashboard/page.js", "/(test)/setup/test-simple/page": "app/(test)/setup/test-simple/page.js", "/(admin)/admin/add-client-public/page": "app/(admin)/admin/add-client-public/page.js", "/(admin)/admin/criadores/[id]/edit/page": "app/(admin)/admin/criadores/[id]/edit/page.js", "/(admin)/admin/criadores/new/page": "app/(admin)/admin/criadores/new/page.js", "/(admin)/admin/campaigns/[campaign_id]/edit/page": "app/(admin)/admin/campaigns/[campaign_id]/edit/page.js", "/(admin)/admin/campaigns/new/page": "app/(admin)/admin/campaigns/new/page.js", "/(admin)/admin/add-client-simple/page": "app/(admin)/admin/add-client-simple/page.js", "/(admin)/admin/campaigns/page": "app/(admin)/admin/campaigns/page.js", "/(admin)/admin/campaigns/[campaign_id]/page": "app/(admin)/admin/campaigns/[campaign_id]/page.js", "/(admin)/admin/add-client/page": "app/(admin)/admin/add-client/page.js", "/(admin)/admin/negocios/[id]/edit/page": "app/(admin)/admin/negocios/[id]/edit/page.js", "/(admin)/admin/criadores/page": "app/(admin)/admin/criadores/page.js", "/(admin)/admin/negocios/page": "app/(admin)/admin/negocios/page.js", "/(admin)/admin/settings/page": "app/(admin)/admin/settings/page.js", "/(admin)/admin/negocios/new/page": "app/(admin)/admin/negocios/new/page.js", "/(admin)/admin/invite/page": "app/(admin)/admin/invite/page.js", "/(admin)/admin/page": "app/(admin)/admin/page.js", "/(admin)/admin/negocios/[id]/page": "app/(admin)/admin/negocios/[id]/page.js", "/(admin)/admin/criadores/[id]/page": "app/(admin)/admin/criadores/[id]/page.js", "/(dashboard)/criador/campanhas/page": "app/(dashboard)/criador/campanhas/page.js", "/(dashboard)/criador/page": "app/(dashboard)/criador/page.js", "/(dashboard)/restaurante/configuracoes/page": "app/(dashboard)/restaurante/configuracoes/page.js", "/(dashboard)/criador/campanhas/[id]/videos/page": "app/(dashboard)/criador/campanhas/[id]/videos/page.js", "/(dashboard)/restaurante/presenca-digital/page": "app/(dashboard)/restaurante/presenca-digital/page.js", "/(dashboard)/restaurante/presenca-digital-exemplo/page": "app/(dashboard)/restaurante/presenca-digital-exemplo/page.js", "/(dashboard)/restaurante/page": "app/(dashboard)/restaurante/page.js"}