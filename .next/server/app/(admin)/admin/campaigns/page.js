/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(admin)/admin/campaigns/page";
exports.ids = ["app/(admin)/admin/campaigns/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&page=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&appPaths=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&pagePath=private-next-app-dir%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&page=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&appPaths=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&pagePath=private-next-app-dir%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/layout.tsx */ \"(rsc)/./src/app/(admin)/layout.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/admin/layout.tsx */ \"(rsc)/./src/app/(admin)/admin/layout.tsx\"));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/admin/campaigns/page.tsx */ \"(rsc)/./src/app/(admin)/admin/campaigns/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(admin)',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'campaigns',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module7, \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout.tsx\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\"],\n'not-found': [module1, \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/not-found.tsx\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(admin)/admin/campaigns/page\",\n        pathname: \"/admin/campaigns\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&page=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&appPaths=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&pagePath=private-next-app-dir%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmFwcC1kaXIlMkZsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFnTCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hcHAtZGlyL2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/admin/campaigns/page.tsx */ \"(rsc)/./src/app/(admin)/admin/campaigns/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkYoYWRtaW4pJTJGYWRtaW4lMkZjYW1wYWlnbnMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2FwcC8oYWRtaW4pL2FkbWluL2NhbXBhaWducy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/admin/layout.tsx */ \"(rsc)/./src/app/(admin)/admin/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkYoYWRtaW4pJTJGYWRtaW4lMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBeUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvYXBwLyhhZG1pbikvYWRtaW4vbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/layout.tsx */ \"(rsc)/./src/app/(admin)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkYoYWRtaW4pJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2FwcC8oYWRtaW4pL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fdev-hide.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-purple-border.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-width.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fcomponents%2Flayout%2FMainLayoutClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fstyles%2Fthemes.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fremove-space.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fdev-hide.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-purple-border.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-width.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fcomponents%2Flayout%2FMainLayoutClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fstyles%2Fthemes.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fremove-space.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/MainLayoutClient.tsx */ \"(rsc)/./src/components/layout/MainLayoutClient.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmx1aXp2aW5jZW56aSUyRkRvY3VtZW50cyUyRkFJX1Byb2plY3RzJTJGQ3JpYWRvcmVzJTJGc3JjJTJGYXBwJTJGZGV2LWhpZGUuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkZmaXgtcHVycGxlLWJvcmRlci5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRmFwcCUyRmZpeC13aWR0aC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZNYWluTGF5b3V0Q2xpZW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRnN0eWxlcyUyRnRoZW1lcy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRmFwcCUyRnJlbW92ZS1zcGFjZS5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUFnSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvY29tcG9uZW50cy9sYXlvdXQvTWFpbkxheW91dENsaWVudC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fdev-hide.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-purple-border.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-width.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fcomponents%2Flayout%2FMainLayoutClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fstyles%2Fthemes.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fremove-space.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(admin)/admin/campaigns/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(admin)/admin/campaigns/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/(admin)/admin/layout.tsx":
/*!******************************************!*\
  !*** ./src/app/(admin)/admin/layout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/(admin)/layout.tsx":
/*!************************************!*\
  !*** ./src/app/(admin)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dev-hide.css":
/*!******************************!*\
  !*** ./src/app/dev-hide.css ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4cfa358326bf\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Rldi1oaWRlLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL3NyYy9hcHAvZGV2LWhpZGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGNmYTM1ODMyNmJmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dev-hide.css\n");

/***/ }),

/***/ "(rsc)/./src/app/fix-purple-border.css":
/*!***************************************!*\
  !*** ./src/app/fix-purple-border.css ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8daa8862a76b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2ZpeC1wdXJwbGUtYm9yZGVyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL3NyYy9hcHAvZml4LXB1cnBsZS1ib3JkZXIuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOGRhYTg4NjJhNzZiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/fix-purple-border.css\n");

/***/ }),

/***/ "(rsc)/./src/app/fix-width.css":
/*!*******************************!*\
  !*** ./src/app/fix-width.css ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"71886f8f3ec4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2ZpeC13aWR0aC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvYXBwL2ZpeC13aWR0aC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTg4NmY4ZjNlYzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/fix-width.css\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c3180661817a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImMzMTgwNjYxODE3YVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _dev_hide_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dev-hide.css */ \"(rsc)/./src/app/dev-hide.css\");\n/* harmony import */ var _styles_themes_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/themes.css */ \"(rsc)/./src/styles/themes.css\");\n/* harmony import */ var _fix_purple_border_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./fix-purple-border.css */ \"(rsc)/./src/app/fix-purple-border.css\");\n/* harmony import */ var _remove_space_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./remove-space.css */ \"(rsc)/./src/app/remove-space.css\");\n/* harmony import */ var _fix_width_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./fix-width.css */ \"(rsc)/./src/app/fix-width.css\");\n/* harmony import */ var _components_layout_MainLayoutClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/MainLayoutClient */ \"(rsc)/./src/components/layout/MainLayoutClient.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"crIAdores - Conectando Restaurantes e Influenciadores\",\n    description: \"Plataforma que conecta restaurantes e influenciadores para impulsionar negócios locais\",\n    icons: {\n        icon: '/images/logo-triangle.svg',\n        apple: '/images/logo-triangle.svg'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        src: \"/remove-dev-menu.js\",\n                        defer: true\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased w-full overflow-x-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MainLayoutClient__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-4 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-4\",\n                children: \"404 - P\\xe1gina n\\xe3o encontrada\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8\",\n                children: \"A p\\xe1gina que voc\\xea est\\xe1 procurando n\\xe3o existe ou foi movida.\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: \"/\",\n                className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                children: \"Voltar para a p\\xe1gina inicial\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/not-found.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMEI7QUFDRztBQUVkLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQTBCOzs7Ozs7MEJBQ3hDLDhEQUFDRTtnQkFBRUYsV0FBVTswQkFBTzs7Ozs7OzBCQUNwQiw4REFBQ0gsa0RBQUlBO2dCQUFDTSxNQUFLO2dCQUFJSCxXQUFVOzBCQUErRTs7Ozs7Ozs7Ozs7O0FBSzlHIiwic291cmNlcyI6WyIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2FwcC9ub3QtZm91bmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlbiBwLTQgdGV4dC1jZW50ZXJcIj5cbiAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgbWItNFwiPjQwNCAtIFDDoWdpbmEgbsOjbyBlbmNvbnRyYWRhPC9oMT5cbiAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLThcIj5BIHDDoWdpbmEgcXVlIHZvY8OqIGVzdMOhIHByb2N1cmFuZG8gbsOjbyBleGlzdGUgb3UgZm9pIG1vdmlkYS48L3A+XG4gICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgaG92ZXI6YmctYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgVm9sdGFyIHBhcmEgYSBww6FnaW5hIGluaWNpYWxcbiAgICAgIDwvTGluaz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxpbmsiLCJOb3RGb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsImhyZWYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/remove-space.css":
/*!**********************************!*\
  !*** ./src/app/remove-space.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7cf7c69b3c0d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3JlbW92ZS1zcGFjZS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvYXBwL3JlbW92ZS1zcGFjZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3Y2Y3YzY5YjNjMGRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/remove-space.css\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/MainLayoutClient.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/MainLayoutClient.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/layout/MainLayoutClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/layout/MainLayoutClient.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/styles/themes.css":
/*!*******************************!*\
  !*** ./src/styles/themes.css ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"97356e042609\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3RoZW1lcy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvc3R5bGVzL3RoZW1lcy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NzM1NmUwNDI2MDlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/themes.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmFwcC1kaXIlMkZsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFnTCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hcHAtZGlyL2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/admin/campaigns/page.tsx */ \"(ssr)/./src/app/(admin)/admin/campaigns/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkYoYWRtaW4pJTJGYWRtaW4lMkZjYW1wYWlnbnMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQWlJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2FwcC8oYWRtaW4pL2FkbWluL2NhbXBhaWducy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/admin/layout.tsx */ \"(ssr)/./src/app/(admin)/admin/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkYoYWRtaW4pJTJGYWRtaW4lMkZsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBeUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvYXBwLyhhZG1pbikvYWRtaW4vbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Fadmin%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(admin)/layout.tsx */ \"(ssr)/./src/app/(admin)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkYoYWRtaW4pJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2FwcC8oYWRtaW4pL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2F(admin)%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fdev-hide.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-purple-border.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-width.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fcomponents%2Flayout%2FMainLayoutClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fstyles%2Fthemes.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fremove-space.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fdev-hide.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-purple-border.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-width.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fcomponents%2Flayout%2FMainLayoutClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fstyles%2Fthemes.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fremove-space.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/MainLayoutClient.tsx */ \"(ssr)/./src/components/layout/MainLayoutClient.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmx1aXp2aW5jZW56aSUyRkRvY3VtZW50cyUyRkFJX1Byb2plY3RzJTJGQ3JpYWRvcmVzJTJGc3JjJTJGYXBwJTJGZGV2LWhpZGUuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbHVpenZpbmNlbnppJTJGRG9jdW1lbnRzJTJGQUlfUHJvamVjdHMlMkZDcmlhZG9yZXMlMkZzcmMlMkZhcHAlMkZmaXgtcHVycGxlLWJvcmRlci5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRmFwcCUyRmZpeC13aWR0aC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZNYWluTGF5b3V0Q2xpZW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRnN0eWxlcyUyRnRoZW1lcy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsdWl6dmluY2VuemklMkZEb2N1bWVudHMlMkZBSV9Qcm9qZWN0cyUyRkNyaWFkb3JlcyUyRnNyYyUyRmFwcCUyRnJlbW92ZS1zcGFjZS5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUFnSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvY29tcG9uZW50cy9sYXlvdXQvTWFpbkxheW91dENsaWVudC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fdev-hide.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-purple-border.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Ffix-width.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fcomponents%2Flayout%2FMainLayoutClient.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fstyles%2Fthemes.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp%2Fremove-space.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(admin)/admin/campaigns/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(admin)/admin/campaigns/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CampaignsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _components_admin_AdminPageWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/AdminPageWrapper */ \"(ssr)/./src/components/admin/AdminPageWrapper.tsx\");\n/* harmony import */ var _components_ui_StandardButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/StandardButton */ \"(ssr)/./src/components/ui/StandardButton.tsx\");\n/* harmony import */ var _components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/DataTable */ \"(ssr)/./src/components/ui/DataTable.tsx\");\n/* harmony import */ var _barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FaEdit,FaEye,FaFilter,FaPlus,FaSearch,FaTrash!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction CampaignsPage() {\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // const [creatingTestData, setCreatingTestData] = useState(false); // Removido estado não utilizado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CampaignsPage.useEffect\": ()=>{\n            const fetchCampaigns = {\n                \"CampaignsPage.useEffect.fetchCampaigns\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.from('campaigns').select('*, restaurants(name)').order('created_at', {\n                            ascending: false\n                        });\n                        if (error) throw error;\n                        setCampaigns(data || []);\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    } catch (_error) {\n                        console.warn('Erro ao buscar campanhas:', _error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_7__.toast.error('Erro ao carregar campanhas');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"CampaignsPage.useEffect.fetchCampaigns\"];\n            fetchCampaigns();\n        }\n    }[\"CampaignsPage.useEffect\"], []);\n    const filteredCampaigns = campaigns.filter((campaign)=>{\n        const matchesSearch = searchTerm === '' || campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) || (campaign.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false) || (campaign.restaurants?.name.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);\n        const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    const formatDate = (dateString)=>{\n        if (!dateString) return '';\n        const date = new Date(dateString);\n        return date.toLocaleDateString('pt-BR');\n    };\n    const getStatusColorClass = (status)=>{\n        switch(status){\n            case 'draft':\n                return 'bg-gray-100 text-gray-800';\n            case 'active':\n                return 'bg-green-100 text-green-800';\n            case 'completed':\n                return 'bg-blue-100 text-blue-800';\n            case 'cancelled':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusLabel = (status)=>{\n        switch(status){\n            case 'draft':\n                return 'Rascunho';\n            case 'active':\n                return 'Ativa';\n            case 'completed':\n                return 'Concluída';\n            case 'cancelled':\n                return 'Cancelada';\n            default:\n                return status;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminPageWrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Campanhas\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaSearch, {\n                                            className: \"text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Buscar campanhas...\",\n                                        className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaFilter, {\n                                            className: \"text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"Todos os status\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"draft\",\n                                                children: \"Rascunho\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"Ativa\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"Conclu\\xedda\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"cancelled\",\n                                                children: \"Cancelada\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/campaigns/new\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StandardButton__WEBPACK_IMPORTED_MODULE_5__.StandardButton, {\n                                variant: \"primary\",\n                                size: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaPlus, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Nova Campanha\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-md p-4 sm:p-6\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this) : filteredCampaigns.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mb-4\",\n                            children: \"Nenhuma campanha encontrada\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/campaigns/new\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StandardButton__WEBPACK_IMPORTED_MODULE_5__.StandardButton, {\n                                variant: \"primary\",\n                                size: \"md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaPlus, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Criar Nova Campanha\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTable, {\n                    headers: [\n                        'Nome',\n                        'Restaurante',\n                        'Período',\n                        'Status',\n                        'Ações'\n                    ],\n                    loading: loading,\n                    emptyMessage: \"Nenhuma campanha encontrada\",\n                    children: filteredCampaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTableRow, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTableCell, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: campaign.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: campaign.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTableCell, {\n                                    children: campaign.restaurants?.name || 'N/A'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTableCell, {\n                                    children: [\n                                        formatDate(campaign.start_date),\n                                        \" a \",\n                                        formatDate(campaign.end_date)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColorClass(campaign.status)}`,\n                                        children: getStatusLabel(campaign.status)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DataTable__WEBPACK_IMPORTED_MODULE_6__.DataTableCell, {\n                                    className: \"text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/admin/campaigns/${campaign.id}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StandardButton__WEBPACK_IMPORTED_MODULE_5__.StandardButton, {\n                                                    variant: \"secondary\",\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaEye, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/admin/campaigns/${campaign.id}/edit`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StandardButton__WEBPACK_IMPORTED_MODULE_5__.StandardButton, {\n                                                    variant: \"secondary\",\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaEdit, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StandardButton__WEBPACK_IMPORTED_MODULE_5__.StandardButton, {\n                                                variant: \"destructive\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    if (confirm('Tem certeza que deseja excluir esta campanha?')) {\n                                                    // TODO: Implement delete functionality\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEdit_FaEye_FaFilter_FaPlus_FaSearch_FaTrash_react_icons_fa__WEBPACK_IMPORTED_MODULE_8__.FaTrash, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, campaign.id, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(admin)/admin/campaigns/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(admin)/admin/components/NavigationTabs.tsx":
/*!*************************************************************!*\
  !*** ./src/app/(admin)/admin/components/NavigationTabs.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FaBullhorn_FaUsers_FaUtensils_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaBullhorn,FaUsers,FaUtensils!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NavigationTabs() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = (path)=>{\n        return pathname === path || pathname.startsWith(`${path}/`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center overflow-x-auto bg-[#f5f5f5] navigation-tabs\",\n        style: {\n            backgroundColor: '#f5f5f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/admin/restaurants\",\n                style: {\n                    backgroundColor: '#f5f5f5'\n                },\n                className: `px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ${isActive('/admin/restaurants') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center bg-[#f5f5f5]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBullhorn_FaUsers_FaUtensils_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUtensils, {\n                            className: \"mr-2 bg-[#f5f5f5]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-[#f5f5f5]\",\n                            children: \"Restaurantes\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/admin/campaigns\",\n                style: {\n                    backgroundColor: '#f5f5f5'\n                },\n                className: `px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ${isActive('/admin/campaigns') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center bg-[#f5f5f5]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBullhorn_FaUsers_FaUtensils_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullhorn, {\n                            className: \"mr-2 bg-[#f5f5f5]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-[#f5f5f5]\",\n                            children: \"Campanhas\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/admin/influencers\",\n                style: {\n                    backgroundColor: '#f5f5f5'\n                },\n                className: `px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ${isActive('/admin/influencers') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center bg-[#f5f5f5]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBullhorn_FaUsers_FaUtensils_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUsers, {\n                            className: \"mr-2 bg-[#f5f5f5]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-[#f5f5f5]\",\n                            children: \"Influenciadores\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(admin)/admin/components/NavigationTabs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(admin)/admin/layout.tsx":
/*!******************************************!*\
  !*** ./src/app/(admin)/admin/layout.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// import NavigationTabs from './components/NavigationTabs'; // Removido\n// import Logo from '@/components/Logo'; // Removido\nfunction AdminDashboardLayout({ children }) {\n    // Adicionar uma variável de estado para forçar uma única renderização\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboardLayout.useEffect\": ()=>{\n            // Adicionar uma classe ao body para indicar que estamos na página do admin\n            document.body.classList.add('admin-page');\n            // Definir uma flag no localStorage para evitar recarregamentos desnecessários\n            if (false) {}\n            return ({\n                \"AdminDashboardLayout.useEffect\": ()=>{\n                    document.body.classList.remove('admin-page');\n                }\n            })[\"AdminDashboardLayout.useEffect\"];\n        }\n    }[\"AdminDashboardLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen h-screen overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-12 px-4 pb-2 w-full flex flex-col\",\n            children: [\n                \" \",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full flex flex-col flex-grow overflow-hidden\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(admin)/admin/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(admin)/layout.tsx":
/*!************************************!*\
  !*** ./src/app/(admin)/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AppBarContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AppBarContext */ \"(ssr)/./src/contexts/AppBarContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AdminLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen h-screen bg-f5f5f7 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AppBarContext__WEBPACK_IMPORTED_MODULE_1__.AppBarProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhhZG1pbikvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUkwRDtBQUUzQyxTQUFTQyxZQUFZLEVBQUVDLFFBQVEsRUFBMkI7SUFDdkUscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNKLG1FQUFjQTtzQkFDWkU7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvYXBwLyhhZG1pbikvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuXG5pbXBvcnQgeyBBcHBCYXJQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXBwQmFyQ29udGV4dCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluTGF5b3V0KHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBoLXNjcmVlbiBiZy1mNWY1Zjcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICA8QXBwQmFyUHJvdmlkZXI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvQXBwQmFyUHJvdmlkZXI+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXBwQmFyUHJvdmlkZXIiLCJBZG1pbkxheW91dCIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(admin)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// import Image from 'next/image'; // Removido\n\nfunction Logo({ className = '', textClassName = 'text-xl font-semibold', showText: _showText = true, size = 'medium', href = '/' }) {\n    const sizeMap = {\n        small: 24,\n        medium: 32,\n        large: 48\n    };\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _logoSize = sizeMap[size]; // logoSize prefixado\n    return href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: href,\n        className: `flex items-center ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: textClassName,\n            children: \"crIAdores\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: textClassName,\n            children: \"crIAdores\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/admin/AdminPageWrapper.tsx":
/*!***************************************************!*\
  !*** ./src/components/admin/AdminPageWrapper.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPageWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FaArrowLeft_FaCog_FaQuestionCircle_FaSignOutAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowLeft,FaCog,FaQuestionCircle,FaSignOutAlt!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_auth_AdminProtected__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/AdminProtected */ \"(ssr)/./src/components/auth/AdminProtected.tsx\");\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _app_admin_admin_components_NavigationTabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/(admin)/admin/components/NavigationTabs */ \"(ssr)/./src/app/(admin)/admin/components/NavigationTabs.tsx\");\n/* harmony import */ var _components_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/DropdownMenu */ \"(ssr)/./src/components/ui/DropdownMenu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n // FaHome removido\n\n\n\n\n\n\nfunction AdminPageWrapper({ children, title, backLink }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSignOut = async ()=>{\n        try {\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signOut();\n            if (error) throw error;\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success('Logout realizado com sucesso');\n            router.push('/login');\n        } catch (error) {\n            console.error('Erro ao fazer logout:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error('Erro ao fazer logout');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_AdminProtected__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"fixed top-0 left-0 right-0 flex justify-between items-center px-6 py-1.5 bg-[#f5f5f5] z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: \"medium\",\n                                textClassName: \"text-xl font-bold\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-1/2 transform -translate-x-1/2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_admin_admin_components_NavigationTabs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                trigger: \"Admin\",\n                                items: [\n                                    {\n                                        label: \"Configurações\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaCog_FaQuestionCircle_FaSignOutAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaCog, {\n                                            className: \"text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        onClick: ()=>router.push('/admin/settings')\n                                    },\n                                    {\n                                        label: \"Ajuda\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaCog_FaQuestionCircle_FaSignOutAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaQuestionCircle, {\n                                            className: \"text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        onClick: ()=>window.open('https://help.connectcity.com.br', '_blank')\n                                    },\n                                    {\n                                        label: \"Logout\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaCog_FaQuestionCircle_FaSignOutAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaSignOutAlt, {\n                                            className: \"text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        onClick: handleSignOut\n                                    }\n                                ]\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push(backLink),\n                        className: \"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowLeft_FaCog_FaQuestionCircle_FaSignOutAlt_react_icons_fa__WEBPACK_IMPORTED_MODULE_9__.FaArrowLeft, {\n                            className: \"text-gray-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"p-5 pt-4 rounded-xl bg-white overflow-y-auto flex-1 flex flex-col shadow-md\",\n                    style: {\n                        minHeight: 'calc(100vh - 6rem)',\n                        maxHeight: 'calc(100vh - 6rem)'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-6\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/admin/AdminPageWrapper.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/admin/AdminPageWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AdminProtected.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/AdminProtected.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminProtected)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _config_admins__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/admins */ \"(ssr)/./src/config/admins.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AdminProtected({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProtected.useEffect\": ()=>{\n            const checkAdminStatus = {\n                \"AdminProtected.useEffect.checkAdminStatus\": async ()=>{\n                    try {\n                        // Verificar se o usuário está autenticado\n                        const { data: { session }, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getSession();\n                        if (error) {\n                            throw error;\n                        }\n                        if (!session) {\n                            // Usuário não está autenticado, redirecionar para login\n                            router.push('/login?redirect=/admin/campaigns');\n                            return;\n                        }\n                        // Verificar se o email do usuário está na lista de administradores\n                        const userEmail = session.user.email;\n                        if (userEmail && (0,_config_admins__WEBPACK_IMPORTED_MODULE_4__.isAdminEmail)(userEmail)) {\n                            setIsAdmin(true);\n                        } else {\n                            // Usuário não é administrador, redirecionar para a página inicial\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Você não tem permissão para acessar esta área');\n                            router.push('/');\n                        }\n                    } catch (error) {\n                        console.error('Erro ao verificar status de administrador:', error);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Erro ao verificar suas permissões');\n                        router.push('/');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdminProtected.useEffect.checkAdminStatus\"];\n            checkAdminStatus();\n        }\n    }[\"AdminProtected.useEffect\"], [\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/auth/AdminProtected.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/auth/AdminProtected.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAdmin) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AdminProtected.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/MainLayoutClient.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/MainLayoutClient.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainLayoutClient)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction MainLayoutClient({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Check if we're on the home page\n    const isHomePage = pathname === \"/\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-full\",\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/layout/MainLayoutClient.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/layout/MainLayoutClient.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/layout/MainLayoutClient.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTWFpbkxheW91dENsaWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QztBQUNKO0FBQ1k7QUFFdkMsU0FBU0csaUJBQWlCLEVBQ3ZDQyxRQUFRLEVBR1Q7SUFDQyxNQUFNQyxXQUFXTCw0REFBV0E7SUFFNUIsa0NBQWtDO0lBQ2xDLE1BQU1NLGFBQWFELGFBQWE7SUFFaEMscUJBQ0UsOERBQUNILCtEQUFZQTtrQkFDWCw0RUFBQ0s7WUFBSUMsV0FBVTs7Z0JBQ1pKOzhCQUNELDhEQUFDSCxvREFBT0E7b0JBQUNRLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSTFCIiwic291cmNlcyI6WyIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL2NvbXBvbmVudHMvbGF5b3V0L01haW5MYXlvdXRDbGllbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tIFwicmVhY3QtaG90LXRvYXN0XCI7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tIFwiQC9jb250ZXh0cy9BdXRoQ29udGV4dFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNYWluTGF5b3V0Q2xpZW50KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcblxuICAvLyBDaGVjayBpZiB3ZSdyZSBvbiB0aGUgaG9tZSBwYWdlXG4gIGNvbnN0IGlzSG9tZVBhZ2UgPSBwYXRobmFtZSA9PT0gXCIvXCI7XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctZnVsbFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDxUb2FzdGVyIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCIgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvQXV0aFByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVBhdGhuYW1lIiwiVG9hc3RlciIsIkF1dGhQcm92aWRlciIsIk1haW5MYXlvdXRDbGllbnQiLCJjaGlsZHJlbiIsInBhdGhuYW1lIiwiaXNIb21lUGFnZSIsImRpdiIsImNsYXNzTmFtZSIsInBvc2l0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/MainLayoutClient.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/DataTable.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/DataTable.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataTable: () => (/* binding */ DataTable),\n/* harmony export */   DataTableCell: () => (/* binding */ DataTableCell),\n/* harmony export */   DataTableRow: () => (/* binding */ DataTableRow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ DataTable,DataTableRow,DataTableCell auto */ \nfunction DataTable({ headers, children, loading = false, emptyMessage = \"Nenhum dado encontrado\", className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-white rounded-lg shadow-md overflow-hidden ${className}`,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                lineNumber: 24,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n            lineNumber: 23,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-x-auto w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                className: \"w-full divide-y divide-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: headers.map((header, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    scope: \"col\",\n                                    className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                    children: header\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: children || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                colSpan: headers.length,\n                                className: \"px-6 py-4 text-center text-gray-500\",\n                                children: emptyMessage\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n                lineNumber: 28,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n            lineNumber: 27,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\nfunction DataTableRow({ children, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n        className: `hover:bg-gray-50 ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\nfunction DataTableCell({ children, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n        className: `px-6 py-4 whitespace-nowrap ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DataTable.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/DataTable.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/DropdownMenu.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DropdownMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaChevronDown_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronDown!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DropdownMenu({ trigger, items, className = '' }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const menuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Fechar o menu quando o usuário clica fora dele\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DropdownMenu.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (menuRef.current && !menuRef.current.contains(event.target)) {\n                    setIsOpen(false);\n                }\n            }\n            // Adicionar o event listener quando o menu está aberto\n            if (isOpen) {\n                document.addEventListener('mousedown', handleClickOutside);\n            }\n            // Limpar o event listener quando o componente é desmontado ou o menu é fechado\n            return ({\n                \"DropdownMenu.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"DropdownMenu.useEffect\"];\n        }\n    }[\"DropdownMenu.useEffect\"], [\n        isOpen\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        ref: menuRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center gap-2 px-4 py-1.5 bg-white rounded-full hover:bg-gray-50 transition-all duration-200 border border-gray-200 shadow-sm font-medium\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-800\",\n                        children: trigger\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 transition-all duration-200 ${isOpen ? 'bg-blue-100' : ''}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_react_icons_fa__WEBPACK_IMPORTED_MODULE_2__.FaChevronDown, {\n                            className: `transition-transform duration-200 ${isOpen ? 'rotate-180 text-blue-500' : 'text-gray-500'}`,\n                            size: 10\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-10 overflow-hidden border border-gray-100 animate-fadeIn\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-3 h-3 bg-white transform rotate-45\",\n                        style: {\n                            top: '-6px',\n                            right: '20px',\n                            boxShadow: '-2px -2px 2px rgba(0, 0, 0, 0.05)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 bg-white rounded-lg overflow-hidden py-1\",\n                        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setIsOpen(false);\n                                    item.onClick();\n                                },\n                                className: \"w-full text-left px-4 py-2.5 hover:bg-gray-50 flex items-center gap-3 transition-colors duration-150\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-700 font-medium\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/DropdownMenu.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/DropdownMenu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/StandardButton.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/StandardButton.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StandardButton: () => (/* binding */ StandardButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* __next_internal_client_entry_do_not_use__ StandardButton auto */ \n\n\nconst StandardButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ variant = 'primary', size = 'md', icon, iconPosition = 'left', isLoading = false, fullWidth = false, children, className, disabled, ...props }, ref)=>{\n    const baseClasses = 'rounded-[12px] font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2';\n    const sizeClasses = {\n        sm: 'h-[36px] px-[16px] text-[14px]',\n        md: 'h-[44px] px-[20px] text-[16px]',\n        lg: 'h-[52px] px-[24px] text-[18px]'\n    };\n    const variantClasses = {\n        primary: 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-300 disabled:bg-blue-300',\n        secondary: 'bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 focus:ring-gray-200 disabled:text-gray-400',\n        destructive: 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-300 disabled:bg-red-300'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)(baseClasses, sizeClasses[size], variantClasses[variant], fullWidth && 'w-full', isLoading && 'opacity-70 cursor-not-allowed', className),\n        disabled: disabled || isLoading,\n        \"aria-busy\": isLoading,\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2 animate-spin\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/StandardButton.tsx\",\n                lineNumber: 58,\n                columnNumber: 11\n            }, undefined),\n            icon && iconPosition === 'left' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"mr-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/StandardButton.tsx\",\n                lineNumber: 63,\n                columnNumber: 11\n            }, undefined),\n            children,\n            icon && iconPosition === 'right' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/StandardButton.tsx\",\n                lineNumber: 67,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/StandardButton.tsx\",\n        lineNumber: 43,\n        columnNumber: 7\n    }, undefined);\n});\nStandardButton.displayName = 'StandardButton';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/StandardButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/admins.ts":
/*!******************************!*\
  !*** ./src/config/admins.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADMIN_EMAILS: () => (/* binding */ ADMIN_EMAILS),\n/* harmony export */   isAdminEmail: () => (/* binding */ isAdminEmail)\n/* harmony export */ });\n// Lista de emails de administradores autorizados\nconst ADMIN_EMAILS = [\n    '<EMAIL>',\n    '<EMAIL>',\n    '<EMAIL>',\n    '<EMAIL>'\n];\n// Função para verificar se um email é de um administrador\nfunction isAdminEmail(email) {\n    return ADMIN_EMAILS.includes(email.toLowerCase());\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uZmlnL2FkbWlucy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLGlEQUFpRDtBQUMxQyxNQUFNQSxlQUFlO0lBQzFCO0lBQ0E7SUFDQTtJQUNBO0NBQ0QsQ0FBQztBQUVGLDBEQUEwRDtBQUNuRCxTQUFTQyxhQUFhQyxLQUFhO0lBQ3hDLE9BQU9GLGFBQWFHLFFBQVEsQ0FBQ0QsTUFBTUUsV0FBVztBQUNoRCIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL3NyYy9jb25maWcvYWRtaW5zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIExpc3RhIGRlIGVtYWlscyBkZSBhZG1pbmlzdHJhZG9yZXMgYXV0b3JpemFkb3NcbmV4cG9ydCBjb25zdCBBRE1JTl9FTUFJTFMgPSBbXG4gICdhZG1pbkBjb25uZWN0Y2l0eS5jb20nLFxuICAnbHVpenZpbmNlbnppQGdtYWlsLmNvbScsXG4gICdndXN0YXZvQGNvbm5lY3RjaXR5LmNvbScsXG4gICdzdXBvcnRlQGNvbm5lY3RjaXR5LmNvbSdcbl07XG5cbi8vIEZ1bsOnw6NvIHBhcmEgdmVyaWZpY2FyIHNlIHVtIGVtYWlsIMOpIGRlIHVtIGFkbWluaXN0cmFkb3JcbmV4cG9ydCBmdW5jdGlvbiBpc0FkbWluRW1haWwoZW1haWw6IHN0cmluZyk6IGJvb2xlYW4ge1xuICByZXR1cm4gQURNSU5fRU1BSUxTLmluY2x1ZGVzKGVtYWlsLnRvTG93ZXJDYXNlKCkpO1xufVxuIl0sIm5hbWVzIjpbIkFETUlOX0VNQUlMUyIsImlzQWRtaW5FbWFpbCIsImVtYWlsIiwiaW5jbHVkZXMiLCJ0b0xvd2VyQ2FzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/config/admins.ts\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AppBarContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/AppBarContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppBarProvider: () => (/* binding */ AppBarProvider),\n/* harmony export */   useAppBar: () => (/* binding */ useAppBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AppBarProvider,useAppBar auto */ \n\nconst AppBarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AppBarProvider = ({ children })=>{\n    const [appBarTitle, setAppBarTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"crIAdores\"); // Default title\n    const [appBarTrailing, setAppBarTrailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Default trailing content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppBarContext.Provider, {\n        value: {\n            appBarTitle,\n            setAppBarTitle,\n            appBarTrailing,\n            setAppBarTrailing\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/contexts/AppBarContext.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAppBar = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppBarContext);\n    if (context === undefined) {\n        throw new Error('useAppBar must be used within an AppBarProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AppBarContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/auth-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Função para atualizar a sessão\n    const refreshSession = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('AuthContext: Refreshing session...');\n            // Primeiro, verificar se há uma sessão atual\n            const { data: sessionData, error: sessionError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error('Erro ao verificar sessão atual:', sessionError.message);\n                setError(sessionError.message);\n                setLoading(false);\n                return;\n            }\n            // Se não há sessão, apenas atualizar o estado local\n            if (!sessionData.session) {\n                console.log('AuthContext: No active session found');\n                setSession(null);\n                setUser(null);\n                setLoading(false);\n                return;\n            }\n            // Se há uma sessão, atualizar o estado local\n            console.log('AuthContext: Active session found');\n            setSession(sessionData.session);\n            setUser(sessionData.session?.user || null);\n        // Não tentar atualizar o token automaticamente, pois isso pode causar problemas\n        // O token será atualizado automaticamente pelo Supabase quando necessário\n        } catch (err) {\n            console.error('Erro inesperado ao obter sessão:', err);\n            setError(err.message || 'Erro desconhecido');\n        // Não redirecionar automaticamente em caso de erro\n        // Isso pode interferir com o processo de login\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Função auxiliar para tratar erros de sessão\n    const handleSessionError = (error)=>{\n        // Limpar o estado local\n        setSession(null);\n        setUser(null);\n        // Verificar se já estamos na página de login para evitar loop infinito\n        const currentPath = window.location.pathname;\n        if (!currentPath.includes('/login') && !currentPath.includes('/registro')) {\n            console.log('AuthContext: Session error, redirecting to login');\n            window.location.href = `/login?error=session_error&redirect=${encodeURIComponent(currentPath)}`;\n        } else {\n            console.log('AuthContext: Already on login page, not redirecting');\n        }\n    };\n    // Função para fazer logout\n    const signOut = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('AuthContext: Signing out...');\n            // Fazer logout no Supabase\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) {\n                console.error('Erro ao fazer logout no Supabase:', error.message);\n                setError(error.message);\n            }\n            // Limpar o estado local\n            setSession(null);\n            setUser(null);\n            // Redirecionar para a página de login\n            console.log('AuthContext: Logout completed, redirecting to login');\n            window.location.href = '/login';\n        } catch (err) {\n            console.error('Erro inesperado ao fazer logout:', err);\n            setError(err.message || 'Erro desconhecido');\n            // Limpar o estado local e redirecionar\n            setSession(null);\n            setUser(null);\n            window.location.href = '/login';\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Carregar a sessão inicial\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Iniciar com uma verificação de sessão\n            refreshSession();\n            // Configurar listener para mudanças na autenticação\n            const { data: authListener } = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('Auth state changed:', event);\n                    // Atualizar o estado com base no evento\n                    if (event === 'SIGNED_IN') {\n                        console.log('User signed in, updating session');\n                        setSession(session);\n                        setUser(session?.user || null);\n                    // Não redirecionar aqui - a página de login já cuida disso\n                    } else if (event === 'SIGNED_OUT') {\n                        console.log('User signed out, clearing session');\n                        setSession(null);\n                        setUser(null);\n                    // Não redirecionar aqui - pode interferir com o processo de login/logout\n                    } else if (event === 'TOKEN_REFRESHED') {\n                        console.log('Token refreshed, updating session');\n                        setSession(session);\n                        setUser(session?.user || null);\n                    } else if (event === 'USER_UPDATED') {\n                        console.log('User updated, updating session');\n                        setSession(session);\n                        setUser(session?.user || null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Limpar listener ao desmontar\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Configurar interceptor para erros de autenticação\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const handleAuthErrors = {\n                \"AuthProvider.useEffect.handleAuthErrors\": (event)=>{\n                    // Verificar se o erro é relacionado à autenticação\n                    if (event.error instanceof _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.AuthError || event.message && (event.message.includes('Invalid Refresh Token') || event.message.includes('JWT expired') || event.message.includes('not authenticated'))) {\n                        console.error('Erro de autenticação interceptado:', event);\n                        // Tentar atualizar a sessão\n                        refreshSession();\n                    }\n                }\n            }[\"AuthProvider.useEffect.handleAuthErrors\"];\n            // Adicionar listener global para erros\n            window.addEventListener('error', handleAuthErrors);\n            // Limpar listener ao desmontar\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener('error', handleAuthErrors);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const value = {\n        session,\n        user,\n        loading,\n        error,\n        signOut,\n        refreshSession\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/contexts/AuthContext.tsx\",\n        lineNumber: 204,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth deve ser usado dentro de um AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClientSupabaseClient: () => (/* binding */ createClientSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Cliente Supabase para o lado do cliente\n *\n * Este arquivo exporta uma instância do cliente Supabase para uso no lado do cliente.\n */ \n// Instância padrão do cliente Supabase\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Função para criar um cliente Supabase para o lado do cliente\nfunction createClientSupabaseClient() {\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7Ozs7Q0FJQyxHQUUyRTtBQUU1RSx1Q0FBdUM7QUFDaEMsTUFBTUMsV0FBV0QsMEZBQTJCQSxHQUFHO0FBRXRELCtEQUErRDtBQUN4RCxTQUFTRTtJQUNkLE9BQU9GLDBGQUEyQkE7QUFDcEMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sdWl6dmluY2VuemkvRG9jdW1lbnRzL0FJX1Byb2plY3RzL0NyaWFkb3Jlcy9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENsaWVudGUgU3VwYWJhc2UgcGFyYSBvIGxhZG8gZG8gY2xpZW50ZVxuICpcbiAqIEVzdGUgYXJxdWl2byBleHBvcnRhIHVtYSBpbnN0w6JuY2lhIGRvIGNsaWVudGUgU3VwYWJhc2UgcGFyYSB1c28gbm8gbGFkbyBkbyBjbGllbnRlLlxuICovXG5cbmltcG9ydCB7IGNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9hdXRoLWhlbHBlcnMtbmV4dGpzJztcblxuLy8gSW5zdMOibmNpYSBwYWRyw6NvIGRvIGNsaWVudGUgU3VwYWJhc2VcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudENvbXBvbmVudENsaWVudCgpO1xuXG4vLyBGdW7Dp8OjbyBwYXJhIGNyaWFyIHVtIGNsaWVudGUgU3VwYWJhc2UgcGFyYSBvIGxhZG8gZG8gY2xpZW50ZVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNsaWVudFN1cGFiYXNlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50KCk7XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Q29tcG9uZW50Q2xpZW50Iiwic3VwYWJhc2UiLCJjcmVhdGVDbGllbnRTdXBhYmFzZUNsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/react-hot-toast","vendor-chunks/set-cookie-parser","vendor-chunks/jose","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/react-icons","vendor-chunks/tailwind-merge"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&page=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&appPaths=%2F(admin)%2Fadmin%2Fcampaigns%2Fpage&pagePath=private-next-app-dir%2F(admin)%2Fadmin%2Fcampaigns%2Fpage.tsx&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();