/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/v1/campaigns/leaderboard/route";
exports.ids = ["app/api/v1/campaigns/leaderboard/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&page=%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&page=%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_v1_campaigns_leaderboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/v1/campaigns/leaderboard/route.ts */ \"(rsc)/./src/app/api/v1/campaigns/leaderboard/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/v1/campaigns/leaderboard/route\",\n        pathname: \"/api/v1/campaigns/leaderboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/v1/campaigns/leaderboard/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/v1/campaigns/leaderboard/route.ts\",\n    nextConfigOutput,\n    userland: _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_v1_campaigns_leaderboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&page=%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/v1/campaigns/leaderboard/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/v1/campaigns/leaderboard/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_services_gamification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/gamification */ \"(rsc)/./src/lib/services/gamification.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * API para obter o ranking de influenciadores em uma campanha\n * \n * Este endpoint retorna o leaderboard de uma campanha, mostrando\n * os influenciadores ordenados por pontuação.\n */ \n\n\n\nasync function GET(req) {\n    try {\n        // Verificar autenticação\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Não autorizado'\n            }, {\n                status: 401\n            });\n        }\n        // Obter ID da campanha da URL\n        const { searchParams } = new URL(req.url);\n        const campaignId = searchParams.get('campaignId');\n        if (!campaignId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID da campanha é obrigatório'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar se o usuário tem acesso à campanha\n        const { data: campaign, error: campaignError } = await supabase.from('campaigns').select('id, restaurant_id').eq('id', campaignId).single();\n        if (campaignError || !campaign) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Campanha não encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Verificar se o usuário é o dono do restaurante, um influenciador na campanha, ou um admin\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', session.user.id).single();\n        const isRestaurantOwner = campaign.restaurant_id === session.user.id;\n        const isAdmin = profile?.role === 'admin';\n        if (!isRestaurantOwner && !isAdmin) {\n            // Verificar se é um influenciador na campanha\n            const { data: campaignInfluencer, error: influencerError } = await supabase.from('campaign_influencers').select('id').eq('campaign_id', campaignId).eq('influencer_id', session.user.id).maybeSingle();\n            if (influencerError || !campaignInfluencer) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Você não tem permissão para acessar este leaderboard'\n                }, {\n                    status: 403\n                });\n            }\n        }\n        // Obter leaderboard\n        const leaderboard = await _lib_services_gamification__WEBPACK_IMPORTED_MODULE_2__.GamificationService.getCampaignLeaderboard(campaignId);\n        // Obter detalhes da campanha\n        const { data: campaignDetails } = await supabase.from('campaigns').select(`\n        id,\n        name,\n        start_date,\n        end_date,\n        restaurants (\n          id,\n          name,\n          profile_image_url\n        )\n      `).eq('id', campaignId).single();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            campaign: campaignDetails,\n            leaderboard\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)('api.campaigns.leaderboard', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Erro ao obter leaderboard'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/v1/campaigns/leaderboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/whatsapp.ts":
/*!*********************************!*\
  !*** ./src/lib/api/whatsapp.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMessageStatus: () => (/* binding */ getMessageStatus),\n/* harmony export */   registerWebhook: () => (/* binding */ registerWebhook),\n/* harmony export */   sendTemplateMessage: () => (/* binding */ sendTemplateMessage),\n/* harmony export */   sendTextMessage: () => (/* binding */ sendTextMessage)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/constants */ \"(rsc)/./src/lib/utils/constants.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * Cliente para a API do WhatsApp Business\n *\n * Este módulo fornece funções para interagir com a API do WhatsApp Business\n * para enviar mensagens e gerenciar webhooks.\n */ \n\n/**\n * Envia uma mensagem de texto para um número de WhatsApp\n * \n * @param to Número de telefone do destinatário (formato internacional sem +)\n * @param message Texto da mensagem\n * @returns Resposta da API com o ID da mensagem\n */ async function sendTextMessage(to, message) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.PHONE_NUMBER_ID}/messages`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messaging_product: 'whatsapp',\n                recipient_type: 'individual',\n                to,\n                type: 'text',\n                text: {\n                    preview_url: false,\n                    body: message\n                }\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao enviar mensagem: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.sendTextMessage', error);\n        throw error;\n    }\n}\n/**\n * Envia uma mensagem de template para um número de WhatsApp\n * \n * @param to Número de telefone do destinatário (formato internacional sem +)\n * @param templateName Nome do template aprovado\n * @param language Código do idioma (ex: pt_BR)\n * @param components Componentes do template (header, body, buttons)\n * @returns Resposta da API com o ID da mensagem\n */ async function sendTemplateMessage(to, templateName, language = 'pt_BR', components = []) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.PHONE_NUMBER_ID}/messages`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messaging_product: 'whatsapp',\n                recipient_type: 'individual',\n                to,\n                type: 'template',\n                template: {\n                    name: templateName,\n                    language: {\n                        code: language\n                    },\n                    components\n                }\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao enviar template: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.sendTemplateMessage', error);\n        throw error;\n    }\n}\n/**\n * Verifica o status de uma mensagem\n * \n * @param messageId ID da mensagem\n * @returns Status da mensagem\n */ async function getMessageStatus(messageId) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${messageId}?fields=status`, {\n            method: 'GET',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao obter status: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.getMessageStatus', error);\n        throw error;\n    }\n}\n/**\n * Registra o webhook para receber atualizações do WhatsApp\n * \n * @param webhookUrl URL do webhook\n * @param verifyToken Token de verificação\n * @returns Resposta da API\n */ async function registerWebhook(webhookUrl, verifyToken) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.BUSINESS_ACCOUNT_ID}/subscribed_apps`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                callback_url: webhookUrl,\n                verify_token: verifyToken,\n                fields: [\n                    'messages',\n                    'message_status'\n                ]\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao registrar webhook: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.registerWebhook', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/whatsapp.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/gamification.ts":
/*!******************************************!*\
  !*** ./src/lib/services/gamification.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GamificationService: () => (/* binding */ GamificationService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/* harmony import */ var _notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notification */ \"(rsc)/./src/lib/services/notification.ts\");\n/**\n * Gamification Service\n * \n * Handles all gamification-related functionality:\n * - Points calculation\n * - Ranking updates\n * - Achievements\n * - Leaderboards\n */ \n\n\n// Points weights for different engagement types\nconst ENGAGEMENT_WEIGHTS = {\n    like: 1,\n    comment: 2,\n    share: 3,\n    save: 4,\n    view: 0.1\n};\nclass GamificationService {\n    /**\n   * Calculate points for a post based on engagement metrics\n   */ static calculatePostPoints(metrics) {\n        let points = 0;\n        // Calculate points based on engagement metrics\n        if (metrics.likes_count) {\n            points += metrics.likes_count * ENGAGEMENT_WEIGHTS.like;\n        }\n        if (metrics.comments_count) {\n            points += metrics.comments_count * ENGAGEMENT_WEIGHTS.comment;\n        }\n        if (metrics.shares_count) {\n            points += metrics.shares_count * ENGAGEMENT_WEIGHTS.share;\n        }\n        if (metrics.saves_count) {\n            points += metrics.saves_count * ENGAGEMENT_WEIGHTS.save;\n        }\n        if (metrics.views_count) {\n            points += metrics.views_count * ENGAGEMENT_WEIGHTS.view;\n        }\n        return Math.round(points);\n    }\n    /**\n   * Update points for a campaign influencer based on post metrics\n   */ static async updateInfluencerPoints(campaignInfluencerId, postId, metrics, reason) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Calculate points\n            const points = this.calculatePostPoints(metrics);\n            // Get current campaign influencer data\n            const { data: campaignInfluencer, error: getError } = await supabase.from('campaign_influencers').select(`\n          id,\n          campaign_id,\n          influencer_id,\n          total_points,\n          likes_count,\n          comments_count,\n          shares_count,\n          saves_count,\n          views_count,\n          points_breakdown\n        `).eq('id', campaignInfluencerId).single();\n            if (getError || !campaignInfluencer) {\n                throw new Error(`Campaign influencer not found: ${getError?.message}`);\n            }\n            // Update metrics\n            const updatedMetrics = {\n                likes_count: (campaignInfluencer.likes_count || 0) + (metrics.likes_count || 0),\n                comments_count: (campaignInfluencer.comments_count || 0) + (metrics.comments_count || 0),\n                shares_count: (campaignInfluencer.shares_count || 0) + (metrics.shares_count || 0),\n                saves_count: (campaignInfluencer.saves_count || 0) + (metrics.saves_count || 0),\n                views_count: (campaignInfluencer.views_count || 0) + (metrics.views_count || 0),\n                total_points: (campaignInfluencer.total_points || 0) + points\n            };\n            // Update points breakdown\n            const pointsBreakdown = campaignInfluencer.points_breakdown || {};\n            pointsBreakdown[postId] = {\n                points,\n                metrics,\n                updated_at: new Date().toISOString()\n            };\n            // Update campaign influencer\n            const { error: updateError } = await supabase.from('campaign_influencers').update({\n                ...updatedMetrics,\n                points_breakdown: pointsBreakdown,\n                updated_at: new Date().toISOString()\n            }).eq('id', campaignInfluencerId);\n            if (updateError) {\n                throw new Error(`Failed to update campaign influencer: ${updateError.message}`);\n            }\n            // Record points history\n            const { error: historyError } = await supabase.from('points_history').insert({\n                campaign_influencer_id: campaignInfluencerId,\n                points,\n                reason,\n                details: {\n                    post_id: postId,\n                    metrics\n                },\n                created_at: new Date().toISOString()\n            });\n            if (historyError) {\n                console.error(`Failed to record points history: ${historyError.message}`);\n            }\n            // Update campaign rankings\n            await this.updateCampaignRankings(campaignInfluencer.campaign_id);\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('gamification.updateInfluencerPoints', error);\n            throw error;\n        }\n    }\n    /**\n   * Update rankings for all influencers in a campaign\n   */ static async updateCampaignRankings(campaignId) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Get all influencers in the campaign\n            const { data: influencers, error: getError } = await supabase.from('campaign_influencers').select(`\n          id,\n          influencer_id,\n          total_points,\n          current_rank,\n          previous_rank,\n          profiles (\n            id,\n            full_name,\n            phone,\n            phone_verified\n          )\n        `).eq('campaign_id', campaignId).order('total_points', {\n                ascending: false\n            });\n            if (getError) {\n                throw new Error(`Failed to get campaign influencers: ${getError.message}`);\n            }\n            if (!influencers || influencers.length === 0) {\n                return;\n            }\n            // Create a ranking snapshot\n            const { data: _snapshot, error: snapshotError } = await supabase // snapshot prefixado\n            .from('ranking_snapshots').insert({\n                campaign_id: campaignId,\n                snapshot_date: new Date().toISOString().split('T')[0],\n                rankings: influencers.map((inf, index)=>({\n                        influencer_id: inf.influencer_id,\n                        rank: index + 1,\n                        total_points: inf.total_points || 0\n                    })),\n                created_at: new Date().toISOString()\n            }).select('id').single();\n            if (snapshotError) {\n                console.error(`Failed to create ranking snapshot: ${snapshotError.message}`);\n            }\n            // Update each influencer's rank\n            for(let i = 0; i < influencers.length; i++){\n                const influencer = influencers[i];\n                const newRank = i + 1;\n                // Skip if rank hasn't changed\n                if (influencer.current_rank === newRank) {\n                    continue;\n                }\n                // Update ranks\n                const { error: updateError } = await supabase.from('campaign_influencers').update({\n                    previous_rank: influencer.current_rank,\n                    current_rank: newRank,\n                    last_rank_update: new Date().toISOString()\n                }).eq('id', influencer.id);\n                if (updateError) {\n                    console.error(`Failed to update influencer rank: ${updateError.message}`);\n                    continue;\n                }\n                // Send notification if rank improved\n                if (influencer.current_rank && newRank < influencer.current_rank) {\n                    try {\n                        // Get campaign details\n                        const { data: campaign } = await supabase.from('campaigns').select('name').eq('id', campaignId).single();\n                        if (campaign && influencer.profiles?.[0]?.phone_verified) {\n                            await (0,_notification__WEBPACK_IMPORTED_MODULE_2__.sendNotification)({\n                                userId: influencer.influencer_id,\n                                typeId: _notification__WEBPACK_IMPORTED_MODULE_2__.NotificationType.RANKING_UPDATE,\n                                title: `Seu ranking melhorou na campanha ${campaign.name}!`,\n                                message: `Parabéns! Você subiu para a posição #${newRank} no ranking da campanha ${campaign.name}.`,\n                                data: {\n                                    campaignId,\n                                    campaignName: campaign.name,\n                                    newRank,\n                                    oldRank: influencer.current_rank\n                                },\n                                sendWhatsApp: true,\n                                sendEmail: false\n                            });\n                        }\n                    } catch (notifyError) {\n                        console.error('Failed to send rank update notification:', notifyError);\n                    }\n                }\n            }\n            // Record ranking history\n            const today = new Date().toISOString().split('T')[0];\n            for (const influencer of influencers){\n                const { error: historyError } = await supabase.from('ranking_history').insert({\n                    campaign_id: campaignId,\n                    influencer_id: influencer.influencer_id,\n                    date: today,\n                    rank: influencer.current_rank || 0,\n                    engagement_rate: 0,\n                    total_points: influencer.total_points || 0,\n                    total_likes: 0,\n                    total_comments: 0,\n                    total_saves: 0,\n                    created_at: new Date().toISOString()\n                });\n                if (historyError) {\n                    console.error(`Failed to record ranking history: ${historyError.message}`);\n                }\n            }\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('gamification.updateCampaignRankings', error);\n            throw error;\n        }\n    }\n    /**\n   * Get leaderboard for a campaign\n   */ static async getCampaignLeaderboard(campaignId) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            const { data, error } = await supabase.from('campaign_influencers').select(`\n          id,\n          influencer_id,\n          current_rank,\n          previous_rank,\n          total_points,\n          likes_count,\n          comments_count,\n          shares_count,\n          saves_count,\n          views_count,\n          profiles (\n            id,\n            full_name,\n            profile_image_url\n          )\n        `).eq('campaign_id', campaignId).order('current_rank', {\n                ascending: true\n            });\n            if (error) {\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('gamification.getCampaignLeaderboard', error);\n            throw error;\n        }\n    }\n    /**\n   * Check and award achievements for an influencer\n   */ static async checkAchievements(influencerId) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Get all available achievements\n            const { data: achievements, error: achievementsError } = await supabase.from('achievements').select('*').eq('is_active', true);\n            if (achievementsError || !achievements) {\n                throw new Error(`Failed to get achievements: ${achievementsError?.message}`);\n            }\n            // Get influencer's current achievements\n            const { data: userAchievements, error: userAchievementsError } = await supabase.from('user_achievements').select('achievement_id').eq('user_id', influencerId);\n            if (userAchievementsError) {\n                throw new Error(`Failed to get user achievements: ${userAchievementsError.message}`);\n            }\n            const earnedAchievementIds = userAchievements?.map((ua)=>ua.achievement_id) || [];\n            // Check each achievement\n            for (const achievement of achievements){\n                // Skip if already earned\n                if (earnedAchievementIds.includes(achievement.id)) {\n                    continue;\n                }\n                // Check if criteria is met\n                const criteriaIsMet = await this.checkAchievementCriteria(influencerId, achievement.criteria);\n                if (criteriaIsMet) {\n                    // Award achievement\n                    const { error: awardError } = await supabase.from('user_achievements').insert({\n                        user_id: influencerId,\n                        achievement_id: achievement.id,\n                        earned_at: new Date().toISOString(),\n                        data: {\n                            points: achievement.points\n                        }\n                    });\n                    if (awardError) {\n                        console.error(`Failed to award achievement: ${awardError.message}`);\n                        continue;\n                    }\n                    // Send notification\n                    try {\n                        await (0,_notification__WEBPACK_IMPORTED_MODULE_2__.sendNotification)({\n                            userId: influencerId,\n                            typeId: _notification__WEBPACK_IMPORTED_MODULE_2__.NotificationType.ACHIEVEMENT_UNLOCKED,\n                            title: `Conquista desbloqueada: ${achievement.name}`,\n                            message: `Parabéns! Você desbloqueou a conquista \"${achievement.name}\": ${achievement.description}`,\n                            data: {\n                                achievementId: achievement.id,\n                                achievementName: achievement.name,\n                                achievementDescription: achievement.description,\n                                points: achievement.points\n                            },\n                            sendWhatsApp: true,\n                            sendEmail: false\n                        });\n                    } catch (notifyError) {\n                        console.error('Failed to send achievement notification:', notifyError);\n                    }\n                }\n            }\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('gamification.checkAchievements', error);\n        }\n    }\n    /**\n   * Check if an achievement's criteria is met\n   */ static async checkAchievementCriteria(influencerId, criteria) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Different types of criteria\n            if (criteria.type === 'campaign_count') {\n                // Check number of campaigns completed\n                const { count, error } = await supabase.from('campaign_influencers').select('id', {\n                    count: 'exact',\n                    head: true\n                }).eq('influencer_id', influencerId).eq('status', 'completed');\n                if (error) {\n                    throw error;\n                }\n                return (count || 0) >= criteria.value;\n            }\n            if (criteria.type === 'total_points') {\n                // Check total points across all campaigns\n                const { data, error } = await supabase.from('campaign_influencers').select('total_points').eq('influencer_id', influencerId);\n                if (error) {\n                    throw error;\n                }\n                const totalPoints = data?.reduce((sum, ci)=>sum + (ci.total_points || 0), 0) || 0;\n                return totalPoints >= criteria.value;\n            }\n            // Add more criteria types as needed\n            return false;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('gamification.checkAchievementCriteria', error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/gamification.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/notification.ts":
/*!******************************************!*\
  !*** ./src/lib/services/notification.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationType: () => (/* binding */ NotificationType),\n/* harmony export */   sendCampaignInviteNotification: () => (/* binding */ sendCampaignInviteNotification),\n/* harmony export */   sendContentSubmittedNotification: () => (/* binding */ sendContentSubmittedNotification),\n/* harmony export */   sendNewInfluencerJoinedNotification: () => (/* binding */ sendNewInfluencerJoinedNotification),\n/* harmony export */   sendNotification: () => (/* binding */ sendNotification),\n/* harmony export */   sendPaymentStatusNotification: () => (/* binding */ sendPaymentStatusNotification),\n/* harmony export */   sendPostApprovalNotification: () => (/* binding */ sendPostApprovalNotification),\n/* harmony export */   sendRankingUpdateNotification: () => (/* binding */ sendRankingUpdateNotification),\n/* harmony export */   sendScheduleConfirmationNotification: () => (/* binding */ sendScheduleConfirmationNotification),\n/* harmony export */   sendScheduleReminderNotification: () => (/* binding */ sendScheduleReminderNotification)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/whatsapp */ \"(rsc)/./src/lib/api/whatsapp.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * Serviço de notificações\n *\n * Este módulo fornece funções para enviar notificações através de diferentes canais\n * (WhatsApp, email, etc.) e gerenciar o registro de notificações.\n */ \n\n\n// Tipos de notificação\nvar NotificationType = /*#__PURE__*/ function(NotificationType) {\n    NotificationType[\"CAMPAIGN_INVITE\"] = \"campaign_invite\";\n    NotificationType[\"CAMPAIGN_STATUS_CHANGE\"] = \"campaign_status_change\";\n    NotificationType[\"RANKING_UPDATE\"] = \"ranking_update\";\n    NotificationType[\"PAYMENT_STATUS\"] = \"payment_status\";\n    NotificationType[\"POST_APPROVAL\"] = \"post_approval\";\n    NotificationType[\"SUBSCRIPTION_EXPIRY\"] = \"subscription_expiry\";\n    NotificationType[\"NEW_INFLUENCER_JOINED\"] = \"new_influencer_joined\";\n    NotificationType[\"NEW_POST_CREATED\"] = \"new_post_created\";\n    NotificationType[\"WEEKLY_REPORT\"] = \"weekly_report\";\n    NotificationType[\"CONTENT_SUBMITTED\"] = \"content_submitted\";\n    NotificationType[\"CONTENT_APPROVED\"] = \"content_approved\";\n    NotificationType[\"CONTENT_REJECTED\"] = \"content_rejected\";\n    NotificationType[\"CONTENT_REVISION\"] = \"content_revision\";\n    NotificationType[\"SCHEDULE_CONFIRMATION\"] = \"schedule_confirmation\";\n    NotificationType[\"SCHEDULE_REMINDER\"] = \"schedule_reminder\";\n    NotificationType[\"SCHEDULE_CANCELLATION\"] = \"schedule_cancellation\";\n    NotificationType[\"ACHIEVEMENT_UNLOCKED\"] = \"achievement_unlocked\"; // Adicionado\n    return NotificationType;\n}({});\n/**\n * Envia uma notificação para um usuário\n *\n * @param notificationData Dados da notificação\n * @returns ID da notificação criada\n */ async function sendNotification(notificationData) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Criar registro de notificação\n        const { data: notification, error } = await supabase.from('notifications').insert({\n            user_id: notificationData.userId,\n            type_id: notificationData.typeId,\n            title: notificationData.title,\n            message: notificationData.message,\n            data: notificationData.data || {},\n            sent_via_email: false,\n            sent_via_whatsapp: false\n        }).select().single();\n        if (error) {\n            throw new Error(`Erro ao criar notificação: ${error.message}`);\n        }\n        // Verificar preferências de notificação do usuário\n        const { data: preferences } = await supabase.from('user_notification_preferences').select('*').eq('user_id', notificationData.userId).maybeSingle();\n        // Se o usuário desativou notificações, não enviar\n        if (preferences && !preferences.enable_notifications) {\n            return notification.id;\n        }\n        // Buscar informações do usuário\n        const { data: profile, error: profileError } = await supabase.from('profiles').select('phone, email').eq('id', notificationData.userId).single();\n        if (profileError) {\n            throw new Error(`Erro ao buscar perfil do usuário: ${profileError.message}`);\n        }\n        // Enviar por WhatsApp se solicitado e o usuário tiver telefone\n        if (notificationData.sendWhatsApp && profile.phone) {\n            // Verificar preferências específicas de WhatsApp\n            const notificationType = notificationData.typeId.toLowerCase();\n            const whatsappPreferenceKey = `whatsapp_${notificationType}`;\n            if (preferences && preferences.whatsapp_enabled !== false && preferences[whatsappPreferenceKey] !== false) {\n                try {\n                    // Formatar número para padrão internacional sem o +\n                    const formattedPhone = profile.phone.replace(/\\D/g, '');\n                    // Enviar mensagem\n                    const whatsappResponse = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_1__.sendTextMessage)(formattedPhone, notificationData.message);\n                    // Registrar mensagem enviada\n                    await supabase.from('whatsapp_messages').insert({\n                        user_id: notificationData.userId,\n                        direction: 'outbound',\n                        message: notificationData.message,\n                        message_type: 'text',\n                        whatsapp_message_id: whatsappResponse.messages[0].id,\n                        status: 'sent',\n                        sent_at: new Date().toISOString(),\n                        metadata: {\n                            notification_id: notification.id,\n                            notification_type: notificationData.typeId\n                        }\n                    });\n                    // Atualizar notificação\n                    await supabase.from('notifications').update({\n                        sent_via_whatsapp: true\n                    }).eq('id', notification.id);\n                } catch (whatsappError) {\n                    (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendWhatsApp', whatsappError);\n                // Continuar mesmo se falhar o envio por WhatsApp\n                }\n            }\n        }\n        // Aqui você adicionaria a lógica para enviar por email se necessário\n        return notification.id;\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação de convite para campanha para um influenciador\n *\n * @param influencerId ID do influenciador\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param restaurantName Nome do restaurante\n * @returns ID da notificação criada\n */ async function sendCampaignInviteNotification(influencerId, campaignId, campaignName, restaurantName) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"campaign_invite\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{restaurant_name}}', restaurantName);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{restaurant_name}}', restaurantName);\n        // Enviar notificação\n        return await sendNotification({\n            userId: influencerId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                campaignId,\n                campaignName,\n                restaurantName\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendCampaignInviteNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação para o restaurante quando um influenciador aceita participar da campanha\n *\n * @param restaurantId ID do restaurante\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param influencerName Nome do influenciador\n * @param influencerFollowers Número de seguidores do influenciador\n * @returns ID da notificação criada\n */ async function sendNewInfluencerJoinedNotification(restaurantId, campaignId, campaignName, influencerName, influencerFollowers) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"new_influencer_joined\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Formatar número de seguidores\n        const formattedFollowers = new Intl.NumberFormat('pt-BR').format(influencerFollowers);\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{influencer_name}}', influencerName);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{influencer_name}}', influencerName).replace('{{influencer_followers}}', formattedFollowers);\n        // Enviar notificação\n        return await sendNotification({\n            userId: restaurantId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                campaignId,\n                campaignName,\n                influencerName,\n                influencerFollowers\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendNewInfluencerJoinedNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação de atualização de ranking para um influenciador\n *\n * @param influencerId ID do influenciador\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param position Nova posição no ranking\n * @param previousPosition Posição anterior no ranking\n * @returns ID da notificação criada\n */ async function sendRankingUpdateNotification(influencerId, campaignId, campaignName, position, previousPosition) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"ranking_update\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{position}}', position.toString());\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{position}}', position.toString());\n        // Enviar notificação\n        return await sendNotification({\n            userId: influencerId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                campaignId,\n                campaignName,\n                position,\n                previousPosition\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendRankingUpdateNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação de aprovação/rejeição de post para um influenciador\n *\n * @param influencerId ID do influenciador\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param postId ID do post\n * @param status Status do post (approved/rejected)\n * @param notes Notas adicionais (opcional)\n * @returns ID da notificação criada\n */ async function sendPostApprovalNotification(influencerId, campaignId, campaignName, postId, status, notes) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"post_approval\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Traduzir status\n        const statusText = status === 'approved' ? 'aprovado' : 'rejeitado';\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{status}}', statusText);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{status}}', statusText).replace('{{notes}}', notes || '');\n        // Enviar notificação\n        return await sendNotification({\n            userId: influencerId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                campaignId,\n                campaignName,\n                postId,\n                status,\n                notes\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendPostApprovalNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação de pagamento para um influenciador\n *\n * @param influencerId ID do influenciador\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param amount Valor do pagamento\n * @param status Status do pagamento (paid/pending/failed)\n * @returns ID da notificação criada\n */ async function sendPaymentStatusNotification(influencerId, campaignId, campaignName, amount, status) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"payment_status\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Traduzir status\n        const statusMap = {\n            paid: 'realizado',\n            pending: 'pendente',\n            failed: 'falhou'\n        };\n        const statusText = statusMap[status] || status;\n        // Formatar valor\n        const formattedAmount = new Intl.NumberFormat('pt-BR', {\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n        }).format(amount);\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{amount}}', formattedAmount).replace('{{status}}', statusText);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{amount}}', formattedAmount).replace('{{status}}', statusText);\n        // Enviar notificação\n        return await sendNotification({\n            userId: influencerId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                campaignId,\n                campaignName,\n                amount,\n                status\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendPaymentStatusNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação de conteúdo enviado para aprovação\n *\n * @param restaurantId ID do restaurante\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param influencerId ID do influenciador\n * @param influencerName Nome do influenciador\n * @param contentId ID do conteúdo\n * @param contentType Tipo do conteúdo (video/image)\n * @returns ID da notificação criada\n */ async function sendContentSubmittedNotification(restaurantId, campaignId, campaignName, influencerId, influencerName, contentId, contentType) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"content_submitted\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Traduzir tipo de conteúdo\n        const contentTypeText = contentType === 'video' ? 'vídeo' : 'imagem';\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{influencer_name}}', influencerName);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{influencer_name}}', influencerName).replace('{{content_type}}', contentTypeText);\n        // Enviar notificação\n        return await sendNotification({\n            userId: restaurantId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                campaignId,\n                campaignName,\n                influencerId,\n                influencerName,\n                contentId,\n                contentType\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendContentSubmittedNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia uma notificação de agendamento confirmado\n *\n * @param userId ID do usuário (restaurante ou influenciador)\n * @param scheduleId ID do agendamento\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param date Data agendada\n * @param time Horário agendado\n * @param otherPartyName Nome da outra parte (restaurante ou influenciador)\n * @returns ID da notificação criada\n */ async function sendScheduleConfirmationNotification(userId, scheduleId, campaignId, campaignName, date, time, otherPartyName) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"schedule_confirmation\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{date}}', date);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{other_party}}', otherPartyName).replace('{{date}}', date).replace('{{time}}', time);\n        // Enviar notificação\n        return await sendNotification({\n            userId: userId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                scheduleId,\n                campaignId,\n                campaignName,\n                date,\n                time,\n                otherPartyName\n            },\n            sendWhatsApp: true,\n            sendEmail: true\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendScheduleConfirmationNotification', error);\n        throw error;\n    }\n}\n/**\n * Envia um lembrete de agendamento\n *\n * @param userId ID do usuário (restaurante ou influenciador)\n * @param scheduleId ID do agendamento\n * @param campaignId ID da campanha\n * @param campaignName Nome da campanha\n * @param date Data agendada\n * @param time Horário agendado\n * @param otherPartyName Nome da outra parte (restaurante ou influenciador)\n * @param reminderType Tipo de lembrete (24h_before, 1h_before)\n * @returns ID da notificação criada\n */ async function sendScheduleReminderNotification(userId, scheduleId, campaignId, campaignName, date, time, otherPartyName, reminderType) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n    try {\n        // Buscar tipo de notificação\n        const { data: notificationType, error: typeError } = await supabase.from('notification_types').select('id, template_subject, template_body').eq('name', \"schedule_reminder\").single();\n        if (typeError) {\n            throw new Error(`Tipo de notificação não encontrado: ${typeError.message}`);\n        }\n        // Traduzir tipo de lembrete\n        const reminderText = reminderType === '24h_before' ? '24 horas' : '1 hora';\n        // Substituir variáveis no template\n        const title = notificationType.template_subject.replace('{{campaign_name}}', campaignName).replace('{{reminder_time}}', reminderText);\n        const message = notificationType.template_body.replace('{{campaign_name}}', campaignName).replace('{{other_party}}', otherPartyName).replace('{{date}}', date).replace('{{time}}', time).replace('{{reminder_time}}', reminderText);\n        // Enviar notificação\n        return await sendNotification({\n            userId: userId,\n            typeId: notificationType.id,\n            title,\n            message,\n            data: {\n                scheduleId,\n                campaignId,\n                campaignName,\n                date,\n                time,\n                otherPartyName,\n                reminderType\n            },\n            sendWhatsApp: true,\n            sendEmail: false\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('notification.sendScheduleReminderNotification', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL25vdGlmaWNhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFa0U7QUFDTztBQUM1QjtBQUU5Qyx1QkFBdUI7QUFDaEIsOENBQUtHOzs7Ozs7Ozs7Ozs7Ozs7Ozt1RUFpQm9DLGFBQWE7V0FqQmpEQTtNQWtCWDtBQWFEOzs7OztDQUtDLEdBQ00sZUFBZUMsaUJBQWlCQyxnQkFBa0M7SUFDdkUsTUFBTUMsV0FBV04sZ0ZBQTBCQTtJQUUzQyxJQUFJO1FBQ0YsZ0NBQWdDO1FBQ2hDLE1BQU0sRUFBRU8sTUFBTUMsWUFBWSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSCxTQUN6Q0ksSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUM7WUFDTkMsU0FBU1AsaUJBQWlCUSxNQUFNO1lBQ2hDQyxTQUFTVCxpQkFBaUJVLE1BQU07WUFDaENDLE9BQU9YLGlCQUFpQlcsS0FBSztZQUM3QkMsU0FBU1osaUJBQWlCWSxPQUFPO1lBQ2pDVixNQUFNRixpQkFBaUJFLElBQUksSUFBSSxDQUFDO1lBQ2hDVyxnQkFBZ0I7WUFDaEJDLG1CQUFtQjtRQUNyQixHQUNDQyxNQUFNLEdBQ05DLE1BQU07UUFFVCxJQUFJWixPQUFPO1lBQ1QsTUFBTSxJQUFJYSxNQUFNLENBQUMsMkJBQTJCLEVBQUViLE1BQU1RLE9BQU8sRUFBRTtRQUMvRDtRQUVBLG1EQUFtRDtRQUNuRCxNQUFNLEVBQUVWLE1BQU1nQixXQUFXLEVBQUUsR0FBRyxNQUFNakIsU0FDakNJLElBQUksQ0FBQyxpQ0FDTFUsTUFBTSxDQUFDLEtBQ1BJLEVBQUUsQ0FBQyxXQUFXbkIsaUJBQWlCUSxNQUFNLEVBQ3JDWSxXQUFXO1FBRWQsa0RBQWtEO1FBQ2xELElBQUlGLGVBQWUsQ0FBQ0EsWUFBWUcsb0JBQW9CLEVBQUU7WUFDcEQsT0FBT2xCLGFBQWFtQixFQUFFO1FBQ3hCO1FBRUEsZ0NBQWdDO1FBQ2hDLE1BQU0sRUFBRXBCLE1BQU1xQixPQUFPLEVBQUVuQixPQUFPb0IsWUFBWSxFQUFFLEdBQUcsTUFBTXZCLFNBQ2xESSxJQUFJLENBQUMsWUFDTFUsTUFBTSxDQUFDLGdCQUNQSSxFQUFFLENBQUMsTUFBTW5CLGlCQUFpQlEsTUFBTSxFQUNoQ1EsTUFBTTtRQUVULElBQUlRLGNBQWM7WUFDaEIsTUFBTSxJQUFJUCxNQUFNLENBQUMsa0NBQWtDLEVBQUVPLGFBQWFaLE9BQU8sRUFBRTtRQUM3RTtRQUVBLCtEQUErRDtRQUMvRCxJQUFJWixpQkFBaUJ5QixZQUFZLElBQUlGLFFBQVFHLEtBQUssRUFBRTtZQUNsRCxpREFBaUQ7WUFDakQsTUFBTUMsbUJBQW1CM0IsaUJBQWlCVSxNQUFNLENBQUNrQixXQUFXO1lBQzVELE1BQU1DLHdCQUF3QixDQUFDLFNBQVMsRUFBRUYsa0JBQWtCO1lBRTVELElBQUlULGVBQ0FBLFlBQVlZLGdCQUFnQixLQUFLLFNBQ2pDWixXQUFXLENBQUNXLHNCQUFzQixLQUFLLE9BQU87Z0JBQ2hELElBQUk7b0JBQ0Ysb0RBQW9EO29CQUNwRCxNQUFNRSxpQkFBaUJSLFFBQVFHLEtBQUssQ0FBQ00sT0FBTyxDQUFDLE9BQU87b0JBRXBELGtCQUFrQjtvQkFDbEIsTUFBTUMsbUJBQW1CLE1BQU1yQyxrRUFBZUEsQ0FDNUNtQyxnQkFDQS9CLGlCQUFpQlksT0FBTztvQkFHMUIsNkJBQTZCO29CQUM3QixNQUFNWCxTQUFTSSxJQUFJLENBQUMscUJBQXFCQyxNQUFNLENBQUM7d0JBQzlDQyxTQUFTUCxpQkFBaUJRLE1BQU07d0JBQ2hDMEIsV0FBVzt3QkFDWHRCLFNBQVNaLGlCQUFpQlksT0FBTzt3QkFDakN1QixjQUFjO3dCQUNkQyxxQkFBcUJILGlCQUFpQkksUUFBUSxDQUFDLEVBQUUsQ0FBQ2YsRUFBRTt3QkFDcERnQixRQUFRO3dCQUNSQyxTQUFTLElBQUlDLE9BQU9DLFdBQVc7d0JBQy9CQyxVQUFVOzRCQUNSQyxpQkFBaUJ4QyxhQUFhbUIsRUFBRTs0QkFDaENzQixtQkFBbUI1QyxpQkFBaUJVLE1BQU07d0JBQzVDO29CQUNGO29CQUVBLHdCQUF3QjtvQkFDeEIsTUFBTVQsU0FDSEksSUFBSSxDQUFDLGlCQUNMd0MsTUFBTSxDQUFDO3dCQUFFL0IsbUJBQW1CO29CQUFLLEdBQ2pDSyxFQUFFLENBQUMsTUFBTWhCLGFBQWFtQixFQUFFO2dCQUM3QixFQUFFLE9BQU93QixlQUFlO29CQUN0QmpELDJEQUFRQSxDQUFDLDZCQUE2QmlEO2dCQUN0QyxpREFBaUQ7Z0JBQ25EO1lBQ0Y7UUFDRjtRQUVBLHFFQUFxRTtRQUVyRSxPQUFPM0MsYUFBYW1CLEVBQUU7SUFDeEIsRUFBRSxPQUFPbEIsT0FBTztRQUNkUCwyREFBUUEsQ0FBQyxpQ0FBaUNPO1FBQzFDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Ozs7OztDQVFDLEdBQ00sZUFBZTJDLCtCQUNwQkMsWUFBb0IsRUFDcEJDLFVBQWtCLEVBQ2xCQyxZQUFvQixFQUNwQkMsY0FBc0I7SUFFdEIsTUFBTWxELFdBQVdOLGdGQUEwQkE7SUFFM0MsSUFBSTtRQUNGLDZCQUE2QjtRQUM3QixNQUFNLEVBQUVPLE1BQU15QixnQkFBZ0IsRUFBRXZCLE9BQU9nRCxTQUFTLEVBQUUsR0FBRyxNQUFNbkQsU0FDeERJLElBQUksQ0FBQyxzQkFDTFUsTUFBTSxDQUFDLHVDQUNQSSxFQUFFLENBQUMsMkJBQ0hILE1BQU07UUFFVCxJQUFJb0MsV0FBVztZQUNiLE1BQU0sSUFBSW5DLE1BQU0sQ0FBQyxvQ0FBb0MsRUFBRW1DLFVBQVV4QyxPQUFPLEVBQUU7UUFDNUU7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTUQsUUFBUWdCLGlCQUFpQjBCLGdCQUFnQixDQUM1Q3JCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyx1QkFBdUJtQjtRQUVsQyxNQUFNdkMsVUFBVWUsaUJBQWlCMkIsYUFBYSxDQUMzQ3RCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyx1QkFBdUJtQjtRQUVsQyxxQkFBcUI7UUFDckIsT0FBTyxNQUFNcEQsaUJBQWlCO1lBQzVCUyxRQUFRd0M7WUFDUnRDLFFBQVFpQixpQkFBaUJMLEVBQUU7WUFDM0JYO1lBQ0FDO1lBQ0FWLE1BQU07Z0JBQUUrQztnQkFBWUM7Z0JBQWNDO1lBQWU7WUFDakQxQixjQUFjO1lBQ2Q4QixXQUFXO1FBQ2I7SUFDRixFQUFFLE9BQU9uRCxPQUFPO1FBQ2RQLDJEQUFRQSxDQUFDLCtDQUErQ087UUFDeEQsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sZUFBZW9ELG9DQUNwQkMsWUFBb0IsRUFDcEJSLFVBQWtCLEVBQ2xCQyxZQUFvQixFQUNwQlEsY0FBc0IsRUFDdEJDLG1CQUEyQjtJQUUzQixNQUFNMUQsV0FBV04sZ0ZBQTBCQTtJQUUzQyxJQUFJO1FBQ0YsNkJBQTZCO1FBQzdCLE1BQU0sRUFBRU8sTUFBTXlCLGdCQUFnQixFQUFFdkIsT0FBT2dELFNBQVMsRUFBRSxHQUFHLE1BQU1uRCxTQUN4REksSUFBSSxDQUFDLHNCQUNMVSxNQUFNLENBQUMsdUNBQ1BJLEVBQUUsQ0FBQyxpQ0FDSEgsTUFBTTtRQUVULElBQUlvQyxXQUFXO1lBQ2IsTUFBTSxJQUFJbkMsTUFBTSxDQUFDLG9DQUFvQyxFQUFFbUMsVUFBVXhDLE9BQU8sRUFBRTtRQUM1RTtRQUVBLGdDQUFnQztRQUNoQyxNQUFNZ0QscUJBQXFCLElBQUlDLEtBQUtDLFlBQVksQ0FBQyxTQUFTQyxNQUFNLENBQUNKO1FBRWpFLG1DQUFtQztRQUNuQyxNQUFNaEQsUUFBUWdCLGlCQUFpQjBCLGdCQUFnQixDQUM1Q3JCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyx1QkFBdUIwQjtRQUVsQyxNQUFNOUMsVUFBVWUsaUJBQWlCMkIsYUFBYSxDQUMzQ3RCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyx1QkFBdUIwQixnQkFDL0IxQixPQUFPLENBQUMsNEJBQTRCNEI7UUFFdkMscUJBQXFCO1FBQ3JCLE9BQU8sTUFBTTdELGlCQUFpQjtZQUM1QlMsUUFBUWlEO1lBQ1IvQyxRQUFRaUIsaUJBQWlCTCxFQUFFO1lBQzNCWDtZQUNBQztZQUNBVixNQUFNO2dCQUFFK0M7Z0JBQVlDO2dCQUFjUTtnQkFBZ0JDO1lBQW9CO1lBQ3RFbEMsY0FBYztZQUNkOEIsV0FBVztRQUNiO0lBQ0YsRUFBRSxPQUFPbkQsT0FBTztRQUNkUCwyREFBUUEsQ0FBQyxvREFBb0RPO1FBQzdELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Ozs7Ozs7Q0FTQyxHQUNNLGVBQWU0RCw4QkFDcEJoQixZQUFvQixFQUNwQkMsVUFBa0IsRUFDbEJDLFlBQW9CLEVBQ3BCZSxRQUFnQixFQUNoQkMsZ0JBQXdCO0lBRXhCLE1BQU1qRSxXQUFXTixnRkFBMEJBO0lBRTNDLElBQUk7UUFDRiw2QkFBNkI7UUFDN0IsTUFBTSxFQUFFTyxNQUFNeUIsZ0JBQWdCLEVBQUV2QixPQUFPZ0QsU0FBUyxFQUFFLEdBQUcsTUFBTW5ELFNBQ3hESSxJQUFJLENBQUMsc0JBQ0xVLE1BQU0sQ0FBQyx1Q0FDUEksRUFBRSxDQUFDLDBCQUNISCxNQUFNO1FBRVQsSUFBSW9DLFdBQVc7WUFDYixNQUFNLElBQUluQyxNQUFNLENBQUMsb0NBQW9DLEVBQUVtQyxVQUFVeEMsT0FBTyxFQUFFO1FBQzVFO1FBRUEsbUNBQW1DO1FBQ25DLE1BQU1ELFFBQVFnQixpQkFBaUIwQixnQkFBZ0IsQ0FDNUNyQixPQUFPLENBQUMscUJBQXFCa0IsY0FDN0JsQixPQUFPLENBQUMsZ0JBQWdCaUMsU0FBU0UsUUFBUTtRQUU1QyxNQUFNdkQsVUFBVWUsaUJBQWlCMkIsYUFBYSxDQUMzQ3RCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyxnQkFBZ0JpQyxTQUFTRSxRQUFRO1FBRTVDLHFCQUFxQjtRQUNyQixPQUFPLE1BQU1wRSxpQkFBaUI7WUFDNUJTLFFBQVF3QztZQUNSdEMsUUFBUWlCLGlCQUFpQkwsRUFBRTtZQUMzQlg7WUFDQUM7WUFDQVYsTUFBTTtnQkFBRStDO2dCQUFZQztnQkFBY2U7Z0JBQVVDO1lBQWlCO1lBQzdEekMsY0FBYztZQUNkOEIsV0FBVztRQUNiO0lBQ0YsRUFBRSxPQUFPbkQsT0FBTztRQUNkUCwyREFBUUEsQ0FBQyw4Q0FBOENPO1FBQ3ZELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Ozs7Ozs7O0NBVUMsR0FDTSxlQUFlZ0UsNkJBQ3BCcEIsWUFBb0IsRUFDcEJDLFVBQWtCLEVBQ2xCQyxZQUFvQixFQUNwQm1CLE1BQWMsRUFDZC9CLE1BQStCLEVBQy9CZ0MsS0FBYztJQUVkLE1BQU1yRSxXQUFXTixnRkFBMEJBO0lBRTNDLElBQUk7UUFDRiw2QkFBNkI7UUFDN0IsTUFBTSxFQUFFTyxNQUFNeUIsZ0JBQWdCLEVBQUV2QixPQUFPZ0QsU0FBUyxFQUFFLEdBQUcsTUFBTW5ELFNBQ3hESSxJQUFJLENBQUMsc0JBQ0xVLE1BQU0sQ0FBQyx1Q0FDUEksRUFBRSxDQUFDLHlCQUNISCxNQUFNO1FBRVQsSUFBSW9DLFdBQVc7WUFDYixNQUFNLElBQUluQyxNQUFNLENBQUMsb0NBQW9DLEVBQUVtQyxVQUFVeEMsT0FBTyxFQUFFO1FBQzVFO1FBRUEsa0JBQWtCO1FBQ2xCLE1BQU0yRCxhQUFhakMsV0FBVyxhQUFhLGFBQWE7UUFFeEQsbUNBQW1DO1FBQ25DLE1BQU0zQixRQUFRZ0IsaUJBQWlCMEIsZ0JBQWdCLENBQzVDckIsT0FBTyxDQUFDLHFCQUFxQmtCLGNBQzdCbEIsT0FBTyxDQUFDLGNBQWN1QztRQUV6QixNQUFNM0QsVUFBVWUsaUJBQWlCMkIsYUFBYSxDQUMzQ3RCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyxjQUFjdUMsWUFDdEJ2QyxPQUFPLENBQUMsYUFBYXNDLFNBQVM7UUFFakMscUJBQXFCO1FBQ3JCLE9BQU8sTUFBTXZFLGlCQUFpQjtZQUM1QlMsUUFBUXdDO1lBQ1J0QyxRQUFRaUIsaUJBQWlCTCxFQUFFO1lBQzNCWDtZQUNBQztZQUNBVixNQUFNO2dCQUFFK0M7Z0JBQVlDO2dCQUFjbUI7Z0JBQVEvQjtnQkFBUWdDO1lBQU07WUFDeEQ3QyxjQUFjO1lBQ2Q4QixXQUFXO1FBQ2I7SUFDRixFQUFFLE9BQU9uRCxPQUFPO1FBQ2RQLDJEQUFRQSxDQUFDLDZDQUE2Q087UUFDdEQsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7Ozs7OztDQVNDLEdBQ00sZUFBZW9FLDhCQUNwQnhCLFlBQW9CLEVBQ3BCQyxVQUFrQixFQUNsQkMsWUFBb0IsRUFDcEJ1QixNQUFjLEVBQ2RuQyxNQUFxQztJQUVyQyxNQUFNckMsV0FBV04sZ0ZBQTBCQTtJQUUzQyxJQUFJO1FBQ0YsNkJBQTZCO1FBQzdCLE1BQU0sRUFBRU8sTUFBTXlCLGdCQUFnQixFQUFFdkIsT0FBT2dELFNBQVMsRUFBRSxHQUFHLE1BQU1uRCxTQUN4REksSUFBSSxDQUFDLHNCQUNMVSxNQUFNLENBQUMsdUNBQ1BJLEVBQUUsQ0FBQywwQkFDSEgsTUFBTTtRQUVULElBQUlvQyxXQUFXO1lBQ2IsTUFBTSxJQUFJbkMsTUFBTSxDQUFDLG9DQUFvQyxFQUFFbUMsVUFBVXhDLE9BQU8sRUFBRTtRQUM1RTtRQUVBLGtCQUFrQjtRQUNsQixNQUFNOEQsWUFBb0M7WUFDeENDLE1BQU07WUFDTkMsU0FBUztZQUNUQyxRQUFRO1FBQ1Y7UUFFQSxNQUFNTixhQUFhRyxTQUFTLENBQUNwQyxPQUFPLElBQUlBO1FBRXhDLGlCQUFpQjtRQUNqQixNQUFNd0Msa0JBQWtCLElBQUlqQixLQUFLQyxZQUFZLENBQUMsU0FBUztZQUNyRGlCLHVCQUF1QjtZQUN2QkMsdUJBQXVCO1FBQ3pCLEdBQUdqQixNQUFNLENBQUNVO1FBRVYsbUNBQW1DO1FBQ25DLE1BQU05RCxRQUFRZ0IsaUJBQWlCMEIsZ0JBQWdCLENBQzVDckIsT0FBTyxDQUFDLHFCQUFxQmtCLGNBQzdCbEIsT0FBTyxDQUFDLGNBQWM4QyxpQkFDdEI5QyxPQUFPLENBQUMsY0FBY3VDO1FBRXpCLE1BQU0zRCxVQUFVZSxpQkFBaUIyQixhQUFhLENBQzNDdEIsT0FBTyxDQUFDLHFCQUFxQmtCLGNBQzdCbEIsT0FBTyxDQUFDLGNBQWM4QyxpQkFDdEI5QyxPQUFPLENBQUMsY0FBY3VDO1FBRXpCLHFCQUFxQjtRQUNyQixPQUFPLE1BQU14RSxpQkFBaUI7WUFDNUJTLFFBQVF3QztZQUNSdEMsUUFBUWlCLGlCQUFpQkwsRUFBRTtZQUMzQlg7WUFDQUM7WUFDQVYsTUFBTTtnQkFBRStDO2dCQUFZQztnQkFBY3VCO2dCQUFRbkM7WUFBTztZQUNqRGIsY0FBYztZQUNkOEIsV0FBVztRQUNiO0lBQ0YsRUFBRSxPQUFPbkQsT0FBTztRQUNkUCwyREFBUUEsQ0FBQyw4Q0FBOENPO1FBQ3ZELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Ozs7Ozs7OztDQVdDLEdBQ00sZUFBZTZFLGlDQUNwQnhCLFlBQW9CLEVBQ3BCUixVQUFrQixFQUNsQkMsWUFBb0IsRUFDcEJGLFlBQW9CLEVBQ3BCVSxjQUFzQixFQUN0QndCLFNBQWlCLEVBQ2pCQyxXQUFtQjtJQUVuQixNQUFNbEYsV0FBV04sZ0ZBQTBCQTtJQUUzQyxJQUFJO1FBQ0YsNkJBQTZCO1FBQzdCLE1BQU0sRUFBRU8sTUFBTXlCLGdCQUFnQixFQUFFdkIsT0FBT2dELFNBQVMsRUFBRSxHQUFHLE1BQU1uRCxTQUN4REksSUFBSSxDQUFDLHNCQUNMVSxNQUFNLENBQUMsdUNBQ1BJLEVBQUUsQ0FBQyw2QkFDSEgsTUFBTTtRQUVULElBQUlvQyxXQUFXO1lBQ2IsTUFBTSxJQUFJbkMsTUFBTSxDQUFDLG9DQUFvQyxFQUFFbUMsVUFBVXhDLE9BQU8sRUFBRTtRQUM1RTtRQUVBLDRCQUE0QjtRQUM1QixNQUFNd0Usa0JBQWtCRCxnQkFBZ0IsVUFBVSxVQUFVO1FBRTVELG1DQUFtQztRQUNuQyxNQUFNeEUsUUFBUWdCLGlCQUFpQjBCLGdCQUFnQixDQUM1Q3JCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyx1QkFBdUIwQjtRQUVsQyxNQUFNOUMsVUFBVWUsaUJBQWlCMkIsYUFBYSxDQUMzQ3RCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyx1QkFBdUIwQixnQkFDL0IxQixPQUFPLENBQUMsb0JBQW9Cb0Q7UUFFL0IscUJBQXFCO1FBQ3JCLE9BQU8sTUFBTXJGLGlCQUFpQjtZQUM1QlMsUUFBUWlEO1lBQ1IvQyxRQUFRaUIsaUJBQWlCTCxFQUFFO1lBQzNCWDtZQUNBQztZQUNBVixNQUFNO2dCQUFFK0M7Z0JBQVlDO2dCQUFjRjtnQkFBY1U7Z0JBQWdCd0I7Z0JBQVdDO1lBQVk7WUFDdkYxRCxjQUFjO1lBQ2Q4QixXQUFXO1FBQ2I7SUFDRixFQUFFLE9BQU9uRCxPQUFPO1FBQ2RQLDJEQUFRQSxDQUFDLGlEQUFpRE87UUFDMUQsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7Ozs7Ozs7O0NBV0MsR0FDTSxlQUFlaUYscUNBQ3BCN0UsTUFBYyxFQUNkOEUsVUFBa0IsRUFDbEJyQyxVQUFrQixFQUNsQkMsWUFBb0IsRUFDcEJxQyxJQUFZLEVBQ1pDLElBQVksRUFDWkMsY0FBc0I7SUFFdEIsTUFBTXhGLFdBQVdOLGdGQUEwQkE7SUFFM0MsSUFBSTtRQUNGLDZCQUE2QjtRQUM3QixNQUFNLEVBQUVPLE1BQU15QixnQkFBZ0IsRUFBRXZCLE9BQU9nRCxTQUFTLEVBQUUsR0FBRyxNQUFNbkQsU0FDeERJLElBQUksQ0FBQyxzQkFDTFUsTUFBTSxDQUFDLHVDQUNQSSxFQUFFLENBQUMsaUNBQ0hILE1BQU07UUFFVCxJQUFJb0MsV0FBVztZQUNiLE1BQU0sSUFBSW5DLE1BQU0sQ0FBQyxvQ0FBb0MsRUFBRW1DLFVBQVV4QyxPQUFPLEVBQUU7UUFDNUU7UUFFQSxtQ0FBbUM7UUFDbkMsTUFBTUQsUUFBUWdCLGlCQUFpQjBCLGdCQUFnQixDQUM1Q3JCLE9BQU8sQ0FBQyxxQkFBcUJrQixjQUM3QmxCLE9BQU8sQ0FBQyxZQUFZdUQ7UUFFdkIsTUFBTTNFLFVBQVVlLGlCQUFpQjJCLGFBQWEsQ0FDM0N0QixPQUFPLENBQUMscUJBQXFCa0IsY0FDN0JsQixPQUFPLENBQUMsbUJBQW1CeUQsZ0JBQzNCekQsT0FBTyxDQUFDLFlBQVl1RCxNQUNwQnZELE9BQU8sQ0FBQyxZQUFZd0Q7UUFFdkIscUJBQXFCO1FBQ3JCLE9BQU8sTUFBTXpGLGlCQUFpQjtZQUM1QlMsUUFBUUE7WUFDUkUsUUFBUWlCLGlCQUFpQkwsRUFBRTtZQUMzQlg7WUFDQUM7WUFDQVYsTUFBTTtnQkFBRW9GO2dCQUFZckM7Z0JBQVlDO2dCQUFjcUM7Z0JBQU1DO2dCQUFNQztZQUFlO1lBQ3pFaEUsY0FBYztZQUNkOEIsV0FBVztRQUNiO0lBQ0YsRUFBRSxPQUFPbkQsT0FBTztRQUNkUCwyREFBUUEsQ0FBQyxxREFBcURPO1FBQzlELE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Ozs7Ozs7Ozs7Q0FZQyxHQUNNLGVBQWVzRixpQ0FDcEJsRixNQUFjLEVBQ2Q4RSxVQUFrQixFQUNsQnJDLFVBQWtCLEVBQ2xCQyxZQUFvQixFQUNwQnFDLElBQVksRUFDWkMsSUFBWSxFQUNaQyxjQUFzQixFQUN0QkUsWUFBb0I7SUFFcEIsTUFBTTFGLFdBQVdOLGdGQUEwQkE7SUFFM0MsSUFBSTtRQUNGLDZCQUE2QjtRQUM3QixNQUFNLEVBQUVPLE1BQU15QixnQkFBZ0IsRUFBRXZCLE9BQU9nRCxTQUFTLEVBQUUsR0FBRyxNQUFNbkQsU0FDeERJLElBQUksQ0FBQyxzQkFDTFUsTUFBTSxDQUFDLHVDQUNQSSxFQUFFLENBQUMsNkJBQ0hILE1BQU07UUFFVCxJQUFJb0MsV0FBVztZQUNiLE1BQU0sSUFBSW5DLE1BQU0sQ0FBQyxvQ0FBb0MsRUFBRW1DLFVBQVV4QyxPQUFPLEVBQUU7UUFDNUU7UUFFQSw0QkFBNEI7UUFDNUIsTUFBTWdGLGVBQWVELGlCQUFpQixlQUFlLGFBQWE7UUFFbEUsbUNBQW1DO1FBQ25DLE1BQU1oRixRQUFRZ0IsaUJBQWlCMEIsZ0JBQWdCLENBQzVDckIsT0FBTyxDQUFDLHFCQUFxQmtCLGNBQzdCbEIsT0FBTyxDQUFDLHFCQUFxQjREO1FBRWhDLE1BQU1oRixVQUFVZSxpQkFBaUIyQixhQUFhLENBQzNDdEIsT0FBTyxDQUFDLHFCQUFxQmtCLGNBQzdCbEIsT0FBTyxDQUFDLG1CQUFtQnlELGdCQUMzQnpELE9BQU8sQ0FBQyxZQUFZdUQsTUFDcEJ2RCxPQUFPLENBQUMsWUFBWXdELE1BQ3BCeEQsT0FBTyxDQUFDLHFCQUFxQjREO1FBRWhDLHFCQUFxQjtRQUNyQixPQUFPLE1BQU03RixpQkFBaUI7WUFDNUJTLFFBQVFBO1lBQ1JFLFFBQVFpQixpQkFBaUJMLEVBQUU7WUFDM0JYO1lBQ0FDO1lBQ0FWLE1BQU07Z0JBQUVvRjtnQkFBWXJDO2dCQUFZQztnQkFBY3FDO2dCQUFNQztnQkFBTUM7Z0JBQWdCRTtZQUFhO1lBQ3ZGbEUsY0FBYztZQUNkOEIsV0FBVztRQUNiO0lBQ0YsRUFBRSxPQUFPbkQsT0FBTztRQUNkUCwyREFBUUEsQ0FBQyxpREFBaURPO1FBQzFELE1BQU1BO0lBQ1I7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL2x1aXp2aW5jZW56aS9Eb2N1bWVudHMvQUlfUHJvamVjdHMvQ3JpYWRvcmVzL3NyYy9saWIvc2VydmljZXMvbm90aWZpY2F0aW9uLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogU2VydmnDp28gZGUgbm90aWZpY2HDp8O1ZXNcbiAqXG4gKiBFc3RlIG3Ds2R1bG8gZm9ybmVjZSBmdW7Dp8O1ZXMgcGFyYSBlbnZpYXIgbm90aWZpY2HDp8O1ZXMgYXRyYXbDqXMgZGUgZGlmZXJlbnRlcyBjYW5haXNcbiAqIChXaGF0c0FwcCwgZW1haWwsIGV0Yy4pIGUgZ2VyZW5jaWFyIG8gcmVnaXN0cm8gZGUgbm90aWZpY2HDp8O1ZXMuXG4gKi9cblxuaW1wb3J0IHsgY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZS9zZXJ2ZXInO1xuaW1wb3J0IHsgc2VuZFRleHRNZXNzYWdlLCBzZW5kVGVtcGxhdGVNZXNzYWdlIH0gZnJvbSAnQC9saWIvYXBpL3doYXRzYXBwJztcbmltcG9ydCB7IGxvZ0Vycm9yIH0gZnJvbSAnQC9saWIvdXRpbHMvZXJyb3JzJztcblxuLy8gVGlwb3MgZGUgbm90aWZpY2HDp8Ojb1xuZXhwb3J0IGVudW0gTm90aWZpY2F0aW9uVHlwZSB7XG4gIENBTVBBSUdOX0lOVklURSA9ICdjYW1wYWlnbl9pbnZpdGUnLFxuICBDQU1QQUlHTl9TVEFUVVNfQ0hBTkdFID0gJ2NhbXBhaWduX3N0YXR1c19jaGFuZ2UnLFxuICBSQU5LSU5HX1VQREFURSA9ICdyYW5raW5nX3VwZGF0ZScsXG4gIFBBWU1FTlRfU1RBVFVTID0gJ3BheW1lbnRfc3RhdHVzJyxcbiAgUE9TVF9BUFBST1ZBTCA9ICdwb3N0X2FwcHJvdmFsJyxcbiAgU1VCU0NSSVBUSU9OX0VYUElSWSA9ICdzdWJzY3JpcHRpb25fZXhwaXJ5JyxcbiAgTkVXX0lORkxVRU5DRVJfSk9JTkVEID0gJ25ld19pbmZsdWVuY2VyX2pvaW5lZCcsXG4gIE5FV19QT1NUX0NSRUFURUQgPSAnbmV3X3Bvc3RfY3JlYXRlZCcsXG4gIFdFRUtMWV9SRVBPUlQgPSAnd2Vla2x5X3JlcG9ydCcsXG4gIENPTlRFTlRfU1VCTUlUVEVEID0gJ2NvbnRlbnRfc3VibWl0dGVkJyxcbiAgQ09OVEVOVF9BUFBST1ZFRCA9ICdjb250ZW50X2FwcHJvdmVkJyxcbiAgQ09OVEVOVF9SRUpFQ1RFRCA9ICdjb250ZW50X3JlamVjdGVkJyxcbiAgQ09OVEVOVF9SRVZJU0lPTiA9ICdjb250ZW50X3JldmlzaW9uJyxcbiAgU0NIRURVTEVfQ09ORklSTUFUSU9OID0gJ3NjaGVkdWxlX2NvbmZpcm1hdGlvbicsXG4gIFNDSEVEVUxFX1JFTUlOREVSID0gJ3NjaGVkdWxlX3JlbWluZGVyJyxcbiAgU0NIRURVTEVfQ0FOQ0VMTEFUSU9OID0gJ3NjaGVkdWxlX2NhbmNlbGxhdGlvbicsXG4gIEFDSElFVkVNRU5UX1VOTE9DS0VEID0gJ2FjaGlldmVtZW50X3VubG9ja2VkJyAvLyBBZGljaW9uYWRvXG59XG5cbi8vIEludGVyZmFjZSBwYXJhIGRhZG9zIGRlIG5vdGlmaWNhw6fDo29cbmludGVyZmFjZSBOb3RpZmljYXRpb25EYXRhIHtcbiAgdXNlcklkOiBzdHJpbmc7XG4gIHR5cGVJZDogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGRhdGE/OiBhbnk7XG4gIHNlbmRXaGF0c0FwcD86IGJvb2xlYW47XG4gIHNlbmRFbWFpbD86IGJvb2xlYW47XG59XG5cbi8qKlxuICogRW52aWEgdW1hIG5vdGlmaWNhw6fDo28gcGFyYSB1bSB1c3XDoXJpb1xuICpcbiAqIEBwYXJhbSBub3RpZmljYXRpb25EYXRhIERhZG9zIGRhIG5vdGlmaWNhw6fDo29cbiAqIEByZXR1cm5zIElEIGRhIG5vdGlmaWNhw6fDo28gY3JpYWRhXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kTm90aWZpY2F0aW9uKG5vdGlmaWNhdGlvbkRhdGE6IE5vdGlmaWNhdGlvbkRhdGEpIHtcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCgpO1xuXG4gIHRyeSB7XG4gICAgLy8gQ3JpYXIgcmVnaXN0cm8gZGUgbm90aWZpY2HDp8Ojb1xuICAgIGNvbnN0IHsgZGF0YTogbm90aWZpY2F0aW9uLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdub3RpZmljYXRpb25zJylcbiAgICAgIC5pbnNlcnQoe1xuICAgICAgICB1c2VyX2lkOiBub3RpZmljYXRpb25EYXRhLnVzZXJJZCxcbiAgICAgICAgdHlwZV9pZDogbm90aWZpY2F0aW9uRGF0YS50eXBlSWQsXG4gICAgICAgIHRpdGxlOiBub3RpZmljYXRpb25EYXRhLnRpdGxlLFxuICAgICAgICBtZXNzYWdlOiBub3RpZmljYXRpb25EYXRhLm1lc3NhZ2UsXG4gICAgICAgIGRhdGE6IG5vdGlmaWNhdGlvbkRhdGEuZGF0YSB8fCB7fSxcbiAgICAgICAgc2VudF92aWFfZW1haWw6IGZhbHNlLFxuICAgICAgICBzZW50X3ZpYV93aGF0c2FwcDogZmFsc2UsXG4gICAgICB9KVxuICAgICAgLnNlbGVjdCgpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRXJybyBhbyBjcmlhciBub3RpZmljYcOnw6NvOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgLy8gVmVyaWZpY2FyIHByZWZlcsOqbmNpYXMgZGUgbm90aWZpY2HDp8OjbyBkbyB1c3XDoXJpb1xuICAgIGNvbnN0IHsgZGF0YTogcHJlZmVyZW5jZXMgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9ub3RpZmljYXRpb25fcHJlZmVyZW5jZXMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCBub3RpZmljYXRpb25EYXRhLnVzZXJJZClcbiAgICAgIC5tYXliZVNpbmdsZSgpO1xuXG4gICAgLy8gU2UgbyB1c3XDoXJpbyBkZXNhdGl2b3Ugbm90aWZpY2HDp8O1ZXMsIG7Do28gZW52aWFyXG4gICAgaWYgKHByZWZlcmVuY2VzICYmICFwcmVmZXJlbmNlcy5lbmFibGVfbm90aWZpY2F0aW9ucykge1xuICAgICAgcmV0dXJuIG5vdGlmaWNhdGlvbi5pZDtcbiAgICB9XG5cbiAgICAvLyBCdXNjYXIgaW5mb3JtYcOnw7VlcyBkbyB1c3XDoXJpb1xuICAgIGNvbnN0IHsgZGF0YTogcHJvZmlsZSwgZXJyb3I6IHByb2ZpbGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdwcm9maWxlcycpXG4gICAgICAuc2VsZWN0KCdwaG9uZSwgZW1haWwnKVxuICAgICAgLmVxKCdpZCcsIG5vdGlmaWNhdGlvbkRhdGEudXNlcklkKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKHByb2ZpbGVFcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvIGFvIGJ1c2NhciBwZXJmaWwgZG8gdXN1w6FyaW86ICR7cHJvZmlsZUVycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgLy8gRW52aWFyIHBvciBXaGF0c0FwcCBzZSBzb2xpY2l0YWRvIGUgbyB1c3XDoXJpbyB0aXZlciB0ZWxlZm9uZVxuICAgIGlmIChub3RpZmljYXRpb25EYXRhLnNlbmRXaGF0c0FwcCAmJiBwcm9maWxlLnBob25lKSB7XG4gICAgICAvLyBWZXJpZmljYXIgcHJlZmVyw6puY2lhcyBlc3BlY8OtZmljYXMgZGUgV2hhdHNBcHBcbiAgICAgIGNvbnN0IG5vdGlmaWNhdGlvblR5cGUgPSBub3RpZmljYXRpb25EYXRhLnR5cGVJZC50b0xvd2VyQ2FzZSgpO1xuICAgICAgY29uc3Qgd2hhdHNhcHBQcmVmZXJlbmNlS2V5ID0gYHdoYXRzYXBwXyR7bm90aWZpY2F0aW9uVHlwZX1gIGFzIGtleW9mIHR5cGVvZiBwcmVmZXJlbmNlcztcblxuICAgICAgaWYgKHByZWZlcmVuY2VzICYmXG4gICAgICAgICAgcHJlZmVyZW5jZXMud2hhdHNhcHBfZW5hYmxlZCAhPT0gZmFsc2UgJiZcbiAgICAgICAgICBwcmVmZXJlbmNlc1t3aGF0c2FwcFByZWZlcmVuY2VLZXldICE9PSBmYWxzZSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIEZvcm1hdGFyIG7Dum1lcm8gcGFyYSBwYWRyw6NvIGludGVybmFjaW9uYWwgc2VtIG8gK1xuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFBob25lID0gcHJvZmlsZS5waG9uZS5yZXBsYWNlKC9cXEQvZywgJycpO1xuXG4gICAgICAgICAgLy8gRW52aWFyIG1lbnNhZ2VtXG4gICAgICAgICAgY29uc3Qgd2hhdHNhcHBSZXNwb25zZSA9IGF3YWl0IHNlbmRUZXh0TWVzc2FnZShcbiAgICAgICAgICAgIGZvcm1hdHRlZFBob25lLFxuICAgICAgICAgICAgbm90aWZpY2F0aW9uRGF0YS5tZXNzYWdlXG4gICAgICAgICAgKTtcblxuICAgICAgICAgIC8vIFJlZ2lzdHJhciBtZW5zYWdlbSBlbnZpYWRhXG4gICAgICAgICAgYXdhaXQgc3VwYWJhc2UuZnJvbSgnd2hhdHNhcHBfbWVzc2FnZXMnKS5pbnNlcnQoe1xuICAgICAgICAgICAgdXNlcl9pZDogbm90aWZpY2F0aW9uRGF0YS51c2VySWQsXG4gICAgICAgICAgICBkaXJlY3Rpb246ICdvdXRib3VuZCcsXG4gICAgICAgICAgICBtZXNzYWdlOiBub3RpZmljYXRpb25EYXRhLm1lc3NhZ2UsXG4gICAgICAgICAgICBtZXNzYWdlX3R5cGU6ICd0ZXh0JyxcbiAgICAgICAgICAgIHdoYXRzYXBwX21lc3NhZ2VfaWQ6IHdoYXRzYXBwUmVzcG9uc2UubWVzc2FnZXNbMF0uaWQsXG4gICAgICAgICAgICBzdGF0dXM6ICdzZW50JyxcbiAgICAgICAgICAgIHNlbnRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgICAgIG5vdGlmaWNhdGlvbl9pZDogbm90aWZpY2F0aW9uLmlkLFxuICAgICAgICAgICAgICBub3RpZmljYXRpb25fdHlwZTogbm90aWZpY2F0aW9uRGF0YS50eXBlSWRcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcblxuICAgICAgICAgIC8vIEF0dWFsaXphciBub3RpZmljYcOnw6NvXG4gICAgICAgICAgYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAgIC5mcm9tKCdub3RpZmljYXRpb25zJylcbiAgICAgICAgICAgIC51cGRhdGUoeyBzZW50X3ZpYV93aGF0c2FwcDogdHJ1ZSB9KVxuICAgICAgICAgICAgLmVxKCdpZCcsIG5vdGlmaWNhdGlvbi5pZCk7XG4gICAgICAgIH0gY2F0Y2ggKHdoYXRzYXBwRXJyb3IpIHtcbiAgICAgICAgICBsb2dFcnJvcignbm90aWZpY2F0aW9uLnNlbmRXaGF0c0FwcCcsIHdoYXRzYXBwRXJyb3IpO1xuICAgICAgICAgIC8vIENvbnRpbnVhciBtZXNtbyBzZSBmYWxoYXIgbyBlbnZpbyBwb3IgV2hhdHNBcHBcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEFxdWkgdm9jw6ogYWRpY2lvbmFyaWEgYSBsw7NnaWNhIHBhcmEgZW52aWFyIHBvciBlbWFpbCBzZSBuZWNlc3PDoXJpb1xuXG4gICAgcmV0dXJuIG5vdGlmaWNhdGlvbi5pZDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dFcnJvcignbm90aWZpY2F0aW9uLnNlbmROb3RpZmljYXRpb24nLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBFbnZpYSB1bWEgbm90aWZpY2HDp8OjbyBkZSBjb252aXRlIHBhcmEgY2FtcGFuaGEgcGFyYSB1bSBpbmZsdWVuY2lhZG9yXG4gKlxuICogQHBhcmFtIGluZmx1ZW5jZXJJZCBJRCBkbyBpbmZsdWVuY2lhZG9yXG4gKiBAcGFyYW0gY2FtcGFpZ25JZCBJRCBkYSBjYW1wYW5oYVxuICogQHBhcmFtIGNhbXBhaWduTmFtZSBOb21lIGRhIGNhbXBhbmhhXG4gKiBAcGFyYW0gcmVzdGF1cmFudE5hbWUgTm9tZSBkbyByZXN0YXVyYW50ZVxuICogQHJldHVybnMgSUQgZGEgbm90aWZpY2HDp8OjbyBjcmlhZGFcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRDYW1wYWlnbkludml0ZU5vdGlmaWNhdGlvbihcbiAgaW5mbHVlbmNlcklkOiBzdHJpbmcsXG4gIGNhbXBhaWduSWQ6IHN0cmluZyxcbiAgY2FtcGFpZ25OYW1lOiBzdHJpbmcsXG4gIHJlc3RhdXJhbnROYW1lOiBzdHJpbmdcbikge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50KCk7XG5cbiAgdHJ5IHtcbiAgICAvLyBCdXNjYXIgdGlwbyBkZSBub3RpZmljYcOnw6NvXG4gICAgY29uc3QgeyBkYXRhOiBub3RpZmljYXRpb25UeXBlLCBlcnJvcjogdHlwZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ25vdGlmaWNhdGlvbl90eXBlcycpXG4gICAgICAuc2VsZWN0KCdpZCwgdGVtcGxhdGVfc3ViamVjdCwgdGVtcGxhdGVfYm9keScpXG4gICAgICAuZXEoJ25hbWUnLCBOb3RpZmljYXRpb25UeXBlLkNBTVBBSUdOX0lOVklURSlcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmICh0eXBlRXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGlwbyBkZSBub3RpZmljYcOnw6NvIG7Do28gZW5jb250cmFkbzogJHt0eXBlRXJyb3IubWVzc2FnZX1gKTtcbiAgICB9XG5cbiAgICAvLyBTdWJzdGl0dWlyIHZhcmnDoXZlaXMgbm8gdGVtcGxhdGVcbiAgICBjb25zdCB0aXRsZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfc3ViamVjdFxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7cmVzdGF1cmFudF9uYW1lfX0nLCByZXN0YXVyYW50TmFtZSk7XG5cbiAgICBjb25zdCBtZXNzYWdlID0gbm90aWZpY2F0aW9uVHlwZS50ZW1wbGF0ZV9ib2R5XG4gICAgICAucmVwbGFjZSgne3tjYW1wYWlnbl9uYW1lfX0nLCBjYW1wYWlnbk5hbWUpXG4gICAgICAucmVwbGFjZSgne3tyZXN0YXVyYW50X25hbWV9fScsIHJlc3RhdXJhbnROYW1lKTtcblxuICAgIC8vIEVudmlhciBub3RpZmljYcOnw6NvXG4gICAgcmV0dXJuIGF3YWl0IHNlbmROb3RpZmljYXRpb24oe1xuICAgICAgdXNlcklkOiBpbmZsdWVuY2VySWQsXG4gICAgICB0eXBlSWQ6IG5vdGlmaWNhdGlvblR5cGUuaWQsXG4gICAgICB0aXRsZSxcbiAgICAgIG1lc3NhZ2UsXG4gICAgICBkYXRhOiB7IGNhbXBhaWduSWQsIGNhbXBhaWduTmFtZSwgcmVzdGF1cmFudE5hbWUgfSxcbiAgICAgIHNlbmRXaGF0c0FwcDogdHJ1ZSxcbiAgICAgIHNlbmRFbWFpbDogdHJ1ZSxcbiAgICB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dFcnJvcignbm90aWZpY2F0aW9uLnNlbmRDYW1wYWlnbkludml0ZU5vdGlmaWNhdGlvbicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vKipcbiAqIEVudmlhIHVtYSBub3RpZmljYcOnw6NvIHBhcmEgbyByZXN0YXVyYW50ZSBxdWFuZG8gdW0gaW5mbHVlbmNpYWRvciBhY2VpdGEgcGFydGljaXBhciBkYSBjYW1wYW5oYVxuICpcbiAqIEBwYXJhbSByZXN0YXVyYW50SWQgSUQgZG8gcmVzdGF1cmFudGVcbiAqIEBwYXJhbSBjYW1wYWlnbklkIElEIGRhIGNhbXBhbmhhXG4gKiBAcGFyYW0gY2FtcGFpZ25OYW1lIE5vbWUgZGEgY2FtcGFuaGFcbiAqIEBwYXJhbSBpbmZsdWVuY2VyTmFtZSBOb21lIGRvIGluZmx1ZW5jaWFkb3JcbiAqIEBwYXJhbSBpbmZsdWVuY2VyRm9sbG93ZXJzIE7Dum1lcm8gZGUgc2VndWlkb3JlcyBkbyBpbmZsdWVuY2lhZG9yXG4gKiBAcmV0dXJucyBJRCBkYSBub3RpZmljYcOnw6NvIGNyaWFkYVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2VuZE5ld0luZmx1ZW5jZXJKb2luZWROb3RpZmljYXRpb24oXG4gIHJlc3RhdXJhbnRJZDogc3RyaW5nLFxuICBjYW1wYWlnbklkOiBzdHJpbmcsXG4gIGNhbXBhaWduTmFtZTogc3RyaW5nLFxuICBpbmZsdWVuY2VyTmFtZTogc3RyaW5nLFxuICBpbmZsdWVuY2VyRm9sbG93ZXJzOiBudW1iZXJcbikge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50KCk7XG5cbiAgdHJ5IHtcbiAgICAvLyBCdXNjYXIgdGlwbyBkZSBub3RpZmljYcOnw6NvXG4gICAgY29uc3QgeyBkYXRhOiBub3RpZmljYXRpb25UeXBlLCBlcnJvcjogdHlwZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ25vdGlmaWNhdGlvbl90eXBlcycpXG4gICAgICAuc2VsZWN0KCdpZCwgdGVtcGxhdGVfc3ViamVjdCwgdGVtcGxhdGVfYm9keScpXG4gICAgICAuZXEoJ25hbWUnLCBOb3RpZmljYXRpb25UeXBlLk5FV19JTkZMVUVOQ0VSX0pPSU5FRClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmICh0eXBlRXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGlwbyBkZSBub3RpZmljYcOnw6NvIG7Do28gZW5jb250cmFkbzogJHt0eXBlRXJyb3IubWVzc2FnZX1gKTtcbiAgICB9XG5cbiAgICAvLyBGb3JtYXRhciBuw7ptZXJvIGRlIHNlZ3VpZG9yZXNcbiAgICBjb25zdCBmb3JtYXR0ZWRGb2xsb3dlcnMgPSBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ3B0LUJSJykuZm9ybWF0KGluZmx1ZW5jZXJGb2xsb3dlcnMpO1xuXG4gICAgLy8gU3Vic3RpdHVpciB2YXJpw6F2ZWlzIG5vIHRlbXBsYXRlXG4gICAgY29uc3QgdGl0bGUgPSBub3RpZmljYXRpb25UeXBlLnRlbXBsYXRlX3N1YmplY3RcbiAgICAgIC5yZXBsYWNlKCd7e2NhbXBhaWduX25hbWV9fScsIGNhbXBhaWduTmFtZSlcbiAgICAgIC5yZXBsYWNlKCd7e2luZmx1ZW5jZXJfbmFtZX19JywgaW5mbHVlbmNlck5hbWUpO1xuXG4gICAgY29uc3QgbWVzc2FnZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfYm9keVxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7aW5mbHVlbmNlcl9uYW1lfX0nLCBpbmZsdWVuY2VyTmFtZSlcbiAgICAgIC5yZXBsYWNlKCd7e2luZmx1ZW5jZXJfZm9sbG93ZXJzfX0nLCBmb3JtYXR0ZWRGb2xsb3dlcnMpO1xuXG4gICAgLy8gRW52aWFyIG5vdGlmaWNhw6fDo29cbiAgICByZXR1cm4gYXdhaXQgc2VuZE5vdGlmaWNhdGlvbih7XG4gICAgICB1c2VySWQ6IHJlc3RhdXJhbnRJZCxcbiAgICAgIHR5cGVJZDogbm90aWZpY2F0aW9uVHlwZS5pZCxcbiAgICAgIHRpdGxlLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGRhdGE6IHsgY2FtcGFpZ25JZCwgY2FtcGFpZ25OYW1lLCBpbmZsdWVuY2VyTmFtZSwgaW5mbHVlbmNlckZvbGxvd2VycyB9LFxuICAgICAgc2VuZFdoYXRzQXBwOiB0cnVlLFxuICAgICAgc2VuZEVtYWlsOiB0cnVlLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ0Vycm9yKCdub3RpZmljYXRpb24uc2VuZE5ld0luZmx1ZW5jZXJKb2luZWROb3RpZmljYXRpb24nLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBFbnZpYSB1bWEgbm90aWZpY2HDp8OjbyBkZSBhdHVhbGl6YcOnw6NvIGRlIHJhbmtpbmcgcGFyYSB1bSBpbmZsdWVuY2lhZG9yXG4gKlxuICogQHBhcmFtIGluZmx1ZW5jZXJJZCBJRCBkbyBpbmZsdWVuY2lhZG9yXG4gKiBAcGFyYW0gY2FtcGFpZ25JZCBJRCBkYSBjYW1wYW5oYVxuICogQHBhcmFtIGNhbXBhaWduTmFtZSBOb21lIGRhIGNhbXBhbmhhXG4gKiBAcGFyYW0gcG9zaXRpb24gTm92YSBwb3Npw6fDo28gbm8gcmFua2luZ1xuICogQHBhcmFtIHByZXZpb3VzUG9zaXRpb24gUG9zacOnw6NvIGFudGVyaW9yIG5vIHJhbmtpbmdcbiAqIEByZXR1cm5zIElEIGRhIG5vdGlmaWNhw6fDo28gY3JpYWRhXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kUmFua2luZ1VwZGF0ZU5vdGlmaWNhdGlvbihcbiAgaW5mbHVlbmNlcklkOiBzdHJpbmcsXG4gIGNhbXBhaWduSWQ6IHN0cmluZyxcbiAgY2FtcGFpZ25OYW1lOiBzdHJpbmcsXG4gIHBvc2l0aW9uOiBudW1iZXIsXG4gIHByZXZpb3VzUG9zaXRpb246IG51bWJlclxuKSB7XG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKTtcblxuICB0cnkge1xuICAgIC8vIEJ1c2NhciB0aXBvIGRlIG5vdGlmaWNhw6fDo29cbiAgICBjb25zdCB7IGRhdGE6IG5vdGlmaWNhdGlvblR5cGUsIGVycm9yOiB0eXBlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnbm90aWZpY2F0aW9uX3R5cGVzJylcbiAgICAgIC5zZWxlY3QoJ2lkLCB0ZW1wbGF0ZV9zdWJqZWN0LCB0ZW1wbGF0ZV9ib2R5JylcbiAgICAgIC5lcSgnbmFtZScsIE5vdGlmaWNhdGlvblR5cGUuUkFOS0lOR19VUERBVEUpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAodHlwZUVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRpcG8gZGUgbm90aWZpY2HDp8OjbyBuw6NvIGVuY29udHJhZG86ICR7dHlwZUVycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgLy8gU3Vic3RpdHVpciB2YXJpw6F2ZWlzIG5vIHRlbXBsYXRlXG4gICAgY29uc3QgdGl0bGUgPSBub3RpZmljYXRpb25UeXBlLnRlbXBsYXRlX3N1YmplY3RcbiAgICAgIC5yZXBsYWNlKCd7e2NhbXBhaWduX25hbWV9fScsIGNhbXBhaWduTmFtZSlcbiAgICAgIC5yZXBsYWNlKCd7e3Bvc2l0aW9ufX0nLCBwb3NpdGlvbi50b1N0cmluZygpKTtcblxuICAgIGNvbnN0IG1lc3NhZ2UgPSBub3RpZmljYXRpb25UeXBlLnRlbXBsYXRlX2JvZHlcbiAgICAgIC5yZXBsYWNlKCd7e2NhbXBhaWduX25hbWV9fScsIGNhbXBhaWduTmFtZSlcbiAgICAgIC5yZXBsYWNlKCd7e3Bvc2l0aW9ufX0nLCBwb3NpdGlvbi50b1N0cmluZygpKTtcblxuICAgIC8vIEVudmlhciBub3RpZmljYcOnw6NvXG4gICAgcmV0dXJuIGF3YWl0IHNlbmROb3RpZmljYXRpb24oe1xuICAgICAgdXNlcklkOiBpbmZsdWVuY2VySWQsXG4gICAgICB0eXBlSWQ6IG5vdGlmaWNhdGlvblR5cGUuaWQsXG4gICAgICB0aXRsZSxcbiAgICAgIG1lc3NhZ2UsXG4gICAgICBkYXRhOiB7IGNhbXBhaWduSWQsIGNhbXBhaWduTmFtZSwgcG9zaXRpb24sIHByZXZpb3VzUG9zaXRpb24gfSxcbiAgICAgIHNlbmRXaGF0c0FwcDogdHJ1ZSxcbiAgICAgIHNlbmRFbWFpbDogdHJ1ZSxcbiAgICB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBsb2dFcnJvcignbm90aWZpY2F0aW9uLnNlbmRSYW5raW5nVXBkYXRlTm90aWZpY2F0aW9uJywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogRW52aWEgdW1hIG5vdGlmaWNhw6fDo28gZGUgYXByb3Zhw6fDo28vcmVqZWnDp8OjbyBkZSBwb3N0IHBhcmEgdW0gaW5mbHVlbmNpYWRvclxuICpcbiAqIEBwYXJhbSBpbmZsdWVuY2VySWQgSUQgZG8gaW5mbHVlbmNpYWRvclxuICogQHBhcmFtIGNhbXBhaWduSWQgSUQgZGEgY2FtcGFuaGFcbiAqIEBwYXJhbSBjYW1wYWlnbk5hbWUgTm9tZSBkYSBjYW1wYW5oYVxuICogQHBhcmFtIHBvc3RJZCBJRCBkbyBwb3N0XG4gKiBAcGFyYW0gc3RhdHVzIFN0YXR1cyBkbyBwb3N0IChhcHByb3ZlZC9yZWplY3RlZClcbiAqIEBwYXJhbSBub3RlcyBOb3RhcyBhZGljaW9uYWlzIChvcGNpb25hbClcbiAqIEByZXR1cm5zIElEIGRhIG5vdGlmaWNhw6fDo28gY3JpYWRhXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kUG9zdEFwcHJvdmFsTm90aWZpY2F0aW9uKFxuICBpbmZsdWVuY2VySWQ6IHN0cmluZyxcbiAgY2FtcGFpZ25JZDogc3RyaW5nLFxuICBjYW1wYWlnbk5hbWU6IHN0cmluZyxcbiAgcG9zdElkOiBzdHJpbmcsXG4gIHN0YXR1czogJ2FwcHJvdmVkJyB8ICdyZWplY3RlZCcsXG4gIG5vdGVzPzogc3RyaW5nXG4pIHtcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCgpO1xuXG4gIHRyeSB7XG4gICAgLy8gQnVzY2FyIHRpcG8gZGUgbm90aWZpY2HDp8Ojb1xuICAgIGNvbnN0IHsgZGF0YTogbm90aWZpY2F0aW9uVHlwZSwgZXJyb3I6IHR5cGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdub3RpZmljYXRpb25fdHlwZXMnKVxuICAgICAgLnNlbGVjdCgnaWQsIHRlbXBsYXRlX3N1YmplY3QsIHRlbXBsYXRlX2JvZHknKVxuICAgICAgLmVxKCduYW1lJywgTm90aWZpY2F0aW9uVHlwZS5QT1NUX0FQUFJPVkFMKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKHR5cGVFcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUaXBvIGRlIG5vdGlmaWNhw6fDo28gbsOjbyBlbmNvbnRyYWRvOiAke3R5cGVFcnJvci5tZXNzYWdlfWApO1xuICAgIH1cblxuICAgIC8vIFRyYWR1emlyIHN0YXR1c1xuICAgIGNvbnN0IHN0YXR1c1RleHQgPSBzdGF0dXMgPT09ICdhcHByb3ZlZCcgPyAnYXByb3ZhZG8nIDogJ3JlamVpdGFkbyc7XG5cbiAgICAvLyBTdWJzdGl0dWlyIHZhcmnDoXZlaXMgbm8gdGVtcGxhdGVcbiAgICBjb25zdCB0aXRsZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfc3ViamVjdFxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7c3RhdHVzfX0nLCBzdGF0dXNUZXh0KTtcblxuICAgIGNvbnN0IG1lc3NhZ2UgPSBub3RpZmljYXRpb25UeXBlLnRlbXBsYXRlX2JvZHlcbiAgICAgIC5yZXBsYWNlKCd7e2NhbXBhaWduX25hbWV9fScsIGNhbXBhaWduTmFtZSlcbiAgICAgIC5yZXBsYWNlKCd7e3N0YXR1c319Jywgc3RhdHVzVGV4dClcbiAgICAgIC5yZXBsYWNlKCd7e25vdGVzfX0nLCBub3RlcyB8fCAnJyk7XG5cbiAgICAvLyBFbnZpYXIgbm90aWZpY2HDp8Ojb1xuICAgIHJldHVybiBhd2FpdCBzZW5kTm90aWZpY2F0aW9uKHtcbiAgICAgIHVzZXJJZDogaW5mbHVlbmNlcklkLFxuICAgICAgdHlwZUlkOiBub3RpZmljYXRpb25UeXBlLmlkLFxuICAgICAgdGl0bGUsXG4gICAgICBtZXNzYWdlLFxuICAgICAgZGF0YTogeyBjYW1wYWlnbklkLCBjYW1wYWlnbk5hbWUsIHBvc3RJZCwgc3RhdHVzLCBub3RlcyB9LFxuICAgICAgc2VuZFdoYXRzQXBwOiB0cnVlLFxuICAgICAgc2VuZEVtYWlsOiB0cnVlLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ0Vycm9yKCdub3RpZmljYXRpb24uc2VuZFBvc3RBcHByb3ZhbE5vdGlmaWNhdGlvbicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vKipcbiAqIEVudmlhIHVtYSBub3RpZmljYcOnw6NvIGRlIHBhZ2FtZW50byBwYXJhIHVtIGluZmx1ZW5jaWFkb3JcbiAqXG4gKiBAcGFyYW0gaW5mbHVlbmNlcklkIElEIGRvIGluZmx1ZW5jaWFkb3JcbiAqIEBwYXJhbSBjYW1wYWlnbklkIElEIGRhIGNhbXBhbmhhXG4gKiBAcGFyYW0gY2FtcGFpZ25OYW1lIE5vbWUgZGEgY2FtcGFuaGFcbiAqIEBwYXJhbSBhbW91bnQgVmFsb3IgZG8gcGFnYW1lbnRvXG4gKiBAcGFyYW0gc3RhdHVzIFN0YXR1cyBkbyBwYWdhbWVudG8gKHBhaWQvcGVuZGluZy9mYWlsZWQpXG4gKiBAcmV0dXJucyBJRCBkYSBub3RpZmljYcOnw6NvIGNyaWFkYVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2VuZFBheW1lbnRTdGF0dXNOb3RpZmljYXRpb24oXG4gIGluZmx1ZW5jZXJJZDogc3RyaW5nLFxuICBjYW1wYWlnbklkOiBzdHJpbmcsXG4gIGNhbXBhaWduTmFtZTogc3RyaW5nLFxuICBhbW91bnQ6IG51bWJlcixcbiAgc3RhdHVzOiAncGFpZCcgfCAncGVuZGluZycgfCAnZmFpbGVkJ1xuKSB7XG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKTtcblxuICB0cnkge1xuICAgIC8vIEJ1c2NhciB0aXBvIGRlIG5vdGlmaWNhw6fDo29cbiAgICBjb25zdCB7IGRhdGE6IG5vdGlmaWNhdGlvblR5cGUsIGVycm9yOiB0eXBlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnbm90aWZpY2F0aW9uX3R5cGVzJylcbiAgICAgIC5zZWxlY3QoJ2lkLCB0ZW1wbGF0ZV9zdWJqZWN0LCB0ZW1wbGF0ZV9ib2R5JylcbiAgICAgIC5lcSgnbmFtZScsIE5vdGlmaWNhdGlvblR5cGUuUEFZTUVOVF9TVEFUVVMpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAodHlwZUVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRpcG8gZGUgbm90aWZpY2HDp8OjbyBuw6NvIGVuY29udHJhZG86ICR7dHlwZUVycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgLy8gVHJhZHV6aXIgc3RhdHVzXG4gICAgY29uc3Qgc3RhdHVzTWFwOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAgICAgcGFpZDogJ3JlYWxpemFkbycsXG4gICAgICBwZW5kaW5nOiAncGVuZGVudGUnLFxuICAgICAgZmFpbGVkOiAnZmFsaG91J1xuICAgIH07XG5cbiAgICBjb25zdCBzdGF0dXNUZXh0ID0gc3RhdHVzTWFwW3N0YXR1c10gfHwgc3RhdHVzO1xuXG4gICAgLy8gRm9ybWF0YXIgdmFsb3JcbiAgICBjb25zdCBmb3JtYXR0ZWRBbW91bnQgPSBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ3B0LUJSJywge1xuICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiAyLFxuICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiAyXG4gICAgfSkuZm9ybWF0KGFtb3VudCk7XG5cbiAgICAvLyBTdWJzdGl0dWlyIHZhcmnDoXZlaXMgbm8gdGVtcGxhdGVcbiAgICBjb25zdCB0aXRsZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfc3ViamVjdFxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7YW1vdW50fX0nLCBmb3JtYXR0ZWRBbW91bnQpXG4gICAgICAucmVwbGFjZSgne3tzdGF0dXN9fScsIHN0YXR1c1RleHQpO1xuXG4gICAgY29uc3QgbWVzc2FnZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfYm9keVxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7YW1vdW50fX0nLCBmb3JtYXR0ZWRBbW91bnQpXG4gICAgICAucmVwbGFjZSgne3tzdGF0dXN9fScsIHN0YXR1c1RleHQpO1xuXG4gICAgLy8gRW52aWFyIG5vdGlmaWNhw6fDo29cbiAgICByZXR1cm4gYXdhaXQgc2VuZE5vdGlmaWNhdGlvbih7XG4gICAgICB1c2VySWQ6IGluZmx1ZW5jZXJJZCxcbiAgICAgIHR5cGVJZDogbm90aWZpY2F0aW9uVHlwZS5pZCxcbiAgICAgIHRpdGxlLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGRhdGE6IHsgY2FtcGFpZ25JZCwgY2FtcGFpZ25OYW1lLCBhbW91bnQsIHN0YXR1cyB9LFxuICAgICAgc2VuZFdoYXRzQXBwOiB0cnVlLFxuICAgICAgc2VuZEVtYWlsOiB0cnVlLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ0Vycm9yKCdub3RpZmljYXRpb24uc2VuZFBheW1lbnRTdGF0dXNOb3RpZmljYXRpb24nLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBFbnZpYSB1bWEgbm90aWZpY2HDp8OjbyBkZSBjb250ZcO6ZG8gZW52aWFkbyBwYXJhIGFwcm92YcOnw6NvXG4gKlxuICogQHBhcmFtIHJlc3RhdXJhbnRJZCBJRCBkbyByZXN0YXVyYW50ZVxuICogQHBhcmFtIGNhbXBhaWduSWQgSUQgZGEgY2FtcGFuaGFcbiAqIEBwYXJhbSBjYW1wYWlnbk5hbWUgTm9tZSBkYSBjYW1wYW5oYVxuICogQHBhcmFtIGluZmx1ZW5jZXJJZCBJRCBkbyBpbmZsdWVuY2lhZG9yXG4gKiBAcGFyYW0gaW5mbHVlbmNlck5hbWUgTm9tZSBkbyBpbmZsdWVuY2lhZG9yXG4gKiBAcGFyYW0gY29udGVudElkIElEIGRvIGNvbnRlw7pkb1xuICogQHBhcmFtIGNvbnRlbnRUeXBlIFRpcG8gZG8gY29udGXDumRvICh2aWRlby9pbWFnZSlcbiAqIEByZXR1cm5zIElEIGRhIG5vdGlmaWNhw6fDo28gY3JpYWRhXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kQ29udGVudFN1Ym1pdHRlZE5vdGlmaWNhdGlvbihcbiAgcmVzdGF1cmFudElkOiBzdHJpbmcsXG4gIGNhbXBhaWduSWQ6IHN0cmluZyxcbiAgY2FtcGFpZ25OYW1lOiBzdHJpbmcsXG4gIGluZmx1ZW5jZXJJZDogc3RyaW5nLFxuICBpbmZsdWVuY2VyTmFtZTogc3RyaW5nLFxuICBjb250ZW50SWQ6IHN0cmluZyxcbiAgY29udGVudFR5cGU6IHN0cmluZ1xuKSB7XG4gIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKTtcblxuICB0cnkge1xuICAgIC8vIEJ1c2NhciB0aXBvIGRlIG5vdGlmaWNhw6fDo29cbiAgICBjb25zdCB7IGRhdGE6IG5vdGlmaWNhdGlvblR5cGUsIGVycm9yOiB0eXBlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnbm90aWZpY2F0aW9uX3R5cGVzJylcbiAgICAgIC5zZWxlY3QoJ2lkLCB0ZW1wbGF0ZV9zdWJqZWN0LCB0ZW1wbGF0ZV9ib2R5JylcbiAgICAgIC5lcSgnbmFtZScsIE5vdGlmaWNhdGlvblR5cGUuQ09OVEVOVF9TVUJNSVRURUQpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAodHlwZUVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRpcG8gZGUgbm90aWZpY2HDp8OjbyBuw6NvIGVuY29udHJhZG86ICR7dHlwZUVycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgLy8gVHJhZHV6aXIgdGlwbyBkZSBjb250ZcO6ZG9cbiAgICBjb25zdCBjb250ZW50VHlwZVRleHQgPSBjb250ZW50VHlwZSA9PT0gJ3ZpZGVvJyA/ICd2w61kZW8nIDogJ2ltYWdlbSc7XG5cbiAgICAvLyBTdWJzdGl0dWlyIHZhcmnDoXZlaXMgbm8gdGVtcGxhdGVcbiAgICBjb25zdCB0aXRsZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfc3ViamVjdFxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7aW5mbHVlbmNlcl9uYW1lfX0nLCBpbmZsdWVuY2VyTmFtZSk7XG5cbiAgICBjb25zdCBtZXNzYWdlID0gbm90aWZpY2F0aW9uVHlwZS50ZW1wbGF0ZV9ib2R5XG4gICAgICAucmVwbGFjZSgne3tjYW1wYWlnbl9uYW1lfX0nLCBjYW1wYWlnbk5hbWUpXG4gICAgICAucmVwbGFjZSgne3tpbmZsdWVuY2VyX25hbWV9fScsIGluZmx1ZW5jZXJOYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7Y29udGVudF90eXBlfX0nLCBjb250ZW50VHlwZVRleHQpO1xuXG4gICAgLy8gRW52aWFyIG5vdGlmaWNhw6fDo29cbiAgICByZXR1cm4gYXdhaXQgc2VuZE5vdGlmaWNhdGlvbih7XG4gICAgICB1c2VySWQ6IHJlc3RhdXJhbnRJZCxcbiAgICAgIHR5cGVJZDogbm90aWZpY2F0aW9uVHlwZS5pZCxcbiAgICAgIHRpdGxlLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGRhdGE6IHsgY2FtcGFpZ25JZCwgY2FtcGFpZ25OYW1lLCBpbmZsdWVuY2VySWQsIGluZmx1ZW5jZXJOYW1lLCBjb250ZW50SWQsIGNvbnRlbnRUeXBlIH0sXG4gICAgICBzZW5kV2hhdHNBcHA6IHRydWUsXG4gICAgICBzZW5kRW1haWw6IHRydWUsXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbG9nRXJyb3IoJ25vdGlmaWNhdGlvbi5zZW5kQ29udGVudFN1Ym1pdHRlZE5vdGlmaWNhdGlvbicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vKipcbiAqIEVudmlhIHVtYSBub3RpZmljYcOnw6NvIGRlIGFnZW5kYW1lbnRvIGNvbmZpcm1hZG9cbiAqXG4gKiBAcGFyYW0gdXNlcklkIElEIGRvIHVzdcOhcmlvIChyZXN0YXVyYW50ZSBvdSBpbmZsdWVuY2lhZG9yKVxuICogQHBhcmFtIHNjaGVkdWxlSWQgSUQgZG8gYWdlbmRhbWVudG9cbiAqIEBwYXJhbSBjYW1wYWlnbklkIElEIGRhIGNhbXBhbmhhXG4gKiBAcGFyYW0gY2FtcGFpZ25OYW1lIE5vbWUgZGEgY2FtcGFuaGFcbiAqIEBwYXJhbSBkYXRlIERhdGEgYWdlbmRhZGFcbiAqIEBwYXJhbSB0aW1lIEhvcsOhcmlvIGFnZW5kYWRvXG4gKiBAcGFyYW0gb3RoZXJQYXJ0eU5hbWUgTm9tZSBkYSBvdXRyYSBwYXJ0ZSAocmVzdGF1cmFudGUgb3UgaW5mbHVlbmNpYWRvcilcbiAqIEByZXR1cm5zIElEIGRhIG5vdGlmaWNhw6fDo28gY3JpYWRhXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZW5kU2NoZWR1bGVDb25maXJtYXRpb25Ob3RpZmljYXRpb24oXG4gIHVzZXJJZDogc3RyaW5nLFxuICBzY2hlZHVsZUlkOiBzdHJpbmcsXG4gIGNhbXBhaWduSWQ6IHN0cmluZyxcbiAgY2FtcGFpZ25OYW1lOiBzdHJpbmcsXG4gIGRhdGU6IHN0cmluZyxcbiAgdGltZTogc3RyaW5nLFxuICBvdGhlclBhcnR5TmFtZTogc3RyaW5nXG4pIHtcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCgpO1xuXG4gIHRyeSB7XG4gICAgLy8gQnVzY2FyIHRpcG8gZGUgbm90aWZpY2HDp8Ojb1xuICAgIGNvbnN0IHsgZGF0YTogbm90aWZpY2F0aW9uVHlwZSwgZXJyb3I6IHR5cGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdub3RpZmljYXRpb25fdHlwZXMnKVxuICAgICAgLnNlbGVjdCgnaWQsIHRlbXBsYXRlX3N1YmplY3QsIHRlbXBsYXRlX2JvZHknKVxuICAgICAgLmVxKCduYW1lJywgTm90aWZpY2F0aW9uVHlwZS5TQ0hFRFVMRV9DT05GSVJNQVRJT04pXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAodHlwZUVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRpcG8gZGUgbm90aWZpY2HDp8OjbyBuw6NvIGVuY29udHJhZG86ICR7dHlwZUVycm9yLm1lc3NhZ2V9YCk7XG4gICAgfVxuXG4gICAgLy8gU3Vic3RpdHVpciB2YXJpw6F2ZWlzIG5vIHRlbXBsYXRlXG4gICAgY29uc3QgdGl0bGUgPSBub3RpZmljYXRpb25UeXBlLnRlbXBsYXRlX3N1YmplY3RcbiAgICAgIC5yZXBsYWNlKCd7e2NhbXBhaWduX25hbWV9fScsIGNhbXBhaWduTmFtZSlcbiAgICAgIC5yZXBsYWNlKCd7e2RhdGV9fScsIGRhdGUpO1xuXG4gICAgY29uc3QgbWVzc2FnZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfYm9keVxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7b3RoZXJfcGFydHl9fScsIG90aGVyUGFydHlOYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7ZGF0ZX19JywgZGF0ZSlcbiAgICAgIC5yZXBsYWNlKCd7e3RpbWV9fScsIHRpbWUpO1xuXG4gICAgLy8gRW52aWFyIG5vdGlmaWNhw6fDo29cbiAgICByZXR1cm4gYXdhaXQgc2VuZE5vdGlmaWNhdGlvbih7XG4gICAgICB1c2VySWQ6IHVzZXJJZCxcbiAgICAgIHR5cGVJZDogbm90aWZpY2F0aW9uVHlwZS5pZCxcbiAgICAgIHRpdGxlLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGRhdGE6IHsgc2NoZWR1bGVJZCwgY2FtcGFpZ25JZCwgY2FtcGFpZ25OYW1lLCBkYXRlLCB0aW1lLCBvdGhlclBhcnR5TmFtZSB9LFxuICAgICAgc2VuZFdoYXRzQXBwOiB0cnVlLFxuICAgICAgc2VuZEVtYWlsOiB0cnVlLFxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGxvZ0Vycm9yKCdub3RpZmljYXRpb24uc2VuZFNjaGVkdWxlQ29uZmlybWF0aW9uTm90aWZpY2F0aW9uJywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogRW52aWEgdW0gbGVtYnJldGUgZGUgYWdlbmRhbWVudG9cbiAqXG4gKiBAcGFyYW0gdXNlcklkIElEIGRvIHVzdcOhcmlvIChyZXN0YXVyYW50ZSBvdSBpbmZsdWVuY2lhZG9yKVxuICogQHBhcmFtIHNjaGVkdWxlSWQgSUQgZG8gYWdlbmRhbWVudG9cbiAqIEBwYXJhbSBjYW1wYWlnbklkIElEIGRhIGNhbXBhbmhhXG4gKiBAcGFyYW0gY2FtcGFpZ25OYW1lIE5vbWUgZGEgY2FtcGFuaGFcbiAqIEBwYXJhbSBkYXRlIERhdGEgYWdlbmRhZGFcbiAqIEBwYXJhbSB0aW1lIEhvcsOhcmlvIGFnZW5kYWRvXG4gKiBAcGFyYW0gb3RoZXJQYXJ0eU5hbWUgTm9tZSBkYSBvdXRyYSBwYXJ0ZSAocmVzdGF1cmFudGUgb3UgaW5mbHVlbmNpYWRvcilcbiAqIEBwYXJhbSByZW1pbmRlclR5cGUgVGlwbyBkZSBsZW1icmV0ZSAoMjRoX2JlZm9yZSwgMWhfYmVmb3JlKVxuICogQHJldHVybnMgSUQgZGEgbm90aWZpY2HDp8OjbyBjcmlhZGFcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNlbmRTY2hlZHVsZVJlbWluZGVyTm90aWZpY2F0aW9uKFxuICB1c2VySWQ6IHN0cmluZyxcbiAgc2NoZWR1bGVJZDogc3RyaW5nLFxuICBjYW1wYWlnbklkOiBzdHJpbmcsXG4gIGNhbXBhaWduTmFtZTogc3RyaW5nLFxuICBkYXRlOiBzdHJpbmcsXG4gIHRpbWU6IHN0cmluZyxcbiAgb3RoZXJQYXJ0eU5hbWU6IHN0cmluZyxcbiAgcmVtaW5kZXJUeXBlOiBzdHJpbmdcbikge1xuICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50KCk7XG5cbiAgdHJ5IHtcbiAgICAvLyBCdXNjYXIgdGlwbyBkZSBub3RpZmljYcOnw6NvXG4gICAgY29uc3QgeyBkYXRhOiBub3RpZmljYXRpb25UeXBlLCBlcnJvcjogdHlwZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ25vdGlmaWNhdGlvbl90eXBlcycpXG4gICAgICAuc2VsZWN0KCdpZCwgdGVtcGxhdGVfc3ViamVjdCwgdGVtcGxhdGVfYm9keScpXG4gICAgICAuZXEoJ25hbWUnLCBOb3RpZmljYXRpb25UeXBlLlNDSEVEVUxFX1JFTUlOREVSKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKHR5cGVFcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUaXBvIGRlIG5vdGlmaWNhw6fDo28gbsOjbyBlbmNvbnRyYWRvOiAke3R5cGVFcnJvci5tZXNzYWdlfWApO1xuICAgIH1cblxuICAgIC8vIFRyYWR1emlyIHRpcG8gZGUgbGVtYnJldGVcbiAgICBjb25zdCByZW1pbmRlclRleHQgPSByZW1pbmRlclR5cGUgPT09ICcyNGhfYmVmb3JlJyA/ICcyNCBob3JhcycgOiAnMSBob3JhJztcblxuICAgIC8vIFN1YnN0aXR1aXIgdmFyacOhdmVpcyBubyB0ZW1wbGF0ZVxuICAgIGNvbnN0IHRpdGxlID0gbm90aWZpY2F0aW9uVHlwZS50ZW1wbGF0ZV9zdWJqZWN0XG4gICAgICAucmVwbGFjZSgne3tjYW1wYWlnbl9uYW1lfX0nLCBjYW1wYWlnbk5hbWUpXG4gICAgICAucmVwbGFjZSgne3tyZW1pbmRlcl90aW1lfX0nLCByZW1pbmRlclRleHQpO1xuXG4gICAgY29uc3QgbWVzc2FnZSA9IG5vdGlmaWNhdGlvblR5cGUudGVtcGxhdGVfYm9keVxuICAgICAgLnJlcGxhY2UoJ3t7Y2FtcGFpZ25fbmFtZX19JywgY2FtcGFpZ25OYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7b3RoZXJfcGFydHl9fScsIG90aGVyUGFydHlOYW1lKVxuICAgICAgLnJlcGxhY2UoJ3t7ZGF0ZX19JywgZGF0ZSlcbiAgICAgIC5yZXBsYWNlKCd7e3RpbWV9fScsIHRpbWUpXG4gICAgICAucmVwbGFjZSgne3tyZW1pbmRlcl90aW1lfX0nLCByZW1pbmRlclRleHQpO1xuXG4gICAgLy8gRW52aWFyIG5vdGlmaWNhw6fDo29cbiAgICByZXR1cm4gYXdhaXQgc2VuZE5vdGlmaWNhdGlvbih7XG4gICAgICB1c2VySWQ6IHVzZXJJZCxcbiAgICAgIHR5cGVJZDogbm90aWZpY2F0aW9uVHlwZS5pZCxcbiAgICAgIHRpdGxlLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGRhdGE6IHsgc2NoZWR1bGVJZCwgY2FtcGFpZ25JZCwgY2FtcGFpZ25OYW1lLCBkYXRlLCB0aW1lLCBvdGhlclBhcnR5TmFtZSwgcmVtaW5kZXJUeXBlIH0sXG4gICAgICBzZW5kV2hhdHNBcHA6IHRydWUsXG4gICAgICBzZW5kRW1haWw6IGZhbHNlLCAvLyBMZW1icmV0ZXMgZ2VyYWxtZW50ZSBzw6NvIG1haXMgZWZpY2F6ZXMgdmlhIFdoYXRzQXBwXG4gICAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbG9nRXJyb3IoJ25vdGlmaWNhdGlvbi5zZW5kU2NoZWR1bGVSZW1pbmRlck5vdGlmaWNhdGlvbicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50Iiwic2VuZFRleHRNZXNzYWdlIiwibG9nRXJyb3IiLCJOb3RpZmljYXRpb25UeXBlIiwic2VuZE5vdGlmaWNhdGlvbiIsIm5vdGlmaWNhdGlvbkRhdGEiLCJzdXBhYmFzZSIsImRhdGEiLCJub3RpZmljYXRpb24iLCJlcnJvciIsImZyb20iLCJpbnNlcnQiLCJ1c2VyX2lkIiwidXNlcklkIiwidHlwZV9pZCIsInR5cGVJZCIsInRpdGxlIiwibWVzc2FnZSIsInNlbnRfdmlhX2VtYWlsIiwic2VudF92aWFfd2hhdHNhcHAiLCJzZWxlY3QiLCJzaW5nbGUiLCJFcnJvciIsInByZWZlcmVuY2VzIiwiZXEiLCJtYXliZVNpbmdsZSIsImVuYWJsZV9ub3RpZmljYXRpb25zIiwiaWQiLCJwcm9maWxlIiwicHJvZmlsZUVycm9yIiwic2VuZFdoYXRzQXBwIiwicGhvbmUiLCJub3RpZmljYXRpb25UeXBlIiwidG9Mb3dlckNhc2UiLCJ3aGF0c2FwcFByZWZlcmVuY2VLZXkiLCJ3aGF0c2FwcF9lbmFibGVkIiwiZm9ybWF0dGVkUGhvbmUiLCJyZXBsYWNlIiwid2hhdHNhcHBSZXNwb25zZSIsImRpcmVjdGlvbiIsIm1lc3NhZ2VfdHlwZSIsIndoYXRzYXBwX21lc3NhZ2VfaWQiLCJtZXNzYWdlcyIsInN0YXR1cyIsInNlbnRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJtZXRhZGF0YSIsIm5vdGlmaWNhdGlvbl9pZCIsIm5vdGlmaWNhdGlvbl90eXBlIiwidXBkYXRlIiwid2hhdHNhcHBFcnJvciIsInNlbmRDYW1wYWlnbkludml0ZU5vdGlmaWNhdGlvbiIsImluZmx1ZW5jZXJJZCIsImNhbXBhaWduSWQiLCJjYW1wYWlnbk5hbWUiLCJyZXN0YXVyYW50TmFtZSIsInR5cGVFcnJvciIsInRlbXBsYXRlX3N1YmplY3QiLCJ0ZW1wbGF0ZV9ib2R5Iiwic2VuZEVtYWlsIiwic2VuZE5ld0luZmx1ZW5jZXJKb2luZWROb3RpZmljYXRpb24iLCJyZXN0YXVyYW50SWQiLCJpbmZsdWVuY2VyTmFtZSIsImluZmx1ZW5jZXJGb2xsb3dlcnMiLCJmb3JtYXR0ZWRGb2xsb3dlcnMiLCJJbnRsIiwiTnVtYmVyRm9ybWF0IiwiZm9ybWF0Iiwic2VuZFJhbmtpbmdVcGRhdGVOb3RpZmljYXRpb24iLCJwb3NpdGlvbiIsInByZXZpb3VzUG9zaXRpb24iLCJ0b1N0cmluZyIsInNlbmRQb3N0QXBwcm92YWxOb3RpZmljYXRpb24iLCJwb3N0SWQiLCJub3RlcyIsInN0YXR1c1RleHQiLCJzZW5kUGF5bWVudFN0YXR1c05vdGlmaWNhdGlvbiIsImFtb3VudCIsInN0YXR1c01hcCIsInBhaWQiLCJwZW5kaW5nIiwiZmFpbGVkIiwiZm9ybWF0dGVkQW1vdW50IiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwic2VuZENvbnRlbnRTdWJtaXR0ZWROb3RpZmljYXRpb24iLCJjb250ZW50SWQiLCJjb250ZW50VHlwZSIsImNvbnRlbnRUeXBlVGV4dCIsInNlbmRTY2hlZHVsZUNvbmZpcm1hdGlvbk5vdGlmaWNhdGlvbiIsInNjaGVkdWxlSWQiLCJkYXRlIiwidGltZSIsIm90aGVyUGFydHlOYW1lIiwic2VuZFNjaGVkdWxlUmVtaW5kZXJOb3RpZmljYXRpb24iLCJyZW1pbmRlclR5cGUiLCJyZW1pbmRlclRleHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/notification.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/config.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clientConfig: () => (/* binding */ clientConfig),\n/* harmony export */   instagramConfig: () => (/* binding */ instagramConfig),\n/* harmony export */   serverConfig: () => (/* binding */ serverConfig)\n/* harmony export */ });\n/**\n * Configuração do Supabase\n *\n * Este arquivo contém a configuração do Supabase para uso em diferentes contextos.\n */ // Configuração para o lado do cliente\nconst clientConfig = {\n    supabaseUrl: \"https://pbehloddlzwandfmpzbo.supabase.co\",\n    supabaseAnonKey: \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38\"\n};\n// Configuração para o lado do servidor\nconst serverConfig = {\n    supabaseUrl: \"https://pbehloddlzwandfmpzbo.supabase.co\",\n    supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY\n};\n// Configuração do Instagram\nconst instagramConfig = {\n    appId: process.env.NEXT_PUBLIC_INSTAGRAM_APP_ID || process.env.INSTAGRAM_APP_ID,\n    appSecret: process.env.INSTAGRAM_APP_SECRET,\n    redirectUri: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://streetbrand-openrouter2.vercel.app'}/api/auth/instagram/callback`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   createServerSupabaseAnonClient: () => (/* binding */ createServerSupabaseAnonClient),\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/supabase/config.ts\");\n/**\n * Cliente Supabase para o lado do servidor\n * \n * Este arquivo contém funções para criar um cliente Supabase para uso no lado do servidor.\n * Não deve ser importado diretamente no lado do cliente.\n */ \n\n// Cliente Supabase para uso no servidor com a chave de serviço\nfunction createServerSupabaseClient() {\n    const supabaseUrl = \"https://pbehloddlzwandfmpzbo.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        console.error('Variáveis de ambiente do Supabase não configuradas corretamente');\n        throw new Error('Configuração do Supabase incompleta');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n}\n// Cliente Supabase para uso no servidor com a chave anônima\nfunction createServerSupabaseAnonClient() {\n    const supabaseUrl = \"https://pbehloddlzwandfmpzbo.supabase.co\";\n    const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38\";\n    if (!supabaseUrl || !supabaseAnonKey) {\n        console.error('Variáveis de ambiente do Supabase não configuradas corretamente');\n        throw new Error('Configuração do Supabase incompleta');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Cria um cliente Supabase para o lado do servidor\nconst createServerClient = ()=>{\n    const { supabaseUrl, supabaseServiceKey } = _config__WEBPACK_IMPORTED_MODULE_0__.serverConfig;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error('Supabase URL and service key are required for server operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/constants.ts":
/*!************************************!*\
  !*** ./src/lib/utils/constants.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   INTEGRATION_CONFIG: () => (/* binding */ INTEGRATION_CONFIG),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES),\n/* harmony export */   WHATSAPP_CONFIG: () => (/* binding */ WHATSAPP_CONFIG)\n/* harmony export */ });\n/**\n * Constantes globais da aplicação\n */ // URL base da aplicação - dinâmica baseada no ambiente\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || (process.env.NEXT_PUBLIC_VERCEL_URL ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}` : 'https://connectcity-openrouter2.vercel.app');\n// Rotas da aplicação\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    REGISTER: '/register',\n    DASHBOARD: '/dashboard',\n    RESTAURANT: '/restaurante',\n    INFLUENCER: '/influenciador',\n    ADMIN: '/admin'\n};\n// Configurações de integração\nconst INTEGRATION_CONFIG = {\n    INSTAGRAM_CALLBACK_URL: `${BASE_URL}/api/auth/instagram/callback`,\n    INSTAGRAM_WEBHOOK_URL: `${BASE_URL}/api/instagram/webhook`,\n    WHATSAPP_WEBHOOK_URL: `${BASE_URL}/api/v1/whatsapp/webhook`\n};\n// Configurações do WhatsApp\nconst WHATSAPP_CONFIG = {\n    API_URL: `https://graph.facebook.com/${process.env.WHATSAPP_API_VERSION}`,\n    BUSINESS_ACCOUNT_ID: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID,\n    PHONE_NUMBER_ID: process.env.WHATSAPP_PHONE_NUMBER_ID\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/errors.ts":
/*!*********************************!*\
  !*** ./src/lib/utils/errors.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   formatSupabaseError: () => (/* binding */ formatSupabaseError),\n/* harmony export */   logError: () => (/* binding */ logError)\n/* harmony export */ });\n/**\n * Utilitários para tratamento de erros\n */ /**\n * Formata um erro do Supabase para exibição\n * @param error O erro do Supabase\n * @returns Uma mensagem de erro formatada\n */ function formatSupabaseError(error) {\n    if (!error) return 'Erro desconhecido';\n    // Mapear códigos de erro comuns para mensagens amigáveis\n    switch(error.code){\n        case '23505':\n            return 'Este registro já existe.';\n        case '23503':\n            return 'Referência inválida a outro registro.';\n        case '42P01':\n            return 'Tabela não encontrada.';\n        case '42703':\n            return 'Coluna não encontrada.';\n        case '22P02':\n            return 'Formato de dados inválido.';\n        default:\n            // Em produção, não expor detalhes técnicos\n            return  false ? 0 : `Erro: ${error.message} (Código: ${error.code})`;\n    }\n}\n/**\n * Formata um erro genérico para exibição\n * @param error O erro a ser formatado\n * @returns Uma mensagem de erro formatada\n */ function formatError(error) {\n    if (!error) return 'Erro desconhecido';\n    // Se for um erro do Supabase\n    if (error.code && error.message && error.details) {\n        return formatSupabaseError(error);\n    }\n    // Se for um erro com mensagem\n    if (error.message) {\n        return  false ? 0 : `Erro: ${error.message}`;\n    }\n    // Se for uma string\n    if (typeof error === 'string') {\n        return error;\n    }\n    // Fallback\n    return 'Ocorreu um erro ao processar sua solicitação.';\n}\n/**\n * Registra um erro no console de forma segura\n * @param context O contexto onde o erro ocorreu\n * @param error O erro a ser registrado\n */ function logError(context, error) {\n    console.error(`[${context}] Erro:`, error.message || error);\n    // Em desenvolvimento, registrar mais detalhes\n    if (true) {\n        if (error.stack) {\n            console.error(`[${context}] Stack:`, error.stack);\n        }\n        if (error.code) {\n            console.error(`[${context}] Código:`, error.code);\n        }\n        if (error.details) {\n            console.error(`[${context}] Detalhes:`, error.details);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/errors.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&page=%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Fcampaigns%2Fleaderboard%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();