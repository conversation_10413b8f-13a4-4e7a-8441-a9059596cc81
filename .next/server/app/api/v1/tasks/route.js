/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/v1/tasks/route";
exports.ids = ["app/api/v1/tasks/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Ftasks%2Froute&page=%2Fapi%2Fv1%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Ftasks%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Ftasks%2Froute&page=%2Fapi%2Fv1%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Ftasks%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_v1_tasks_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/v1/tasks/route.ts */ \"(rsc)/./src/app/api/v1/tasks/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/v1/tasks/route\",\n        pathname: \"/api/v1/tasks\",\n        filename: \"route\",\n        bundlePath: \"app/api/v1/tasks/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/v1/tasks/route.ts\",\n    nextConfigOutput,\n    userland: _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_v1_tasks_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Ftasks%2Froute&page=%2Fapi%2Fv1%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Ftasks%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/v1/tasks/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/v1/tasks/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_services_tasks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/tasks */ \"(rsc)/./src/lib/services/tasks.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * API para gerenciamento de tarefas\n */ \n\n // TaskPriority removido\n\n// GET /api/v1/tasks - Obter tarefas do usuário atual\nasync function GET(req) {\n    try {\n        // Verificar autenticação\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Não autorizado'\n            }, {\n                status: 401\n            });\n        }\n        // Obter parâmetros de consulta\n        const { searchParams } = new URL(req.url);\n        const campaignId = searchParams.get('campaignId');\n        const status = searchParams.get('status');\n        const taskType = searchParams.get('taskType');\n        const dueDateStart = searchParams.get('dueDateStart');\n        const dueDateEnd = searchParams.get('dueDateEnd');\n        // Construir filtros\n        const filters = {};\n        if (campaignId) {\n            filters.campaign_id = campaignId;\n        }\n        if (status) {\n            filters.status = status.split(',');\n        }\n        if (taskType) {\n            filters.task_type = taskType.split(',');\n        }\n        if (dueDateStart) {\n            filters.due_date_start = dueDateStart;\n        }\n        if (dueDateEnd) {\n            filters.due_date_end = dueDateEnd;\n        }\n        // Obter tarefas\n        const tasks = await _lib_services_tasks__WEBPACK_IMPORTED_MODULE_2__.TasksService.getUserTasks(session.user.id, filters);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            tasks\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)('api.tasks.get', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Erro ao obter tarefas'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/v1/tasks - Criar uma nova tarefa\nasync function POST(req) {\n    try {\n        // Verificar autenticação\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Não autorizado'\n            }, {\n                status: 401\n            });\n        }\n        // Obter dados da requisição\n        const taskData = await req.json();\n        // Validar dados\n        if (!taskData.title || !taskData.task_type) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Título e tipo de tarefa são obrigatórios'\n            }, {\n                status: 400\n            });\n        }\n        // Verificar se o usuário pode criar a tarefa\n        if (taskData.user_id && taskData.user_id !== session.user.id) {\n            // Verificar se o usuário é admin\n            const { data: profile } = await supabase.from('profiles').select('role').eq('id', session.user.id).single();\n            if (profile?.role !== 'admin') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Você não tem permissão para criar tarefas para outros usuários'\n                }, {\n                    status: 403\n                });\n            }\n        } else {\n            // Se não for especificado, usar o ID do usuário atual\n            taskData.user_id = session.user.id;\n        }\n        // Criar tarefa\n        const task = await _lib_services_tasks__WEBPACK_IMPORTED_MODULE_2__.TasksService.createTask(taskData);\n        if (!task) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Erro ao criar tarefa'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            task\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)('api.tasks.post', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || 'Erro ao criar tarefa'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/v1/tasks/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/whatsapp.ts":
/*!*********************************!*\
  !*** ./src/lib/api/whatsapp.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMessageStatus: () => (/* binding */ getMessageStatus),\n/* harmony export */   registerWebhook: () => (/* binding */ registerWebhook),\n/* harmony export */   sendTemplateMessage: () => (/* binding */ sendTemplateMessage),\n/* harmony export */   sendTextMessage: () => (/* binding */ sendTextMessage)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/constants */ \"(rsc)/./src/lib/utils/constants.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * Cliente para a API do WhatsApp Business\n *\n * Este módulo fornece funções para interagir com a API do WhatsApp Business\n * para enviar mensagens e gerenciar webhooks.\n */ \n\n/**\n * Envia uma mensagem de texto para um número de WhatsApp\n * \n * @param to Número de telefone do destinatário (formato internacional sem +)\n * @param message Texto da mensagem\n * @returns Resposta da API com o ID da mensagem\n */ async function sendTextMessage(to, message) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.PHONE_NUMBER_ID}/messages`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messaging_product: 'whatsapp',\n                recipient_type: 'individual',\n                to,\n                type: 'text',\n                text: {\n                    preview_url: false,\n                    body: message\n                }\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao enviar mensagem: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.sendTextMessage', error);\n        throw error;\n    }\n}\n/**\n * Envia uma mensagem de template para um número de WhatsApp\n * \n * @param to Número de telefone do destinatário (formato internacional sem +)\n * @param templateName Nome do template aprovado\n * @param language Código do idioma (ex: pt_BR)\n * @param components Componentes do template (header, body, buttons)\n * @returns Resposta da API com o ID da mensagem\n */ async function sendTemplateMessage(to, templateName, language = 'pt_BR', components = []) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.PHONE_NUMBER_ID}/messages`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                messaging_product: 'whatsapp',\n                recipient_type: 'individual',\n                to,\n                type: 'template',\n                template: {\n                    name: templateName,\n                    language: {\n                        code: language\n                    },\n                    components\n                }\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao enviar template: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.sendTemplateMessage', error);\n        throw error;\n    }\n}\n/**\n * Verifica o status de uma mensagem\n * \n * @param messageId ID da mensagem\n * @returns Status da mensagem\n */ async function getMessageStatus(messageId) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${messageId}?fields=status`, {\n            method: 'GET',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao obter status: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.getMessageStatus', error);\n        throw error;\n    }\n}\n/**\n * Registra o webhook para receber atualizações do WhatsApp\n * \n * @param webhookUrl URL do webhook\n * @param verifyToken Token de verificação\n * @returns Resposta da API\n */ async function registerWebhook(webhookUrl, verifyToken) {\n    try {\n        const response = await fetch(`${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.API_URL}/${_lib_utils_constants__WEBPACK_IMPORTED_MODULE_0__.WHATSAPP_CONFIG.BUSINESS_ACCOUNT_ID}/subscribed_apps`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                callback_url: webhookUrl,\n                verify_token: verifyToken,\n                fields: [\n                    'messages',\n                    'message_status'\n                ]\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`Erro ao registrar webhook: ${JSON.stringify(errorData)}`);\n        }\n        return await response.json();\n    } catch (error) {\n        (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_1__.logError)('whatsapp.registerWebhook', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/whatsapp.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/tasks.ts":
/*!***********************************!*\
  !*** ./src/lib/services/tasks.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TaskPriority: () => (/* binding */ TaskPriority),\n/* harmony export */   TaskStatus: () => (/* binding */ TaskStatus),\n/* harmony export */   TaskType: () => (/* binding */ TaskType),\n/* harmony export */   TasksService: () => (/* binding */ TasksService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _whatsapp_notifications__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./whatsapp-notifications */ \"(rsc)/./src/lib/services/whatsapp-notifications.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * Tasks Service\n * \n * Serviço para gerenciamento de tarefas (to-do list)\n */ \n\n\nvar TaskType = /*#__PURE__*/ function(TaskType) {\n    TaskType[\"CONTENT_CREATION\"] = \"content_creation\";\n    TaskType[\"CONTENT_APPROVAL\"] = \"content_approval\";\n    TaskType[\"CONTENT_PUBLICATION\"] = \"content_publication\";\n    TaskType[\"SCHEDULE_VISIT\"] = \"schedule_visit\";\n    TaskType[\"ADMINISTRATIVE\"] = \"administrative\";\n    TaskType[\"PAYMENT\"] = \"payment\";\n    TaskType[\"OTHER\"] = \"other\";\n    return TaskType;\n}({});\nvar TaskStatus = /*#__PURE__*/ function(TaskStatus) {\n    TaskStatus[\"PENDING\"] = \"pending\";\n    TaskStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    TaskStatus[\"COMPLETED\"] = \"completed\";\n    TaskStatus[\"CANCELLED\"] = \"cancelled\";\n    TaskStatus[\"OVERDUE\"] = \"overdue\";\n    return TaskStatus;\n}({});\nvar TaskPriority = /*#__PURE__*/ function(TaskPriority) {\n    TaskPriority[\"LOW\"] = \"low\";\n    TaskPriority[\"MEDIUM\"] = \"medium\";\n    TaskPriority[\"HIGH\"] = \"high\";\n    TaskPriority[\"URGENT\"] = \"urgent\";\n    return TaskPriority;\n}({});\nclass TasksService {\n    /**\n   * Criar uma nova tarefa\n   */ static async createTask(task) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Validar dados\n            if (!task.user_id || !task.title || !task.task_type) {\n                throw new Error('Dados incompletos para criar tarefa');\n            }\n            // Criar tarefa\n            const { data, error } = await supabase.from('tasks').insert({\n                user_id: task.user_id,\n                campaign_id: task.campaign_id,\n                title: task.title,\n                description: task.description,\n                task_type: task.task_type,\n                status: task.status || \"pending\",\n                priority: task.priority || \"medium\",\n                due_date: task.due_date,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select('*').single();\n            if (error) {\n                throw error;\n            }\n            // Adicionar categorias, se fornecidas\n            if (task.categories && task.categories.length > 0) {\n                for (const category of task.categories){\n                    await supabase.from('task_category_relations').insert({\n                        task_id: data.id,\n                        category_id: category.id\n                    });\n                }\n            }\n            // Adicionar lembretes, se fornecidos\n            if (task.reminders && task.reminders.length > 0) {\n                for (const reminder of task.reminders){\n                    await supabase.from('task_reminders').insert({\n                        task_id: data.id,\n                        reminder_time: reminder.reminder_time\n                    });\n                }\n            }\n            // Adicionar atribuições, se fornecidas\n            if (task.assignments && task.assignments.length > 0) {\n                for (const assignment of task.assignments){\n                    await supabase.from('task_assignments').insert({\n                        task_id: data.id,\n                        user_id: assignment.user_id,\n                        assigned_by: assignment.assigned_by,\n                        status: assignment.status || \"pending\"\n                    });\n                }\n            }\n            // Registrar histórico\n            await supabase.from('task_history').insert({\n                task_id: data.id,\n                user_id: task.user_id,\n                action: 'create',\n                details: {\n                    task\n                }\n            });\n            // Enviar notificação, se aplicável\n            if (task.user_id && task.campaign_id) {\n                // Obter detalhes da campanha\n                const { data: campaign } = await supabase.from('campaigns').select('name').eq('id', task.campaign_id).single();\n                if (campaign) {\n                    // Enviar notificação por WhatsApp\n                    await _whatsapp_notifications__WEBPACK_IMPORTED_MODULE_1__.WhatsAppNotificationsService.sendTaskReminderNotification(task.user_id, task.task_type, campaign.name, task.due_date);\n                }\n            }\n            return data;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('tasks.createTask', error);\n            return null;\n        }\n    }\n    /**\n   * Atualizar uma tarefa existente\n   */ static async updateTask(taskId, updates) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Obter tarefa atual\n            const { data: currentTask, error: getError } = await supabase.from('tasks').select('*').eq('id', taskId).single();\n            if (getError || !currentTask) {\n                throw new Error(`Tarefa não encontrada: ${getError?.message}`);\n            }\n            // Atualizar tarefa\n            const { data, error } = await supabase.from('tasks').update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq('id', taskId).select('*').single();\n            if (error) {\n                throw error;\n            }\n            // Registrar histórico\n            await supabase.from('task_history').insert({\n                task_id: taskId,\n                user_id: updates.user_id || currentTask.user_id,\n                action: 'update',\n                details: {\n                    previous: currentTask,\n                    updates\n                }\n            });\n            return data;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('tasks.updateTask', error);\n            return null;\n        }\n    }\n    /**\n   * Marcar uma tarefa como concluída\n   */ static async completeTask(taskId, userId) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Atualizar tarefa\n            const { data, error } = await supabase.from('tasks').update({\n                status: \"completed\",\n                completed_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).eq('id', taskId).select('*').single();\n            if (error) {\n                throw error;\n            }\n            // Registrar histórico\n            await supabase.from('task_history').insert({\n                task_id: taskId,\n                user_id: userId,\n                action: 'complete',\n                details: {\n                    completed_at: data.completed_at\n                }\n            });\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('tasks.completeTask', error);\n            return false;\n        }\n    }\n    /**\n   * Adicionar um comentário a uma tarefa\n   */ static async addComment(taskId, userId, comment) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Adicionar comentário\n            const { data, error } = await supabase.from('task_comments').insert({\n                task_id: taskId,\n                user_id: userId,\n                comment,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select('*').single();\n            if (error) {\n                throw error;\n            }\n            // Registrar histórico\n            await supabase.from('task_history').insert({\n                task_id: taskId,\n                user_id: userId,\n                action: 'comment',\n                details: {\n                    comment\n                }\n            });\n            return data;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('tasks.addComment', error);\n            return null;\n        }\n    }\n    /**\n   * Obter tarefas de um usuário\n   */ static async getUserTasks(userId, filters) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            let query = supabase.from('tasks').select(`\n          *,\n          categories:task_category_relations(\n            task_categories(*)\n          ),\n          reminders:task_reminders(*),\n          comments:task_comments(\n            *,\n            user:profiles(full_name, profile_image_url)\n          ),\n          assignments:task_assignments(\n            *,\n            user:profiles(full_name, profile_image_url)\n          )\n        `).eq('user_id', userId);\n            // Aplicar filtros\n            if (filters) {\n                if (filters.status) {\n                    if (Array.isArray(filters.status)) {\n                        query = query.in('status', filters.status);\n                    } else {\n                        query = query.eq('status', filters.status);\n                    }\n                }\n                if (filters.campaign_id) {\n                    query = query.eq('campaign_id', filters.campaign_id);\n                }\n                if (filters.due_date_start) {\n                    query = query.gte('due_date', filters.due_date_start);\n                }\n                if (filters.due_date_end) {\n                    query = query.lte('due_date', filters.due_date_end);\n                }\n                if (filters.task_type) {\n                    if (Array.isArray(filters.task_type)) {\n                        query = query.in('task_type', filters.task_type);\n                    } else {\n                        query = query.eq('task_type', filters.task_type);\n                    }\n                }\n            }\n            // Ordenar por data de vencimento e prioridade\n            query = query.order('due_date', {\n                ascending: true,\n                nullsLast: true\n            }).order('priority', {\n                ascending: false\n            });\n            const { data, error } = await query;\n            if (error) {\n                throw error;\n            }\n            return data || [];\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('tasks.getUserTasks', error);\n            return [];\n        }\n    }\n    /**\n   * Obter tarefas de uma campanha\n   */ static async getCampaignTasks(campaignId) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            const { data, error } = await supabase.from('tasks').select(`\n          *,\n          categories:task_category_relations(\n            task_categories(*)\n          ),\n          reminders:task_reminders(*),\n          comments:task_comments(\n            *,\n            user:profiles(full_name, profile_image_url)\n          ),\n          assignments:task_assignments(\n            *,\n            user:profiles(full_name, profile_image_url)\n          )\n        `).eq('campaign_id', campaignId).order('due_date', {\n                ascending: true,\n                nullsLast: true\n            }).order('priority', {\n                ascending: false\n            });\n            if (error) {\n                throw error;\n            }\n            return data || [];\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_2__.logError)('tasks.getCampaignTasks', error);\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/tasks.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/whatsapp-notifications.ts":
/*!****************************************************!*\
  !*** ./src/lib/services/whatsapp-notifications.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WhatsAppNotificationsService: () => (/* binding */ WhatsAppNotificationsService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _whatsapp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./whatsapp */ \"(rsc)/./src/lib/services/whatsapp.ts\");\n/* harmony import */ var _lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/whatsapp */ \"(rsc)/./src/lib/api/whatsapp.ts\");\n/* harmony import */ var _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/whatsapp/config */ \"(rsc)/./src/lib/whatsapp/config.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * WhatsApp Notifications Service\n *\n * Serviço especializado para envio de notificações via WhatsApp\n * relacionadas ao sistema de gamificação e tarefas.\n */ \n // Reintroduzido para logWhatsAppMessage\n // Importar sendTemplateMessage diretamente\n\n\nclass WhatsAppNotificationsService {\n    /**\n   * Envia notificação sobre pontos ganhos\n   */ static async sendPointsEarnedNotification(userId, campaignName, points, reason) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Verificar se o usuário tem WhatsApp verificado\n            const { data: profile, error: profileError } = await supabase.from('profiles').select('phone, phone_verified, full_name').eq('id', userId).single();\n            if (profileError || !profile || !profile.phone_verified) {\n                return false;\n            }\n            // Enviar notificação\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(profile.phone, _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.pointsEarned, 'pt_BR', [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: profile.full_name\n                        },\n                        {\n                            type: 'text',\n                            text: points.toString()\n                        },\n                        {\n                            type: 'text',\n                            text: campaignName\n                        },\n                        {\n                            type: 'text',\n                            text: reason\n                        }\n                    ]\n                }\n            ]);\n            if (!messageId) {\n                return false;\n            }\n            // Registrar mensagem\n            await _whatsapp__WEBPACK_IMPORTED_MODULE_1__.WhatsAppService.logWhatsAppMessage(profile.phone, userId, 'outbound', `template_${_lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.pointsEarned}`, messageId, 'sent', {\n                template_name_used: _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.pointsEarned,\n                // provider_template_id might be unknown here if sendTemplateMessage is called directly\n                params: {\n                    fullName: profile.full_name,\n                    points,\n                    campaignName,\n                    reason\n                }\n            });\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_4__.logError)('whatsappNotifications.sendPointsEarnedNotification', error);\n            return false;\n        }\n    }\n    /**\n   * Envia notificação sobre conquista desbloqueada\n   */ static async sendAchievementUnlockedNotification(userId, achievementName, achievementDescription, points) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Verificar se o usuário tem WhatsApp verificado\n            const { data: profile, error: profileError } = await supabase.from('profiles').select('phone, phone_verified, full_name').eq('id', userId).single();\n            if (profileError || !profile || !profile.phone_verified) {\n                return false;\n            }\n            // Enviar notificação\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(profile.phone, _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.achievementUnlocked, 'pt_BR', [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: profile.full_name\n                        },\n                        {\n                            type: 'text',\n                            text: achievementName\n                        },\n                        {\n                            type: 'text',\n                            text: achievementDescription\n                        },\n                        {\n                            type: 'text',\n                            text: points.toString()\n                        }\n                    ]\n                }\n            ]);\n            if (!messageId) {\n                return false;\n            }\n            // Registrar mensagem\n            await _whatsapp__WEBPACK_IMPORTED_MODULE_1__.WhatsAppService.logWhatsAppMessage(profile.phone, userId, 'outbound', `template_${_lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.achievementUnlocked}`, messageId, 'sent', {\n                template_name_used: _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.achievementUnlocked,\n                params: {\n                    fullName: profile.full_name,\n                    achievementName,\n                    achievementDescription,\n                    points\n                }\n            });\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_4__.logError)('whatsappNotifications.sendAchievementUnlockedNotification', error);\n            return false;\n        }\n    }\n    /**\n   * Envia notificação sobre atualização de ranking\n   */ static async sendRankingUpdateNotification(userId, campaignName, newRank, oldRank, totalInfluencers) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Verificar se o usuário tem WhatsApp verificado\n            const { data: profile, error: profileError } = await supabase.from('profiles').select('phone, phone_verified, full_name').eq('id', userId).single();\n            if (profileError || !profile || !profile.phone_verified) {\n                return false;\n            }\n            // Determinar se subiu ou desceu no ranking\n            const rankChange = oldRank - newRank;\n            const rankDirection = rankChange > 0 ? 'subiu' : 'desceu';\n            const rankChangeAbs = Math.abs(rankChange);\n            // Enviar notificação\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(profile.phone, _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.rankingUpdate, 'pt_BR', [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: profile.full_name\n                        },\n                        {\n                            type: 'text',\n                            text: campaignName\n                        },\n                        {\n                            type: 'text',\n                            text: newRank.toString()\n                        },\n                        {\n                            type: 'text',\n                            text: totalInfluencers.toString()\n                        },\n                        {\n                            type: 'text',\n                            text: rankDirection\n                        },\n                        {\n                            type: 'text',\n                            text: rankChangeAbs.toString()\n                        }\n                    ]\n                }\n            ]);\n            if (!messageId) {\n                return false;\n            }\n            // Registrar mensagem\n            await _whatsapp__WEBPACK_IMPORTED_MODULE_1__.WhatsAppService.logWhatsAppMessage(profile.phone, userId, 'outbound', `template_${_lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.rankingUpdate}`, messageId, 'sent', {\n                template_name_used: _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.rankingUpdate,\n                params: {\n                    fullName: profile.full_name,\n                    campaignName,\n                    newRank,\n                    totalInfluencers,\n                    rankDirection,\n                    rankChangeAbs\n                }\n            });\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_4__.logError)('whatsappNotifications.sendRankingUpdateNotification', error);\n            return false;\n        }\n    }\n    /**\n   * Envia notificação sobre tarefa pendente\n   */ static async sendTaskReminderNotification(userId, taskType, campaignName, dueDate) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Verificar se o usuário tem WhatsApp verificado\n            const { data: profile, error: profileError } = await supabase.from('profiles').select('phone, phone_verified, full_name').eq('id', userId).single();\n            if (profileError || !profile || !profile.phone_verified) {\n                return false;\n            }\n            // Traduzir tipo de tarefa\n            let taskDescription = '';\n            switch(taskType){\n                case 'record':\n                    taskDescription = 'gravar o vídeo';\n                    break;\n                case 'submit':\n                    taskDescription = 'enviar o vídeo para aprovação';\n                    break;\n                case 'approved':\n                    taskDescription = 'publicar o vídeo aprovado';\n                    break;\n                case 'rejected':\n                    taskDescription = 'gravar um novo vídeo (o anterior foi rejeitado)';\n                    break;\n                case 'content_creation':\n                    taskDescription = 'criar conteúdo para a campanha';\n                    break;\n                case 'content_approval':\n                    taskDescription = 'aprovar conteúdo pendente';\n                    break;\n                case 'content_publication':\n                    taskDescription = 'publicar conteúdo aprovado';\n                    break;\n                case 'schedule_visit':\n                    taskDescription = 'comparecer à visita agendada';\n                    break;\n                case 'schedule_confirmed':\n                    taskDescription = 'visita confirmada';\n                    break;\n                case 'schedule_cancelled':\n                    taskDescription = 'visita cancelada';\n                    break;\n                case 'schedule_update':\n                    taskDescription = 'atualização de agendamento';\n                    break;\n                case 'visit_completed':\n                    taskDescription = 'visita concluída';\n                    break;\n                default:\n                    taskDescription = 'completar a tarefa pendente';\n            }\n            // Enviar notificação\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(profile.phone, _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.taskReminder, 'pt_BR', [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: profile.full_name\n                        },\n                        {\n                            type: 'text',\n                            text: taskDescription\n                        },\n                        {\n                            type: 'text',\n                            text: campaignName\n                        },\n                        {\n                            type: 'text',\n                            text: dueDate || 'o mais breve possível'\n                        }\n                    ]\n                }\n            ]);\n            if (!messageId) {\n                return false;\n            }\n            // Registrar mensagem\n            await _whatsapp__WEBPACK_IMPORTED_MODULE_1__.WhatsAppService.logWhatsAppMessage(profile.phone, userId, 'outbound', `template_${_lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.taskReminder}`, messageId, 'sent', {\n                template_name_used: _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.taskReminder,\n                params: {\n                    fullName: profile.full_name,\n                    taskDescription,\n                    campaignName,\n                    dueDate: dueDate || 'o mais breve possível'\n                }\n            });\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_4__.logError)('whatsappNotifications.sendTaskReminderNotification', error);\n            return false;\n        }\n    }\n    /**\n   * Envia notificação sobre agendamento\n   */ static async sendScheduleNotification(userId, scheduleType, campaignName, scheduledDate, additionalInfo) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            // Verificar se o usuário tem WhatsApp verificado\n            const { data: profile, error: profileError } = await supabase.from('profiles').select('phone, phone_verified, full_name').eq('id', userId).single();\n            if (profileError || !profile || !profile.phone_verified) {\n                return false;\n            }\n            // Formatar data\n            let formattedDate = scheduledDate;\n            try {\n                const date = new Date(scheduledDate);\n                formattedDate = date.toLocaleDateString('pt-BR', {\n                    day: '2-digit',\n                    month: '2-digit',\n                    year: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit'\n                });\n            } catch (_e) {\n            // Manter o formato original se houver erro\n            }\n            // Enviar notificação\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(profile.phone, _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.scheduleReminder, 'pt_BR', [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: profile.full_name\n                        },\n                        {\n                            type: 'text',\n                            text: campaignName\n                        },\n                        {\n                            type: 'text',\n                            text: formattedDate\n                        },\n                        {\n                            type: 'text',\n                            text: additionalInfo || 'Sem informações adicionais.'\n                        }\n                    ]\n                }\n            ]);\n            if (!messageId) {\n                return false;\n            }\n            // Registrar mensagem\n            await _whatsapp__WEBPACK_IMPORTED_MODULE_1__.WhatsAppService.logWhatsAppMessage(profile.phone, userId, 'outbound', `template_${_lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.scheduleReminder}`, messageId, 'sent', {\n                template_name_used: _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_3__.WHATSAPP_API_CONFIG.templates.scheduleReminder,\n                params: {\n                    fullName: profile.full_name,\n                    campaignName,\n                    scheduledDate: formattedDate,\n                    additionalInfo: additionalInfo || 'Sem informações adicionais.'\n                }\n            });\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_4__.logError)('whatsappNotifications.sendScheduleNotification', error);\n            return false;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/whatsapp-notifications.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/whatsapp.ts":
/*!**************************************!*\
  !*** ./src/lib/services/whatsapp.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WhatsAppService: () => (/* binding */ WhatsAppService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/whatsapp/config */ \"(rsc)/./src/lib/whatsapp/config.ts\");\n/* harmony import */ var _lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/whatsapp */ \"(rsc)/./src/lib/api/whatsapp.ts\");\n/* harmony import */ var _lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/errors */ \"(rsc)/./src/lib/utils/errors.ts\");\n/**\n * WhatsApp Service\n * \n * Comprehensive service for handling all WhatsApp-related functionality:\n * - Phone verification\n * - Template messaging\n * - Notification delivery\n * - Message tracking\n */ \n\n\n\nclass WhatsAppService {\n    /**\n   * Helper to get user_id from phone number for notification preference checks.\n   */ static async getUserIdFromPhone(phone, supabaseInstance) {\n        const supabase = supabaseInstance || (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const formattedPhone = phone.replace(/\\D/g, '');\n        const { data: userProfile, error } = await supabase.from('profiles').select('id').eq('phone', formattedPhone).single();\n        if (error && error.code !== 'PGRST116') {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)('whatsapp.getUserIdFromPhone: Error fetching profile by phone.', error);\n        }\n        return userProfile?.id || null;\n    }\n    /**\n   * Send verification code to a phone number\n   */ static async sendVerificationCode(phone, userId) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'verification';\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        try {\n            const { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name') // Ensure 'name' is selected\n            .eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendVerificationCode: Active template '${internalTemplateName}' not found.`, dbTemplateError);\n                return null;\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const actualTemplateNameUsed = dbTemplate.name;\n            const formattedPhone = phone.replace(/\\D/g, '');\n            const code = Math.floor(100000 + Math.random() * 900000).toString();\n            const expiresAt = new Date();\n            expiresAt.setMinutes(expiresAt.getMinutes() + 15);\n            await supabase.from('whatsapp_verification').insert({\n                user_id: userId,\n                phone: formattedPhone,\n                verification_code: code,\n                created_at: new Date().toISOString(),\n                expires_at: expiresAt.toISOString()\n            });\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(formattedPhone, providerTemplateId, languageCode, [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: code\n                        }\n                    ]\n                }\n            ]);\n            if (messageId) {\n                await this.logWhatsAppMessage(formattedPhone, userId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId,\n                    params: {\n                        code\n                    }\n                });\n            }\n            return code;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendVerificationCode (template: ${internalTemplateName})`, error);\n            throw new Error('Failed to send verification code');\n        }\n    }\n    static async verifyCode(phone, code, userId) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        try {\n            const formattedPhone = phone.replace(/\\D/g, '');\n            const { data, error } = await supabase.from('whatsapp_verification').select('*').eq('phone', formattedPhone).eq('verification_code', code).eq('user_id', userId).eq('verified', false).gt('expires_at', new Date().toISOString()).order('created_at', {\n                ascending: false\n            }).limit(1).single();\n            if (error || !data) {\n                return false;\n            }\n            await supabase.from('whatsapp_verification').update({\n                verified: true,\n                verified_at: new Date().toISOString()\n            }).eq('id', data.id);\n            await supabase.from('profiles').update({\n                phone: formattedPhone,\n                phone_verified: true\n            }).eq('id', userId);\n            await this.sendWelcomeMessage(formattedPhone, userId);\n            return true;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)('whatsapp.verifyCode', error);\n            return false;\n        }\n    }\n    static async sendWelcomeMessage(phone, userId) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'welcome';\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        try {\n            // Check user notification preferences\n            // userId is passed directly to sendWelcomeMessage\n            if (userId) {\n                const { data: userPrefs, error: prefError } = await supabase.from('user_notification_preferences').select('whatsapp_enabled').eq('user_id', userId).single();\n                if (prefError && prefError.code !== 'PGRST116') {\n                    (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendWelcomeMessage: Error fetching user preferences for user ${userId}`, prefError);\n                // Decide if to proceed or not; for welcome, maybe proceed if no record.\n                }\n                if (userPrefs && userPrefs.whatsapp_enabled === false) {\n                    console.log(`WhatsApp welcome disabled for user ${userId} via preferences.`);\n                    return;\n                }\n            } else {\n                // If userId is not available (e.g. phone not linked yet), cannot check preferences.\n                // Decide if welcome message should be sent in this case. For now, proceeding.\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendWelcomeMessage: userId not available for preference check for phone ${phone}. Proceeding with send.`, null);\n            }\n            const { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name') // Ensure 'name' is selected\n            .eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendWelcomeMessage: Active template '${internalTemplateName}' not found.`, dbTemplateError);\n                return;\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const actualTemplateNameUsed = dbTemplate.name;\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(phone, providerTemplateId, languageCode, []);\n            if (messageId) {\n                await this.logWhatsAppMessage(phone, userId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId\n                });\n            }\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendWelcomeMessage (template: ${internalTemplateName})`, error);\n        }\n    }\n    static async sendNewInfluencerNotification(to, restaurantName, influencerName, campaignName) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'newInfluencer';\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        try {\n            const recipientUserId = await this.getUserIdFromPhone(to, supabase);\n            if (recipientUserId) {\n                const { data: userPrefs, error: prefError } = await supabase.from('user_notification_preferences').select('whatsapp_campaign_status_change') // Specific preference for this type of notification\n                .eq('user_id', recipientUserId).single();\n                if (prefError && prefError.code !== 'PGRST116') {\n                    (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendNewInfluencerNotification: Error fetching preferences for user ${recipientUserId}`, prefError);\n                // Potentially proceed if prefs not found, or return false depending on desired behavior\n                }\n                if (userPrefs && userPrefs.whatsapp_campaign_status_change === false) {\n                    console.log(`WhatsApp notification '${internalTemplateName}' disabled for user ${recipientUserId} by preference.`);\n                    return false;\n                }\n            } else {\n                // If no user profile is linked to the phone number, we might still send or log differently\n                console.log(`whatsapp.sendNewInfluencerNotification: No user profile found for phone ${to} to check preferences. Proceeding with send.`);\n            }\n            const { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name').eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendNewInfluencerNotification: Active template '${internalTemplateName}' not found.`, dbTemplateError);\n                return false;\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const actualTemplateNameUsed = dbTemplate.name;\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(to, providerTemplateId, languageCode, [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: influencerName\n                        },\n                        {\n                            type: 'text',\n                            text: campaignName\n                        },\n                        {\n                            type: 'text',\n                            text: restaurantName\n                        }\n                    ]\n                }\n            ]);\n            if (messageId) {\n                await this.logWhatsAppMessage(to, recipientUserId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId,\n                    params: {\n                        influencerName,\n                        campaignName,\n                        restaurantName\n                    }\n                });\n            }\n            return !!messageId;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendNewInfluencerNotification (template: ${internalTemplateName})`, error);\n            return false;\n        }\n    }\n    static async sendNewContentSubmittedNotification(to, restaurantName, influencerName, campaignName, reviewLink) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'newContent';\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        try {\n            const recipientUserId = await this.getUserIdFromPhone(to, supabase);\n            if (recipientUserId) {\n                const { data: userPrefs, error: prefError } = await supabase.from('user_notification_preferences').select('whatsapp_post_approval').eq('user_id', recipientUserId).single();\n                if (prefError && prefError.code !== 'PGRST116') {\n                    (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendNewContentSubmittedNotification: Error fetching preferences for user ${recipientUserId}`, prefError);\n                }\n                if (userPrefs && userPrefs.whatsapp_post_approval === false) {\n                    console.log(`WhatsApp notification '${internalTemplateName}' disabled for user ${recipientUserId} by preference.`);\n                    return false;\n                }\n            } else {\n                console.log(`whatsapp.sendNewContentSubmittedNotification: No user profile found for phone ${to} to check preferences. Proceeding with send.`);\n            }\n            const { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name').eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendNewContentSubmittedNotification: Active template '${internalTemplateName}' not found.`, dbTemplateError);\n                return false;\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const actualTemplateNameUsed = dbTemplate.name;\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(to, providerTemplateId, languageCode, [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: influencerName\n                        },\n                        {\n                            type: 'text',\n                            text: campaignName\n                        }\n                    ]\n                },\n                {\n                    type: 'button',\n                    sub_type: 'url',\n                    index: '0',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: reviewLink.replace(/^https?:\\/\\//, '')\n                        }\n                    ]\n                }\n            ]);\n            if (messageId) {\n                await this.logWhatsAppMessage(to, recipientUserId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId,\n                    params: {\n                        restaurantName,\n                        influencerName,\n                        campaignName,\n                        reviewLink\n                    }\n                });\n            }\n            return !!messageId;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendNewContentSubmittedNotification (template: ${internalTemplateName})`, error);\n            return false;\n        }\n    }\n    static async sendContentApprovedNotification(to, influencerName, campaignName, restaurantName, nextStepLink) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'contentApproved';\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        try {\n            const recipientUserId = await this.getUserIdFromPhone(to, supabase);\n            if (recipientUserId) {\n                const { data: userPrefs } = await supabase.from('user_notification_preferences').select('whatsapp_content_reviewed').eq('user_id', recipientUserId).single();\n                if (userPrefs && !userPrefs.whatsapp_content_reviewed) {\n                    return false;\n                }\n            }\n            const { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name').eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentApprovedNotification: Active template '${internalTemplateName}' not found.`, dbTemplateError);\n                return false;\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const actualTemplateNameUsed = dbTemplate.name;\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(to, providerTemplateId, languageCode, [\n                {\n                    type: 'body',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: campaignName\n                        },\n                        {\n                            type: 'text',\n                            text: restaurantName\n                        }\n                    ]\n                },\n                {\n                    type: 'button',\n                    sub_type: 'url',\n                    index: '0',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: nextStepLink.replace(/^https?:\\/\\//, '')\n                        }\n                    ]\n                }\n            ]);\n            if (messageId) {\n                await this.logWhatsAppMessage(to, recipientUserId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId,\n                    params: {\n                        influencerName,\n                        campaignName,\n                        restaurantName,\n                        nextStepLink\n                    }\n                });\n            }\n            return !!messageId;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentApprovedNotification (template: ${internalTemplateName})`, error);\n            return false;\n        }\n    }\n    static async sendContentRevisionRequestedNotification(to, influencerName, campaignName, restaurantName, reviewLink, feedbackSummary) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'contentFeedback';\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        try {\n            const recipientUserId = await this.getUserIdFromPhone(to, supabase);\n            if (recipientUserId) {\n                const { data: userPrefs } = await supabase.from('user_notification_preferences').select('whatsapp_content_reviewed').eq('user_id', recipientUserId).single();\n                if (userPrefs && !userPrefs.whatsapp_content_reviewed) {\n                    return false;\n                }\n            }\n            const { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name').eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentRevisionRequestedNotification: Active template '${internalTemplateName}' not found.`, dbTemplateError);\n                return false;\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const actualTemplateNameUsed = dbTemplate.name;\n            const bodyParams = [\n                {\n                    type: 'text',\n                    text: restaurantName\n                },\n                {\n                    type: 'text',\n                    text: campaignName\n                }\n            ];\n            // if (feedbackSummary) { bodyParams.push({ type: 'text', text: feedbackSummary.substring(0, 60) + (feedbackSummary.length > 60 ? '...' : '') }); }\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(to, providerTemplateId, languageCode, [\n                {\n                    type: 'body',\n                    parameters: bodyParams\n                },\n                {\n                    type: 'button',\n                    sub_type: 'url',\n                    index: '0',\n                    parameters: [\n                        {\n                            type: 'text',\n                            text: reviewLink.replace(/^https?:\\/\\//, '')\n                        }\n                    ]\n                }\n            ]);\n            if (messageId) {\n                await this.logWhatsAppMessage(to, recipientUserId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId,\n                    params: {\n                        influencerName,\n                        campaignName,\n                        restaurantName,\n                        reviewLink,\n                        feedbackSummary\n                    }\n                });\n            }\n            return !!messageId;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentRevisionRequestedNotification (template: ${internalTemplateName})`, error);\n            return false;\n        }\n    }\n    static async sendContentRejectedNotification(to, influencerName, campaignName, restaurantName, feedback) {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n        const templateNameKey = 'contentRejectedNotification'; // Use the new key\n        const internalTemplateName = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[templateNameKey];\n        let actualTemplateNameUsed = internalTemplateName; // For logging\n        try {\n            const recipientUserId = await this.getUserIdFromPhone(to, supabase);\n            if (recipientUserId) {\n                const { data: userPrefs } = await supabase.from('user_notification_preferences').select('whatsapp_content_reviewed').eq('user_id', recipientUserId).single();\n                if (userPrefs && !userPrefs.whatsapp_content_reviewed) {\n                    return false;\n                }\n            }\n            let { data: dbTemplate, error: dbTemplateError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name').eq('name', internalTemplateName).eq('status', 'active').single();\n            if (dbTemplateError || !dbTemplate) {\n                (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentRejectedNotification: Active template '${internalTemplateName}' not found. Attempting fallback 'selectionResult'.`, dbTemplateError);\n                const fallbackTemplateKey = 'selectionResult';\n                actualTemplateNameUsed = _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.templates[fallbackTemplateKey];\n                const { data: fallbackDbTemplate, error: fallbackDbError } = await supabase.from('whatsapp_templates').select('template_id, language, status, name').eq('name', actualTemplateNameUsed).eq('status', 'active').single();\n                if (fallbackDbError || !fallbackDbTemplate) {\n                    (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentRejectedNotification: Fallback template '${actualTemplateNameUsed}' also not found.`, fallbackDbError);\n                    return false;\n                }\n                dbTemplate = fallbackDbTemplate; // Use fallback template details\n            }\n            const providerTemplateId = dbTemplate.template_id;\n            const languageCode = dbTemplate.language || _lib_whatsapp_config__WEBPACK_IMPORTED_MODULE_1__.WHATSAPP_API_CONFIG.defaultLanguage;\n            const bodyParams = [\n                {\n                    type: 'text',\n                    text: campaignName\n                },\n                {\n                    type: 'text',\n                    text: restaurantName\n                }\n            ];\n            if (feedback) {\n                bodyParams.push({\n                    type: 'text',\n                    text: feedback.substring(0, 100) + (feedback.length > 100 ? '...' : '')\n                });\n            }\n            const messageId = await (0,_lib_api_whatsapp__WEBPACK_IMPORTED_MODULE_2__.sendTemplateMessage)(to, providerTemplateId, languageCode, [\n                {\n                    type: 'body',\n                    parameters: bodyParams\n                }\n            ]);\n            if (messageId) {\n                await this.logWhatsAppMessage(to, recipientUserId, 'outbound', `template_${actualTemplateNameUsed}`, messageId, 'sent', {\n                    template_name_used: actualTemplateNameUsed,\n                    provider_template_id: providerTemplateId,\n                    params: {\n                        influencerName,\n                        campaignName,\n                        restaurantName,\n                        feedback\n                    }\n                });\n            }\n            return !!messageId;\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)(`whatsapp.sendContentRejectedNotification (attempted template: ${internalTemplateName}, used: ${actualTemplateNameUsed})`, error);\n            return false;\n        }\n    }\n    static async logWhatsAppMessage(phone, userId, direction, messageType, messageId, status, metadata = {}) {\n        try {\n            const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createServerSupabaseClient)();\n            const formattedPhone = phone.replace(/\\D/g, '');\n            const now = new Date().toISOString();\n            let messageContent = `Mensagem de template: ${messageType}`;\n            if (metadata && metadata.params) {\n                messageContent = `Template: ${metadata.template_name_used || messageType}. Params: ${JSON.stringify(metadata.params)}`;\n            } else if (metadata && metadata.content_body) {\n                messageContent = metadata.content_body;\n            }\n            await supabase.from('whatsapp_messages').insert({\n                user_id: userId,\n                direction,\n                message: messageContent.substring(0, 1024),\n                whatsapp_message_id: messageId,\n                status,\n                sent_at: now,\n                created_at: now // As per user's schema\n            });\n        } catch (error) {\n            (0,_lib_utils_errors__WEBPACK_IMPORTED_MODULE_3__.logError)('whatsapp.logWhatsAppMessage', error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/whatsapp.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/config.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clientConfig: () => (/* binding */ clientConfig),\n/* harmony export */   instagramConfig: () => (/* binding */ instagramConfig),\n/* harmony export */   serverConfig: () => (/* binding */ serverConfig)\n/* harmony export */ });\n/**\n * Configuração do Supabase\n *\n * Este arquivo contém a configuração do Supabase para uso em diferentes contextos.\n */ // Configuração para o lado do cliente\nconst clientConfig = {\n    supabaseUrl: \"https://pbehloddlzwandfmpzbo.supabase.co\",\n    supabaseAnonKey: \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38\"\n};\n// Configuração para o lado do servidor\nconst serverConfig = {\n    supabaseUrl: \"https://pbehloddlzwandfmpzbo.supabase.co\",\n    supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY\n};\n// Configuração do Instagram\nconst instagramConfig = {\n    appId: process.env.NEXT_PUBLIC_INSTAGRAM_APP_ID || process.env.INSTAGRAM_APP_ID,\n    appSecret: process.env.INSTAGRAM_APP_SECRET,\n    redirectUri: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://streetbrand-openrouter2.vercel.app'}/api/auth/instagram/callback`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   createServerSupabaseAnonClient: () => (/* binding */ createServerSupabaseAnonClient),\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/lib/supabase/config.ts\");\n/**\n * Cliente Supabase para o lado do servidor\n * \n * Este arquivo contém funções para criar um cliente Supabase para uso no lado do servidor.\n * Não deve ser importado diretamente no lado do cliente.\n */ \n\n// Cliente Supabase para uso no servidor com a chave de serviço\nfunction createServerSupabaseClient() {\n    const supabaseUrl = \"https://pbehloddlzwandfmpzbo.supabase.co\";\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        console.error('Variáveis de ambiente do Supabase não configuradas corretamente');\n        throw new Error('Configuração do Supabase incompleta');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n}\n// Cliente Supabase para uso no servidor com a chave anônima\nfunction createServerSupabaseAnonClient() {\n    const supabaseUrl = \"https://pbehloddlzwandfmpzbo.supabase.co\";\n    const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38\";\n    if (!supabaseUrl || !supabaseAnonKey) {\n        console.error('Variáveis de ambiente do Supabase não configuradas corretamente');\n        throw new Error('Configuração do Supabase incompleta');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Cria um cliente Supabase para o lado do servidor\nconst createServerClient = ()=>{\n    const { supabaseUrl, supabaseServiceKey } = _config__WEBPACK_IMPORTED_MODULE_0__.serverConfig;\n    if (!supabaseUrl || !supabaseServiceKey) {\n        throw new Error('Supabase URL and service key are required for server operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/constants.ts":
/*!************************************!*\
  !*** ./src/lib/utils/constants.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),\n/* harmony export */   INTEGRATION_CONFIG: () => (/* binding */ INTEGRATION_CONFIG),\n/* harmony export */   ROUTES: () => (/* binding */ ROUTES),\n/* harmony export */   WHATSAPP_CONFIG: () => (/* binding */ WHATSAPP_CONFIG)\n/* harmony export */ });\n/**\n * Constantes globais da aplicação\n */ // URL base da aplicação - dinâmica baseada no ambiente\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || (process.env.NEXT_PUBLIC_VERCEL_URL ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}` : 'https://connectcity-openrouter2.vercel.app');\n// Rotas da aplicação\nconst ROUTES = {\n    HOME: '/',\n    LOGIN: '/login',\n    REGISTER: '/register',\n    DASHBOARD: '/dashboard',\n    RESTAURANT: '/restaurante',\n    INFLUENCER: '/influenciador',\n    ADMIN: '/admin'\n};\n// Configurações de integração\nconst INTEGRATION_CONFIG = {\n    INSTAGRAM_CALLBACK_URL: `${BASE_URL}/api/auth/instagram/callback`,\n    INSTAGRAM_WEBHOOK_URL: `${BASE_URL}/api/instagram/webhook`,\n    WHATSAPP_WEBHOOK_URL: `${BASE_URL}/api/v1/whatsapp/webhook`\n};\n// Configurações do WhatsApp\nconst WHATSAPP_CONFIG = {\n    API_URL: `https://graph.facebook.com/${process.env.WHATSAPP_API_VERSION}`,\n    BUSINESS_ACCOUNT_ID: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID,\n    PHONE_NUMBER_ID: process.env.WHATSAPP_PHONE_NUMBER_ID\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/errors.ts":
/*!*********************************!*\
  !*** ./src/lib/utils/errors.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   formatSupabaseError: () => (/* binding */ formatSupabaseError),\n/* harmony export */   logError: () => (/* binding */ logError)\n/* harmony export */ });\n/**\n * Utilitários para tratamento de erros\n */ /**\n * Formata um erro do Supabase para exibição\n * @param error O erro do Supabase\n * @returns Uma mensagem de erro formatada\n */ function formatSupabaseError(error) {\n    if (!error) return 'Erro desconhecido';\n    // Mapear códigos de erro comuns para mensagens amigáveis\n    switch(error.code){\n        case '23505':\n            return 'Este registro já existe.';\n        case '23503':\n            return 'Referência inválida a outro registro.';\n        case '42P01':\n            return 'Tabela não encontrada.';\n        case '42703':\n            return 'Coluna não encontrada.';\n        case '22P02':\n            return 'Formato de dados inválido.';\n        default:\n            // Em produção, não expor detalhes técnicos\n            return  false ? 0 : `Erro: ${error.message} (Código: ${error.code})`;\n    }\n}\n/**\n * Formata um erro genérico para exibição\n * @param error O erro a ser formatado\n * @returns Uma mensagem de erro formatada\n */ function formatError(error) {\n    if (!error) return 'Erro desconhecido';\n    // Se for um erro do Supabase\n    if (error.code && error.message && error.details) {\n        return formatSupabaseError(error);\n    }\n    // Se for um erro com mensagem\n    if (error.message) {\n        return  false ? 0 : `Erro: ${error.message}`;\n    }\n    // Se for uma string\n    if (typeof error === 'string') {\n        return error;\n    }\n    // Fallback\n    return 'Ocorreu um erro ao processar sua solicitação.';\n}\n/**\n * Registra um erro no console de forma segura\n * @param context O contexto onde o erro ocorreu\n * @param error O erro a ser registrado\n */ function logError(context, error) {\n    console.error(`[${context}] Erro:`, error.message || error);\n    // Em desenvolvimento, registrar mais detalhes\n    if (true) {\n        if (error.stack) {\n            console.error(`[${context}] Stack:`, error.stack);\n        }\n        if (error.code) {\n            console.error(`[${context}] Código:`, error.code);\n        }\n        if (error.details) {\n            console.error(`[${context}] Detalhes:`, error.details);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/errors.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/whatsapp/config.ts":
/*!************************************!*\
  !*** ./src/lib/whatsapp/config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHATSAPP_API_CONFIG: () => (/* binding */ WHATSAPP_API_CONFIG),\n/* harmony export */   isWhatsAppEnabled: () => (/* binding */ isWhatsAppEnabled),\n/* harmony export */   validateWhatsAppConfig: () => (/* binding */ validateWhatsAppConfig)\n/* harmony export */ });\n// WhatsApp Business API Configuration\nconst WHATSAPP_API_CONFIG = {\n    // Meta WhatsApp Business API credentials\n    apiVersion: process.env.WHATSAPP_API_VERSION || 'v18.0',\n    phoneNumberId: '***************',\n    accessToken: process.env.WHATSAPP_ACCESS_TOKEN || 'SEU_ACCESS_TOKEN_AQUI',\n    businessAccountId: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID || 'SEU_BUSINESS_ID_AQUI',\n    // API endpoints\n    baseUrl: 'https://graph.facebook.com',\n    // Message templates\n    templates: {\n        welcome: 'welcome_message',\n        verification: 'verification_code',\n        newInfluencer: 'new_influencer_notification',\n        newPost: 'new_post_notification',\n        weeklyReport: 'weekly_report',\n        rankingUpdate: 'ranking_update',\n        // Novos templates para gamificação\n        pointsEarned: 'points_earned_notification',\n        achievementUnlocked: 'achievement_unlocked',\n        taskReminder: 'task_reminder',\n        scheduleReminder: 'schedule_reminder',\n        // Templates para restaurantes\n        restaurantWelcome: 'restaurant_welcome',\n        restaurantProfileReminder: 'restaurant_profile_reminder',\n        briefingSaved: 'briefing_saved',\n        campaignLaunched: 'campaign_launched',\n        newApplicants: 'new_applicants',\n        selectionDeadline: 'selection_deadline',\n        teamSelected: 'team_selected',\n        calendarReminder: 'calendar_reminder',\n        visitScheduled: 'visit_scheduled',\n        newContent: 'new_content',\n        contentReviewReminder: 'content_review_reminder',\n        postScheduled: 'post_scheduled',\n        postLive: 'post_live',\n        campaignReport: 'campaign_report',\n        // Templates para influenciadores\n        influencerWelcome: 'influencer_welcome',\n        campaignOpportunity: 'campaign_opportunity',\n        applicationConfirmation: 'application_confirmation',\n        selectionResult: 'selection_result',\n        visitConfirmation: 'visit_confirmation',\n        visitReminder: 'visit_reminder',\n        contentUploadReminder: 'content_upload_reminder',\n        contentFeedback: 'content_feedback',\n        contentApproved: 'content_approved',\n        postReminder: 'post_reminder',\n        postLinkReminder: 'post_link_reminder',\n        performanceUpdate: 'performance_update',\n        campaignWinner: 'campaign_winner',\n        paymentSent: 'payment_sent',\n        // Notification for influencer when their content is rejected\n        contentRejectedNotification: 'content_rejected_notification'\n    },\n    // Default settings\n    defaultLanguage: 'pt_BR',\n    // Webhook verification\n    webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN\n};\n// Check if required environment variables are set\nfunction validateWhatsAppConfig() {\n    const requiredVars = [\n        'WHATSAPP_PHONE_NUMBER_ID',\n        'WHATSAPP_ACCESS_TOKEN',\n        'WHATSAPP_BUSINESS_ACCOUNT_ID',\n        'WHATSAPP_WEBHOOK_VERIFY_TOKEN'\n    ];\n    const missingVars = requiredVars.filter((varName)=>!process.env[varName]);\n    if (missingVars.length > 0) {\n        console.warn(`Missing WhatsApp API environment variables: ${missingVars.join(', ')}`);\n        return false;\n    }\n    return true;\n}\n// Check if WhatsApp API is enabled\nfunction isWhatsAppEnabled() {\n    return process.env.ENABLE_WHATSAPP_API === 'true' && validateWhatsAppConfig();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/whatsapp/config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fv1%2Ftasks%2Froute&page=%2Fapi%2Fv1%2Ftasks%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fv1%2Ftasks%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();