/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/restaurants/[id]/route";
exports.ids = ["app/api/admin/restaurants/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_restaurants_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/restaurants/[id]/route.ts */ \"(rsc)/./src/app/api/admin/restaurants/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/restaurants/[id]/route\",\n        pathname: \"/api/admin/restaurants/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/restaurants/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/admin/restaurants/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_restaurants_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/restaurants/[id]/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/admin/restaurants/[id]/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Configuração do Supabase\nconst supabaseUrl = \"https://pbehloddlzwandfmpzbo.supabase.co\" || 0;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';\n// Headers CORS\nconst corsHeaders = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n};\n// OPTIONS - Preflight para CORS\nasync function OPTIONS() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}, {\n        headers: corsHeaders\n    });\n}\n// GET - Buscar um restaurante específico\nasync function GET(request// Context parameter removed\n) {\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Obter ID do restaurante da URL\n        const url = new URL(request.url);\n        const pathSegments = url.pathname.split('/');\n        const restaurantId = pathSegments[pathSegments.length - 1]; // Extract from URL\n        if (!restaurantId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID do restaurante não fornecido'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        console.log('Buscando restaurante com ID:', restaurantId);\n        // Criar cliente Supabase com a chave de serviço para contornar RLS\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Buscar o restaurante pelo ID\n        const { data, error } = await supabase.from('restaurant_profiles').select('*').eq('id', restaurantId).single();\n        if (error) {\n            console.error('Erro ao buscar restaurante:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: error.code === 'PGRST116' ? 404 : 500,\n                headers\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            headers\n        });\n    } catch (error) {\n        console.error('Erro ao processar requisição:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500,\n            headers: {\n                ...corsHeaders,\n                'Content-Type': 'application/json'\n            }\n        });\n    }\n}\n// PUT - Atualizar um restaurante específico\nasync function PUT(request// Context parameter removed\n) {\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Obter ID do restaurante da URL\n        const url = new URL(request.url);\n        const pathSegments = url.pathname.split('/');\n        const restaurantId = pathSegments[pathSegments.length - 1]; // Extract from URL\n        if (!restaurantId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID do restaurante não fornecido'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Obter dados da requisição\n        const restaurantData = await request.json();\n        console.log('Atualizando restaurante:', restaurantId, restaurantData);\n        // Validar dados obrigatórios\n        const requiredFields = [\n            'business_name',\n            'city',\n            'state'\n        ];\n        const missingFields = requiredFields.filter((field)=>!restaurantData[field]);\n        if (missingFields.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Campos obrigatórios ausentes: ${missingFields.join(', ')}`\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Criar cliente Supabase com a chave de serviço para contornar RLS\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Verificar se o restaurante existe\n        const { data: existingRestaurant, error: checkError } = await supabase.from('restaurant_profiles').select('id').eq('id', restaurantId).maybeSingle();\n        if (checkError) {\n            console.error('Erro ao verificar restaurante:', checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: checkError.message\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!existingRestaurant) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Restaurante não encontrado'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        // Adicionar data de atualização\n        const updateData = {\n            ...restaurantData,\n            updated_at: new Date().toISOString()\n        };\n        // Atualizar o restaurante na tabela restaurant_profiles\n        const { data, error } = await supabase.from('restaurant_profiles').update(updateData).eq('id', restaurantId).select().single();\n        if (error) {\n            console.error('Erro ao atualizar restaurant_profile:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        // Atualizar também o nome no perfil básico\n        if (restaurantData.business_name) {\n            const { error: profileError } = await supabase.from('profiles').update({\n                full_name: restaurantData.business_name,\n                updated_at: new Date().toISOString()\n            }).eq('id', restaurantId);\n            if (profileError) {\n                console.warn('Erro ao atualizar perfil:', profileError);\n            // Não falhar a operação principal se a atualização do perfil falhar\n            }\n            // Atualizar também na tabela restaurants\n            const { error: restaurantsError } = await supabase.from('restaurants').update({\n                name: restaurantData.business_name,\n                instagram_handle: restaurantData.instagram_url ? restaurantData.instagram_url.split('/').pop() : null,\n                updated_at: new Date().toISOString()\n            }).eq('id', restaurantId);\n            if (restaurantsError) {\n                console.warn('Erro ao atualizar registro na tabela restaurants:', restaurantsError);\n                // Verificar se o registro existe na tabela restaurants\n                const { data: existingRestaurantRecord, error: checkRestaurantError } = await supabase.from('restaurants').select('id').eq('id', restaurantId).maybeSingle();\n                if (checkRestaurantError) {\n                    console.error('Erro ao verificar registro na tabela restaurants:', checkRestaurantError);\n                } else if (!existingRestaurantRecord) {\n                    // Se o registro não existe, criar um novo\n                    console.log('Registro não encontrado na tabela restaurants, criando um novo...');\n                    const { error: insertError } = await supabase.from('restaurants').insert({\n                        id: restaurantId,\n                        owner_id: restaurantId,\n                        name: restaurantData.business_name,\n                        instagram_handle: restaurantData.instagram_url ? restaurantData.instagram_url.split('/').pop() : null,\n                        created_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    });\n                    if (insertError) {\n                        console.error('Erro ao criar registro na tabela restaurants:', insertError);\n                    }\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            headers\n        });\n    } catch (error) {\n        console.error('Erro ao processar requisição:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500,\n            headers: {\n                ...corsHeaders,\n                'Content-Type': 'application/json'\n            }\n        });\n    }\n}\n// DELETE - Excluir um restaurante específico\nasync function DELETE(request// Context parameter removed\n) {\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Obter ID do restaurante da URL\n        const url = new URL(request.url);\n        const pathSegments = url.pathname.split('/');\n        const restaurantId = pathSegments[pathSegments.length - 1]; // Extract from URL\n        if (!restaurantId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID do restaurante não fornecido'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        console.log('Excluindo restaurante:', restaurantId);\n        // Criar cliente Supabase com a chave de serviço para contornar RLS\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey);\n        // Verificar se o restaurante existe\n        const { data: existingRestaurant, error: checkError } = await supabase.from('restaurant_profiles').select('id').eq('id', restaurantId).maybeSingle();\n        if (checkError) {\n            console.error('Erro ao verificar restaurante:', checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: checkError.message\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!existingRestaurant) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Restaurante não encontrado'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        // Excluir registros relacionados primeiro\n        // 1. Excluir registros de presença digital\n        const { error: digitalPresenceError } = await supabase.from('digital_presence').delete().eq('restaurant_id', restaurantId);\n        if (digitalPresenceError) {\n            console.warn('Erro ao excluir registros de presença digital:', digitalPresenceError);\n        // Continuar mesmo com erro\n        }\n        // 2. Excluir registros de contagem de conteúdo\n        const { error: contentCountError } = await supabase.from('content_count').delete().eq('restaurant_id', restaurantId);\n        if (contentCountError) {\n            console.warn('Erro ao excluir registros de contagem de conteúdo:', contentCountError);\n        // Continuar mesmo com erro\n        }\n        // 3. Excluir registro na tabela restaurants\n        const { error: restaurantsError } = await supabase.from('restaurants').delete().eq('id', restaurantId);\n        if (restaurantsError) {\n            console.warn('Erro ao excluir registro na tabela restaurants:', restaurantsError);\n        // Continuar mesmo com erro\n        }\n        // 4. Excluir o restaurante na tabela restaurant_profiles\n        const { error } = await supabase.from('restaurant_profiles').delete().eq('id', restaurantId);\n        if (error) {\n            console.error('Erro ao excluir restaurant_profile:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        // 5. Excluir também o perfil básico\n        const { error: profileError } = await supabase.from('profiles').delete().eq('id', restaurantId);\n        if (profileError) {\n            console.warn('Erro ao excluir perfil:', profileError);\n        // Não falhar a operação principal se a exclusão do perfil falhar\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Restaurante excluído com sucesso'\n        }, {\n            headers\n        });\n    } catch (error) {\n        console.error('Erro ao processar requisição:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500,\n            headers: {\n                ...corsHeaders,\n                'Content-Type': 'application/json'\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/restaurants/[id]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frestaurants%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();