globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/admin/restaurants/[id]/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/layout/MainLayoutClient.tsx":{"*":{"id":"(ssr)/./src/components/layout/MainLayoutClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/influenciador/layout.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/influenciador/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/influenciador/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/influenciador/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/influenciador/campanhas/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/influenciador/campanhas/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/layout.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/campaigns/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/campaigns/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(auth)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/login/page.tsx":{"*":{"id":"(ssr)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/restaurante-novo/page.tsx":{"*":{"id":"(ssr)/./src/app/(dashboard)/restaurante-novo/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/restaurants/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/influencers/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/influencers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/new/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/restaurants/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/campaigns/new/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/campaigns/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/influencers/new/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/influencers/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/[id]/edit/EditRestaurantWrapper.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/restaurants/[id]/edit/EditRestaurantWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/[id]/RestaurantDetailsWrapper.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/restaurants/[id]/RestaurantDetailsWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/negocios/page.tsx":{"*":{"id":"(ssr)/./src/app/(admin)/admin/negocios/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/dev-hide.css":{"id":"(app-pages-browser)/./src/app/dev-hide.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/fix-purple-border.css":{"id":"(app-pages-browser)/./src/app/fix-purple-border.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/fix-width.css":{"id":"(app-pages-browser)/./src/app/fix-width.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/layout/MainLayoutClient.tsx":{"id":"(app-pages-browser)/./src/components/layout/MainLayoutClient.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/styles/themes.css":{"id":"(app-pages-browser)/./src/styles/themes.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/remove-space.css":{"id":"(app-pages-browser)/./src/app/remove-space.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/app-dir/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(dashboard)/layout.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/layout.tsx","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(dashboard)/influenciador/layout.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/influenciador/layout.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(dashboard)/influenciador/page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/influenciador/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(dashboard)/influenciador/campanhas/page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/influenciador/campanhas/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/layout.tsx","name":"*","chunks":["app/(admin)/layout","static/chunks/app/(admin)/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/layout.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/layout.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/campaigns/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(auth)/layout.tsx":{"id":"(app-pages-browser)/./src/app/(auth)/layout.tsx","name":"*","chunks":["app/(auth)/layout","static/chunks/app/(auth)/layout.js"],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(auth)/login/page.tsx":{"id":"(app-pages-browser)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(dashboard)/restaurante-novo/page.tsx":{"id":"(app-pages-browser)/./src/app/(dashboard)/restaurante-novo/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/restaurants/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/restaurants/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/influencers/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/influencers/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/restaurants/new/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/restaurants/new/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/campaigns/new/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/campaigns/new/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/influencers/new/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/influencers/new/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/restaurants/[id]/edit/EditRestaurantWrapper.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/restaurants/[id]/edit/EditRestaurantWrapper.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/restaurants/[id]/RestaurantDetailsWrapper.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/restaurants/[id]/RestaurantDetailsWrapper.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/negocios/page.tsx":{"id":"(app-pages-browser)/./src/app/(admin)/admin/negocios/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/AI_Projects/Criadores/src/":[],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/not-found":[],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/page":[],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(dashboard)/layout":[],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/layout":[],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(auth)/layout":[],"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/admin/restaurants/[id]/route":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dev-hide.css":{"*":{"id":"(rsc)/./src/app/dev-hide.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/fix-purple-border.css":{"*":{"id":"(rsc)/./src/app/fix-purple-border.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/fix-width.css":{"*":{"id":"(rsc)/./src/app/fix-width.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/MainLayoutClient.tsx":{"*":{"id":"(rsc)/./src/components/layout/MainLayoutClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/themes.css":{"*":{"id":"(rsc)/./src/styles/themes.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/remove-space.css":{"*":{"id":"(rsc)/./src/app/remove-space.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/influenciador/layout.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/influenciador/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/influenciador/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/influenciador/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/influenciador/campanhas/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/influenciador/campanhas/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/layout.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/campaigns/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/campaigns/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(auth)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(auth)/login/page.tsx":{"*":{"id":"(rsc)/./src/app/(auth)/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/restaurante-novo/page.tsx":{"*":{"id":"(rsc)/./src/app/(dashboard)/restaurante-novo/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/restaurants/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/influencers/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/influencers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/new/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/restaurants/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/campaigns/new/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/campaigns/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/influencers/new/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/influencers/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/[id]/edit/EditRestaurantWrapper.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/restaurants/[id]/edit/EditRestaurantWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/restaurants/[id]/RestaurantDetailsWrapper.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/restaurants/[id]/RestaurantDetailsWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(admin)/admin/negocios/page.tsx":{"*":{"id":"(rsc)/./src/app/(admin)/admin/negocios/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}