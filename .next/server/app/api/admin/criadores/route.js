/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/criadores/route";
exports.ids = ["app/api/admin/criadores/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_criadores_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/criadores/route.ts */ \"(rsc)/./src/app/api/admin/criadores/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/criadores/route\",\n        pathname: \"/api/admin/criadores\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/criadores/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/admin/criadores/route.ts\",\n    nextConfigOutput,\n    userland: _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_criadores_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/criadores/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/admin/criadores/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _utils_passwordGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/passwordGenerator */ \"(rsc)/./src/utils/passwordGenerator.ts\");\n\n\n\n// Inicializar cliente Supabase com a chave de serviço\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://pbehloddlzwandfmpzbo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Headers CORS para permitir acesso à API\nconst corsHeaders = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n};\n// OPTIONS - Para lidar com preflight requests do CORS\nasync function OPTIONS() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}, {\n        headers: corsHeaders\n    });\n}\n// POST - Criar um novo criador\nasync function POST(request) {\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Obter dados do corpo da requisição\n        const criadorData = await request.json();\n        // Validar dados obrigatórios\n        if (!criadorData.name || !criadorData.username || !criadorData.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Nome, nome de usuário e email são obrigatórios'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Verificar se o email já existe\n        const { data: existingUser, error: userError } = await supabase.from('profiles').select('id').eq('email', criadorData.email).maybeSingle();\n        if (userError) {\n            console.error('Erro ao verificar usuário existente:', userError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao verificar usuário existente: ${userError.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        let userId;\n        let isNewUser = false;\n        let temporaryPassword = '';\n        if (existingUser) {\n            // Usar o ID do usuário existente\n            userId = existingUser.id;\n            console.log('Usuário existente encontrado:', userId);\n        } else {\n            // Criar um novo usuário no Auth do Supabase\n            isNewUser = true;\n            temporaryPassword = (0,_utils_passwordGenerator__WEBPACK_IMPORTED_MODULE_1__.generatePassword)();\n            const { data: userData, error: createUserError } = await supabase.auth.admin.createUser({\n                email: criadorData.email,\n                password: temporaryPassword,\n                email_confirm: true\n            });\n            if (createUserError) {\n                console.error('Erro ao criar usuário:', createUserError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Erro ao criar usuário: ${createUserError.message}`\n                }, {\n                    status: 500,\n                    headers\n                });\n            }\n            userId = userData.user.id;\n            console.log('Novo usuário criado:', userId);\n        }\n        try {\n            // Criar ou atualizar o perfil do usuário\n            if (isNewUser) {\n                // Criar novo perfil\n                const { error: profileError } = await supabase.from('profiles').insert({\n                    id: userId,\n                    role: 'criador',\n                    full_name: criadorData.name,\n                    email: criadorData.email,\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                });\n                if (profileError) {\n                    console.error('Erro ao criar perfil:', profileError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao criar perfil: ${profileError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            } else {\n                // Atualizar perfil existente\n                const { error: updateProfileError } = await supabase.from('profiles').update({\n                    full_name: criadorData.name,\n                    updated_at: new Date().toISOString()\n                }).eq('id', userId);\n                if (updateProfileError) {\n                    console.error('Erro ao atualizar perfil:', updateProfileError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao atualizar perfil: ${updateProfileError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            }\n            // Verificar se o criador já existe\n            const { data: existingCriador } = await supabase.from('influencers').select('id').eq('id', userId).single();\n            if (existingCriador) {\n                // Atualizar criador existente\n                const { error: updateError } = await supabase.from('influencers').update({\n                    name: criadorData.name,\n                    username: criadorData.username,\n                    classification: criadorData.classification || 'Standard',\n                    updated_at: new Date().toISOString()\n                }).eq('id', userId);\n                if (updateError) {\n                    console.error('Erro ao atualizar registro de influenciador:', updateError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao atualizar criador: ${updateError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            } else {\n                // Criar novo registro de criador\n                const { error: criadorError } = await supabase.from('influencers').insert({\n                    id: userId,\n                    name: criadorData.name,\n                    username: criadorData.username,\n                    classification: criadorData.classification || 'Standard',\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                });\n                if (criadorError) {\n                    console.error('Erro ao criar registro de criador:', criadorError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao criar criador: ${criadorError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            }\n            // Verificar se o perfil de criador já existe\n            const { data: existingCriadorProfile } = await supabase.from('influencer_profiles').select('id').eq('id', userId).single();\n            const profile = criadorData.profile || {};\n            if (existingCriadorProfile) {\n                // Atualizar perfil de criador existente\n                const { error: updateError } = await supabase.from('influencer_profiles').update({\n                    bio: profile.bio || '',\n                    content_niche: profile.content_niche || [],\n                    primary_platform: profile.primary_platform || 'instagram',\n                    instagram_username: profile.instagram_username || '',\n                    tiktok_username: profile.tiktok_username || '',\n                    location_city: profile.location_city || '',\n                    location_state: profile.location_state || '',\n                    avg_engagement_rate: profile.avg_engagement_rate || 0,\n                    follower_count: profile.follower_count || 0,\n                    updated_at: new Date().toISOString()\n                }).eq('id', userId);\n                if (updateError) {\n                    console.error('Erro ao atualizar perfil de criador:', updateError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao atualizar perfil de criador: ${updateError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            } else {\n                // Criar novo perfil de criador\n                const { error: criadorProfileError } = await supabase.from('influencer_profiles').insert({\n                    id: userId,\n                    bio: profile.bio || '',\n                    content_niche: profile.content_niche || [],\n                    primary_platform: profile.primary_platform || 'instagram',\n                    instagram_username: profile.instagram_username || '',\n                    tiktok_username: profile.tiktok_username || '',\n                    location_city: profile.location_city || '',\n                    location_state: profile.location_state || '',\n                    avg_engagement_rate: profile.avg_engagement_rate || 0,\n                    follower_count: profile.follower_count || 0,\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                });\n                if (criadorProfileError) {\n                    console.error('Erro ao criar perfil de criador:', criadorProfileError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao criar perfil de criador: ${criadorProfileError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            }\n            // Retornar informações do criador criado/atualizado\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: isNewUser ? 'Criador adicionado com sucesso' : 'Criador atualizado com sucesso',\n                id: userId,\n                email: criadorData.email,\n                password: isNewUser ? temporaryPassword : undefined,\n                name: criadorData.name,\n                username: criadorData.username,\n                isNewUser\n            }, {\n                headers\n            });\n        } catch (profileError) {\n            console.error('Erro ao criar/atualizar perfil:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao criar/atualizar perfil: ${profileError.message}`,\n                details: profileError\n            }, {\n                status: 500,\n                headers\n            });\n        }\n    } catch (err) {\n        console.error('Erro inesperado:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro interno do servidor: ${err.message}`,\n            stack: err.stack,\n            name: err.name,\n            code: err.code\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n// GET - Listar todos os criadores\nasync function GET(_request) {\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Obter criadores\n        const { data: criadores, error } = await supabase.from('influencers').select('*').order('name', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Erro ao buscar criadores:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao buscar criadores: ${error.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(criadores, {\n            headers\n        });\n    } catch (err) {\n        console.error('Erro inesperado:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro interno do servidor: ${err.message}`\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/criadores/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/passwordGenerator.ts":
/*!****************************************!*\
  !*** ./src/utils/passwordGenerator.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generatePassword: () => (/* binding */ generatePassword)\n/* harmony export */ });\n/**\n * Gera uma senha aleatória com o comprimento especificado\n * @param length Comprimento da senha (padrão: 10)\n * @returns Senha aleatória\n */ function generatePassword(length = 10) {\n    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+';\n    let password = '';\n    // Garantir que a senha tenha pelo menos uma letra maiúscula, uma minúscula, um número e um caractere especial\n    password += getRandomChar('ABCDEFGHIJKLMNOPQRSTUVWXYZ');\n    password += getRandomChar('abcdefghijklmnopqrstuvwxyz');\n    password += getRandomChar('0123456789');\n    password += getRandomChar('!@#$%^&*()_+');\n    // Preencher o restante da senha com caracteres aleatórios\n    for(let i = password.length; i < length; i++){\n        const randomIndex = Math.floor(Math.random() * charset.length);\n        password += charset[randomIndex];\n    }\n    // Embaralhar a senha para que os caracteres obrigatórios não fiquem sempre nas mesmas posições\n    return shuffleString(password);\n}\n/**\n * Obtém um caractere aleatório de uma string\n * @param characters String de caracteres\n * @returns Um caractere aleatório da string\n */ function getRandomChar(characters) {\n    const randomIndex = Math.floor(Math.random() * characters.length);\n    return characters[randomIndex];\n}\n/**\n * Embaralha os caracteres de uma string\n * @param str String a ser embaralhada\n * @returns String embaralhada\n */ function shuffleString(str) {\n    const array = str.split('');\n    for(let i = array.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [array[i], array[j]] = [\n            array[j],\n            array[i]\n        ];\n    }\n    return array.join('');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvcGFzc3dvcmRHZW5lcmF0b3IudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7O0NBSUMsR0FDTSxTQUFTQSxpQkFBaUJDLFNBQWlCLEVBQUU7SUFDbEQsTUFBTUMsVUFBVTtJQUNoQixJQUFJQyxXQUFXO0lBRWYsOEdBQThHO0lBQzlHQSxZQUFZQyxjQUFjO0lBQzFCRCxZQUFZQyxjQUFjO0lBQzFCRCxZQUFZQyxjQUFjO0lBQzFCRCxZQUFZQyxjQUFjO0lBRTFCLDBEQUEwRDtJQUMxRCxJQUFLLElBQUlDLElBQUlGLFNBQVNGLE1BQU0sRUFBRUksSUFBSUosUUFBUUksSUFBSztRQUM3QyxNQUFNQyxjQUFjQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS1AsUUFBUUQsTUFBTTtRQUM3REUsWUFBWUQsT0FBTyxDQUFDSSxZQUFZO0lBQ2xDO0lBRUEsK0ZBQStGO0lBQy9GLE9BQU9JLGNBQWNQO0FBQ3ZCO0FBRUE7Ozs7Q0FJQyxHQUNELFNBQVNDLGNBQWNPLFVBQWtCO0lBQ3ZDLE1BQU1MLGNBQWNDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLRSxXQUFXVixNQUFNO0lBQ2hFLE9BQU9VLFVBQVUsQ0FBQ0wsWUFBWTtBQUNoQztBQUVBOzs7O0NBSUMsR0FDRCxTQUFTSSxjQUFjRSxHQUFXO0lBQ2hDLE1BQU1DLFFBQVFELElBQUlFLEtBQUssQ0FBQztJQUN4QixJQUFLLElBQUlULElBQUlRLE1BQU1aLE1BQU0sR0FBRyxHQUFHSSxJQUFJLEdBQUdBLElBQUs7UUFDekMsTUFBTVUsSUFBSVIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQU1KLENBQUFBLElBQUk7UUFDMUMsQ0FBQ1EsS0FBSyxDQUFDUixFQUFFLEVBQUVRLEtBQUssQ0FBQ0UsRUFBRSxDQUFDLEdBQUc7WUFBQ0YsS0FBSyxDQUFDRSxFQUFFO1lBQUVGLEtBQUssQ0FBQ1IsRUFBRTtTQUFDO0lBQzdDO0lBQ0EsT0FBT1EsTUFBTUcsSUFBSSxDQUFDO0FBQ3BCIiwic291cmNlcyI6WyIvVXNlcnMvbHVpenZpbmNlbnppL0RvY3VtZW50cy9BSV9Qcm9qZWN0cy9DcmlhZG9yZXMvc3JjL3V0aWxzL3Bhc3N3b3JkR2VuZXJhdG9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogR2VyYSB1bWEgc2VuaGEgYWxlYXTDs3JpYSBjb20gbyBjb21wcmltZW50byBlc3BlY2lmaWNhZG9cbiAqIEBwYXJhbSBsZW5ndGggQ29tcHJpbWVudG8gZGEgc2VuaGEgKHBhZHLDo286IDEwKVxuICogQHJldHVybnMgU2VuaGEgYWxlYXTDs3JpYVxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVQYXNzd29yZChsZW5ndGg6IG51bWJlciA9IDEwKTogc3RyaW5nIHtcbiAgY29uc3QgY2hhcnNldCA9ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSFAIyQlXiYqKClfKyc7XG4gIGxldCBwYXNzd29yZCA9ICcnO1xuICBcbiAgLy8gR2FyYW50aXIgcXVlIGEgc2VuaGEgdGVuaGEgcGVsbyBtZW5vcyB1bWEgbGV0cmEgbWFpw7pzY3VsYSwgdW1hIG1pbsO6c2N1bGEsIHVtIG7Dum1lcm8gZSB1bSBjYXJhY3RlcmUgZXNwZWNpYWxcbiAgcGFzc3dvcmQgKz0gZ2V0UmFuZG9tQ2hhcignQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVonKTtcbiAgcGFzc3dvcmQgKz0gZ2V0UmFuZG9tQ2hhcignYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXonKTtcbiAgcGFzc3dvcmQgKz0gZ2V0UmFuZG9tQ2hhcignMDEyMzQ1Njc4OScpO1xuICBwYXNzd29yZCArPSBnZXRSYW5kb21DaGFyKCchQCMkJV4mKigpXysnKTtcbiAgXG4gIC8vIFByZWVuY2hlciBvIHJlc3RhbnRlIGRhIHNlbmhhIGNvbSBjYXJhY3RlcmVzIGFsZWF0w7NyaW9zXG4gIGZvciAobGV0IGkgPSBwYXNzd29yZC5sZW5ndGg7IGkgPCBsZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IHJhbmRvbUluZGV4ID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogY2hhcnNldC5sZW5ndGgpO1xuICAgIHBhc3N3b3JkICs9IGNoYXJzZXRbcmFuZG9tSW5kZXhdO1xuICB9XG4gIFxuICAvLyBFbWJhcmFsaGFyIGEgc2VuaGEgcGFyYSBxdWUgb3MgY2FyYWN0ZXJlcyBvYnJpZ2F0w7NyaW9zIG7Do28gZmlxdWVtIHNlbXByZSBuYXMgbWVzbWFzIHBvc2nDp8O1ZXNcbiAgcmV0dXJuIHNodWZmbGVTdHJpbmcocGFzc3dvcmQpO1xufVxuXG4vKipcbiAqIE9idMOpbSB1bSBjYXJhY3RlcmUgYWxlYXTDs3JpbyBkZSB1bWEgc3RyaW5nXG4gKiBAcGFyYW0gY2hhcmFjdGVycyBTdHJpbmcgZGUgY2FyYWN0ZXJlc1xuICogQHJldHVybnMgVW0gY2FyYWN0ZXJlIGFsZWF0w7NyaW8gZGEgc3RyaW5nXG4gKi9cbmZ1bmN0aW9uIGdldFJhbmRvbUNoYXIoY2hhcmFjdGVyczogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgcmFuZG9tSW5kZXggPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBjaGFyYWN0ZXJzLmxlbmd0aCk7XG4gIHJldHVybiBjaGFyYWN0ZXJzW3JhbmRvbUluZGV4XTtcbn1cblxuLyoqXG4gKiBFbWJhcmFsaGEgb3MgY2FyYWN0ZXJlcyBkZSB1bWEgc3RyaW5nXG4gKiBAcGFyYW0gc3RyIFN0cmluZyBhIHNlciBlbWJhcmFsaGFkYVxuICogQHJldHVybnMgU3RyaW5nIGVtYmFyYWxoYWRhXG4gKi9cbmZ1bmN0aW9uIHNodWZmbGVTdHJpbmcoc3RyOiBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBhcnJheSA9IHN0ci5zcGxpdCgnJyk7XG4gIGZvciAobGV0IGkgPSBhcnJheS5sZW5ndGggLSAxOyBpID4gMDsgaS0tKSB7XG4gICAgY29uc3QgaiA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIChpICsgMSkpO1xuICAgIFthcnJheVtpXSwgYXJyYXlbal1dID0gW2FycmF5W2pdLCBhcnJheVtpXV07XG4gIH1cbiAgcmV0dXJuIGFycmF5LmpvaW4oJycpO1xufVxuIl0sIm5hbWVzIjpbImdlbmVyYXRlUGFzc3dvcmQiLCJsZW5ndGgiLCJjaGFyc2V0IiwicGFzc3dvcmQiLCJnZXRSYW5kb21DaGFyIiwiaSIsInJhbmRvbUluZGV4IiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwic2h1ZmZsZVN0cmluZyIsImNoYXJhY3RlcnMiLCJzdHIiLCJhcnJheSIsInNwbGl0IiwiaiIsImpvaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/passwordGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();