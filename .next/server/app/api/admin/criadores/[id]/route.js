/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/criadores/[id]/route";
exports.ids = ["app/api/admin/criadores/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_criadores_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/criadores/[id]/route.ts */ \"(rsc)/./src/app/api/admin/criadores/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/criadores/[id]/route\",\n        pathname: \"/api/admin/criadores/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/criadores/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/admin/criadores/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_criadores_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/criadores/[id]/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/admin/criadores/[id]/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Inicializar cliente Supabase com a chave de serviço\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://pbehloddlzwandfmpzbo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Headers CORS para permitir acesso à API\nconst corsHeaders = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n};\n// OPTIONS - Para lidar com preflight requests do CORS\nasync function OPTIONS() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}, {\n        headers: corsHeaders\n    });\n}\n// GET - Obter um criador específico\nasync function GET(request, { params }) {\n    const id = params.id;\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Validar ID\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID do criador é obrigatório'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Buscar criador\n        const { data: criador, error: criadorError } = await supabase.from('influencers').select('*').eq('id', id).single();\n        if (criadorError) {\n            console.error('Erro ao buscar criador:', criadorError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao buscar criador: ${criadorError.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!criador) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Criador não encontrado'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        // Buscar perfil do criador\n        const { data: profile, error: profileError } = await supabase.from('influencer_profiles').select('*').eq('id', id).single();\n        if (profileError && !profileError.message.includes('No rows found')) {\n            console.error('Erro ao buscar perfil do criador:', profileError);\n        }\n        // Buscar email do usuário\n        const { data: user, error: userError } = await supabase.from('profiles').select('email').eq('id', id).single();\n        if (userError && !userError.message.includes('No rows found')) {\n            console.error('Erro ao buscar email do usuário:', userError);\n        }\n        // Combinar dados\n        const result = {\n            ...criador,\n            email: user?.email || null,\n            profile: profile || null\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result, {\n            headers\n        });\n    } catch (err) {\n        console.error('Erro inesperado:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro interno do servidor: ${err.message}`\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n// PUT - Atualizar um criador específico\nasync function PUT(request, { params }) {\n    const id = params.id;\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Validar ID\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID do criador é obrigatório'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Obter dados do corpo da requisição\n        const criadorData = await request.json();\n        // Validar dados obrigatórios\n        if (!criadorData.name || !criadorData.username) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Nome e nome de usuário são obrigatórios'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Verificar se o criador existe\n        const { data: existingCriador, error: checkError } = await supabase.from('influencers').select('id').eq('id', id).single();\n        if (checkError) {\n            console.error('Erro ao verificar criador:', checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao verificar criador: ${checkError.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!existingCriador) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Criador não encontrado'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        try {\n            // Atualizar perfil do usuário se o email foi alterado\n            if (criadorData.email) {\n                const { error: updateProfileError } = await supabase.from('profiles').update({\n                    full_name: criadorData.name,\n                    email: criadorData.email,\n                    updated_at: new Date().toISOString()\n                }).eq('id', id);\n                if (updateProfileError) {\n                    console.error('Erro ao atualizar perfil:', updateProfileError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao atualizar perfil: ${updateProfileError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            }\n            // Atualizar criador\n            const { error: updateError } = await supabase.from('influencers').update({\n                name: criadorData.name,\n                username: criadorData.username,\n                classification: criadorData.classification || 'Standard',\n                updated_at: new Date().toISOString()\n            }).eq('id', id);\n            if (updateError) {\n                console.error('Erro ao atualizar criador:', updateError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Erro ao atualizar criador: ${updateError.message}`\n                }, {\n                    status: 500,\n                    headers\n                });\n            }\n            // Verificar se o perfil de criador existe\n            const { data: existingProfile } = await supabase.from('influencer_profiles').select('id').eq('id', id).single();\n            const profile = criadorData.profile || {};\n            if (existingProfile) {\n                // Atualizar perfil de criador existente\n                const { error: updateProfileError } = await supabase.from('influencer_profiles').update({\n                    bio: profile.bio || '',\n                    content_niche: profile.content_niche || [],\n                    primary_platform: profile.primary_platform || 'instagram',\n                    instagram_username: profile.instagram_username || '',\n                    tiktok_username: profile.tiktok_username || '',\n                    location_city: profile.location_city || '',\n                    location_state: profile.location_state || '',\n                    avg_engagement_rate: profile.avg_engagement_rate || 0,\n                    follower_count: profile.follower_count || 0,\n                    updated_at: new Date().toISOString()\n                }).eq('id', id);\n                if (updateProfileError) {\n                    console.error('Erro ao atualizar perfil de criador:', updateProfileError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao atualizar perfil de criador: ${updateProfileError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            } else {\n                // Criar perfil de criador se não existir\n                const { error: createProfileError } = await supabase.from('influencer_profiles').insert({\n                    id: id,\n                    bio: profile.bio || '',\n                    content_niche: profile.content_niche || [],\n                    primary_platform: profile.primary_platform || 'instagram',\n                    instagram_username: profile.instagram_username || '',\n                    tiktok_username: profile.tiktok_username || '',\n                    location_city: profile.location_city || '',\n                    location_state: profile.location_state || '',\n                    avg_engagement_rate: profile.avg_engagement_rate || 0,\n                    follower_count: profile.follower_count || 0,\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                });\n                if (createProfileError) {\n                    console.error('Erro ao criar perfil de criador:', createProfileError);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `Erro ao criar perfil de criador: ${createProfileError.message}`\n                    }, {\n                        status: 500,\n                        headers\n                    });\n                }\n            }\n            // Retornar sucesso\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: 'Criador atualizado com sucesso',\n                id: id\n            }, {\n                headers\n            });\n        } catch (updateError) {\n            console.error('Erro ao atualizar criador:', updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao atualizar criador: ${updateError.message}`,\n                details: updateError\n            }, {\n                status: 500,\n                headers\n            });\n        }\n    } catch (err) {\n        console.error('Erro inesperado:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro interno do servidor: ${err.message}`,\n            stack: err.stack,\n            name: err.name,\n            code: err.code\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n// DELETE - Excluir um criador específico\nasync function DELETE(request, { params }) {\n    const id = params.id;\n    try {\n        // Adicionar headers CORS à resposta\n        const headers = {\n            ...corsHeaders,\n            'Content-Type': 'application/json'\n        };\n        // Validar ID\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID do criador é obrigatório'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Verificar se o criador existe\n        const { data: existingCriador, error: checkError } = await supabase.from('influencers').select('id').eq('id', id).single();\n        if (checkError) {\n            console.error('Erro ao verificar criador:', checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao verificar criador: ${checkError.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!existingCriador) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Criador não encontrado'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        // Excluir perfil de criador (as chaves estrangeiras devem ter ON DELETE CASCADE)\n        const { error: profileError } = await supabase.from('influencer_profiles').delete().eq('id', id);\n        if (profileError && !profileError.message.includes('No rows found')) {\n            console.error('Erro ao excluir perfil de criador:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao excluir perfil de criador: ${profileError.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        // Excluir criador\n        const { error: deleteError } = await supabase.from('influencers').delete().eq('id', id);\n        if (deleteError) {\n            console.error('Erro ao excluir criador:', deleteError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao excluir criador: ${deleteError.message}`\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        // Retornar sucesso\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Criador excluído com sucesso'\n        }, {\n            headers\n        });\n    } catch (err) {\n        console.error('Erro inesperado:', err);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro interno do servidor: ${err.message}`\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/criadores/[id]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@supabase","vendor-chunks/next","vendor-chunks/tr46","vendor-chunks/whatwg-url"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&page=%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcriadores%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();