/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/campaigns/[campaign_id]/route";
exports.ids = ["app/api/admin/campaigns/[campaign_id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&page=%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&page=%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_campaigns_campaign_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/campaigns/[campaign_id]/route.ts */ \"(rsc)/./src/app/api/admin/campaigns/[campaign_id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/campaigns/[campaign_id]/route\",\n        pathname: \"/api/admin/campaigns/[campaign_id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/campaigns/[campaign_id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/api/admin/campaigns/[campaign_id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_luizvincenzi_Documents_AI_Projects_Criadores_src_app_api_admin_campaigns_campaign_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&page=%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/campaigns/[campaign_id]/route.ts":
/*!************************************************************!*\
  !*** ./src/app/api/admin/campaigns/[campaign_id]/route.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   corsHeaders: () => (/* binding */ corsHeaders)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// import { createServerSupabaseClient } from '@/lib/supabase/server'; // Commented out\n // Added for direct client creation\n// Configuração do Supabase (similar to restaurants API route)\nconst supabaseUrl = \"https://pbehloddlzwandfmpzbo.supabase.co\" || 0;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';\n// Configurar CORS para permitir requisições da mesma origem\nconst corsHeaders = {\n    'Access-Control-Allow-Origin': '*',\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n};\n// Handler para requisições OPTIONS (preflight CORS)\nasync function OPTIONS() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({}, {\n        headers: corsHeaders\n    });\n}\n// GET - Buscar uma campanha específica\nasync function GET(request// Context parameter removed\n) {\n    // Adicionar headers CORS à resposta\n    const headers = {\n        ...corsHeaders,\n        'Content-Type': 'application/json'\n    };\n    try {\n        const url = new URL(request.url);\n        const pathSegments = url.pathname.split('/');\n        const campaign_id = pathSegments[pathSegments.length - 1]; // Extract from URL\n        if (!campaign_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID da campanha não fornecido'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        console.log('Buscando campanha com ID:', campaign_id);\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey); // Use direct client\n        // Buscar campanha\n        const { data, error } = await supabase.from('campaigns').select('*').eq('id', campaign_id).single();\n        if (error) {\n            console.error('Erro ao buscar campanha:', error);\n            // Se o erro for que a campanha não existe, retornar 404 em vez de 500\n            if (error.code === 'PGRST116') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Campanha não encontrada',\n                    code: 'NOT_FOUND'\n                }, {\n                    status: 404,\n                    headers\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao buscar campanha: ${error.message}`,\n                code: error.code\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Campanha não encontrada'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            data\n        }, {\n            status: 200,\n            headers\n        });\n    } catch (error) {\n        console.error('Erro ao processar requisição:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro interno do servidor: ${error.message}`\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n// PUT - Atualizar uma campanha existente\nasync function PUT(request// Context parameter removed\n) {\n    // Adicionar headers CORS à resposta\n    const headers = {\n        ...corsHeaders,\n        'Content-Type': 'application/json'\n    };\n    try {\n        const url = new URL(request.url);\n        const pathSegments = url.pathname.split('/');\n        const campaign_id = pathSegments[pathSegments.length - 1]; // Extract from URL\n        if (!campaign_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID da campanha não fornecido'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        // Obter dados da requisição\n        const campaignData = await request.json();\n        console.log('Atualizando campanha:', campaign_id, campaignData);\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey); // Use direct client\n        // Verificar se a campanha existe\n        const { data: existingCampaign, error: checkError } = await supabase.from('campaigns').select('id, restaurant_id').eq('id', campaign_id).single();\n        if (checkError) {\n            console.error('Erro ao verificar campanha:', checkError);\n            // Se o erro for que a campanha não existe, retornar 404 em vez de 500\n            if (checkError.code === 'PGRST116') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Campanha não encontrada',\n                    code: 'NOT_FOUND'\n                }, {\n                    status: 404,\n                    headers\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao verificar campanha: ${checkError.message}`,\n                code: checkError.code\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!existingCampaign) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Campanha não encontrada'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        // Verificar se o restaurante existe\n        if (campaignData.restaurant_id) {\n            // Se o restaurante_id for diferente do original, verificar se o novo restaurante existe\n            if (campaignData.restaurant_id !== existingCampaign.restaurant_id) {\n                const { data: restaurant, error: restaurantError } = await supabase.from('restaurant_profiles').select('id').eq('id', campaignData.restaurant_id).single();\n                if (restaurantError || !restaurant) {\n                    console.error('Restaurante não encontrado:', restaurantError);\n                    // Se o restaurante não existir, criar um restaurante de teste\n                    console.log('Criando restaurante de teste');\n                    const { data: newRestaurant, error: createRestaurantError } = await supabase.from('restaurant_profiles').insert({\n                        id: campaignData.restaurant_id,\n                        business_name: 'Restaurante de Teste',\n                        description: 'Este é um restaurante de teste',\n                        instagram_url: 'https://instagram.com/restaurante_teste',\n                        city: 'São Paulo',\n                        state: 'SP',\n                        created_at: new Date().toISOString()\n                    }).select('id').single();\n                    if (createRestaurantError) {\n                        console.error('Erro ao criar restaurante de teste:', createRestaurantError);\n                        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                            error: `Erro ao criar restaurante de teste: ${createRestaurantError.message}`,\n                            code: createRestaurantError.code\n                        }, {\n                            status: 500,\n                            headers\n                        });\n                    }\n                    console.log('Restaurante de teste criado:', newRestaurant.id);\n                }\n            }\n        }\n        // Adicionar data de atualização\n        campaignData.updated_at = new Date().toISOString();\n        // Atualizar campanha no banco de dados\n        const { data, error } = await supabase.from('campaigns').update(campaignData).eq('id', campaign_id).select('*').single();\n        if (error) {\n            console.error('Erro ao atualizar campanha:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao atualizar campanha: ${error.message}`,\n                code: error.code\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        console.log('Campanha atualizada com sucesso:', data.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Campanha atualizada com sucesso',\n            data\n        }, {\n            status: 200,\n            headers\n        });\n    } catch (error) {\n        console.error('Erro ao processar requisição:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro ao processar requisição: ${error instanceof Error ? error.message : 'Erro desconhecido'}`\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n// DELETE - Excluir uma campanha\nasync function DELETE(request// Context parameter removed\n) {\n    // Adicionar headers CORS à resposta\n    const headers = {\n        ...corsHeaders,\n        'Content-Type': 'application/json'\n    };\n    try {\n        const url = new URL(request.url);\n        const pathSegments = url.pathname.split('/');\n        const campaign_id = pathSegments[pathSegments.length - 1]; // Extract from URL\n        if (!campaign_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'ID da campanha não fornecido'\n            }, {\n                status: 400,\n                headers\n            });\n        }\n        const supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceKey); // Use direct client\n        console.log('Excluindo campanha:', campaign_id);\n        // Verificar se a campanha existe\n        const { data: existingCampaign, error: checkError } = await supabase.from('campaigns').select('id').eq('id', campaign_id).single();\n        if (checkError) {\n            console.error('Erro ao verificar campanha:', checkError);\n            // Se o erro for que a campanha não existe, retornar 404 em vez de 500\n            if (checkError.code === 'PGRST116') {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Campanha não encontrada',\n                    code: 'NOT_FOUND'\n                }, {\n                    status: 404,\n                    headers\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao verificar campanha: ${checkError.message}`,\n                code: checkError.code\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        if (!existingCampaign) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Campanha não encontrada'\n            }, {\n                status: 404,\n                headers\n            });\n        }\n        // Excluir campanha do banco de dados\n        const { error } = await supabase.from('campaigns').delete().eq('id', campaign_id);\n        if (error) {\n            console.error('Erro ao excluir campanha:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Erro ao excluir campanha: ${error.message}`,\n                code: error.code\n            }, {\n                status: 500,\n                headers\n            });\n        }\n        console.log('Campanha excluída com sucesso:', campaign_id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Campanha excluída com sucesso',\n            id: campaign_id\n        }, {\n            status: 200,\n            headers\n        });\n    } catch (error) {\n        console.error('Erro ao processar requisição:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Erro ao processar requisição: ${error instanceof Error ? error.message : 'Erro desconhecido'}`\n        }, {\n            status: 500,\n            headers: corsHeaders\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/campaigns/[campaign_id]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&page=%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcampaigns%2F%5Bcampaign_id%5D%2Froute.ts&appDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fluizvincenzi%2FDocuments%2FAI_Projects%2FCriadores&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();