/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/planDetailsPopup.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* Estilos específicos para o popup de Detalhes do Plano */

/* Estilo para o cabeçalho e menus */
.plan-details-tabs {
  background-color: #f9fafb !important;
}

/* Estilo para as abas */
.plan-details-tabs [role="tab"] {
  background-color: #f9fafb !important;
  padding: 0 1rem !important;
  height: 36px !important;
  font-size: 0.875rem !important;
  border-radius: 0 !important;
  transition: all 0.2s ease !important;
  color: #6b7280 !important;
  font-weight: normal !important;
  border-bottom: 2px solid transparent !important;
}

/* Estilo para abas ativas */
.plan-details-tabs [role="tab"][data-state="active"] {
  background-color: #f9fafb !important;
  box-shadow: none !important;
  border-bottom: 2px solid #1f2937 !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
}

/* Estilo para o container das abas */
.plan-details-tabs [role="tablist"] {
  background-color: #f9fafb !important;
  padding: 0 !important;
  margin: 0 !important;
  height: 36px !important;
  border-bottom: 1px solid #e5e7eb !important;
}

