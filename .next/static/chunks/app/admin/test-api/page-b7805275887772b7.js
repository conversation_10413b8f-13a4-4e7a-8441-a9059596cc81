(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{23300:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(95155),a=t(12115);function n(){let[e,s]=(0,a.useState)(!1),[t,n]=(0,a.useState)(null),[o,d]=(0,a.useState)(null),i=async()=>{try{s(!0),n(null),d(null);let e=await fetch("/api/test"),t=await e.text();console.log("Texto da resposta:",t);try{let s=JSON.parse(t);d({status:e.status,headers:Object.fromEntries(e.headers.entries()),body:s})}catch(e){throw Error("Resposta n\xe3o \xe9 um JSON v\xe1lido: ".concat(t.substring(0,100),"..."))}}catch(e){console.error("Erro ao testar API (GET):",e),n(e.message||"Erro desconhecido")}finally{s(!1)}},l=async()=>{try{s(!0),n(null),d(null);let e={name:"Teste",email:"<EMAIL>",timestamp:new Date().toISOString()},t=await fetch("/api/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await t.text();console.log("Texto da resposta:",r);try{let e=JSON.parse(r);d({status:t.status,headers:Object.fromEntries(t.headers.entries()),body:e})}catch(e){throw Error("Resposta n\xe3o \xe9 um JSON v\xe1lido: ".concat(r.substring(0,100),"..."))}}catch(e){console.error("Erro ao testar API (POST):",e),n(e.message||"Erro desconhecido")}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Teste de API"}),t&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:t})})]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:i,disabled:e,className:"flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:e?"Testando...":"Testar GET"}),(0,r.jsx)("button",{onClick:l,disabled:e,className:"flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:e?"Testando...":"Testar POST"})]}),o&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Resposta:"}),(0,r.jsxs)("div",{className:"bg-gray-100 p-3 rounded-md text-xs font-mono text-gray-800 max-h-60 overflow-y-auto",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Status:"})," ",o.status]}),(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Headers:"})}),(0,r.jsx)("pre",{children:JSON.stringify(o.headers,null,2)}),(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Body:"})}),(0,r.jsx)("pre",{children:JSON.stringify(o.body,null,2)})]})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)("a",{href:"/admin/add-client-simple",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Ir para Adicionar Cliente (Simplificado)"})})]})]})})}},29910:(e,s,t)=>{Promise.resolve().then(t.bind(t,23300))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(29910)),_N_E=e.O()}]);