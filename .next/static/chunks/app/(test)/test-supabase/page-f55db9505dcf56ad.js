(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[594],{32653:(e,a,r)=>{Promise.resolve().then(r.bind(r,58242))},58242:(e,a,r)=>{"use strict";r.d(a,{default:()=>c});var s=r(95155),o=r(12115),n=r(73579);function c(){let[e,a]=(0,o.useState)(null),[r,c]=(0,o.useState)(null),[t,l]=(0,o.useState)(!0),i=(0,n.createClientComponentClient)();return(0,o.useEffect)(()=>{(async function(){try{l(!0);let{data:e,error:r}=await i.from("restaurants").select("*").limit(5),{data:s,error:o}=await i.from("campaigns").select("*, restaurant:restaurant_id(name, id)").limit(10),{data:n,error:t}=await i.from("campaign_influencers").select("*").limit(10),{data:d,error:u}=await i.from("influencers").select("*").limit(10);r?(console.error("Erro ao buscar restaurants:",r),c("Erro ao buscar restaurants: ".concat(r.message))):o?(console.error("Erro ao buscar campaigns:",o),c("Erro ao buscar campaigns: ".concat(o.message))):t?(console.error("Erro ao buscar campaign_influencers:",t),c("Erro ao buscar campaign_influencers: ".concat(t.message))):u?(console.error("Erro ao buscar influencers:",u),c("Erro ao buscar influencers: ".concat(u.message))):(console.log("Dados de restaurants:",e),console.log("Dados de campaigns:",s),console.log("Dados de campaign_influencers:",n),console.log("Dados de influencers:",d),a({restaurants:e,campaigns:s,campaign_influencers:n,influencers:d}))}catch(e){console.error("Exce\xe7\xe3o ao buscar dados:",e),c("Exce\xe7\xe3o: ".concat(e))}finally{l(!1)}})()},[i]),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h1",{className:"text-xl font-bold mb-4",children:"Teste de Acesso ao Supabase"}),t?(0,s.jsx)("p",{children:"Carregando..."}):r?(0,s.jsxs)("div",{className:"bg-red-100 p-4 rounded-md",children:[(0,s.jsx)("h2",{className:"text-red-700 font-bold",children:"Erro:"}),(0,s.jsx)("p",{className:"text-red-700",children:r})]}):(0,s.jsxs)("div",{className:"bg-green-100 p-4 rounded-md",children:[(0,s.jsx)("h2",{className:"text-green-700 font-bold",children:"Dados obtidos com sucesso:"}),(0,s.jsx)("pre",{className:"mt-2 bg-white p-2 rounded overflow-auto max-h-96",children:JSON.stringify(e,null,2)})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[9724,3579,8441,1684,7358],()=>a(32653)),_N_E=e.O()}]);