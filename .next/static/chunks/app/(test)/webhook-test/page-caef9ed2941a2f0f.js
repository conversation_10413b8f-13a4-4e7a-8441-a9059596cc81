(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[181],{15691:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(95155),n=a(12115);function i(){let[e,t]=(0,n.useState)(""),[a,i]=(0,n.useState)(!1),c=async()=>{i(!0);try{let e=await fetch("/api/instagram-webhook?hub.mode=subscribe&hub.verify_token=streetbrand_webhook_verify_token&hub.challenge=challenge_accepted"),a=await e.text();t("Status: ".concat(e.status,"\nResponse: ").concat(a))}catch(e){t("Error: ".concat(e instanceof Error?e.message:String(e)))}finally{i(!1)}},r=async()=>{i(!0);try{let e=await fetch("/api/instagram-webhook",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({entry:[{changes:[{field:"media",value:{media_id:"123456789",user_id:"987654321",media_type:"IMAGE",permalink:"https://instagram.com/p/test"}}]}]})}),a=await e.text();t("Status: ".concat(e.status,"\nResponse: ").concat(a))}catch(e){t("Error: ".concat(e instanceof Error?e.message:String(e)))}finally{i(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto p-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Instagram Webhook Test"}),(0,s.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,s.jsx)("button",{onClick:c,disabled:a,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:"Test GET (Verification)"}),(0,s.jsx)("button",{onClick:r,disabled:a,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50",children:"Test POST (Webhook)"})]}),a&&(0,s.jsx)("p",{className:"mb-4",children:"Loading..."}),e&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Result:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded whitespace-pre-wrap",children:e})]})]})}},94947:(e,t,a)=>{Promise.resolve().then(a.bind(a,15691))}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(94947)),_N_E=e.O()}]);