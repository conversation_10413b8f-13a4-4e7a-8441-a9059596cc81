(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5609],{13637:(e,s,r)=>{Promise.resolve().then(r.bind(r,42911))},42911:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});var a=r(95155),i=r(12115),l=r(99724);function t(){let[e,s]=(0,i.useState)(""),[r,t]=(0,i.useState)("Test@123456"),[o,d]=(0,i.useState)(!1),[n,c]=(0,i.useState)(null),[m,u]=(0,i.useState)(null),[x,h]=(0,i.useState)([]),g=e=>{h(s=>[...s,"".concat(new Date().toISOString().split("T")[1].split(".")[0],": ").concat(e)])},p=async()=>{try{d(!0),c(null),u(null),h([]),g("Iniciando processo de registro para ".concat(e));let s="https://pbehloddlzwandfmpzbo.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38";if(!s||!a)throw Error("Vari\xe1veis de ambiente n\xe3o configuradas corretamente");g("Criando cliente Supabase com URL: ".concat(s.substring(0,20),"..."));let i=(0,l.UU)(s,a);g("Tentando registrar usu\xe1rio...");let{data:t,error:o}=await i.auth.signUp({email:e,password:r});if(o)throw g("Erro ao registrar: ".concat(o.message)),Error("Erro ao registrar: ".concat(o.message));if(!t.user)throw g("Nenhum usu\xe1rio retornado na resposta"),Error("Nenhum usu\xe1rio retornado na resposta");g("Usu\xe1rio registrado com sucesso! ID: ".concat(t.user.id)),u({message:"Usu\xe1rio registrado com sucesso!",user:{id:t.user.id,email:t.user.email,created_at:t.user.created_at}})}catch(e){console.error("Erro:",e),c(e.message||"Ocorreu um erro desconhecido")}finally{d(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-md w-full",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Registro M\xednimo"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("input",{type:"email",id:"email",value:e,onChange:e=>s(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Senha"}),(0,a.jsx)("input",{type:"text",id:"password",value:r,onChange:e=>t(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Senha",required:!0})]}),(0,a.jsx)("button",{onClick:p,disabled:o||!e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:o?"Registrando...":"Registrar Usu\xe1rio"}),n&&(0,a.jsx)("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:n})})]})}),m&&(0,a.jsx)("div",{className:"bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm text-green-700",children:m.message}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-green-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"ID:"})," ",m.user.id]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",m.user.email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Criado em:"})," ",m.user.created_at]})]})]})]})}),x.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Logs:"}),(0,a.jsx)("div",{className:"bg-gray-100 p-3 rounded-md text-xs font-mono text-gray-800 max-h-40 overflow-y-auto",children:x.map((e,s)=>(0,a.jsx)("div",{className:"mb-1",children:e},s))})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9724,8441,1684,7358],()=>s(13637)),_N_E=e.O()}]);