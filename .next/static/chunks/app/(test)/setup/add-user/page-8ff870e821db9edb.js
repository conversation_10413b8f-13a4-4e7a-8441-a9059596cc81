(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3354],{94764:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var r=a(95155),i=a(12115),l=a(73579);function n(){let[e,s]=(0,i.useState)(!1),[a,n]=(0,i.useState)(null),[t,o]=(0,i.useState)(null),c=(0,l.createClientComponentClient)(),d=async()=>{try{s(!0),n(null),o(null);let{data:e,error:a}=await c.auth.signUp({email:"<EMAIL>",password:"Test@123456",options:{emailRedirectTo:"".concat(window.location.origin,"/login")}});if(a)throw Error("Erro ao criar usu\xe1rio: ".concat(a.message));if(!e.user)throw Error("Usu\xe1rio n\xe3o foi criado");let r=e.user.id,{error:i}=await c.from("profiles").insert({id:r,role:"influencer",full_name:"Gustavo Caliani",email:"<EMAIL>",password_changed:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});if(i)throw Error("Erro ao criar perfil: ".concat(i.message));let{error:l}=await c.from("influencers").insert({id:r,name:"Gustavo Caliani",username:"gustavucaliani",classification:"Gold",conversions:75,total_earnings:1500,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});l&&console.error("Erro ao criar influenciador:",l);let{error:t}=await c.from("influencer_profiles").insert({id:r,bio:"Food and lifestyle influencer",content_niche:["food","lifestyle"],primary_platform:"instagram",instagram_username:"gustavucaliani",location_city:"S\xe3o Paulo",location_state:"SP",avg_engagement_rate:4.5,follower_count:15e3,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});t&&console.error("Erro ao criar perfil de influenciador:",t),o({message:"Usu\xe1rio criado com sucesso!",user:{id:r,email:"<EMAIL>",password:"Test@123456"}})}catch(e){console.error("Erro ao adicionar usu\xe1rio:",e),n(e.message||"Erro desconhecido")}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-md w-full",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Adicionar Usu\xe1rio de Teste"}),a&&(0,r.jsx)("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:a})})]})}),t&&(0,r.jsx)("div",{className:"bg-green-50 border-l-4 border-green-500 p-4 mb-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm text-green-700",children:t.message}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-green-600",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"ID:"})," ",t.user.id]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",t.user.email]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Senha:"})," ",t.user.password]})]})]})]})}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-6",children:[(0,r.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Adicionar Usu\xe1rio Gustavucaliani"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Este processo ir\xe1 criar o usu\xe1rio <NAME_EMAIL> no sistema."}),(0,r.jsxs)("div",{className:"bg-gray-100 p-3 rounded-lg mb-4",children:[(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Senha:"})," Test@123456"]}),(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Nome:"})," Gustavo Caliani"]}),(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Papel:"})," Influenciador"]})]}),(0,r.jsx)("button",{onClick:d,disabled:e,className:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50",children:e?"Adicionando...":"Adicionar Usu\xe1rio"})]}),(0,r.jsx)("div",{className:"text-center text-sm text-gray-500",children:(0,r.jsx)("p",{children:"Esta p\xe1gina \xe9 apenas para fins de teste e desenvolvimento."})})]})})}},99932:(e,s,a)=>{Promise.resolve().then(a.bind(a,94764))}},e=>{var s=s=>e(e.s=s);e.O(0,[9724,3579,8441,1684,7358],()=>s(99932)),_N_E=e.O()}]);