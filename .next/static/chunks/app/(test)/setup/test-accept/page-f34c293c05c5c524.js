(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7458],{79412:(e,s,o)=>{Promise.resolve().then(o.bind(o,82116))},82116:(e,s,o)=>{"use strict";o.r(s),o.d(s,{default:()=>l});var r=o(95155),n=o(12115),t=o(6874),a=o.n(t);function l(){let[e,s]=(0,n.useState)(!1),[o,t]=(0,n.useState)(null),[l,d]=(0,n.useState)(null),[i,c]=(0,n.useState)({token:"",password:"",name:"",phone:""}),m=e=>{let{name:s,value:o}=e.target;c(e=>({...e,[s]:o}))},u=async e=>{e.preventDefault(),s(!0),t(null),d(null);try{let e;console.log("Enviando dados:",i);let s=await fetch("/api/invite/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});console.log("Status da resposta:",s.status);let o=await s.text();console.log("Texto da resposta:",o);try{e=JSON.parse(o),console.log("Dados da resposta:",e)}catch(e){throw console.error("Erro ao analisar JSON:",e),Error("Resposta n\xe3o \xe9 um JSON v\xe1lido: ".concat(o.substring(0,100),"..."))}if(!s.ok)throw Error(e.error||"Erro ao aceitar convite");d(e)}catch(e){console.error("Erro ao aceitar convite:",e),t(e.message||"Ocorreu um erro ao aceitar o convite")}finally{s(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Teste de Aceita\xe7\xe3o de Convite"}),o&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:o})})]})}),l&&(0,r.jsx)("div",{className:"mb-4 bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm text-green-700",children:l.message}),(0,r.jsx)("div",{className:"mt-2 text-xs text-green-600",children:(0,r.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(l,null,2)})})]})]})}),(0,r.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"token",className:"block text-sm font-medium text-gray-700",children:"Token do Convite"}),(0,r.jsx)("input",{type:"text",name:"token",id:"token",value:i.token,onChange:m,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Token do convite"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Senha"}),(0,r.jsx)("input",{type:"password",name:"password",id:"password",value:i.password,onChange:m,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Senha"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Nome Completo (opcional)"}),(0,r.jsx)("input",{type:"text",name:"name",id:"name",value:i.name,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nome Completo"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Telefone (opcional)"}),(0,r.jsx)("input",{type:"tel",name:"phone",id:"phone",value:i.phone,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"(00) 00000-0000"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:e?"Aceitando...":"Aceitar Convite"})})]}),(0,r.jsxs)("div",{className:"mt-6 flex space-x-4",children:[(0,r.jsx)(a(),{href:"/setup/test-flow",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Teste Completo"}),(0,r.jsx)(a(),{href:"/admin/add-client",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Adicionar Cliente"})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(79412)),_N_E=e.O()}]);