(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3761],{18663:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var n=r(95155),a=r(12115),t=r(35695);function l(){let[e,s]=(0,a.useState)(!1),[r,l]=(0,a.useState)(null),[i,o]=(0,a.useState)(null),d=(0,t.useRouter)(),c=async()=>{try{s(!0),l(null),o(null);let e={email:"<EMAIL>",fullName:"Usu\xe1rio de Teste",role:"restaurant",phone:"(11) 99999-9999"};console.log("Enviando dados:",e);let r=await fetch("/api/admin/add-client",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),n=await r.json();if(console.log("Resposta recebida:",n),!r.ok)throw Error(n.error||"Erro ao adicionar cliente");o(n)}catch(e){console.error("Erro ao adicionar cliente:",e),l(e.message||"Erro desconhecido")}finally{s(!1)}};return(0,n.jsx)("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-md w-full",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Teste Simples - Adicionar Cliente"}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,n.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Adicionar Cliente de Teste"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Este processo ir\xe1 adicionar um cliente de teste com dados fixos."}),(0,n.jsxs)("div",{className:"bg-gray-100 p-3 rounded-lg mb-4",children:[(0,n.jsxs)("p",{className:"text-sm",children:[(0,n.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,n.jsxs)("p",{className:"text-sm",children:[(0,n.jsx)("strong",{children:"Nome:"})," Usu\xe1rio de Teste"]}),(0,n.jsxs)("p",{className:"text-sm",children:[(0,n.jsx)("strong",{children:"Tipo:"})," Restaurante"]}),(0,n.jsxs)("p",{className:"text-sm",children:[(0,n.jsx)("strong",{children:"Telefone:"})," (11) 99999-9999"]})]}),(0,n.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50",children:e?"Adicionando...":"Adicionar Cliente"})]}),r&&(0,n.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg text-red-700",children:[(0,n.jsx)("p",{className:"font-semibold",children:"Erro:"}),(0,n.jsx)("p",{children:r})]}),i&&(0,n.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,n.jsx)("p",{className:"font-semibold text-green-700 mb-2",children:i.message}),i.user&&(0,n.jsxs)("div",{className:"text-sm text-gray-700 space-y-1 mb-4",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"ID:"})," ",i.user.id]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Email:"})," ",i.user.email]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Senha:"})," ",i.user.password]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Nome:"})," ",i.user.name]})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("button",{onClick:()=>window.location.href=i.loginUrl,className:"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-200",children:"Ir para Login"})})]}),(0,n.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,n.jsx)("button",{onClick:()=>d.push("/admin/add-client"),className:"w-full bg-gray-200 text-gray-700 py-2 rounded-lg hover:bg-gray-300 transition duration-200",children:"Voltar para Formul\xe1rio Completo"})})]})]})})}},29853:(e,s,r)=>{Promise.resolve().then(r.bind(r,18663))},35695:(e,s,r)=>{"use strict";var n=r(18999);r.o(n,"useParams")&&r.d(s,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(s,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(s,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(s,{useSearchParams:function(){return n.useSearchParams}})}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(29853)),_N_E=e.O()}]);