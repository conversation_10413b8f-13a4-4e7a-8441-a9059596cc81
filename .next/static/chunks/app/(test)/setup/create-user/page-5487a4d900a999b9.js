(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7381],{43249:(e,s,r)=>{Promise.resolve().then(r.bind(r,64227))},64227:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var a=r(95155),l=r(12115);function i(){let[e,s]=(0,l.useState)(!1),[r,i]=(0,l.useState)(null),[t,n]=(0,l.useState)(null),c=async()=>{try{s(!0),i(null),n(null);let e=await fetch("/api/setup/create-user"),r=await e.json();if(!e.ok)throw console.error("Erro na resposta:",r),Error(r.error||"Falha ao criar o usu\xe1rio");n(r)}catch(e){console.error("Erro ao criar usu\xe1rio:",e),i(e.message||"Erro desconhecido")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-md w-full",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Criar Usu\xe1rio de Teste"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Criar Usu\xe1rio Gustavucaliani"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Este processo ir\xe1 criar o usu\xe1rio <NAME_EMAIL> no sistema de autentica\xe7\xe3o do Supabase."}),(0,a.jsxs)("div",{className:"bg-gray-100 p-3 rounded-lg mb-4",children:[(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Senha:"})," Test@123456"]}),(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Nome:"})," Gustavo Caliani"]})]}),(0,a.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50",children:e?"Criando...":"Criar Usu\xe1rio"})]}),r&&(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg text-red-700",children:[(0,a.jsx)("p",{className:"font-semibold",children:"Erro:"}),(0,a.jsx)("p",{children:r})]}),t&&(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-green-700 mb-2",children:t.message}),t.user&&(0,a.jsxs)("div",{className:"text-sm text-gray-700 space-y-1 mb-4",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"ID:"})," ",t.user.id]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",t.user.email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Senha:"})," ",t.user.password]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Nome:"})," ",t.user.name]})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("button",{onClick:()=>window.location.href=t.loginUrl,className:"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-200",children:"Ir para Login"})})]}),(0,a.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"Esta p\xe1gina s\xf3 deve ser usada em ambiente de desenvolvimento."})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(43249)),_N_E=e.O()}]);