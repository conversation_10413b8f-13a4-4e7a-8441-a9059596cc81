(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3082],{25422:(e,s,r)=>{Promise.resolve().then(r.bind(r,50884))},35695:(e,s,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},50884:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(95155),n=r(12115),t=r(35695);function o(){let[e,s]=(0,n.useState)(!1),[r,o]=(0,n.useState)(null),[i,l]=(0,n.useState)(null),c=(0,t.useRouter)(),d=async()=>{try{s(!0),o(null),l(null);let e=await fetch("/api/setup/add-gustavucaliani"),r=await e.json();if(!e.ok)throw console.error("Erro na resposta:",r),Error(r.error||"Falha ao configurar o usu\xe1rio");l(r)}catch(e){console.error("Erro ao configurar:",e),o(e.message||"Erro desconhecido")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-md w-full",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Configura\xe7\xe3o do Sistema"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Adicionar Usu\xe1rio Gustavucaliani"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Este processo ir\xe1 adicionar o usu\xe1rio <NAME_EMAIL> ao banco de dados com todas as configura\xe7\xf5es necess\xe1rias para testar a integra\xe7\xe3o com Instagram."}),(0,a.jsx)("button",{onClick:d,disabled:e,className:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50",children:e?"Configurando...":"Configurar Usu\xe1rio"})]}),r&&(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg text-red-700",children:[(0,a.jsx)("p",{className:"font-semibold",children:"Erro:"}),(0,a.jsx)("p",{children:r})]}),i&&(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-green-700 mb-2",children:"Usu\xe1rio configurado com sucesso!"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"ID:"})," ",i.user.id]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",i.user.email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Senha:"})," ",i.user.password]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Nome:"})," ",i.user.name]})]}),(0,a.jsxs)("div",{className:"mt-4 flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>window.location.href=i.loginUrl,className:"flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-200",children:"Ir para Login"}),(0,a.jsx)("button",{onClick:()=>c.push("/demo/instagram-integration"),className:"flex-1 bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition duration-200",children:"Ver Demo"})]})]}),(0,a.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"Esta p\xe1gina s\xf3 deve ser usada em ambiente de desenvolvimento."})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(25422)),_N_E=e.O()}]);