(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9682],{12644:(e,o,s)=>{"use strict";s.r(o),s.d(o,{default:()=>t});var r=s(95155),n=s(12115),i=s(6874),a=s.n(i);function t(){let[e,o]=(0,n.useState)(!1),[s,i]=(0,n.useState)(null),[t,l]=(0,n.useState)(null),[d,c]=(0,n.useState)("invite"),[m,u]=(0,n.useState)({email:"",role:"influencer",name:""}),[x,h]=(0,n.useState)({token:"",password:"",name:"",phone:""}),[g,p]=(0,n.useState)({email:"",password:""}),f=e=>{let{name:o,value:s}=e.target;u(e=>({...e,[o]:s}))},b=e=>{let{name:o,value:s}=e.target;h(e=>({...e,[o]:s}))},y=e=>{let{name:o,value:s}=e.target;p(e=>({...e,[o]:s}))},v=async e=>{e.preventDefault(),o(!0),i(null),l(null);try{let e=await fetch("/api/invite/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)}),o=await e.json();if(!e.ok)throw Error(o.error||"Erro ao criar convite");l(o),o.invite&&o.invite.token&&h(e=>({...e,token:o.invite.token})),c("accept")}catch(e){console.error("Erro ao criar convite:",e),i(e.message||"Ocorreu um erro ao criar o convite")}finally{o(!1)}},j=async e=>{e.preventDefault(),o(!0),i(null),l(null);try{let e=await fetch("/api/invite/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(x)}),o=await e.json();if(!e.ok)throw Error(o.error||"Erro ao aceitar convite");l(o),o.user&&o.user.email&&p(e=>({...e,email:o.user.email,password:x.password})),c("login")}catch(e){console.error("Erro ao aceitar convite:",e),i(e.message||"Ocorreu um erro ao aceitar o convite")}finally{o(!1)}},w=async e=>{e.preventDefault(),o(!0),i(null),l(null);try{l({message:"Login simulado com sucesso",user:{email:g.email}})}catch(e){console.error("Erro ao fazer login:",e),i(e.message||"Ocorreu um erro ao fazer login")}finally{o(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Teste do Fluxo Completo"}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("button",{onClick:()=>c("invite"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("invite"===d?"bg-indigo-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"1. Criar Convite"}),(0,r.jsx)("button",{onClick:()=>c("accept"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("accept"===d?"bg-indigo-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"2. Aceitar Convite"}),(0,r.jsx)("button",{onClick:()=>c("login"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("login"===d?"bg-indigo-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"3. Login"})]})}),s&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:s})})]})}),t&&(0,r.jsx)("div",{className:"mb-4 bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm text-green-700",children:t.message}),(0,r.jsx)("div",{className:"mt-2 text-xs text-green-600",children:(0,r.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(t,null,2)})})]})]})}),"invite"===d&&(0,r.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,r.jsx)("input",{type:"email",name:"email",id:"email",value:m.email,onChange:f,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Nome (opcional)"}),(0,r.jsx)("input",{type:"text",name:"name",id:"name",value:m.name,onChange:f,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nome do Cliente"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Tipo de Cliente"}),(0,r.jsxs)("select",{name:"role",id:"role",value:m.role,onChange:f,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",children:[(0,r.jsx)("option",{value:"restaurant",children:"Restaurante"}),(0,r.jsx)("option",{value:"influencer",children:"Influenciador"})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:e?"Criando...":"Criar Convite"})})]}),"accept"===d&&(0,r.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"token",className:"block text-sm font-medium text-gray-700",children:"Token do Convite"}),(0,r.jsx)("input",{type:"text",name:"token",id:"token",value:x.token,onChange:b,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Token do convite"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Senha"}),(0,r.jsx)("input",{type:"password",name:"password",id:"password",value:x.password,onChange:b,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Senha"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Nome Completo (opcional)"}),(0,r.jsx)("input",{type:"text",name:"name",id:"name",value:x.name,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nome Completo"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Telefone (opcional)"}),(0,r.jsx)("input",{type:"tel",name:"phone",id:"phone",value:x.phone,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"(00) 00000-0000"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:e?"Aceitando...":"Aceitar Convite"})})]}),"login"===d&&(0,r.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,r.jsx)("input",{type:"email",name:"email",id:"email",value:g.email,onChange:y,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Senha"}),(0,r.jsx)("input",{type:"password",name:"password",id:"password",value:g.password,onChange:y,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Senha"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:e?"Entrando...":"Entrar"})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Ou v\xe1 para a"," ",(0,r.jsx)(a(),{href:"/login",className:"font-medium text-indigo-600 hover:text-indigo-500",children:"p\xe1gina de login real"})]})})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(a(),{href:"/admin/add-client",className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Ir para Adicionar Cliente"})})]})})}},52644:(e,o,s)=>{Promise.resolve().then(s.bind(s,12644))}},e=>{var o=o=>e(e.s=o);e.O(0,[6874,8441,1684,7358],()=>o(52644)),_N_E=e.O()}]);