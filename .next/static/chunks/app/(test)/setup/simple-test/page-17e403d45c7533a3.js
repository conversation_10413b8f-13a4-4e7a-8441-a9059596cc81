(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6800],{4086:(e,s,r)=>{Promise.resolve().then(r.bind(r,61794))},35695:(e,s,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},61794:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var a=r(95155),n=r(12115),t=r(35695);function l(){let[e,s]=(0,n.useState)(!1),[r,l]=(0,n.useState)(null),[i,d]=(0,n.useState)(null),o=(0,t.useRouter)(),c=async()=>{try{s(!0),l(null),d(null);let e=await fetch("/api/setup/simple-add-client"),r=await e.json();if(console.log("Resposta recebida:",r),r.error)throw Error(r.error);d(r)}catch(e){console.error("Erro ao adicionar cliente:",e),l(e.message||"Erro desconhecido")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 max-w-md w-full",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Teste Super Simples"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h2",{className:"font-semibold text-blue-800 mb-2",children:"Adicionar Cliente de Teste"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Este processo ir\xe1 adicionar um cliente de teste com dados fixos usando um endpoint simplificado."}),(0,a.jsxs)("div",{className:"bg-gray-100 p-3 rounded-lg mb-4",children:[(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Senha:"})," Test@123456"]}),(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Nome:"})," Usu\xe1rio de Teste"]}),(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Tipo:"})," Restaurante"]})]}),(0,a.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50",children:e?"Adicionando...":"Adicionar Cliente"})]}),r&&(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg text-red-700",children:[(0,a.jsx)("p",{className:"font-semibold",children:"Erro:"}),(0,a.jsx)("p",{children:r})]}),i&&(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-green-700 mb-2",children:i.message}),i.user&&(0,a.jsxs)("div",{className:"text-sm text-gray-700 space-y-1 mb-4",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"ID:"})," ",i.user.id]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",i.user.email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Senha:"})," ",i.user.password]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Nome:"})," ",i.user.name]})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("button",{onClick:()=>window.location.href=i.loginUrl,className:"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-200",children:"Ir para Login"})})]}),(0,a.jsx)("div",{className:"border-t pt-4 mt-4",children:(0,a.jsx)("button",{onClick:()=>o.push("/admin/add-client"),className:"w-full bg-gray-200 text-gray-700 py-2 rounded-lg hover:bg-gray-300 transition duration-200",children:"Voltar para Formul\xe1rio Completo"})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(4086)),_N_E=e.O()}]);