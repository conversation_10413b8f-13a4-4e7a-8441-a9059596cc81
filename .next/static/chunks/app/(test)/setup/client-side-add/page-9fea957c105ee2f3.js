(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7190],{35695:(e,r,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(r,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},86344:(e,r,s)=>{Promise.resolve().then(s.bind(s,91624))},91624:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var a=s(95155),n=s(12115),o=s(73579),t=s(35695);function i(){let[e,r]=(0,n.useState)({email:"",fullName:"",role:"restaurant",phone:""}),[s,i]=(0,n.useState)(!1),[l,d]=(0,n.useState)(null),[c,m]=(0,n.useState)(null),u=(0,t.useRouter)(),x=(0,o.createClientComponentClient)(),h=e=>{let{name:s,value:a}=e.target;r(e=>({...e,[s]:a}))},f=async s=>{s.preventDefault(),i(!0),d(null),m(null);try{let{email:s,fullName:a,role:n,phone:o}=e,t="Test@123456",{data:i,error:l}=await x.auth.signUp({email:s,password:t,options:{data:{full_name:a,role:n,phone:o||null}}});if(l)throw Error("Erro ao criar usu\xe1rio: ".concat(l.message));if(!i.user)throw Error("Usu\xe1rio n\xe3o foi criado");let d=i.user.id,{error:c}=await x.from("profiles").insert({id:d,role:n,full_name:a,email:s,phone:o||null,password_changed:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});if(c)throw Error("Erro ao criar perfil: ".concat(c.message));if("restaurant"===n){let{error:e}=await x.from("restaurants").insert({id:d,name:a,created_at:new Date().toISOString(),updated_at:new Date().toISOString()});e&&console.error("Erro ao criar registro de restaurante:",e)}else if("influencer"===n){let e=s.split("@")[0],{error:r}=await x.from("influencers").insert({id:d,name:a,username:e,classification:"Standard",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});r&&console.error("Erro ao criar registro de influenciador:",r);let{error:n}=await x.from("influencer_profiles").insert({id:d,bio:"",content_niche:[],primary_platform:"instagram",location_city:"",location_state:"",created_at:new Date().toISOString(),updated_at:new Date().toISOString()});n&&console.error("Erro ao criar perfil de influenciador:",n)}m({message:"Cliente adicionado com sucesso",user:{id:d,email:s,password:t,name:a,role:n},loginUrl:"/login"}),r({email:"",fullName:"",role:"restaurant",phone:""})}catch(e){console.error("Erro ao adicionar cliente:",e),d(e.message||"Ocorreu um erro ao adicionar o cliente")}finally{i(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl p-6",children:(0,a.jsx)("div",{className:"md:flex",children:(0,a.jsxs)("div",{className:"p-4 w-full",children:[(0,a.jsx)("div",{className:"uppercase tracking-wide text-sm text-indigo-500 font-semibold mb-1",children:"Cliente"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Adicionar Novo Cliente (Client-Side)"}),l&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:l})})]})}),c&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm text-green-700",children:"Cliente adicionado com sucesso!"}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-green-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",c.user.email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Senha tempor\xe1ria:"})," ",c.user.password]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"ID:"})," ",c.user.id]})]})]})]})}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",id:"email",value:e.email,onChange:h,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Nome Completo"}),(0,a.jsx)("input",{type:"text",name:"fullName",id:"fullName",value:e.fullName,onChange:h,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nome do Cliente"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Telefone (opcional)"}),(0,a.jsx)("input",{type:"tel",name:"phone",id:"phone",value:e.phone,onChange:h,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"(00) 00000-0000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Tipo de Cliente"}),(0,a.jsxs)("select",{name:"role",id:"role",value:e.role,onChange:h,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",children:[(0,a.jsx)("option",{value:"restaurant",children:"Restaurante"}),(0,a.jsx)("option",{value:"influencer",children:"Influenciador"})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)("p",{className:"text-gray-500",children:"A senha tempor\xe1ria ser\xe1: Test@123456"})})}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:s,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:s?"Adicionando...":"Adicionar Cliente"})})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("button",{onClick:()=>u.back(),className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Voltar"})})]})})})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[9724,3579,8441,1684,7358],()=>r(86344)),_N_E=e.O()}]);