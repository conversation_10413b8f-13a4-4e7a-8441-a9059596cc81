(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8821],{1141:(e,s,r)=>{Promise.resolve().then(r.bind(r,15555))},15555:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var o=r(95155),a=r(12115),t=r(73970);function l(){let[e,s]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[i,n]=(0,a.useState)(null),[d,c]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),x=async s=>{s.preventDefault(),c(!0),n(null),m(!1),f(null);try{if(console.log("Tentando fazer login com:",{email:e,password:r}),!t.N)throw Error("Cliente Supabase n\xe3o inicializado");console.log("URL do Supabase:",t.N.supabaseUrl);let{data:s,error:o}=await t.N.auth.signInWithPassword({email:e,password:r});if(o)throw o;if(console.log("Login bem-sucedido:",s),m(!0),f(s),s.user){let{data:e,error:r}=await t.N.from("profiles").select("*").eq("id",s.user.id).single();r?console.error("Erro ao buscar perfil:",r):(console.log("Perfil do usu\xe1rio:",e),f(s=>({...s,profile:e})))}}catch(e){console.error("Erro ao fazer login:",e),n(e.message||"Erro desconhecido ao fazer login")}finally{c(!1)}};return(0,o.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center p-4",children:(0,o.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-center mb-6",children:"Login Simplificado"}),(0,o.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,o.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>s(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Senha"}),(0,o.jsx)("input",{id:"password",type:"password",value:r,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",required:!0})]}),i&&(0,o.jsx)("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,o.jsx)("p",{className:"text-sm text-red-700",children:i})}),u&&(0,o.jsx)("div",{className:"bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,o.jsx)("p",{className:"text-sm text-green-700",children:"Login realizado com sucesso!"})}),(0,o.jsx)("button",{type:"submit",disabled:d,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:d?"Entrando...":"Entrar"})]}),g&&(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Dados do Usu\xe1rio"}),(0,o.jsx)("pre",{className:"bg-gray-100 p-3 rounded overflow-auto text-xs",children:JSON.stringify(g,null,2)})]}),(0,o.jsx)("div",{className:"mt-6 text-center",children:(0,o.jsx)("a",{href:"/test-supabase",className:"text-sm text-indigo-600 hover:text-indigo-500",children:"Testar Conex\xe3o com Supabase"})})]})})}},73970:(e,s,r)=>{"use strict";r.d(s,{N:()=>o});let o=(0,r(73579).createClientComponentClient)()}},e=>{var s=s=>e(e.s=s);e.O(0,[9724,3579,8441,1684,7358],()=>s(1141)),_N_E=e.O()}]);