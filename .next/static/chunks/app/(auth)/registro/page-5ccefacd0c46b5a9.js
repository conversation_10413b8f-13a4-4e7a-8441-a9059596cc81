(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5810],{8980:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(95155),a=t(12115),l=t(35695),n=t(6874),o=t.n(n),i=t(51645);function d(){let[e,s]=(0,a.useState)(""),[t,n]=(0,a.useState)(""),[d,c]=(0,a.useState)(""),[u,m]=(0,a.useState)("restaurant"),[h,x]=(0,a.useState)(null),[f,b]=(0,a.useState)(!1),[p,g]=(0,a.useState)(!1),j=(0,l.useRouter)(),v=async s=>{s.preventDefault(),b(!0),x(null);try{let s=await fetch("/api/admin/add-client-public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,fullName:e,role:u,phone:d})}),r=await s.json();if(!s.ok)throw Error(r.error||"Erro ao registrar usu\xe1rio");g(!0),setTimeout(()=>{j.push("/login")},3e3)}catch(e){x(e.message)}finally{b(!1)}};return(0,r.jsxs)(i.A,{title:"Criar sua conta",subtitle:"Preencha os dados abaixo para criar sua conta",rightSideIcon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),children:[p?(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:[(0,r.jsx)("p",{className:"text-green-800 font-medium",children:"Registro realizado com sucesso!"}),(0,r.jsx)("p",{className:"text-green-700 text-sm mt-1",children:"Uma senha tempor\xe1ria foi enviada para o seu email. Voc\xea ser\xe1 redirecionado para a p\xe1gina de login em instantes."})]}):(0,r.jsxs)("form",{onSubmit:v,className:"space-y-5",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",htmlFor:"fullName",children:"Nome completo"}),(0,r.jsx)("input",{type:"text",id:"fullName",value:e,onChange:e=>s(e.target.value),placeholder:"Digite seu nome completo",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",htmlFor:"email",children:"Email"}),(0,r.jsx)("input",{type:"email",id:"email",value:t,onChange:e=>n(e.target.value),placeholder:"Digite seu email",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",htmlFor:"phone",children:"Telefone (opcional)"}),(0,r.jsx)("input",{type:"tel",id:"phone",value:d,onChange:e=>c(e.target.value),placeholder:"Digite seu telefone",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",htmlFor:"role",children:"Tipo de conta"}),(0,r.jsxs)("select",{id:"role",value:u,onChange:e=>m(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[(0,r.jsx)("option",{value:"restaurant",children:"Restaurante"}),(0,r.jsx)("option",{value:"influencer",children:"Influenciador"})]})]}),h&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:h}),(0,r.jsx)("button",{type:"submit",disabled:f,className:"w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition duration-200 font-medium disabled:opacity-50",children:f?"Processando...":"Criar conta"})]}),(0,r.jsxs)("div",{className:"mt-8 text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"J\xe1 tem uma conta?"}),(0,r.jsx)(o(),{href:"/login",className:"text-blue-600 hover:underline font-medium",children:"Fa\xe7a login"})]})]})}},35695:(e,s,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},51645:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(95155),a=t(6874),l=t.n(a),n=t(60637);function o(e){let{children:s,title:t,subtitle:a="Aproveite todos os recursos da nossa plataforma",rightSideIcon:o,rightSideTitle:i="crIAdores",rightSideDescription:d="Conectando restaurantes e influenciadores para impulsionar seu neg\xf3cio",rightSideFooter:c="Aproveite uma experi\xeancia de 30 dias gratuita"}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-[#f5f5f7] font-[-apple-system,BlinkMacSystemFont,'SF_Pro','Inter',sans-serif]",children:(0,r.jsxs)("div",{className:"flex overflow-hidden rounded-2xl shadow-lg w-full max-w-5xl bg-white border border-[rgba(0,0,0,0.05)]",children:[(0,r.jsxs)("div",{className:"w-full md:w-1/2 p-10",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-[#1d1d1f]",children:t}),(0,r.jsxs)(l(),{href:"/",className:"text-[#0071e3] hover:text-[#0077ED] flex items-center gap-1 text-sm transition-colors duration-200",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Voltar ao in\xedcio"]})]}),a&&(0,r.jsx)("p",{className:"text-[#86868b] mb-8",children:a}),s]}),(0,r.jsxs)("div",{className:"hidden md:block md:w-1/2 bg-gradient-to-br from-[#0071e3] to-[#003a73] p-12 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#0071e3] to-[#003a73] opacity-90"}),(0,r.jsxs)("div",{className:"relative z-10 h-full flex flex-col justify-center",children:[(0,r.jsx)("div",{className:"mb-6 inline-block p-3 bg-white/10 backdrop-blur-md rounded-2xl",children:o||(0,r.jsx)(n.default,{size:"large",showText:!1,className:"text-white"})}),(0,r.jsx)("h2",{className:"text-3xl font-semibold text-white mb-4",children:i}),(0,r.jsx)("p",{className:"text-white/90 text-lg mb-6",children:d}),(0,r.jsx)("p",{className:"text-white/80 text-sm",children:c})]}),(0,r.jsx)("div",{className:"absolute -bottom-16 -right-16 w-64 h-64 rounded-full bg-white/10 backdrop-blur-md"}),(0,r.jsx)("div",{className:"absolute top-16 -right-8 w-32 h-32 rounded-full bg-white/10 backdrop-blur-md"}),(0,r.jsx)("div",{className:"absolute top-1/4 left-1/4 w-48 h-48 rounded-full bg-white/5 backdrop-blur-md"})]})]})})}},60637:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var r=t(95155),a=t(6874),l=t.n(a);function n(e){let{className:s="",textClassName:t="text-xl font-semibold",showText:a=!0,size:n="medium",href:o="/"}=e;return o?(0,r.jsx)(l(),{href:o,className:"flex items-center ".concat(s),children:(0,r.jsx)("span",{className:t,children:"crIAdores"})}):(0,r.jsx)("div",{className:"flex items-center ".concat(s),children:(0,r.jsx)("span",{className:t,children:"crIAdores"})})}},98918:(e,s,t)=>{Promise.resolve().then(t.bind(t,8980))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(98918)),_N_E=e.O()}]);