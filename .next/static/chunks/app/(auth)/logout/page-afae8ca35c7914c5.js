(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8017],{2375:(e,s,o)=>{Promise.resolve().then(o.bind(o,82079))},40283:(e,s,o)=>{"use strict";o.d(s,{A:()=>u,O:()=>i});var t=o(95155),n=o(12115),r=o(52643),l=o(44032);let a=(0,n.createContext)(void 0);function i(e){let{children:s}=e,[o,i]=(0,n.useState)(null),[u,c]=(0,n.useState)(null),[d,g]=(0,n.useState)(!0),[h,m]=(0,n.useState)(null),x=async()=>{try{var e;g(!0),m(null),console.log("AuthContext: Refreshing session...");let{data:s,error:o}=await r.N.auth.getSession();if(o){console.error("Erro ao verificar sess\xe3o atual:",o.message),m(o.message),g(!1);return}if(!s.session){console.log("AuthContext: No active session found"),i(null),c(null),g(!1);return}console.log("AuthContext: Active session found"),i(s.session),c((null===(e=s.session)||void 0===e?void 0:e.user)||null)}catch(e){console.error("Erro inesperado ao obter sess\xe3o:",e),m(e.message||"Erro desconhecido")}finally{g(!1)}},f=async()=>{try{g(!0),m(null),console.log("AuthContext: Signing out...");let{error:e}=await r.N.auth.signOut();e&&(console.error("Erro ao fazer logout no Supabase:",e.message),m(e.message)),i(null),c(null),console.log("AuthContext: Logout completed, redirecting to login"),window.location.href="/login"}catch(e){console.error("Erro inesperado ao fazer logout:",e),m(e.message||"Erro desconhecido"),i(null),c(null),window.location.href="/login"}finally{g(!1)}};return(0,n.useEffect)(()=>{x();let{data:e}=r.N.auth.onAuthStateChange(async(e,s)=>{console.log("Auth state changed:",e),"SIGNED_IN"===e?(console.log("User signed in, updating session"),i(s),c((null==s?void 0:s.user)||null)):"SIGNED_OUT"===e?(console.log("User signed out, clearing session"),i(null),c(null)):"TOKEN_REFRESHED"===e?(console.log("Token refreshed, updating session"),i(s),c((null==s?void 0:s.user)||null)):"USER_UPDATED"===e&&(console.log("User updated, updating session"),i(s),c((null==s?void 0:s.user)||null)),g(!1)});return()=>{e.subscription.unsubscribe()}},[]),(0,n.useEffect)(()=>{let e=e=>{(e.error instanceof l.lR||e.message&&(e.message.includes("Invalid Refresh Token")||e.message.includes("JWT expired")||e.message.includes("not authenticated")))&&(console.error("Erro de autentica\xe7\xe3o interceptado:",e),x())};return window.addEventListener("error",e),()=>{window.removeEventListener("error",e)}},[]),(0,t.jsx)(a.Provider,{value:{session:o,user:u,loading:d,error:h,signOut:f,refreshSession:x},children:s})}function u(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAuth deve ser usado dentro de um AuthProvider");return e}},52643:(e,s,o)=>{"use strict";o.d(s,{N:()=>n,b:()=>r});var t=o(73579);let n=(0,t.createClientComponentClient)();function r(){return(0,t.createClientComponentClient)()}},82079:(e,s,o)=>{"use strict";o.r(s),o.d(s,{default:()=>l});var t=o(95155),n=o(12115),r=o(40283);function l(){let[e,s]=(0,n.useState)("loading"),[o,l]=(0,n.useState)(null),{signOut:a}=(0,r.A)();return(0,n.useEffect)(()=>{(async()=>{try{await a(),s("success"),setTimeout(()=>{window.location.href="/"},2e3)}catch(e){console.error("Erro ao fazer logout:",e),l(e.message||"Erro desconhecido ao fazer logout"),s("error")}})()},[]),(0,t.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-center mb-6",children:"Logout"}),"loading"===e&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Fazendo logout..."})]}),"success"===e&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"rounded-full h-12 w-12 bg-green-100 mx-auto flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Logout realizado com sucesso!"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Voc\xea ser\xe1 redirecionado para a p\xe1gina inicial em alguns segundos."})]}),"error"===e&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"rounded-full h-12 w-12 bg-red-100 mx-auto flex items-center justify-center",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,t.jsx)("p",{className:"mt-4 text-red-600",children:"Erro ao fazer logout"}),o&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-500",children:o}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)("a",{href:"/",className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition",children:"Voltar para a P\xe1gina Inicial"})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9724,3579,8441,1684,7358],()=>s(2375)),_N_E=e.O()}]);