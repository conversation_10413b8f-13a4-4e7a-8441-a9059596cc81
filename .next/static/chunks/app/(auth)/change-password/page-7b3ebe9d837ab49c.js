(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4807],{35695:(e,a,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(a,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(a,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(a,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(a,{useSearchParams:function(){return s.useSearchParams}})},47841:(e,a,r)=>{Promise.resolve().then(r.bind(r,93301))},73970:(e,a,r)=>{"use strict";r.d(a,{N:()=>s});let s=(0,r(73579).createClientComponentClient)()},93301:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>o});var s=r(95155),n=r(12115),t=r(73970),l=r(35695);function o(){let[e,a]=(0,n.useState)(""),[r,o]=(0,n.useState)(""),[d,i]=(0,n.useState)(""),[c,u]=(0,n.useState)(!1),[m,h]=(0,n.useState)(null),[x,p]=(0,n.useState)(!1),[f,g]=(0,n.useState)(null),v=(0,l.useRouter)();(0,n.useEffect)(()=>{(async()=>{let{data:{session:e}}=await t.N.auth.getSession();e?g(e.user):v.push("/login?redirectedFrom=/change-password")})()},[v]);let w=e=>/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(e),j=async s=>{s.preventDefault(),u(!0),h(null),p(!1);try{if(r!==d)throw Error("A nova senha e a confirma\xe7\xe3o n\xe3o coincidem");if(!w(r))throw Error("A senha deve ter pelo menos 8 caracteres, incluindo uma letra mai\xfascula, uma min\xfascula, um n\xfamero e um caractere especial");let{error:s}=await t.N.auth.signInWithPassword({email:f.email,password:e});if(s)throw Error("Senha atual incorreta");let{error:n}=await t.N.auth.updateUser({password:r});if(n)throw Error("Erro ao atualizar senha: ".concat(n.message));p(!0),a(""),o(""),i(""),setTimeout(()=>{v.push("/")},3e3)}catch(e){console.error("Erro ao alterar senha:",e),h(e.message||"Ocorreu um erro ao alterar a senha")}finally{u(!1)}};return f?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Alterar Senha"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Crie uma nova senha segura para sua conta"})]}),m&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-red-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Erro ao alterar senha"}),(0,s.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,s.jsx)("p",{children:m})})]})]})}),x&&(0,s.jsx)("div",{className:"rounded-md bg-green-50 p-4",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-green-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Senha alterada com sucesso!"}),(0,s.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,s.jsx)("p",{children:"Sua senha foi atualizada. Voc\xea ser\xe1 redirecionado em alguns segundos."})})]})]})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:j,children:[(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"current-password",className:"sr-only",children:"Senha Atual"}),(0,s.jsx)("input",{id:"current-password",name:"current-password",type:"password",autoComplete:"current-password",required:!0,value:e,onChange:e=>a(e.target.value),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Senha Atual"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"new-password",className:"sr-only",children:"Nova Senha"}),(0,s.jsx)("input",{id:"new-password",name:"new-password",type:"password",autoComplete:"new-password",required:!0,value:r,onChange:e=>o(e.target.value),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Nova Senha"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirm-password",className:"sr-only",children:"Confirmar Nova Senha"}),(0,s.jsx)("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,value:d,onChange:e=>i(e.target.value),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Confirmar Nova Senha"})]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)("p",{children:"A senha deve ter pelo menos 8 caracteres e incluir:"}),(0,s.jsxs)("ul",{className:"list-disc pl-5 mt-1",children:[(0,s.jsx)("li",{children:"Uma letra mai\xfascula"}),(0,s.jsx)("li",{children:"Uma letra min\xfascula"}),(0,s.jsx)("li",{children:"Um n\xfamero"}),(0,s.jsx)("li",{children:"Um caractere especial (ex: @, #, $, etc.)"})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:c?"Alterando...":"Alterar Senha"})})]})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsx)("div",{children:(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Carregando..."})})})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[9724,3579,8441,1684,7358],()=>a(47841)),_N_E=e.O()}]);