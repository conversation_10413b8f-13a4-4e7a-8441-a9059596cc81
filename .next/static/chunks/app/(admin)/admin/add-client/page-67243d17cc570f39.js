(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[373],{35695:(e,s,r)=>{"use strict";var l=r(18999);r.o(l,"useParams")&&r.d(s,{useParams:function(){return l.useParams}}),r.o(l,"usePathname")&&r.d(s,{usePathname:function(){return l.usePathname}}),r.o(l,"useRouter")&&r.d(s,{useRouter:function(){return l.useRouter}}),r.o(l,"useSearchParams")&&r.d(s,{useSearchParams:function(){return l.useSearchParams}})},50613:(e,s,r)=>{Promise.resolve().then(r.bind(r,67819))},67819:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var l=r(95155),a=r(12115),n=r(35695);function i(){let[e,s]=(0,a.useState)({email:"",fullName:"",role:"restaurant",phone:""}),[r,i]=(0,a.useState)(!1),[o,t]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),m=(0,n.useRouter)(),u=e=>{let{name:r,value:l}=e.target;s(e=>({...e,[r]:l}))},x=async r=>{r.preventDefault(),i(!0),t(null),c(null);try{let r=await fetch("/api/admin/add-client",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),l=await r.json();if(!r.ok)throw Error(l.error||"Erro ao adicionar cliente");c(l),s({email:"",fullName:"",role:"restaurant",phone:""})}catch(e){console.error("Erro ao adicionar cliente:",e),t(e.message||"Ocorreu um erro ao adicionar o cliente")}finally{i(!1)}};return(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,l.jsx)("div",{className:"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl p-6",children:(0,l.jsx)("div",{className:"md:flex",children:(0,l.jsxs)("div",{className:"p-4 w-full",children:[(0,l.jsx)("div",{className:"uppercase tracking-wide text-sm text-indigo-500 font-semibold mb-1",children:"Administra\xe7\xe3o"}),(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Adicionar Novo Cliente"}),o&&(0,l.jsx)("div",{className:"mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,l.jsx)("div",{className:"ml-3",children:(0,l.jsx)("p",{className:"text-sm text-red-700",children:o})})]})}),d&&(0,l.jsx)("div",{className:"mb-4 bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,l.jsxs)("div",{className:"ml-3",children:[(0,l.jsx)("p",{className:"text-sm text-green-700",children:"Cliente adicionado com sucesso!"}),(0,l.jsxs)("div",{className:"mt-2 text-xs text-green-600",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Email:"})," ",d.user.email]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Senha tempor\xe1ria:"})," ",d.user.password]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"ID:"})," ",d.user.id]})]})]})]})}),(0,l.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,l.jsx)("input",{type:"email",name:"email",id:"email",value:e.email,onChange:u,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Nome Completo"}),(0,l.jsx)("input",{type:"text",name:"fullName",id:"fullName",value:e.fullName,onChange:u,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nome do Cliente"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Telefone (opcional)"}),(0,l.jsx)("input",{type:"tel",name:"phone",id:"phone",value:e.phone,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"(00) 00000-0000"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Tipo de Cliente"}),(0,l.jsxs)("select",{name:"role",id:"role",value:e.role,onChange:u,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",children:[(0,l.jsx)("option",{value:"restaurant",children:"Restaurante"}),(0,l.jsx)("option",{value:"influencer",children:"Influenciador"})]})]}),(0,l.jsx)("div",{className:"flex items-center justify-between",children:(0,l.jsx)("div",{className:"text-sm",children:(0,l.jsx)("p",{className:"text-gray-500",children:"Uma senha tempor\xe1ria ser\xe1 gerada automaticamente."})})}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{type:"submit",disabled:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:r?"Adicionando...":"Adicionar Cliente"})})]}),(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)("button",{onClick:()=>m.back(),className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Voltar"})})]})})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(50613)),_N_E=e.O()}]);