(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3369],{24665:(e,s,r)=>{Promise.resolve().then(r.bind(r,53471))},53471:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var a=r(95155),t=r(12115),i=r(6874),c=r.n(i),n=r(52643),l=r(61257),o=r(85229),d=r(29911),x=r(13568);function m(){let[e,s]=(0,t.useState)([]),[r,i]=(0,t.useState)([]),[m,p]=(0,t.useState)(!0),[u,h]=(0,t.useState)(""),[g,f]=(0,t.useState)("all");(0,t.useEffect)(()=>{j()},[]),(0,t.useEffect)(()=>{e&&i(e.filter(e=>e.business_name.toLowerCase().includes(u.toLowerCase())||e.description&&e.description.toLowerCase().includes(u.toLowerCase())))},[e,u,g]);let j=async()=>{try{p(!0);let{data:e,error:r}=await n.N.from("restaurant_profiles").select("*").order("business_name",{ascending:!0});if(r){console.error("Erro ao buscar neg\xf3cios:",r),x.oR.error("Erro ao carregar neg\xf3cios");return}s(e||[]),i(e||[])}catch(e){console.error("Erro ao buscar neg\xf3cios:",e),x.oR.error("Erro ao carregar neg\xf3cios")}finally{p(!1)}},y=async e=>{if(window.confirm("Tem certeza que deseja excluir este neg\xf3cio? Esta a\xe7\xe3o n\xe3o pode be desfeita."))try{let{error:s}=await n.N.from("restaurant_profiles").delete().eq("id",e);if(s){console.error("Erro ao excluir neg\xf3cio:",s),x.oR.error("Erro ao excluir neg\xf3cio");return}x.oR.success("Neg\xf3cio exclu\xeddo com sucesso"),j()}catch(e){console.error("Erro ao excluir neg\xf3cio:",e),x.oR.error("Erro ao excluir neg\xf3cio")}},N=e=>e?new Date(e).toLocaleDateString("pt-BR"):"N/A";return(0,a.jsxs)(l.A,{title:"Neg\xf3cios",children:[(0,a.jsxs)("div",{className:"mb-6 flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(d.KSO,{className:"text-gray-400"})}),(0,a.jsx)("input",{type:"text",placeholder:"Buscar neg\xf3cios...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",value:u,onChange:e=>h(e.target.value)})]})}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsx)(c(),{href:"/admin/negocios/new",children:(0,a.jsxs)(o.S,{variant:"primary",size:"md",children:[(0,a.jsx)(d.OiG,{className:"mr-2"}),"Novo Neg\xf3cio"]})})})]}),m?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):0===r.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Nenhum neg\xf3cio encontrado"}),(0,a.jsx)(c(),{href:"/admin/negocios/new",children:(0,a.jsxs)(o.S,{variant:"primary",size:"md",children:[(0,a.jsx)(d.OiG,{className:"mr-2"}),"Criar Novo Neg\xf3cio"]})})]}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Neg\xf3cio"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Localiza\xe7\xe3o"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Instagram"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Data de Cria\xe7\xe3o"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7\xf5es"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.business_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description})]})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[e.city,", ",e.state]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.instagram_url?(0,a.jsx)("a",{href:e.instagram_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:e.instagram_url.replace("https://instagram.com/","@")}):"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:N(e.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(c(),{href:"/admin/negocios/".concat(e.id),children:(0,a.jsx)(o.S,{variant:"secondary",size:"sm",children:(0,a.jsx)(d.Ny1,{})})}),(0,a.jsx)(c(),{href:"/admin/negocios/".concat(e.id,"/edit"),children:(0,a.jsx)(o.S,{variant:"secondary",size:"sm",children:(0,a.jsx)(d.uO9,{})})}),(0,a.jsx)(o.S,{variant:"destructive",size:"sm",onClick:()=>y(e.id),children:(0,a.jsx)(d.qbC,{})})]})})]},e.id))})]})})]})}},85229:(e,s,r)=>{"use strict";r.d(s,{S:()=>c});var a=r(95155),t=r(12115),i=r(39688);let c=t.forwardRef((e,s)=>{let{variant:r="primary",size:t="md",icon:c,iconPosition:n="left",isLoading:l=!1,fullWidth:o=!1,children:d,className:x,disabled:m,...p}=e;return(0,a.jsxs)("button",{ref:s,className:(0,i.QP)("rounded-[12px] font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2",{sm:"h-[36px] px-[16px] text-[14px]",md:"h-[44px] px-[20px] text-[16px]",lg:"h-[52px] px-[24px] text-[18px]"}[t],{primary:"bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-300 disabled:bg-blue-300",secondary:"bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 focus:ring-gray-200 disabled:text-gray-400",destructive:"bg-red-500 text-white hover:bg-red-600 focus:ring-red-300 disabled:bg-red-300"}[r],o&&"w-full",l&&"opacity-70 cursor-not-allowed",x),disabled:m||l,"aria-busy":l,...p,children:[l&&(0,a.jsx)("span",{className:"mr-2 animate-spin"}),c&&"left"===n&&(0,a.jsx)("span",{className:"mr-2",children:c}),d,c&&"right"===n&&(0,a.jsx)("span",{className:"ml-2",children:c})]})});c.displayName="StandardButton"}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,9724,3579,9688,6874,8006,5612,8441,1684,7358],()=>s(24665)),_N_E=e.O()}]);