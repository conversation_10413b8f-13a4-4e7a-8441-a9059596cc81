(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9469],{20211:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(95155);r(12115);var a=r(35695),i=r(29911),n=r(60440);function o(e){let{children:t,title:r,backLink:o}=e,c=(0,a.useRouter)();return(0,a.usePathname)(),(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[o&&(0,s.jsx)("div",{className:"mt-2 mb-4",children:(0,s.jsx)("button",{onClick:()=>c.push(o),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,s.jsx)(i.QVr,{className:"text-gray-600"})})}),(0,s.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-f5f5f7 overflow-y-auto flex-1 flex flex-col shadow-md",style:{minHeight:"calc(100vh - 6rem)",maxHeight:"calc(100vh - 6rem)"},children:(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:r}),t]})})]})})}r(52643),r(13568)},37178:(e,t,r)=>{Promise.resolve().then(r.bind(r,93690))},52643:(e,t,r)=>{"use strict";r.d(t,{N:()=>a,b:()=>i});var s=r(73579);let a=(0,s.createClientComponentClient)();function i(){return(0,s.createClientComponentClient)()}},60440:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(95155),a=r(12115),i=r(35695),n=r(52643),o=r(81452),c=r(13568);function l(e){let{children:t}=e,r=(0,i.useRouter)(),[l,d]=(0,a.useState)(!0),[m,x]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await n.N.auth.getSession();if(t)throw t;if(!e){r.push("/login?redirect=/admin/campaigns");return}let s=e.user.email;s&&(0,o.K)(s)?x(!0):(c.oR.error("Voc\xea n\xe3o tem permiss\xe3o para acessar esta \xe1rea"),r.push("/"))}catch(e){console.error("Erro ao verificar status de administrador:",e),c.oR.error("Erro ao verificar suas permiss\xf5es"),r.push("/")}finally{d(!1)}})()},[r]),l)?(0,s.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):m?(0,s.jsx)(s.Fragment,{children:t}):null}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=s.createContext&&s.createContext(a),n=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var s,a,i;s=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):s[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(m,o({attr:l({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,l({key:r},t.attr),e(t.child)))}(e.child))}function m(e){var t=t=>{var r,{attr:a,size:i,title:c}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)r=i[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,n),m=i||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:l(l({color:e.color||t.color},t.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==i?s.createElement(i.Consumer,null,e=>t(e)):t(a)}},81452:(e,t,r)=>{"use strict";r.d(t,{I:()=>s,K:()=>a});let s=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];function a(e){return s.includes(e.toLowerCase())}},93690:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(95155),a=r(35695),i=r(12115),n=r(6874),o=r.n(n),c=r(52643),l=r(20211),d=r(29911),m=r(13568);function x(e){let{negocioId:t}=e;console.log("[NegocioDetailsClient] Rendering with negocioId:",t);let r=(0,a.useRouter)(),[n,x]=(0,i.useState)(null),[u,h]=(0,i.useState)(!0),[p,f]=(0,i.useState)(null),[g,b]=(0,i.useState)([]);(0,i.useEffect)(()=>{console.log("[NegocioDetailsClient] useEffect triggered. negocioId:",t),(async()=>{try{h(!0);let{data:e,error:r}=await c.N.from("negocio_profiles").select("*").eq("id",t).single();if(r){console.error("Erro ao buscar negocioe:",r),f("Erro ao carregar dados do negocioe");return}x(e),console.log("Fetching campaigns for negocioId:",t);let{data:s,error:a}=await c.N.from("campaigns").select("*").eq("negocio_id",t).order("created_at",{ascending:!1});a?(console.error("[NegocioDetailsClient] Erro ao buscar campanhas:",a),m.oR.error("Erro ao buscar campanhas: ".concat(a.message))):(console.log("[NegocioDetailsClient] Campaigns data fetched:",s),b(s||[]))}catch(e){console.error("[NegocioDetailsClient] Erro geral ao buscar dados do negocioe ou campanhas:",e),f("Erro ao carregar dados: ".concat(e.message))}finally{h(!1)}})()},[t]);let j=async()=>{if(window.confirm("Tem certeza que deseja excluir este negocioe? Esta a\xe7\xe3o n\xe3o pode ser desfeita."))try{let{error:e}=await c.N.from("negocio_profiles").delete().eq("id",t);if(e){console.error("Erro ao excluir negocioe:",e),m.oR.error("Erro ao excluir negocioe");return}m.oR.success("Negocioe exclu\xeddo com sucesso"),r.push("/admin/negocios")}catch(e){console.error("Erro ao excluir negocioe:",e),m.oR.error("Erro ao excluir negocioe")}},y=e=>e?new Date(e).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A";return(0,s.jsx)(l.default,{title:"Detalhes do Negocioe",backLink:"/admin/negocios",children:u?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):p?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,s.jsx)("p",{children:p}),(0,s.jsx)(o(),{href:"/admin/negocios",children:(0,s.jsx)("button",{className:"mt-2 text-red-700 hover:text-red-900 underline",children:"Voltar para a lista de negocioes"})})]}):n?(0,s.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,s.jsxs)("div",{className:"p-6 border-b flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold",children:n.business_name}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(o(),{href:"/admin/negocios/".concat(t,"/edit"),children:(0,s.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:[(0,s.jsx)(d.uO9,{className:"mr-2"}),"Editar"]})}),(0,s.jsxs)("button",{onClick:j,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,s.jsx)(d.qbC,{className:"mr-2"}),"Excluir"]})]})]}),(0,s.jsxs)("div",{className:"p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-4 border-b pb-2",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Nome"}),(0,s.jsx)("p",{className:"text-base",children:n.business_name})]}),n.description&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Descri\xe7\xe3o"}),(0,s.jsx)("p",{className:"text-base",children:n.description})]}),n.cuisine_type&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Tipo de Culin\xe1ria"}),(0,s.jsx)("p",{className:"text-base",children:n.cuisine_type})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Data de Cria\xe7\xe3o"}),(0,s.jsx)("p",{className:"text-base",children:y(n.created_at)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"\xdaltima Atualiza\xe7\xe3o"}),(0,s.jsx)("p",{className:"text-base",children:y(n.updated_at)})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-4 border-b pb-2",children:"Localiza\xe7\xe3o"}),(0,s.jsxs)("div",{className:"space-y-3",children:[n.address&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Endere\xe7o"}),(0,s.jsx)("p",{className:"text-base",children:n.address})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Cidade/Estado"}),(0,s.jsxs)("p",{className:"text-base",children:[n.city,", ",n.state]})]}),n.postal_code&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"CEP"}),(0,s.jsx)("p",{className:"text-base",children:n.postal_code})]})]}),(0,s.jsx)("h3",{className:"text-lg font-medium mt-6 mb-4 border-b pb-2",children:"Redes Sociais"}),(0,s.jsxs)("div",{className:"space-y-3",children:[n.website&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.f35,{className:"text-gray-500 mr-2"}),(0,s.jsx)("a",{href:n.website,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:n.website})]}),n.instagram_url&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.ao$,{className:"text-pink-600 mr-2"}),(0,s.jsx)("a",{href:n.instagram_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:n.instagram_url})]}),n.facebook_url&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.iYk,{className:"text-blue-600 mr-2"}),(0,s.jsx)("a",{href:n.facebook_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:n.facebook_url})]}),n.tiktok_url&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.kkU,{className:"text-black mr-2"}),(0,s.jsx)("a",{href:n.tiktok_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800",children:n.tiktok_url})]})]})]})]}),(0,s.jsxs)("div",{className:"p-6 border-t",children:[(0,s.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Campanhas Associadas"}),0===g.length?(0,s.jsx)("p",{className:"text-gray-500",children:"Nenhuma campanha associada a este negocioe."}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Nome"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Per\xedodo"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7\xf5es"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[y(e.start_date)," a ",y(e.end_date)]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===e.status?"bg-green-100 text-green-800":"draft"===e.status?"bg-gray-100 text-gray-800":"completed"===e.status?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"),children:"active"===e.status?"Ativa":"draft"===e.status?"Rascunho":"completed"===e.status?"Conclu\xedda":"Cancelada"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsx)(o(),{href:"/admin/campaigns/".concat(e.id),children:(0,s.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:"Ver Detalhes"})})})]},e.id))})]})})]})]})}):(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md",children:[(0,s.jsx)("p",{children:"Negocioe n\xe3o encontrado"}),(0,s.jsx)(o(),{href:"/admin/negocios",children:(0,s.jsx)("button",{className:"mt-2 text-yellow-700 hover:text-yellow-900 underline",children:"Voltar para a lista de negocioes"})})]})})}function u(){let e=(0,a.useParams)(),t=Array.isArray(e.id)?e.id[0]:e.id;return(0,s.jsx)(x,{negocioId:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,9724,3579,6874,8006,8441,1684,7358],()=>t(37178)),_N_E=e.O()}]);