(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1472],{49762:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>r});var a=i(95155),c=i(12115),o=i(35695),t=i(21115),n=i(20211),u=i(13568);function r(){let e=(0,o.useRouter)(),[s,i]=(0,c.useState)(!1);return(0,a.jsx)(n.default,{title:"Novo Neg\xf3cio",backLink:"/admin/negocios",children:(0,a.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,a.jsx)(t.A,{initialData:{business_name:"",description:"",cuisine_type:"",address:"",city:"",state:"",postal_code:"",website:"",instagram_url:"",facebook_url:"",tiktok_url:""},onCancel:()=>{e.push("/admin/negocios")},onSuccess:s=>{u.oR.success("Neg\xf3cio criado com sucesso!"),e.push("/admin/negocios/".concat(s))},isEditing:!1})})})}},86128:(e,s,i)=>{Promise.resolve().then(i.bind(i,49762))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,9724,3579,9688,8006,2177,6480,8441,1684,7358],()=>s(86128)),_N_E=e.O()}]);