(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7199],{10830:(e,r,o)=>{"use strict";o.d(r,{default:()=>u});var t=o(95155),a=o(12115),i=o(35695),s=o(62177),n=o(29911),l=o(13568),c=o(34603);function d(e){var r,o,i,d,u,m,f,p;let{onCancel:x,onSuccess:b,initialData:g,isEditing:h=!1}=e,[v,y]=(0,a.useState)(!1),{register:j,handleSubmit:N,formState:{errors:w}}=(0,s.mN)({defaultValues:{name:(null==g?void 0:g.name)||"",username:(null==g?void 0:g.username)||"",email:(null==g?void 0:g.email)||"",bio:(null==g?void 0:null===(r=g.profile)||void 0===r?void 0:r.bio)||"",instagram_username:(null==g?void 0:null===(o=g.profile)||void 0===o?void 0:o.instagram_username)||"",tiktok_username:(null==g?void 0:null===(i=g.profile)||void 0===i?void 0:i.tiktok_username)||"",location_city:(null==g?void 0:null===(d=g.profile)||void 0===d?void 0:d.location_city)||"",location_state:(null==g?void 0:null===(u=g.profile)||void 0===u?void 0:u.location_state)||"",follower_count:(null==g?void 0:null===(m=g.profile)||void 0===m?void 0:m.follower_count)||0,avg_engagement_rate:(null==g?void 0:null===(f=g.profile)||void 0===f?void 0:f.avg_engagement_rate)||0,classification:(null==g?void 0:g.classification)||"Standard",content_niche:(null==g?void 0:null===(p=g.profile)||void 0===p?void 0:p.content_niche)?g.profile.content_niche.join(", "):""}}),_=async e=>{try{var r,o;let t;y(!0);let a={name:e.name,username:e.username,email:e.email,classification:e.classification,profile:{bio:e.bio||"",instagram_username:e.instagram_username||"",tiktok_username:e.tiktok_username||"",location_city:e.location_city,location_state:e.location_state,follower_count:parseInt((null===(r=e.follower_count)||void 0===r?void 0:r.toString())||"0"),avg_engagement_rate:parseFloat((null===(o=e.avg_engagement_rate)||void 0===o?void 0:o.toString())||"0"),content_niche:e.content_niche?e.content_niche.split(",").map(e=>e.trim()):[],primary_platform:e.instagram_username?"instagram":"tiktok"},updated_at:new Date().toISOString()};if(h&&(null==g?void 0:g.id)){console.log("Atualizando influenciador existente:",g.id);try{let e={...a,id:g.id},r=window.location.origin,o=await fetch("".concat(r,"/api/admin/criadores/").concat(g.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!o.ok){let e=await o.json();throw Error(e.error||"Erro HTTP: ".concat(o.status))}t=await o.json(),l.oR.success("Influenciador atualizado com sucesso!")}catch(e){console.error("Erro ao atualizar influenciador:",e),l.oR.error("Erro ao atualizar influenciador: ".concat(e.message)),y(!1);return}}else{console.log("Criando novo influenciador");try{let e={...a,created_at:new Date().toISOString()},r=window.location.origin,o=await fetch("".concat(r,"/api/admin/criadores"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!o.ok){let e=await o.json();throw Error(e.error||"Erro HTTP: ".concat(o.status))}t=await o.json(),l.oR.success("Influenciador criado com sucesso!")}catch(e){console.error("Erro ao criar influenciador:",e),l.oR.error("Erro ao criar influenciador: ".concat(e.message)),y(!1);return}}t&&t.id?b(t.id):(null==g?void 0:g.id)&&b(g.id)}catch(e){console.error("Erro ao processar formul\xe1rio:",e),l.oR.error("Erro ao processar formul\xe1rio: ".concat(e.message))}finally{y(!1)}};return(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-md",children:(0,t.jsxs)("form",{onSubmit:N(_),children:[(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome Completo *"}),(0,t.jsx)("input",{type:"text",...j("name",{required:"Nome \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: Jo\xe3o Silva"}),w.name&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome de Usu\xe1rio *"}),(0,t.jsx)("input",{type:"text",...j("username",{required:"Nome de usu\xe1rio \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: joaosilva"}),w.username&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.username.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),(0,t.jsx)("input",{type:"email",...j("email",{required:"Email \xe9 obrigat\xf3rio",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Email inv\xe1lido"}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: <EMAIL>"}),w.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.email.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Classifica\xe7\xe3o *"}),(0,t.jsxs)("select",{...j("classification",{required:"Classifica\xe7\xe3o \xe9 obrigat\xf3ria"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"Standard",children:"Standard"}),(0,t.jsx)("option",{value:"Bronze",children:"Bronze"}),(0,t.jsx)("option",{value:"Silver",children:"Silver"}),(0,t.jsx)("option",{value:"Gold",children:"Gold"}),(0,t.jsx)("option",{value:"Diamond",children:"Diamond"})]}),w.classification&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.classification.message})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Perfil"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bio"}),(0,t.jsx)("textarea",{...j("bio"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Descri\xe7\xe3o do influenciador",rows:3})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nichos de Conte\xfado"}),(0,t.jsx)("input",{type:"text",...j("content_niche"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: food, lifestyle, moda (separados por v\xedrgula)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cidade *"}),(0,t.jsx)("input",{type:"text",...j("location_city",{required:"Cidade \xe9 obrigat\xf3ria"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: S\xe3o Paulo"}),w.location_city&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.location_city.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Estado *"}),(0,t.jsx)("input",{type:"text",...j("location_state",{required:"Estado \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: SP"}),w.location_state&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.location_state.message})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Redes Sociais"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Usu\xe1rio do Instagram"}),(0,t.jsx)("input",{type:"text",...j("instagram_username"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: joaosilva (sem @)"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Usu\xe1rio do TikTok"}),(0,t.jsx)("input",{type:"text",...j("tiktok_username"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: joaosilva (sem @)"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"M\xe9tricas"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"N\xfamero de Seguidores"}),(0,t.jsx)("input",{type:"number",...j("follower_count"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: 10000"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Taxa de Engajamento (%)"}),(0,t.jsx)("input",{type:"number",step:"0.01",...j("avg_engagement_rate"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: 3.5"})]})]})]})]}),(0,t.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t flex justify-end space-x-3 rounded-b-lg",children:[x&&(0,t.jsx)(c.d,{type:"button",onClick:x,disabled:v,icon:(0,t.jsx)(n.QCr,{}),children:"Cancelar"}),(0,t.jsxs)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",disabled:v,children:[(0,t.jsx)(n.dIn,{className:"mr-2"}),v?"Salvando...":h?"Atualizar":"Criar"]})]})]})})}function u(e){let{initialData:r,criadorId:o}=e,a=(0,i.useRouter)();return(0,t.jsx)(d,{initialData:r,onCancel:()=>{a.push("/admin/criadors/".concat(o))},onSuccess:e=>{l.oR.success("Influenciador atualizado com sucesso!"),a.push("/admin/criadors/".concat(e))},isEditing:!0})}},20211:(e,r,o)=>{"use strict";o.d(r,{default:()=>n});var t=o(95155);o(12115);var a=o(35695),i=o(29911),s=o(60440);function n(e){let{children:r,title:o,backLink:n}=e,l=(0,a.useRouter)();return(0,a.usePathname)(),(0,t.jsx)(s.A,{children:(0,t.jsxs)("div",{className:"h-full flex flex-col",children:[n&&(0,t.jsx)("div",{className:"mt-2 mb-4",children:(0,t.jsx)("button",{onClick:()=>l.push(n),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,t.jsx)(i.QVr,{className:"text-gray-600"})})}),(0,t.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-f5f5f7 overflow-y-auto flex-1 flex flex-col shadow-md",style:{minHeight:"calc(100vh - 6rem)",maxHeight:"calc(100vh - 6rem)"},children:(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:o}),r]})})]})})}o(52643),o(13568)},34603:(e,r,o)=>{"use strict";o.d(r,{d:()=>s});var t=o(95155),a=o(12115),i=o(39688);let s=a.forwardRef((e,r)=>{let{icon:o,iconPosition:a="left",isLoading:s=!1,fullWidth:n=!1,children:l,className:c,disabled:d,...u}=e;return(0,t.jsxs)("button",{ref:r,className:(0,i.QP)("rounded-md font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2","px-4 py-2 text-sm border border-gray-300 shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 disabled:opacity-50",n&&"w-full",s&&"opacity-70 cursor-not-allowed",c),disabled:d||s,"aria-busy":s,...u,children:[s&&(0,t.jsx)("span",{className:"mr-2 animate-spin"}),o&&"left"===a&&(0,t.jsx)("span",{className:"mr-2",children:o}),l,o&&"right"===a&&(0,t.jsx)("span",{className:"ml-2",children:o})]})});s.displayName="SecondaryActionButton"},52643:(e,r,o)=>{"use strict";o.d(r,{N:()=>a,b:()=>i});var t=o(73579);let a=(0,t.createClientComponentClient)();function i(){return(0,t.createClientComponentClient)()}},54020:(e,r,o)=>{Promise.resolve().then(o.bind(o,10830)),Promise.resolve().then(o.bind(o,20211))},60440:(e,r,o)=>{"use strict";o.d(r,{A:()=>c});var t=o(95155),a=o(12115),i=o(35695),s=o(52643),n=o(81452),l=o(13568);function c(e){let{children:r}=e,o=(0,i.useRouter)(),[c,d]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:r}=await s.N.auth.getSession();if(r)throw r;if(!e){o.push("/login?redirect=/admin/campaigns");return}let t=e.user.email;t&&(0,n.K)(t)?m(!0):(l.oR.error("Voc\xea n\xe3o tem permiss\xe3o para acessar esta \xe1rea"),o.push("/"))}catch(e){console.error("Erro ao verificar status de administrador:",e),l.oR.error("Erro ao verificar suas permiss\xf5es"),o.push("/")}finally{d(!1)}})()},[o]),c)?(0,t.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):u?(0,t.jsx)(t.Fragment,{children:r}):null}},74436:(e,r,o)=>{"use strict";o.d(r,{k5:()=>d});var t=o(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=t.createContext&&t.createContext(a),s=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var o=arguments[r];for(var t in o)Object.prototype.hasOwnProperty.call(o,t)&&(e[t]=o[t])}return e}).apply(this,arguments)}function l(e,r){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),o.push.apply(o,t)}return o}function c(e){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?l(Object(o),!0).forEach(function(r){var t,a,i;t=e,a=r,i=o[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var t=o.call(e,r||"default");if("object"!=typeof t)return t;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in t?Object.defineProperty(t,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):l(Object(o)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(o,r))})}return e}function d(e){return r=>t.createElement(u,n({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,o)=>t.createElement(r.tag,c({key:o},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var o,{attr:a,size:i,title:l}=e,d=function(e,r){if(null==e)return{};var o,t,a=function(e,r){if(null==e)return{};var o={};for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)){if(r.indexOf(t)>=0)continue;o[t]=e[t]}return o}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(t=0;t<i.length;t++)o=i[t],!(r.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(e,o)&&(a[o]=e[o])}return a}(e,s),u=i||r.size||"1em";return r.className&&(o=r.className),e.className&&(o=(o?o+" ":"")+e.className),t.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,d,{className:o,style:c(c({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&t.createElement("title",null,l),e.children)};return void 0!==i?t.createElement(i.Consumer,null,e=>r(e)):r(a)}},81452:(e,r,o)=>{"use strict";o.d(r,{I:()=>t,K:()=>a});let t=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];function a(e){return t.includes(e.toLowerCase())}}},e=>{var r=r=>e(e.s=r);e.O(0,[6711,9724,3579,9688,8006,2177,8441,1684,7358],()=>r(54020)),_N_E=e.O()}]);