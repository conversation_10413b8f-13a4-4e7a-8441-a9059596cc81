(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[337],{20211:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});var o=t(95155);t(12115);var a=t(35695),i=t(29911),n=t(60440);function s(e){let{children:r,title:t,backLink:s}=e,l=(0,a.useRouter)();return(0,a.usePathname)(),(0,o.jsx)(n.A,{children:(0,o.jsxs)("div",{className:"h-full flex flex-col",children:[s&&(0,o.jsx)("div",{className:"mt-2 mb-4",children:(0,o.jsx)("button",{onClick:()=>l.push(s),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,o.jsx)(i.QVr,{className:"text-gray-600"})})}),(0,o.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-f5f5f7 overflow-y-auto flex-1 flex flex-col shadow-md",style:{minHeight:"calc(100vh - 6rem)",maxHeight:"calc(100vh - 6rem)"},children:(0,o.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:t}),r]})})]})})}t(52643),t(13568)},34603:(e,r,t)=>{"use strict";t.d(r,{d:()=>n});var o=t(95155),a=t(12115),i=t(39688);let n=a.forwardRef((e,r)=>{let{icon:t,iconPosition:a="left",isLoading:n=!1,fullWidth:s=!1,children:l,className:c,disabled:d,...u}=e;return(0,o.jsxs)("button",{ref:r,className:(0,i.QP)("rounded-md font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2","px-4 py-2 text-sm border border-gray-300 shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 disabled:opacity-50",s&&"w-full",n&&"opacity-70 cursor-not-allowed",c),disabled:d||n,"aria-busy":n,...u,children:[n&&(0,o.jsx)("span",{className:"mr-2 animate-spin"}),t&&"left"===a&&(0,o.jsx)("span",{className:"mr-2",children:t}),l,t&&"right"===a&&(0,o.jsx)("span",{className:"ml-2",children:t})]})});n.displayName="SecondaryActionButton"},52643:(e,r,t)=>{"use strict";t.d(r,{N:()=>a,b:()=>i});var o=t(73579);let a=(0,o.createClientComponentClient)();function i(){return(0,o.createClientComponentClient)()}},60440:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var o=t(95155),a=t(12115),i=t(35695),n=t(52643),s=t(81452),l=t(13568);function c(e){let{children:r}=e,t=(0,i.useRouter)(),[c,d]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:r}=await n.N.auth.getSession();if(r)throw r;if(!e){t.push("/login?redirect=/admin/campaigns");return}let o=e.user.email;o&&(0,s.K)(o)?m(!0):(l.oR.error("Voc\xea n\xe3o tem permiss\xe3o para acessar esta \xe1rea"),t.push("/"))}catch(e){console.error("Erro ao verificar status de administrador:",e),l.oR.error("Erro ao verificar suas permiss\xf5es"),t.push("/")}finally{d(!1)}})()},[t]),c)?(0,o.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):u?(0,o.jsx)(o.Fragment,{children:r}):null}},74436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>d});var o=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=o.createContext&&o.createContext(a),n=["attr","size","title"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var o,a,i;o=e,a=r,i=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,r||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in o?Object.defineProperty(o,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>o.createElement(u,s({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,t)=>o.createElement(r.tag,c({key:t},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var t,{attr:a,size:i,title:l}=e,d=function(e,r){if(null==e)return{};var t,o,a=function(e,r){if(null==e)return{};var t={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(r.indexOf(o)>=0)continue;t[o]=e[o]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)t=i[o],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,n),u=i||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),o.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,d,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&o.createElement("title",null,l),e.children)};return void 0!==i?o.createElement(i.Consumer,null,e=>r(e)):r(a)}},81452:(e,r,t)=>{"use strict";t.d(r,{I:()=>o,K:()=>a});let o=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];function a(e){return o.includes(e.toLowerCase())}},84102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var o=t(95155),a=t(12115),i=t(35695),n=t(62177),s=t(29911),l=t(13568),c=t(34603);function d(e){var r,t,i,d,u,m,f,p;let{onCancel:x,onSuccess:g,initialData:b,isEditing:h=!1}=e,[v,y]=(0,a.useState)(!1),{register:j,handleSubmit:N,formState:{errors:w}}=(0,n.mN)({defaultValues:{name:(null==b?void 0:b.name)||"",username:(null==b?void 0:b.username)||"",email:(null==b?void 0:b.email)||"",bio:(null==b?void 0:null===(r=b.profile)||void 0===r?void 0:r.bio)||"",instagram_username:(null==b?void 0:null===(t=b.profile)||void 0===t?void 0:t.instagram_username)||"",tiktok_username:(null==b?void 0:null===(i=b.profile)||void 0===i?void 0:i.tiktok_username)||"",location_city:(null==b?void 0:null===(d=b.profile)||void 0===d?void 0:d.location_city)||"",location_state:(null==b?void 0:null===(u=b.profile)||void 0===u?void 0:u.location_state)||"",follower_count:(null==b?void 0:null===(m=b.profile)||void 0===m?void 0:m.follower_count)||0,avg_engagement_rate:(null==b?void 0:null===(f=b.profile)||void 0===f?void 0:f.avg_engagement_rate)||0,classification:(null==b?void 0:b.classification)||"Standard",content_niche:(null==b?void 0:null===(p=b.profile)||void 0===p?void 0:p.content_niche)?b.profile.content_niche.join(", "):""}}),_=async e=>{try{var r,t;let o;y(!0);let a={name:e.name,username:e.username,email:e.email,classification:e.classification,profile:{bio:e.bio||"",instagram_username:e.instagram_username||"",tiktok_username:e.tiktok_username||"",location_city:e.location_city,location_state:e.location_state,follower_count:parseInt((null===(r=e.follower_count)||void 0===r?void 0:r.toString())||"0"),avg_engagement_rate:parseFloat((null===(t=e.avg_engagement_rate)||void 0===t?void 0:t.toString())||"0"),content_niche:e.content_niche?e.content_niche.split(",").map(e=>e.trim()):[],primary_platform:e.instagram_username?"instagram":"tiktok"},updated_at:new Date().toISOString()};if(h&&(null==b?void 0:b.id)){console.log("Atualizando influenciador existente:",b.id);try{let e={...a,id:b.id},r=window.location.origin,t=await fetch("".concat(r,"/api/admin/influencers/").concat(b.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Erro HTTP: ".concat(t.status))}o=await t.json(),l.oR.success("Influenciador atualizado com sucesso!")}catch(e){console.error("Erro ao atualizar influenciador:",e),l.oR.error("Erro ao atualizar influenciador: ".concat(e.message)),y(!1);return}}else{console.log("Criando novo influenciador");try{let e={...a,created_at:new Date().toISOString()},r=window.location.origin,t=await fetch("".concat(r,"/api/admin/influencers"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Erro HTTP: ".concat(t.status))}o=await t.json(),l.oR.success("Influenciador criado com sucesso!")}catch(e){console.error("Erro ao criar influenciador:",e),l.oR.error("Erro ao criar influenciador: ".concat(e.message)),y(!1);return}}o&&o.id?g(o.id):(null==b?void 0:b.id)&&g(b.id)}catch(e){console.error("Erro ao processar formul\xe1rio:",e),l.oR.error("Erro ao processar formul\xe1rio: ".concat(e.message))}finally{y(!1)}};return(0,o.jsx)("div",{className:"bg-white rounded-lg shadow-md",children:(0,o.jsxs)("form",{onSubmit:N(_),children:[(0,o.jsxs)("div",{className:"p-6 space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome Completo *"}),(0,o.jsx)("input",{type:"text",...j("name",{required:"Nome \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: Jo\xe3o Silva"}),w.name&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.name.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome de Usu\xe1rio *"}),(0,o.jsx)("input",{type:"text",...j("username",{required:"Nome de usu\xe1rio \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: joaosilva"}),w.username&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.username.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),(0,o.jsx)("input",{type:"email",...j("email",{required:"Email \xe9 obrigat\xf3rio",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Email inv\xe1lido"}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: <EMAIL>"}),w.email&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.email.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Classifica\xe7\xe3o *"}),(0,o.jsxs)("select",{...j("classification",{required:"Classifica\xe7\xe3o \xe9 obrigat\xf3ria"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,o.jsx)("option",{value:"Standard",children:"Standard"}),(0,o.jsx)("option",{value:"Bronze",children:"Bronze"}),(0,o.jsx)("option",{value:"Silver",children:"Silver"}),(0,o.jsx)("option",{value:"Gold",children:"Gold"}),(0,o.jsx)("option",{value:"Diamond",children:"Diamond"})]}),w.classification&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.classification.message})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Perfil"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bio"}),(0,o.jsx)("textarea",{...j("bio"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Descri\xe7\xe3o do influenciador",rows:3})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nichos de Conte\xfado"}),(0,o.jsx)("input",{type:"text",...j("content_niche"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: food, lifestyle, moda (separados por v\xedrgula)"})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cidade *"}),(0,o.jsx)("input",{type:"text",...j("location_city",{required:"Cidade \xe9 obrigat\xf3ria"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: S\xe3o Paulo"}),w.location_city&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.location_city.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Estado *"}),(0,o.jsx)("input",{type:"text",...j("location_state",{required:"Estado \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: SP"}),w.location_state&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:w.location_state.message})]})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Redes Sociais"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Usu\xe1rio do Instagram"}),(0,o.jsx)("input",{type:"text",...j("instagram_username"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: joaosilva (sem @)"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Usu\xe1rio do TikTok"}),(0,o.jsx)("input",{type:"text",...j("tiktok_username"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: joaosilva (sem @)"})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"M\xe9tricas"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"N\xfamero de Seguidores"}),(0,o.jsx)("input",{type:"number",...j("follower_count"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: 10000"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Taxa de Engajamento (%)"}),(0,o.jsx)("input",{type:"number",step:"0.01",...j("avg_engagement_rate"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Ex: 3.5"})]})]})]})]}),(0,o.jsxs)("div",{className:"px-6 py-4 bg-gray-50 border-t flex justify-end space-x-3 rounded-b-lg",children:[x&&(0,o.jsx)(c.d,{type:"button",onClick:x,disabled:v,icon:(0,o.jsx)(s.QCr,{}),children:"Cancelar"}),(0,o.jsxs)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",disabled:v,children:[(0,o.jsx)(s.dIn,{className:"mr-2"}),v?"Salvando...":h?"Atualizar":"Criar"]})]})]})})}var u=t(20211);function m(){let e=(0,i.useRouter)();return(0,o.jsx)(u.default,{title:"Novo Criador",backLink:"/admin/criadores",children:(0,o.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,o.jsx)(d,{initialData:{name:"",username:"",email:"",classification:"Standard",profile:{bio:"",instagram_username:"",tiktok_username:"",location_city:"",location_state:"",follower_count:0,avg_engagement_rate:0,content_niche:[],primary_platform:"instagram"}},onCancel:()=>{e.push("/admin/criadores")},onSuccess:r=>{l.oR.success("Criador criado com sucesso!"),e.push("/admin/criadores/".concat(r))},isEditing:!1})})})}},98059:(e,r,t)=>{Promise.resolve().then(t.bind(t,84102))}},e=>{var r=r=>e(e.s=r);e.O(0,[6711,9724,3579,9688,8006,2177,8441,1684,7358],()=>r(98059)),_N_E=e.O()}]);