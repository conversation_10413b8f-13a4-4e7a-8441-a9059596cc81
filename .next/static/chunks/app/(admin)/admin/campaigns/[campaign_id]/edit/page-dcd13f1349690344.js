(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2669],{48596:(a,e,r)=>{"use strict";r.d(e,{default:()=>l});var n=r(95155),t=r(35695),s=r(12115),o=r(20211),c=r(98301),i=r(13568);function d(a){let{campaignId:e}=a,r=(0,t.useRouter)(),[d,l]=(0,s.useState)(null),[m,u]=(0,s.useState)(!0),[p,h]=(0,s.useState)(null);(0,s.useEffect)(()=>{e&&(async()=>{try{u(!0),h(null),console.log("Buscando campanha com ID:",e);try{let a=await fetch("/api/admin/campaigns/".concat(e));if(a.ok){let e=await a.json();console.log("Campanha encontrada:",e.data),l(e.data),u(!1);return}}catch(a){console.error("Erro ao buscar campanha da API:",a)}console.log("Criando campanha fake para edi\xe7\xe3o"),l({id:e,name:"Campanha",description:"Descri\xe7\xe3o da campanha",restaurant_id:"",start_date:new Date().toISOString(),end_date:new Date(Date.now()+2592e6).toISOString(),status:"draft",budget:0,requirements:"",briefing:"",influencer_count_target:5,created_at:new Date().toISOString()})}catch(a){console.error("Erro ao processar campanha:",a),h(a.message||"Erro ao carregar os detalhes da campanha"),i.oR.error("N\xe3o foi poss\xedvel carregar a campanha")}finally{u(!1)}})()},[e]);let x=()=>{r.push("/admin/campaigns")};return(0,n.jsx)(o.default,{title:"Editar Campanha",backLink:"/admin/campaigns/".concat(e),children:m?(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):p?(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,n.jsx)("p",{children:p}),(0,n.jsx)("button",{onClick:x,className:"mt-2 text-red-700 hover:text-red-900 underline",children:"Voltar para a lista de campanhas"})]}):d?(0,n.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,n.jsx)(c.A,{initialData:d,onCancel:x,onSuccess:a=>{i.oR.success("Campanha atualizada com sucesso!"),r.push("/admin/campaigns/".concat(a))},isEditing:!0})}):(0,n.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md",children:[(0,n.jsx)("p",{children:"Nenhuma informa\xe7\xe3o dispon\xedvel para esta campanha."}),(0,n.jsx)("button",{onClick:x,className:"mt-2 text-yellow-700 hover:text-yellow-900 underline",children:"Voltar para a lista de campanhas"})]})})}function l(){let a=(0,t.useParams)(),e=Array.isArray(a.campaign_id)?a.campaign_id[0]:a.campaign_id;return(0,n.jsx)(d,{campaignId:e})}},71438:(a,e,r)=>{Promise.resolve().then(r.bind(r,48596))}},a=>{var e=e=>a(a.s=e);a.O(0,[6711,9724,3579,9688,8006,2177,4330,8441,1684,7358],()=>e(71438)),_N_E=a.O()}]);