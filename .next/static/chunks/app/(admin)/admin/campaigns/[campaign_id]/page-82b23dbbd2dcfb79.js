(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4908],{31239:(a,e,n)=>{Promise.resolve().then(n.bind(n,59440))},59440:(a,e,n)=>{"use strict";n.d(e,{default:()=>m});var r=n(95155),s=n(35695),l=n(12115),o=n(52643),t=n(98301),i=n(13568),c=n(20211);function d(a){let{campaignId:e}=a,n=(0,s.useRouter)(),[d,m]=(0,l.useState)(null),[u,p]=(0,l.useState)(!0),[h,x]=(0,l.useState)(null);(0,l.useEffect)(()=>{if(!e){p(!1),x("ID da campanha n\xe3o fornecido.");return}(async()=>{p(!0),x(null);try{console.log("CampaignDetailsClient: Buscando campanha com ID:",e);let{data:a,error:n}=await o.N.from("campaigns").select("*").eq("id",e).single();if(n)throw n;a?(console.log("CampaignDetailsClient: Campanha encontrada:",a),m(a)):(x("Campanha n\xe3o encontrada."),i.oR.error("Campanha n\xe3o encontrada."))}catch(a){console.error("CampaignDetailsClient: Erro ao buscar campanha:",a),x(a.message||"Erro ao carregar os detalhes da campanha"),i.oR.error("N\xe3o foi poss\xedvel carregar a campanha")}finally{p(!1)}})()},[e]);let b=()=>{n.push("/admin/campaigns")};return(0,r.jsx)(c.default,{title:"Editar Campanha",backLink:"/admin/campaigns",children:u?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):h?(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:[(0,r.jsx)("p",{children:h}),(0,r.jsx)("button",{onClick:b,className:"mt-2 text-red-700 hover:text-red-900 underline",children:"Voltar para a lista de campanhas"})]}):d?(0,r.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,r.jsx)(t.A,{initialData:d,onSuccess:a=>{i.oR.success("Campanha atualizada com sucesso!"),n.push("/admin/campaigns/".concat(a))},onCancel:b,isEditing:!0})}):(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md",children:[(0,r.jsx)("p",{children:"Nenhuma informa\xe7\xe3o dispon\xedvel para esta campanha ou campanha n\xe3o encontrada."}),(0,r.jsx)("button",{onClick:b,className:"mt-2 text-yellow-700 hover:text-yellow-900 underline",children:"Voltar para a lista de campanhas"})]})})}function m(){let a=(0,s.useParams)(),e=null==a?void 0:a.campaign_id,n=Array.isArray(e)?e[0]:e;return(0,r.jsx)(d,{campaignId:n})}}},a=>{var e=e=>a(a.s=e);a.O(0,[6711,9724,3579,9688,8006,2177,4330,8441,1684,7358],()=>e(31239)),_N_E=a.O()}]);