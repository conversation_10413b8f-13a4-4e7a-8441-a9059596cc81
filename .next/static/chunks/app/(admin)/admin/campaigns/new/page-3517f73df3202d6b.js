(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1774],{61878:(a,e,n)=>{Promise.resolve().then(n.bind(n,98192))},98192:(a,e,n)=>{"use strict";n.r(e),n.d(e,{default:()=>u});var t=n(95155);n(12115);var s=n(35695),i=n(98301),r=n(20211),c=n(13568);function u(){let a=(0,s.useRouter)(),e={name:"",description:"",restaurant_id:"",status:"draft",budget:0,requirements:"",briefing:"",influencer_count_target:5,start_date:new Date().toISOString(),end_date:new Date(Date.now()+2592e6).toISOString()};return(0,t.jsx)(r.default,{title:"Nova Campanha",backLink:"/admin/campaigns",children:(0,t.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,t.jsx)(i.A,{initialData:e,onCancel:()=>{a.push("/admin/campaigns")},onSuccess:e=>{c.oR.success("Campanha criada com sucesso!"),a.push("/admin/campaigns/".concat(e))},isEditing:!1})})})}}},a=>{var e=e=>a(a.s=e);a.O(0,[6711,9724,3579,9688,8006,2177,4330,8441,1684,7358],()=>e(61878)),_N_E=a.O()}]);