(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6427],{23017:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>m});var r=s(95155),t=s(12115),n=s(6874),l=s.n(n),i=s(52643),c=s(61257),d=s(85229),o=s(92039),u=s(29911),x=s(13568);function m(){let[e,a]=(0,t.useState)([]),[s,n]=(0,t.useState)(!0),[m,h]=(0,t.useState)(""),[p,f]=(0,t.useState)("all");(0,t.useEffect)(()=>{(async()=>{try{n(!0);let{data:e,error:s}=await i.N.from("campaigns").select("*, restaurants(name)").order("created_at",{ascending:!1});if(s)throw s;a(e||[])}catch(e){console.warn("Erro ao buscar campanhas:",e),x.oR.error("Erro ao carregar campanhas")}finally{n(!1)}})()},[]);let g=e.filter(e=>{var a,s,r,t;let n=""===m||e.name.toLowerCase().includes(m.toLowerCase())||null!==(r=null===(a=e.description)||void 0===a?void 0:a.toLowerCase().includes(m.toLowerCase()))&&void 0!==r&&r||null!==(t=null===(s=e.restaurants)||void 0===s?void 0:s.name.toLowerCase().includes(m.toLowerCase()))&&void 0!==t&&t,l="all"===p||e.status===p;return n&&l}),v=e=>e?new Date(e).toLocaleDateString("pt-BR"):"",j=e=>{switch(e){case"draft":default:return"bg-gray-100 text-gray-800";case"active":return"bg-green-100 text-green-800";case"completed":return"bg-blue-100 text-blue-800";case"cancelled":return"bg-red-100 text-red-800"}},b=e=>{switch(e){case"draft":return"Rascunho";case"active":return"Ativa";case"completed":return"Conclu\xedda";case"cancelled":return"Cancelada";default:return e}};return(0,r.jsxs)(c.A,{title:"Campanhas",children:[(0,r.jsxs)("div",{className:"mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(u.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{type:"text",placeholder:"Buscar campanhas...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",value:m,onChange:e=>h(e.target.value)})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(u.YsJ,{className:"text-gray-400"})}),(0,r.jsxs)("select",{className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",value:p,onChange:e=>f(e.target.value),children:[(0,r.jsx)("option",{value:"all",children:"Todos os status"}),(0,r.jsx)("option",{value:"draft",children:"Rascunho"}),(0,r.jsx)("option",{value:"active",children:"Ativa"}),(0,r.jsx)("option",{value:"completed",children:"Conclu\xedda"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelada"})]})]})]}),(0,r.jsx)("div",{className:"flex space-x-2",children:(0,r.jsx)(l(),{href:"/admin/campaigns/new",children:(0,r.jsxs)(d.S,{variant:"primary",size:"md",children:[(0,r.jsx)(u.OiG,{className:"mr-2"}),"Nova Campanha"]})})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md p-4 sm:p-6",children:s?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):0===g.length?(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Nenhuma campanha encontrada"}),(0,r.jsx)(l(),{href:"/admin/campaigns/new",children:(0,r.jsxs)(d.S,{variant:"primary",size:"md",children:[(0,r.jsx)(u.OiG,{className:"mr-2"}),"Criar Nova Campanha"]})})]}):(0,r.jsx)(o.bQ,{headers:["Nome","Restaurante","Per\xedodo","Status","A\xe7\xf5es"],loading:s,emptyMessage:"Nenhuma campanha encontrada",children:g.map(e=>{var a;return(0,r.jsxs)(o.J2,{children:[(0,r.jsxs)(o.Bv,{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]}),(0,r.jsx)(o.Bv,{children:(null===(a=e.restaurants)||void 0===a?void 0:a.name)||"N/A"}),(0,r.jsxs)(o.Bv,{children:[v(e.start_date)," a ",v(e.end_date)]}),(0,r.jsx)(o.Bv,{children:(0,r.jsx)("span",{className:"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(j(e.status)),children:b(e.status)})}),(0,r.jsx)(o.Bv,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,r.jsx)(l(),{href:"/admin/campaigns/".concat(e.id),children:(0,r.jsx)(d.S,{variant:"secondary",size:"sm",children:(0,r.jsx)(u.Ny1,{})})}),(0,r.jsx)(l(),{href:"/admin/campaigns/".concat(e.id,"/edit"),children:(0,r.jsx)(d.S,{variant:"secondary",size:"sm",children:(0,r.jsx)(u.uO9,{})})}),(0,r.jsx)(d.S,{variant:"destructive",size:"sm",onClick:()=>{confirm("Tem certeza que deseja excluir esta campanha?")},children:(0,r.jsx)(u.qbC,{})})]})})]},e.id)})})})]})}},41575:(e,a,s)=>{Promise.resolve().then(s.bind(s,23017))},85229:(e,a,s)=>{"use strict";s.d(a,{S:()=>l});var r=s(95155),t=s(12115),n=s(39688);let l=t.forwardRef((e,a)=>{let{variant:s="primary",size:t="md",icon:l,iconPosition:i="left",isLoading:c=!1,fullWidth:d=!1,children:o,className:u,disabled:x,...m}=e;return(0,r.jsxs)("button",{ref:a,className:(0,n.QP)("rounded-[12px] font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2",{sm:"h-[36px] px-[16px] text-[14px]",md:"h-[44px] px-[20px] text-[16px]",lg:"h-[52px] px-[24px] text-[18px]"}[t],{primary:"bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-300 disabled:bg-blue-300",secondary:"bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 focus:ring-gray-200 disabled:text-gray-400",destructive:"bg-red-500 text-white hover:bg-red-600 focus:ring-red-300 disabled:bg-red-300"}[s],d&&"w-full",c&&"opacity-70 cursor-not-allowed",u),disabled:x||c,"aria-busy":c,...m,children:[c&&(0,r.jsx)("span",{className:"mr-2 animate-spin"}),l&&"left"===i&&(0,r.jsx)("span",{className:"mr-2",children:l}),o,l&&"right"===i&&(0,r.jsx)("span",{className:"ml-2",children:l})]})});l.displayName="StandardButton"},92039:(e,a,s)=>{"use strict";s.d(a,{Bv:()=>l,J2:()=>n,bQ:()=>t});var r=s(95155);function t(e){let{headers:a,children:s,loading:t=!1,emptyMessage:n="Nenhum dado encontrado",className:l=""}=e;return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden ".concat(l),children:t?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):(0,r.jsx)("div",{className:"overflow-x-auto w-full",children:(0,r.jsxs)("table",{className:"w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsx)("tr",{children:a.map((e,a)=>(0,r.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e},a))})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s||(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:a.length,className:"px-6 py-4 text-center text-gray-500",children:n})})})]})})})}function n(e){let{children:a,className:s=""}=e;return(0,r.jsx)("tr",{className:"hover:bg-gray-50 ".concat(s),children:a})}function l(e){let{children:a,className:s=""}=e;return(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap ".concat(s),children:a})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6711,9724,3579,9688,6874,8006,5612,8441,1684,7358],()=>a(41575)),_N_E=e.O()}]);