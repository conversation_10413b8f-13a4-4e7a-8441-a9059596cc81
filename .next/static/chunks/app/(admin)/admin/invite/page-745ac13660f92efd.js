(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[349],{25173:(e,n,t)=>{Promise.resolve().then(t.bind(t,33531))},33531:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>r});var i=t(95155),a=t(12115);function r(){let[e,n]=(0,a.useState)(""),[t,r]=(0,a.useState)("restaurant"),[s,l]=(0,a.useState)(""),c=async n=>{n.preventDefault(),l("Sending invite...");let i=await fetch("/api/invite/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,role:t})}),a=await i.json();i.ok?l("Invite sent! Link: ".concat(a.inviteLink)):l("Error: ".concat(a.error))};return(0,i.jsxs)("div",{style:{padding:20},children:[(0,i.jsx)("h1",{children:"Send Invitation"}),(0,i.jsxs)("form",{onSubmit:c,children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{children:"Email:"}),(0,i.jsx)("input",{type:"email",value:e,onChange:e=>n(e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{children:"Role:"}),(0,i.jsxs)("select",{value:t,onChange:e=>r(e.target.value),children:[(0,i.jsx)("option",{value:"restaurant",children:"Restaurant"}),(0,i.jsx)("option",{value:"influencer",children:"Influencer"})]})]}),(0,i.jsx)("button",{type:"submit",children:"Send Invite"})]}),(0,i.jsx)("p",{children:s})]})}}},e=>{var n=n=>e(e.s=n);e.O(0,[8441,1684,7358],()=>n(25173)),_N_E=e.O()}]);