(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2388],{57406:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>n});var t=s(95155),a=s(12115);function n(e){let{children:l}=e;return(0,a.useEffect)(()=>{document.body.classList.add("admin-page");{let e=localStorage.getItem("admin_last_visit"),l=Date.now();(!e||l-parseInt(e,10)>6e4)&&localStorage.setItem("admin_last_visit",l.toString())}return()=>{document.body.classList.remove("admin-page")}},[]),(0,t.jsx)("div",{className:"flex flex-col min-h-screen h-screen overflow-hidden",children:(0,t.jsxs)("div",{className:"pt-12 px-4 pb-2 w-full flex flex-col",children:[" ",(0,t.jsx)("div",{className:"w-full h-full flex flex-col flex-grow overflow-hidden",children:l})]})})}},77284:(e,l,s)=>{Promise.resolve().then(s.bind(s,57406))}},e=>{var l=l=>e(e.s=l);e.O(0,[8441,1684,7358],()=>l(77284)),_N_E=e.O()}]);