(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5226],{17580:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var a=r(95155),l=r(12115),o=r(35695);function n(){let[e,s]=(0,l.useState)({email:"",fullName:"",role:"restaurant",phone:""}),[r,n]=(0,l.useState)(!1),[t,i]=(0,l.useState)(null),[d,c]=(0,l.useState)(null),[m,u]=(0,l.useState)(null),x=(0,o.useRouter)(),h=e=>{let{name:r,value:a}=e.target;s(e=>({...e,[r]:a}))},f=async r=>{r.preventDefault(),n(!0),i(null),c(null),u(null);try{let r;console.log("Enviando dados:",e);let a=await fetch("/api/admin/add-client",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("Status da resposta:",a.status),console.log("Headers da resposta:",Object.fromEntries(a.headers.entries()));let l=await a.text();console.log("Texto da resposta:",l);try{r=JSON.parse(l),console.log("Dados da resposta:",r)}catch(e){throw console.error("Erro ao analisar JSON:",e),Error("Resposta n\xe3o \xe9 um JSON v\xe1lido: ".concat(l.substring(0,100),"..."))}if(u({status:a.status,headers:Object.fromEntries(a.headers.entries()),body:r}),!a.ok)throw Error(r.error||"Erro ao adicionar cliente");c(r),s({email:"",fullName:"",role:"restaurant",phone:""})}catch(e){console.error("Erro ao adicionar cliente:",e),i(e.message||"Ocorreu um erro ao adicionar o cliente")}finally{n(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl p-6",children:(0,a.jsx)("div",{className:"md:flex",children:(0,a.jsxs)("div",{className:"p-4 w-full",children:[(0,a.jsx)("div",{className:"uppercase tracking-wide text-sm text-indigo-500 font-semibold mb-1",children:"Administra\xe7\xe3o"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Adicionar Novo Cliente (Simplificado)"}),t&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-700",children:t})})]})}),d&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 border-l-4 border-green-500 p-4 rounded",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm text-green-700",children:"Cliente adicionado com sucesso!"}),(0,a.jsxs)("div",{className:"mt-2 text-xs text-green-600",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",d.user.email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Senha tempor\xe1ria:"})," ",d.user.password]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"ID:"})," ",d.user.id]})]})]})]})}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,a.jsx)("input",{type:"email",name:"email",id:"email",value:e.email,onChange:h,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Nome Completo"}),(0,a.jsx)("input",{type:"text",name:"fullName",id:"fullName",value:e.fullName,onChange:h,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"Nome do Cliente"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Telefone (opcional)"}),(0,a.jsx)("input",{type:"tel",name:"phone",id:"phone",value:e.phone,onChange:h,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:"(00) 00000-0000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Tipo de Cliente"}),(0,a.jsxs)("select",{name:"role",id:"role",value:e.role,onChange:h,required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",children:[(0,a.jsx)("option",{value:"restaurant",children:"Restaurante"}),(0,a.jsx)("option",{value:"influencer",children:"Influenciador"})]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)("p",{className:"text-gray-500",children:"Uma senha tempor\xe1ria ser\xe1 gerada automaticamente."})})}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:r?"Adicionando...":"Adicionar Cliente"})})]}),m&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Resposta da API:"}),(0,a.jsxs)("div",{className:"bg-gray-100 p-3 rounded-md text-xs font-mono text-gray-800 max-h-60 overflow-y-auto",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Status:"})," ",m.status]}),(0,a.jsx)("p",{children:(0,a.jsx)("strong",{children:"Headers:"})}),(0,a.jsx)("pre",{children:JSON.stringify(m.headers,null,2)}),(0,a.jsx)("p",{children:(0,a.jsx)("strong",{children:"Body:"})}),(0,a.jsx)("pre",{children:JSON.stringify(m.body,null,2)})]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("button",{onClick:()=>x.back(),className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:"Voltar"})})]})})})})}},35695:(e,s,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(s,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(s,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(s,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(s,{useSearchParams:function(){return a.useSearchParams}})},65640:(e,s,r)=>{Promise.resolve().then(r.bind(r,17580))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(65640)),_N_E=e.O()}]);