(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5129],{59615:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var r=a(95155),n=a(12115),c=a(52643),i=a(61257),l=a(29911),t=a(13568),d=a(81452);function o(){let[e,s]=(0,n.useState)(!1),[a,o]=(0,n.useState)({campaigns:0,restaurants:0,influencers:0});(0,n.useEffect)(()=>{(async()=>{try{s(!0);let[e,a,r]=await Promise.all([c.N.from("campaigns").select("id",{count:"exact",head:!0}),c.N.from("restaurants").select("id",{count:"exact",head:!0}),c.N.from("influencer_profiles").select("id",{count:"exact",head:!0})]);o({campaigns:e.count||0,restaurants:a.count||0,influencers:r.count||0})}catch(e){console.error("Erro ao buscar estat\xedsticas:",e)}finally{s(!1)}})()},[]);let m=async()=>{try{s(!0);let e=window.location.origin;console.log("Fazendo requisi\xe7\xe3o para:","".concat(e,"/api/admin/create-tables"));let a=await fetch("".concat(e,"/api/admin/create-tables"),{method:"GET",headers:{"Content-Type":"application/json","Cache-Control":"no-cache"}});if(!a.ok)throw Error("Erro HTTP: ".concat(a.status));let r=await a.json();console.log("Resposta recebida:",r),r.success?t.oR.success("Tabelas verificadas/criadas com sucesso!"):t.oR.error("Erro: ".concat(r.error||"Falha ao criar tabelas"))}catch(e){console.error("Erro ao criar tabelas:",e),t.oR.error("Erro ao criar tabelas: ".concat(e instanceof Error?e.message:"Erro desconhecido"))}finally{s(!1)}};return(0,r.jsx)(i.A,{title:"Configura\xe7\xf5es",backLink:"/admin/campaigns",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(l.x$1,{className:"text-green-600 mr-2"}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Administradores"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Emails com acesso administrativo:"}),(0,r.jsx)("ul",{className:"space-y-2 bg-gray-50 p-3 rounded-md",children:d.I.map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,r.jsx)("span",{children:e})]},s))})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Para adicionar ou remover administradores, edite o arquivo de configura\xe7\xe3o."})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(l.nBS,{className:"text-green-600 mr-2"}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Banco de Dados"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Estat\xedsticas:"}),(0,r.jsxs)("ul",{className:"space-y-2 bg-gray-50 p-3 rounded-md",children:[(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Campanhas:"}),(0,r.jsx)("span",{className:"font-medium",children:e?"...":a.campaigns})]}),(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Restaurantes:"}),(0,r.jsx)("span",{className:"font-medium",children:e?"...":a.restaurants})]}),(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Influenciadores:"}),(0,r.jsx)("span",{className:"font-medium",children:e?"...":a.influencers})]})]})]}),(0,r.jsx)("button",{onClick:m,disabled:e,className:"w-full mt-4 inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:e?"Processando...":"Verificar/Criar Tabelas"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(l.Pcn,{className:"text-green-600 mr-2"}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Configura\xe7\xf5es do Sistema"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Informa\xe7\xf5es do Sistema:"}),(0,r.jsxs)("ul",{className:"space-y-2 bg-gray-50 p-3 rounded-md",children:[(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Ambiente:"}),(0,r.jsx)("span",{className:"font-medium",children:"production"})]}),(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Vers\xe3o:"}),(0,r.jsx)("span",{className:"font-medium",children:"1.0.0"})]})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Configura\xe7\xf5es adicionais podem ser definidas no arquivo .env"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(l.pXu,{className:"text-green-600 mr-2"}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Seguran\xe7a"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Configura\xe7\xf5es de Seguran\xe7a:"}),(0,r.jsxs)("ul",{className:"space-y-2 bg-gray-50 p-3 rounded-md",children:[(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Autentica\xe7\xe3o:"}),(0,r.jsx)("span",{className:"font-medium",children:"Supabase Auth"})]}),(0,r.jsxs)("li",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Prote\xe7\xe3o de Rotas:"}),(0,r.jsx)("span",{className:"font-medium",children:"Ativa"})]})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"As configura\xe7\xf5es de seguran\xe7a s\xe3o gerenciadas pelo Supabase."})]})]})})}},71129:(e,s,a)=>{Promise.resolve().then(a.bind(a,59615))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,9724,3579,6874,8006,5612,8441,1684,7358],()=>s(71129)),_N_E=e.O()}]);