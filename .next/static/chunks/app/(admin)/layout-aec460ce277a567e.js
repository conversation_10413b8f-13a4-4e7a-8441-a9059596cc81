(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4012],{19558:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(95155),n=t(51493);function i(e){let{children:r}=e;return(0,s.jsx)("div",{className:"min-h-screen h-screen bg-f5f5f7 overflow-hidden",children:(0,s.jsx)(n.C,{children:r})})}},51493:(e,r,t)=>{"use strict";t.d(r,{C:()=>u,y:()=>d});var s=t(95155),n=t(12115);let i=(0,n.createContext)(void 0),u=e=>{let{children:r}=e,[t,u]=(0,n.useState)("crIAdores"),[d,l]=(0,n.useState)(null);return(0,s.jsx)(i.Provider,{value:{appBarTitle:t,setAppBarTitle:u,appBarTrailing:d,setAppBarTrailing:l},children:r})},d=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAppBar must be used within an AppBarProvider");return e}},83390:(e,r,t)=>{Promise.resolve().then(t.bind(t,19558))}},e=>{var r=r=>e(e.s=r);e.O(0,[8441,1684,7358],()=>r(83390)),_N_E=e.O()}]);