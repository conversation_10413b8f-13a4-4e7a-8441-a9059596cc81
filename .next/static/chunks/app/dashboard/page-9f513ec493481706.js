(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{35695:(e,r,o)=>{"use strict";var a=o(18999);o.o(a,"useParams")&&o.d(r,{useParams:function(){return a.useParams}}),o.o(a,"usePathname")&&o.d(r,{usePathname:function(){return a.usePathname}}),o.o(a,"useRouter")&&o.d(r,{useRouter:function(){return a.useRouter}}),o.o(a,"useSearchParams")&&o.d(r,{useSearchParams:function(){return a.useSearchParams}})},44879:(e,r,o)=>{"use strict";o.r(r),o.d(r,{default:()=>i});var a=o(95155),n=o(12115),s=o(35695),t=o(73970);function i(){(0,s.useRouter)();let[e,r]=(0,n.useState)(!0),[o,i]=(0,n.useState)(null);return(0,n.useEffect)(()=>{(async()=>{try{r(!0),i(null);let{data:{session:e},error:o}=await t.N.auth.getSession();if(o){console.error("Erro ao obter sess\xe3o:",o),i("Erro ao obter sess\xe3o: ".concat(o.message)),window.location.href="/login";return}if(!e){console.log("Nenhuma sess\xe3o encontrada, redirecionando para login"),window.location.href="/login";return}let{data:a,error:n}=await t.N.from("profiles").select("*").eq("id",e.user.id).single();if(n){console.error("Erro ao obter perfil:",n),i("Erro ao obter perfil: ".concat(n.message)),window.location.href="/login";return}if(!a){console.error("Nenhum perfil encontrado para o usu\xe1rio"),i("Nenhum perfil encontrado para o usu\xe1rio"),window.location.href="/login";return}"influencer"===a.role?(console.log("Usu\xe1rio \xe9 influenciador, redirecionando para /influenciador"),window.location.href="/influenciador"):"restaurant"===a.role?(console.log("Usu\xe1rio \xe9 restaurante, redirecionando para /restaurante"),window.location.href="/restaurante"):(console.error("Papel desconhecido:",a.role),i("Papel desconhecido: ".concat(a.role)),window.location.href="/login")}catch(e){console.error("Erro inesperado:",e),i("Erro inesperado: ".concat(e.message)),window.location.href="/login"}finally{r(!1)}})()},[]),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md w-full",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4 text-center",children:"Dashboard"}),e&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Redirecionando para o dashboard apropriado..."})]}),o&&(0,a.jsxs)("div",{className:"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4",children:[(0,a.jsx)("p",{children:o}),(0,a.jsx)("p",{className:"mt-2",children:"Por favor, tente fazer login novamente."})]}),o&&(0,a.jsx)("div",{className:"mt-6 flex justify-center",children:(0,a.jsx)("a",{href:"/login",className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition",children:"Ir para Login"})})]})})}},53409:(e,r,o)=>{Promise.resolve().then(o.bind(o,44879))},73970:(e,r,o)=>{"use strict";o.d(r,{N:()=>a});let a=(0,o(73579).createClientComponentClient)()}},e=>{var r=r=>e(e.s=r);e.O(0,[9724,3579,8441,1684,7358],()=>r(53409)),_N_E=e.O()}]);