(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6360],{11954:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),s=r(12115),n=r(73579),i=r(66695),l=r(17313),c=r(95486),o=r(51154),d=r(29911);function u(){let e=(0,n.createClientComponentClient)(),[t,r]=(0,s.useState)(!1),[u,f]=(0,s.useState)(!0),[p,v]=(0,s.useState)("my-messages");return((0,s.useEffect)(()=>{(async function(){try{let{data:{session:t}}=await e.auth.getSession();if(!t){r(!1);return}let{data:a}=await e.from("profiles").select("role").eq("id",t.user.id).single();r((null==a?void 0:a.role)==="admin")}catch(e){console.error("Erro ao verificar permiss\xf5es:",e),r(!1)}finally{f(!1)}})()},[e]),u)?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-6 flex items-center",children:[(0,a.jsx)(d.EcP,{className:"text-green-500 mr-3"}),"Mensagens do WhatsApp"]}),(0,a.jsxs)(l.tU,{value:p,onValueChange:v,className:"w-full",children:[(0,a.jsxs)(l.j7,{className:"mb-6",children:[(0,a.jsx)(l.Xi,{value:"my-messages",children:"Minhas Mensagens"}),t&&(0,a.jsx)(l.Xi,{value:"all-messages",children:"Todas as Mensagens"})]}),(0,a.jsx)(l.av,{value:"my-messages",children:(0,a.jsx)(c.A,{limit:20})}),t&&(0,a.jsx)(l.av,{value:"all-messages",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Todas as Mensagens"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(m,{})})]})})]})]})}function m(){let e=(0,n.createClientComponentClient)(),[t,r]=(0,s.useState)([]),[i,l]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{(async function(){try{l(!0);let{data:t,error:a}=await e.from("whatsapp_messages").select("\n            id,\n            user_id,\n            phone_number,\n            direction,\n            message,\n            status,\n            sent_at,\n            delivered_at,\n            read_at,\n            profiles(full_name, username)\n          ").order("sent_at",{ascending:!1}).limit(50);if(a)throw a;r(t||[])}catch(e){console.error("Erro ao carregar mensagens:",e)}finally{l(!1)}})()},[e]),i)?(0,a.jsx)("div",{className:"flex justify-center p-4",children:(0,a.jsx)(o.A,{className:"h-6 w-6 animate-spin text-gray-500"})}):0===t.length?(0,a.jsx)("div",{className:"text-center text-gray-500 py-8",children:"Nenhuma mensagem encontrada"}):(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>{var t,r;return(0,a.jsxs)("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:(null===(t=e.profiles)||void 0===t?void 0:t.full_name)||"Usu\xe1rio desconhecido"}),(0,a.jsx)("span",{className:"text-sm text-gray-500 ml-2",children:(null===(r=e.profiles)||void 0===r?void 0:r.username)?"@".concat(e.profiles.username):e.phone_number})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.sent_at).toLocaleString("pt-BR")})]}),(0,a.jsxs)("div",{className:"p-3 rounded-lg ".concat("inbound"===e.direction?"bg-gray-100":"bg-green-50"),children:[(0,a.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:["inbound"===e.direction?"Recebida":"Enviada"," •",(0,a.jsxs)("span",{className:"ml-1",children:["sent"===e.status&&"Enviada","delivered"===e.status&&"Entregue","read"===e.status&&"Lida","failed"===e.status&&"Falha","received"===e.status&&"Recebida"]})]}),(0,a.jsx)("p",{className:"whitespace-pre-wrap",children:e.message})]})]},e.id)})})}},12722:(e,t,r)=>{Promise.resolve().then(r.bind(r,11954))},17313:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>l});var a=r(95155),s=r(12115),n=r(36217),i=r(59434);let l=n.bL,c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...s})});c.displayName=n.B8.displayName;let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...s})});o.displayName=n.l9.displayName;let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...s})});d.displayName=n.UC.displayName},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:o="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:i?24*Number(n)/Number(s):n,className:l("lucide",o),...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:c,...d}=r;return(0,a.createElement)(o,{ref:n,iconNode:t,className:l("lucide-".concat(s(i(e))),"lucide-".concat(e),c),...d})});return r.displayName=i(e),r}},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(95155);r(12115);var s=r(74466),n=r(59434);let i=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200/80"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...s})}},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>l,wL:()=>u});var a=r(95155),s=r(12115),n=r(59434);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});c.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});o.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var a=r(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(s),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var a,s,n;a=e,s=t,n=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in a?Object.defineProperty(a,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>a.createElement(u,l({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:s,size:n,title:c}=e,d=function(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,i),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>t(e)):t(s)}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var a=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.$,i=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,c=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let n=s(t)||s(a);return i[e][n]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return n(e,c,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:r,className:a,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...o}[t]):({...l,...o})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},95486:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(95155),s=r(12115),n=r(73579),i=r(66695),l=r(26126),c=r(51154),o=r(29911);function d(e){let{userId:t,limit:r=10,showHeader:d=!0,className:u=""}=e,m=(0,n.createClientComponentClient)(),[f,p]=(0,s.useState)([]),[v,g]=(0,s.useState)(!0),[x,h]=(0,s.useState)(null);return((0,s.useEffect)(()=>{(async function(){try{g(!0),h(null);let e=t;if(!e){let{data:{session:t}}=await m.auth.getSession();if(!t){h("Usu\xe1rio n\xe3o autenticado");return}e=t.user.id}let{data:a,error:s}=await m.from("whatsapp_messages").select("*").eq("user_id",e).order("sent_at",{ascending:!1}).limit(r);if(s)throw s;p(a||[])}catch(e){console.error("Erro ao carregar mensagens:",e),h("N\xe3o foi poss\xedvel carregar o hist\xf3rico de mensagens")}finally{g(!1)}})()},[m,t,r]),v)?(0,a.jsx)("div",{className:"flex justify-center items-center p-4",children:(0,a.jsx)(c.A,{className:"h-6 w-6 animate-spin text-gray-500"})}):x?(0,a.jsx)("div",{className:"text-red-500 p-4 text-center",children:x}):0===f.length?(0,a.jsx)("div",{className:"text-gray-500 p-4 text-center",children:"Nenhuma mensagem encontrada"}):(0,a.jsxs)(i.Zp,{className:u,children:[d&&(0,a.jsx)(i.aR,{className:"pb-2",children:(0,a.jsxs)(i.ZB,{className:"text-lg flex items-center",children:[(0,a.jsx)(o.EcP,{className:"text-green-500 mr-2"}),"Hist\xf3rico de Mensagens"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,a.jsx)("div",{className:"flex ".concat("inbound"===e.direction?"justify-start":"justify-end"),children:(0,a.jsxs)("div",{className:"\n                  max-w-[80%] rounded-lg p-3 \n                  ".concat("inbound"===e.direction?"bg-gray-100 text-gray-800":"bg-green-50 text-green-800","\n                "),children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 mb-1 text-xs text-gray-500",children:["inbound"===e.direction?(0,a.jsx)(o.QVr,{className:"text-blue-500"}):(0,a.jsx)(o.Z0P,{className:"text-green-500"}),(0,a.jsx)("span",{children:function(e){if(!e)return"";let t=new Date(e);return new Intl.DateTimeFormat("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}(e.sent_at)}),(0,a.jsx)("span",{className:"mx-1",children:"•"}),function(e){let t={sent:{label:"Enviada",variant:"default"},delivered:{label:"Entregue",variant:"secondary"},read:{label:"Lida",variant:"success"},failed:{label:"Falha",variant:"destructive"},received:{label:"Recebida",variant:"outline"}}[e]||{label:e,variant:"default"};return(0,a.jsx)(l.E,{variant:t.variant,className:"text-xs",children:t.label})}(e.status)]}),(0,a.jsx)("p",{className:"whitespace-pre-wrap break-words",children:e.message})]})},e.id))})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,9724,3579,9688,7084,8441,1684,7358],()=>t(12722)),_N_E=e.O()}]);