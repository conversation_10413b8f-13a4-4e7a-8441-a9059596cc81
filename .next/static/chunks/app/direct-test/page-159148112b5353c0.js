(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5381],{86469:(e,s,n)=>{Promise.resolve().then(n.bind(n,90099))},90099:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>i});var l=n(95155),t=n(12115),r=n(99724);function i(){var e;let[s,n]=(0,t.useState)(!0),[i,a]=(0,t.useState)(null),[c,o]=(0,t.useState)(null),[d,m]=(0,t.useState)(null),[h,p]=(0,t.useState)(!1),[u,b]=(0,t.useState)(null);(0,t.useEffect)(()=>{!async function(){try{n(!0),a(null);let e=(0,r.UU)("https://pbehloddlzwandfmpzbo.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38");console.log("Testing Supabase connection...");let{data:s,error:l}=await e.from("campaigns").select("*").limit(5);if(l)throw console.error("Error fetching campaigns:",l),l;console.log("Campaigns fetched successfully:",s),o({campaigns:s})}catch(e){console.error("Error testing connection:",e),a("Error testing connection: ".concat(e instanceof Error?e.message:String(e)))}finally{n(!1)}}()},[]);let x=async()=>{try{p(!0),b(null);let e=await fetch("/api/test-supabase"),s=await e.json();if(!e.ok)throw Error(s.message||"API request failed");console.log("API response:",s),m(s)}catch(e){console.error("API test error:",e),b("API test error: ".concat(e instanceof Error?e.message:String(e)))}finally{p(!1)}};return(0,l.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Direct Supabase Test"}),(0,l.jsxs)("div",{className:"mb-8 p-6 bg-white rounded-lg shadow-md",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Client-Side Test"}),s?(0,l.jsx)("p",{children:"Testing connection..."}):i?(0,l.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:[(0,l.jsx)("p",{className:"font-bold",children:"Error:"}),(0,l.jsx)("p",{children:i})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-green-600 font-semibold mb-4",children:"Connection successful!"}),(0,l.jsx)("h3",{className:"font-medium mb-2",children:"Campaigns:"}),(null==c?void 0:null===(e=c.campaigns)||void 0===e?void 0:e.length)>0?(0,l.jsx)("ul",{className:"list-disc pl-5",children:c.campaigns.map(e=>(0,l.jsxs)("li",{className:"mb-2",children:[e.name," (ID: ",e.id,")"]},e.id))}):(0,l.jsx)("p",{children:"No campaigns found."})]})]}),(0,l.jsxs)("div",{className:"mb-8 p-6 bg-white rounded-lg shadow-md",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"API Endpoint Test"}),(0,l.jsx)("button",{onClick:x,disabled:h,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors mb-4",children:h?"Testing...":"Test API Endpoint"}),u&&(0,l.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,l.jsx)("p",{className:"font-bold",children:"API Error:"}),(0,l.jsx)("p",{children:u})]}),d&&(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-semibold mb-4 ".concat(d.success?"text-green-600":"text-red-600"),children:d.success?"API test successful!":"API test failed!"}),(0,l.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto max-h-96",children:JSON.stringify(d,null,2)})]})]}),(0,l.jsxs)("div",{className:"p-6 bg-white rounded-lg shadow-md",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Variables"}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"NEXT_PUBLIC_SUPABASE_URL:"})," ","https://pbehloddlzwandfmpzbo.supabase.co"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"NEXT_PUBLIC_SUPABASE_ANON_KEY:"})," ","Set (hidden)"]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9724,8441,1684,7358],()=>s(86469)),_N_E=e.O()}]);