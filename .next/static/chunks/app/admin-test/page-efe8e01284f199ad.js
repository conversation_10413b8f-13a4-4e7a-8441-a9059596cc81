(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4885],{40459:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(95155),a=s(12115),n=s(99724);function i(){var e;let[r,s]=(0,a.useState)(!0),[i,l]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[o,m]=(0,a.useState)(null),h=(0,n.UU)("https://pbehloddlzwandfmpzbo.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgwMjA2MCwiZXhwIjoyMDU5Mzc4MDYwfQ.E4lpZHVbvr97hZCpn784Hoi0rBeawDQQu0YV8-ec9Ys");(0,a.useEffect)(()=>{!async function(){try{s(!0),l(null),console.log("Fetching data with admin client...");let{data:e,error:r}=await h.from("campaigns").select("*").limit(5);if(r)throw console.error("Error fetching campaigns:",r),r;console.log("Campaigns fetched successfully:",e),d({campaigns:e})}catch(e){console.error("Error fetching data:",e),l("Error fetching data: ".concat(e instanceof Error?e.message:String(e)))}finally{s(!1)}}()},[]);let b=async()=>{try{let e;s(!0),l(null),m(null);let{data:r,error:t}=await h.from("restaurant_profiles").select("id").limit(1);if(t)throw t;if(r&&0!==r.length)e=r[0].id;else{let r={business_name:"Test Restaurant ".concat(new Date().toISOString().substring(0,10)),city:"Test City",state:"TS",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:s,error:t}=await h.from("restaurant_profiles").insert(r).select();if(t)throw t;e=s[0].id}let a={name:"Admin Test Campaign ".concat(new Date().toISOString().substring(0,10)),description:"Campaign created by admin test",restaurant_id:e,status:"active",created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:n,error:i}=await h.from("campaigns").insert(a).select();if(i)throw i;m({success:!0,campaign:n[0]});let{data:c}=await h.from("campaigns").select("*").limit(10);d({campaigns:c})}catch(e){console.error("Error creating test campaign:",e),m({success:!1,error:e instanceof Error?e.message:String(e)})}finally{s(!1)}};return(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Supabase Admin Test"}),(0,t.jsxs)("div",{className:"mb-8 p-6 bg-white rounded-lg shadow-md",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Admin Client Test"}),(0,t.jsx)("button",{onClick:b,disabled:r,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors",children:r?"Processing...":"Create Test Campaign"})]}),r&&!o?(0,t.jsx)("p",{children:"Loading data..."}):i?(0,t.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded",children:[(0,t.jsx)("p",{className:"font-bold",children:"Error:"}),(0,t.jsx)("p",{children:i})]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-green-600 font-semibold mb-4",children:"Connection successful!"}),(0,t.jsx)("h3",{className:"font-medium mb-2",children:"Campaigns:"}),(null==c?void 0:null===(e=c.campaigns)||void 0===e?void 0:e.length)>0?(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full bg-white border border-gray-300",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"py-2 px-4 border-b border-gray-300 text-left",children:"ID"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b border-gray-300 text-left",children:"Name"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b border-gray-300 text-left",children:"Restaurant ID"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b border-gray-300 text-left",children:"Status"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b border-gray-300 text-left",children:"Created At"})]})}),(0,t.jsx)("tbody",{children:c.campaigns.map(e=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"py-2 px-4 border-b border-gray-300",children:e.id}),(0,t.jsx)("td",{className:"py-2 px-4 border-b border-gray-300",children:e.name}),(0,t.jsx)("td",{className:"py-2 px-4 border-b border-gray-300",children:e.restaurant_id}),(0,t.jsx)("td",{className:"py-2 px-4 border-b border-gray-300",children:e.status||"N/A"}),(0,t.jsx)("td",{className:"py-2 px-4 border-b border-gray-300",children:new Date(e.created_at).toLocaleString()})]},e.id))})]})}):(0,t.jsx)("p",{children:"No campaigns found."})]}),o&&(0,t.jsxs)("div",{className:"mt-6 p-4 rounded ".concat(o.success?"bg-green-100":"bg-red-100"),children:[(0,t.jsx)("h3",{className:"font-medium mb-2",children:"Test Result:"}),o.success?(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-green-600 mb-2",children:"Campaign created successfully!"}),(0,t.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto max-h-60",children:JSON.stringify(o.campaign,null,2)})]}):(0,t.jsx)("p",{className:"text-red-600",children:o.error})]})]})]})}},85197:(e,r,s)=>{Promise.resolve().then(s.bind(s,40459))}},e=>{var r=r=>e(e.s=r);e.O(0,[9724,8441,1684,7358],()=>r(85197)),_N_E=e.O()}]);