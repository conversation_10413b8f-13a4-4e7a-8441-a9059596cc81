(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[159],{90229:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var l=r(95155),a=r(12115);function n(){let[e,s]=(0,a.useState)(null),[r,n]=(0,a.useState)(!1),[t,c]=(0,a.useState)(null),i=async()=>{try{n(!0),c(null);let e=await fetch("/api/test/create-simple-user"),r=await e.json();if(!e.ok)throw console.error("Error response:",r),Error(r.error||"Failed to create user account");s(r)}catch(e){console.error("Error creating account:",e),c(e.message)}finally{n(!1)}};return(0,l.jsxs)("div",{className:"p-8 max-w-2xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Create Simple User Account"}),(0,l.jsx)("p",{className:"mb-4 text-gray-600",children:"This page creates a basic user account with the following details:"}),(0,l.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Password:"})," Test@123456"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Name:"})," Gustavo Caliani"]})]}),(0,l.jsx)("button",{onClick:i,disabled:r,className:"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50",children:r?"Creating...":"Create User Account"}),t&&(0,l.jsxs)("div",{className:"mt-4 p-4 bg-red-100 text-red-700 rounded-lg",children:[(0,l.jsx)("p",{className:"font-bold",children:"Error:"}),(0,l.jsx)("p",{children:t}),(0,l.jsx)("p",{className:"mt-2 text-sm",children:"Check the browser console for more details."})]}),e&&(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Account Created Successfully!"}),(0,l.jsxs)("div",{className:"bg-green-100 p-4 rounded-lg",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"User ID:"})," ",e.user.id]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Email:"})," ",e.user.email]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Password:"})," ",e.user.password]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Name:"})," ",e.user.name]})]}),(0,l.jsx)("div",{className:"mt-4",children:(0,l.jsx)("a",{href:e.loginUrl,className:"bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded inline-block",children:"Go to Login Page"})})]})]})}},99389:(e,s,r)=>{Promise.resolve().then(r.bind(r,90229))}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(99389)),_N_E=e.O()}]);