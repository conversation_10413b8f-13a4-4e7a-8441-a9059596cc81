(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3852],{12564:(e,s,r)=>{Promise.resolve().then(r.bind(r,88446))},88446:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var n=r(95155),l=r(12115);function a(){let[e,s]=(0,l.useState)(null),[r,a]=(0,l.useState)(!1),[c,t]=(0,l.useState)(null),i=async()=>{try{a(!0),t(null);let e=await fetch("/api/test/create-influencer"),r=await e.json();if(!e.ok)throw console.error("Error response:",r),Error(r.error||"Failed to create influencer account");s(r)}catch(e){console.error("Error creating account:",e),t(e.message)}finally{a(!1)}};return(0,n.jsxs)("div",{className:"p-8 max-w-2xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Create Test Influencer Account"}),(0,n.jsx)("p",{className:"mb-4 text-gray-600",children:"This page creates a test influencer account with the following details:"}),(0,n.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Password:"})," Test@123456"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Name:"})," Gustavo Caliani"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Username:"})," gustavucaliani"]})]}),(0,n.jsx)("button",{onClick:i,disabled:r,className:"bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50",children:r?"Creating...":"Create Influencer Account"}),c&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-red-100 text-red-700 rounded-lg",children:[(0,n.jsx)("p",{className:"font-bold",children:"Error:"}),(0,n.jsx)("p",{children:c}),(0,n.jsx)("p",{className:"mt-2 text-sm",children:"Check the browser console for more details."})]}),e&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Account Created Successfully!"}),(0,n.jsxs)("div",{className:"bg-green-100 p-4 rounded-lg",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"User ID:"})," ",e.user.id]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Email:"})," ",e.user.email]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Password:"})," ",e.user.password]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Name:"})," ",e.user.name]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Username:"})," ",e.user.username]})]}),(0,n.jsx)("div",{className:"mt-4",children:(0,n.jsx)("a",{href:e.loginUrl,className:"bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded inline-block",children:"Go to Login Page"})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(12564)),_N_E=e.O()}]);