(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7724],{74436:(e,s,t)=>{"use strict";t.d(s,{k5:()=>o});var a=t(12115),r={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(r),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var t=arguments[s];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e}).apply(this,arguments)}function c(e,s){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);s&&(a=a.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),t.push.apply(t,a)}return t}function d(e){for(var s=1;s<arguments.length;s++){var t=null!=arguments[s]?arguments[s]:{};s%2?c(Object(t),!0).forEach(function(s){var a,r,n;a=e,r=s,n=t[s],(r=function(e){var s=function(e,s){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,s||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===s?String:Number)(e)}(e,"string");return"symbol"==typeof s?s:s+""}(r))in a?Object.defineProperty(a,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})}return e}function o(e){return s=>a.createElement(m,l({attr:d({},e.attr)},s),function e(s){return s&&s.map((s,t)=>a.createElement(s.tag,d({key:t},s.attr),e(s.child)))}(e.child))}function m(e){var s=s=>{var t,{attr:r,size:n,title:c}=e,o=function(e,s){if(null==e)return{};var t,a,r=function(e,s){if(null==e)return{};var t={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(s.indexOf(a)>=0)continue;t[a]=e[a]}return t}(e,s);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)t=n[a],!(s.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(r[t]=e[t])}return r}(e,i),m=n||s.size||"1em";return s.className&&(t=s.className),e.className&&(t=(t?t+" ":"")+e.className),a.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},s.attr,r,o,{className:t,style:d(d({color:e.color||s.color},s.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>s(e)):s(r)}},84536:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(95155),r=t(12115),n=t(73579),i=t(25593),l=t(44912),c=t(15891),d=t(4573),o=t(66766),m=t(6874),x=t.n(m);function u(e){let{userId:s,limit:t=6}=e,[n,i]=(0,r.useState)([]),[m,u]=(0,r.useState)(!0),[h,g]=(0,r.useState)(null);return((0,r.useEffect)(()=>{async function e(){try{u(!0),g(null);let e=await (0,l.MH)(s,t);i(e)}catch(e){console.error("Erro ao carregar posts do Instagram:",e),g("N\xe3o foi poss\xedvel carregar as postagens do Instagram")}finally{u(!1)}}s&&e()},[s,t]),m)?(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:Array.from({length:t}).map((e,s)=>(0,a.jsx)("div",{className:"aspect-square bg-gray-200 animate-pulse rounded-md"},s))}):h?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:h}):0===n.length?(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 p-4 rounded-md text-center",children:"Nenhuma postagem encontrada."}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:n.map(e=>{var s;return(0,a.jsx)(x(),{href:e.permalink,target:"_blank",rel:"noopener noreferrer",className:"block overflow-hidden rounded-md hover:opacity-90 transition-opacity",children:(0,a.jsxs)("div",{className:"relative aspect-square",children:[(0,a.jsx)(o.default,{src:e.media_url||e.thumbnail_url||"",alt:(null===(s=e.caption)||void 0===s?void 0:s.substring(0,50))||"Instagram post",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 50vw, 33vw"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all flex items-end p-2",children:(0,a.jsx)("div",{className:"text-white text-xs opacity-0 hover:opacity-100 transition-opacity",children:(0,c.m)(new Date(e.timestamp),{addSuffix:!0,locale:d.F})})})]})},e.id)})})}function h(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e>=1e6?"".concat((e/1e6).toFixed(s),"M"):e>=1e3?"".concat((e/1e3).toFixed(s),"K"):e.toString()}function g(e){var s;let{restaurantId:t}=e,[i,l]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),[o,m]=(0,r.useState)(null),x=(0,n.createClientComponentClient)();if((0,r.useEffect)(()=>{async function e(){try{d(!0),m(null);let{data:e,error:s}=await x.from("digital_presence").select("*").eq("restaurant_id",t).order("last_sync_date",{ascending:!1}).limit(1).single();if(s)throw s;l(e)}catch(e){console.error("Erro ao carregar m\xe9tricas do Instagram:",e),m("N\xe3o foi poss\xedvel carregar as m\xe9tricas")}finally{d(!1)}}t&&e()},[t,x]),c)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm animate-pulse",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"})]});if(o)return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:o});if(!i||!i.instagram_followers)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Instagram"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Dados n\xe3o dispon\xedveis"})]});let u=i.instagram_previous_followers?i.instagram_followers-i.instagram_previous_followers:0,g=i.instagram_previous_followers&&i.instagram_previous_followers>0?(u/i.instagram_previous_followers*100).toFixed(1):"0";return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Instagram"}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h(i.instagram_followers)}),(0,a.jsx)("div",{className:"flex items-center mt-1",children:(0,a.jsxs)("span",{className:"text-sm ".concat(u>=0?"text-green-500":"text-red-500"),children:[u>=0?"+":"",h(u),(0,a.jsxs)("span",{className:"text-xs ml-1",children:["(",g,"%)"]})]})})]}),(0,a.jsxs)("div",{className:"mt-3 grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Taxa de Engajamento"}),(0,a.jsxs)("div",{className:"font-medium",children:[null===(s=i.instagram_engagement_rate)||void 0===s?void 0:s.toFixed(1),"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Posts Recentes"}),(0,a.jsx)("div",{className:"font-medium",children:i.instagram_recent_posts||0})]})]}),i.last_sync_date&&(0,a.jsxs)("div",{className:"mt-3 text-xs text-gray-400",children:["Atualizado em ",new Date(i.last_sync_date).toLocaleDateString("pt-BR")]})]})}var j=t(29911);function p(e){let{userId:s}=e,[t,i]=(0,r.useState)(null),[l,c]=(0,r.useState)(!0),[d,o]=(0,r.useState)(null),m=(0,n.createClientComponentClient)();if((0,r.useEffect)(()=>{async function e(){try{c(!0),o(null);let{data:e,error:t}=await m.from("social_connections").select("id, platform, username, is_active, token_expires_at, created_at, updated_at").eq("user_id",s).eq("platform","instagram").single();if(t&&"PGRST116"!==t.code)throw t;i(e)}catch(e){console.error("Erro ao buscar conex\xe3o com Instagram:",e),o("N\xe3o foi poss\xedvel verificar a conex\xe3o com o Instagram")}finally{c(!1)}}s&&e()},[s,m]),l)return(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,a.jsx)(j.ao$,{className:"animate-pulse"}),(0,a.jsx)("span",{children:"Verificando conex\xe3o..."})]});if(d)return(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-red-500",children:[(0,a.jsx)(j.BS8,{}),(0,a.jsx)("span",{children:d})]});if(!t)return(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,a.jsx)(j._Hm,{className:"text-gray-400"}),(0,a.jsx)("span",{children:"N\xe3o conectado ao Instagram"})]});let x=!!t.token_expires_at&&new Date(t.token_expires_at)<new Date,u=t.token_expires_at?Math.floor((new Date(t.token_expires_at).getTime()-new Date().getTime())/864e5):null,h=null!==u&&u<7&&u>=0;return!t.is_active||x?(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-red-500",children:[(0,a.jsx)(j._Hm,{}),(0,a.jsx)("span",{children:x?"Token do Instagram expirado":"Conex\xe3o com Instagram inativa"})]}):h?(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-yellow-500",children:[(0,a.jsx)(j.BS8,{}),(0,a.jsxs)("span",{children:["Conectado como @",t.username,(0,a.jsxs)("span",{className:"ml-1 text-xs",children:["(expira em ",u," ",1===u?"dia":"dias",")"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-500",children:[(0,a.jsx)(j.A7C,{}),(0,a.jsxs)("span",{children:["Conectado como @",t.username]})]})}function f(e){let{userId:s}=e,[t,i]=(0,r.useState)(null),[l,c]=(0,r.useState)(!0),[d,o]=(0,r.useState)(null),m=(0,n.createClientComponentClient)();return((0,r.useEffect)(()=>{async function e(){try{c(!0),o(null);let{data:e,error:t}=await m.from("posts").select("\n            id,\n            likes_count,\n            comments_count,\n            saves_count,\n            views_count,\n            engagement_rate\n          ").eq("campaign_influencer.influencer_id",s).eq("platform","instagram");if(t)throw t;if(!e||0===e.length){i({totalPosts:0,totalLikes:0,totalComments:0,totalSaves:0,totalViews:0,avgEngagementRate:0});return}let a=e.length,r=e.reduce((e,s)=>e+(s.likes_count||0),0),n=e.reduce((e,s)=>e+(s.comments_count||0),0),l=e.reduce((e,s)=>e+(s.saves_count||0),0),d=e.reduce((e,s)=>e+(s.views_count||0),0),x=e.reduce((e,s)=>e+(s.engagement_rate||0),0)/a;i({totalPosts:a,totalLikes:r,totalComments:n,totalSaves:l,totalViews:d,avgEngagementRate:x})}catch(e){console.error("Erro ao buscar estat\xedsticas do Instagram:",e),o("N\xe3o foi poss\xedvel carregar as estat\xedsticas")}finally{c(!1)}}s&&e()},[s,m]),l)?(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm animate-pulse",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"grid grid-cols-4 gap-4",children:Array.from({length:4}).map((e,s)=>(0,a.jsx)("div",{className:"h-12 bg-gray-200 rounded"},s))})]}):d?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:d}):t?(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(j.ao$,{className:"text-pink-500 mr-2"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Estat\xedsticas do Instagram"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Posts"}),(0,a.jsx)("div",{className:"text-lg font-bold",children:t.totalPosts})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Curtidas"}),(0,a.jsx)("div",{className:"text-lg font-bold text-red-500",children:(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)(j.Mbv,{className:"mr-1 text-sm"}),h(t.totalLikes)]})})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Coment\xe1rios"}),(0,a.jsx)("div",{className:"text-lg font-bold text-blue-500",children:(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)(j.j1Q,{className:"mr-1 text-sm"}),h(t.totalComments)]})})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Salvamentos"}),(0,a.jsx)("div",{className:"text-lg font-bold text-green-500",children:(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)(j.U$b,{className:"mr-1 text-sm"}),h(t.totalSaves)]})})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Engajamento"}),(0,a.jsxs)("div",{className:"text-lg font-bold text-purple-500",children:[t.avgEngagementRate.toFixed(1),"%"]})]})]})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Estat\xedsticas do Instagram"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Dados n\xe3o dispon\xedveis"})]})}function v(e){let{userId:s,limit:t=10}=e,[i,l]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[o,m]=(0,r.useState)(null),x=(0,n.createClientComponentClient)();if((0,r.useEffect)(()=>{async function e(){try{d(!0),m(null);let{data:e,error:a}=await x.from("posts").select("\n            id,\n            caption,\n            post_url,\n            created_at,\n            likes_count,\n            comments_count,\n            saves_count,\n            engagement_rate\n          ").eq("campaign_influencer.influencer_id",s).eq("platform","instagram").order("created_at",{ascending:!1}).limit(t);if(a)throw a;l(e||[])}catch(e){console.error("Erro ao buscar posts do Instagram:",e),m("N\xe3o foi poss\xedvel carregar os dados de engajamento")}finally{d(!1)}}s&&e()},[s,t,x]),c)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm animate-pulse",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-60 bg-gray-200 rounded w-full"})]});if(o)return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:o});if(0===i.length)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(j.ao$,{className:"text-pink-500 mr-2"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Engajamento do Instagram"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Nenhum post encontrado para an\xe1lise"})]});let u=[...i].sort((e,s)=>new Date(e.created_at).getTime()-new Date(s.created_at).getTime()),g=Math.max(...u.map(e=>e.likes_count||0)),p=Math.max(g,5*Math.max(...u.map(e=>e.comments_count||0)),3*Math.max(...u.map(e=>e.saves_count||0)));return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(j.ao$,{className:"text-pink-500 mr-2"}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Engajamento do Instagram"})]}),(0,a.jsxs)("div",{className:"relative h-60",children:[(0,a.jsxs)("div",{className:"absolute left-0 top-0 bottom-0 w-12 flex flex-col justify-between text-xs text-gray-500",children:[(0,a.jsx)("div",{children:h(p)}),(0,a.jsx)("div",{children:h(.75*p)}),(0,a.jsx)("div",{children:h(.5*p)}),(0,a.jsx)("div",{children:h(.25*p)}),(0,a.jsx)("div",{children:"0"})]}),(0,a.jsxs)("div",{className:"absolute left-12 right-0 top-0 bottom-0",children:[(0,a.jsx)("div",{className:"absolute left-0 right-0 top-0 border-t border-gray-100"}),(0,a.jsx)("div",{className:"absolute left-0 right-0 top-1/4 border-t border-gray-100"}),(0,a.jsx)("div",{className:"absolute left-0 right-0 top-2/4 border-t border-gray-100"}),(0,a.jsx)("div",{className:"absolute left-0 right-0 top-3/4 border-t border-gray-100"}),(0,a.jsx)("div",{className:"absolute left-0 right-0 bottom-0 border-t border-gray-200"})]}),(0,a.jsx)("div",{className:"absolute left-12 right-0 top-0 bottom-0 flex items-end",children:u.map((e,s)=>{let t=e.likes_count/p*100,r=5*e.comments_count/p*100,n=3*e.saves_count/p*100,i="calc(".concat(100/u.length,"% - 8px)");return(0,a.jsxs)("div",{className:"flex flex-col items-center mx-1",style:{width:i},children:[(0,a.jsxs)("div",{className:"relative w-full h-full flex items-end",children:[(0,a.jsx)("div",{className:"absolute bottom-0 left-0 w-full bg-red-400 rounded-t",style:{height:"".concat(t,"%")},title:"".concat(e.likes_count," curtidas")}),(0,a.jsx)("div",{className:"absolute bottom-0 left-1/4 w-1/2 bg-blue-500 rounded-t",style:{height:"".concat(r,"%")},title:"".concat(e.comments_count," coment\xe1rios")}),(0,a.jsx)("div",{className:"absolute bottom-0 right-1/4 w-1/2 bg-green-500 rounded-t",style:{height:"".concat(n,"%")},title:"".concat(e.saves_count||0," salvamentos")})]}),s%Math.ceil(u.length/5)==0&&(0,a.jsx)("div",{className:"mt-1 text-xs text-gray-500 rotate-45 origin-left",children:new Date(e.created_at).toLocaleDateString("pt-BR",{month:"short",day:"numeric"})})]},e.id)})})]}),(0,a.jsxs)("div",{className:"flex justify-center mt-8 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center mr-4",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-400 rounded mr-1"}),(0,a.jsx)("span",{children:"Curtidas"})]}),(0,a.jsxs)("div",{className:"flex items-center mr-4",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded mr-1"}),(0,a.jsx)("span",{children:"Coment\xe1rios (x5)"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded mr-1"}),(0,a.jsx)("span",{children:"Salvamentos (x3)"})]})]})]})}function b(e){let{userId:s,onSyncComplete:t}=e,[n,i]=(0,r.useState)(!1),[l,o]=(0,r.useState)(null),m=async()=>{try{i(!0),o(null);let e=await fetch("/api/v1/instagram/sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:s})}),a=await e.json();o({success:a.success,message:a.message,data:a.data}),a.success&&t&&t()}catch(e){o({success:!1,message:e.message||"Erro ao sincronizar dados do Instagram"})}finally{i(!1)}};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium",children:"Sincroniza\xe7\xe3o do Instagram"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Sincronize seus posts recentes do Instagram"})]}),(0,a.jsxs)("button",{onClick:m,disabled:n,className:"inline-flex items-center px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50",children:[(0,a.jsx)(j.DIg,{className:"mr-2 ".concat(n?"animate-spin":"")}),n?"Sincronizando...":"Sincronizar Agora"]})]}),l&&(0,a.jsx)("div",{className:"p-3 rounded-md text-sm ".concat(l.success?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:(0,a.jsxs)("div",{className:"flex items-center",children:[l.success?(0,a.jsx)(j.A7C,{className:"mr-2"}):(0,a.jsx)(j.BS8,{className:"mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:l.message}),l.success&&l.data&&(0,a.jsxs)("p",{className:"text-xs mt-1",children:[l.data.syncedCount," posts sincronizados em ",(0,c.m)(new Date,{addSuffix:!1,locale:d.F})]})]})]})})]})}function N(e){var s;let{userId:t,onRefresh:n}=e,[i,l]=(0,r.useState)(null),[o,m]=(0,r.useState)(!0),[x,u]=(0,r.useState)(null),h=async()=>{try{m(!0),u(null);let e=await fetch("/api/v1/instagram/sync?userId=".concat(t));if(!e.ok)throw Error("Erro ao obter status: ".concat(e.statusText));let s=await e.json();if(!s.success)throw Error(s.message||"Erro ao obter status");l(s.data)}catch(e){console.error("Erro ao obter status da sincroniza\xe7\xe3o:",e),u(e.message||"Erro ao obter status da sincroniza\xe7\xe3o")}finally{m(!1)}};(0,r.useEffect)(()=>{t&&h()},[t]);let g=()=>{h(),n&&n()};if(o)return(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm animate-pulse",children:[(0,a.jsx)("div",{className:"h-5 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})]});if(x)return(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.BS8,{className:"mr-2"}),(0,a.jsx)("span",{children:x})]}),(0,a.jsx)("button",{onClick:g,className:"mt-2 text-sm text-red-700 hover:text-red-800 underline",children:"Tentar novamente"})]});if(!i)return(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Nenhum dado dispon\xedvel"}),(0,a.jsx)("button",{onClick:g,className:"mt-2 text-sm text-blue-500 hover:text-blue-600 underline",children:"Verificar status"})]});let p=i.connection&&i.connection.is_active,f=i.last_sync?new Date(i.last_sync):null,v=f?"\xdaltima sincroniza\xe7\xe3o ".concat((0,c.m)(f,{addSuffix:!0,locale:d.F})):"Nunca sincronizado";return(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.ao$,{className:"text-pink-500 mr-2"}),(0,a.jsx)("h3",{className:"font-medium",children:"Status do Instagram"})]}),(0,a.jsx)("button",{onClick:g,className:"text-blue-500 hover:text-blue-600",title:"Atualizar status",children:(0,a.jsx)(j.DIg,{})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Conex\xe3o"}),p?(0,a.jsxs)("div",{className:"text-sm text-green-600",children:["Conectado como @",null===(s=i.connection)||void 0===s?void 0:s.username]}):(0,a.jsx)("div",{className:"text-sm text-red-600",children:"N\xe3o conectado"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Sincroniza\xe7\xe3o"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:v})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"M\xe9tricas"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 mt-1",children:[(0,a.jsxs)("div",{className:"text-center bg-gray-50 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Posts"}),(0,a.jsx)("div",{className:"font-medium",children:i.metrics.total_posts})]}),(0,a.jsxs)("div",{className:"text-center bg-gray-50 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Curtidas"}),(0,a.jsx)("div",{className:"font-medium",children:i.metrics.total_likes})]}),(0,a.jsxs)("div",{className:"text-center bg-gray-50 p-2 rounded",children:[(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Engajamento"}),(0,a.jsxs)("div",{className:"font-medium",children:[i.metrics.avg_engagement_rate.toFixed(1),"%"]})]})]})]}),i.posts.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium mb-2",children:"Posts Recentes"}),(0,a.jsx)("div",{className:"space-y-2",children:i.posts.slice(0,3).map(e=>(0,a.jsxs)("a",{href:e.post_url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors",children:[(0,a.jsxs)("div",{className:"text-xs truncate flex-1",children:["Post de ",new Date(e.created_at).toLocaleDateString("pt-BR")]}),(0,a.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"mr-2",children:["❤️ ",e.likes_count]}),(0,a.jsxs)("span",{children:["\uD83D\uDCAC ",e.comments_count]})]})]},e.id))})]})]})]})}function y(e){let{campaignId:s,influencerId:t}=e,[i,m]=(0,r.useState)([]),[u,h]=(0,r.useState)(!0),[g,p]=(0,r.useState)(!1),[f,v]=(0,r.useState)(null),b=(0,n.createClientComponentClient)(),N=async()=>{try{h(!0),v(null);let e=b.from("posts").select("\n          id,\n          platform,\n          platform_post_id,\n          post_url,\n          post_type,\n          caption,\n          media_urls,\n          likes_count,\n          comments_count,\n          views_count,\n          created_at,\n          campaign_influencer:campaign_influencer_id(\n            influencer:influencer_id(name, username)\n          )\n        ").eq("campaign_influencer.campaign_id",s).order("created_at",{ascending:!1});t&&(e=e.eq("campaign_influencer.influencer_id",t));let{data:a,error:r}=await e;if(r)throw r;m(a)}catch(e){console.error("Erro ao carregar postagens:",e),v("N\xe3o foi poss\xedvel carregar as postagens")}finally{h(!1)}};(0,r.useEffect)(()=>{N()},[s,t]);let y=async()=>{if(t)try{p(!0),v(null),await (0,l.Yv)(t),await N()}catch(e){console.error("Erro ao sincronizar postagens:",e),v("N\xe3o foi poss\xedvel sincronizar as postagens")}finally{p(!1)}};return u?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,s)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden animate-pulse",children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]})]},s))}):f?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:f}):0===i.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[(0,a.jsx)("div",{className:"text-gray-500 mb-4",children:"Nenhuma postagem encontrada para esta campanha."}),t&&(0,a.jsxs)("button",{onClick:y,disabled:g,className:"inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50",children:[(0,a.jsx)(j.DIg,{className:"mr-2 ".concat(g?"animate-spin":"")}),g?"Sincronizando...":"Sincronizar Postagens"]})]}):(0,a.jsxs)("div",{children:[t&&(0,a.jsx)("div",{className:"mb-4 flex justify-end",children:(0,a.jsxs)("button",{onClick:y,disabled:g,className:"inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50",children:[(0,a.jsx)(j.DIg,{className:"mr-2 ".concat(g?"animate-spin":"")}),g?"Sincronizando...":"Sincronizar Postagens"]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map(e=>{var s,t;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative aspect-square",children:[e.media_urls&&e.media_urls[0]&&(0,a.jsx)(o.default,{src:e.media_urls[0],alt:e.caption||"Post da campanha",fill:!0,className:"object-cover"}),(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-black bg-opacity-50 text-white",children:["instagram"===e.platform?(0,a.jsx)(j.ao$,{className:"mr-1"}):e.platform,e.post_type]})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:(null===(t=e.campaign_influencer)||void 0===t?void 0:null===(s=t.influencer)||void 0===s?void 0:s.username)||"Influenciador"}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,c.m)(new Date(e.created_at),{addSuffix:!0,locale:d.F})})]}),e.caption&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.caption}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.Mbv,{className:"mr-1 text-red-500"}),e.likes_count]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.j1Q,{className:"mr-1 text-blue-500"}),e.comments_count]}),e.views_count>0&&(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(j.Ny1,{className:"mr-1 text-green-500"}),e.views_count]})]}),(0,a.jsx)(x(),{href:e.post_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:underline",children:"Ver post"})]})]})]},e.id)})})]})}function w(){let[e,s]=(0,r.useState)(null),[t,c]=(0,r.useState)(null),[d,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),[h,w]=(0,r.useState)(null),[_,S]=(0,r.useState)(!0),[C,E]=(0,r.useState)(null),I=(0,n.createClientComponentClient)();(0,r.useEffect)(()=>{!async function(){try{S(!0),E(null);let{data:{session:e}}=await I.auth.getSession();if(!e){E("Voc\xea precisa estar logado para acessar esta p\xe1gina");return}s(e.user.id);let{data:t}=await I.from("profiles").select("role").eq("id",e.user.id).single();if((null==t?void 0:t.role)==="restaurant"){let{data:s}=await I.from("restaurants").select("id").eq("owner_id",e.user.id).maybeSingle();s&&c(s.id)}}catch(e){console.error("Erro ao obter usu\xe1rio:",e),E("N\xe3o foi poss\xedvel obter informa\xe7\xf5es do usu\xe1rio")}finally{S(!1)}}()},[I]);let k=async()=>{if(e)try{x(!0),w(null);let s=await (0,l.Yv)(e);w(s)}catch(e){console.error("Erro ao sincronizar postagens:",e),w({success:!1,error:e.message})}finally{x(!1)}};return _?(0,a.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Teste de Integra\xe7\xe3o com Instagram"}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/2 mb-6"}),(0,a.jsx)("div",{className:"h-40 bg-gray-200 rounded w-full"})]})]}):C?(0,a.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Teste de Integra\xe7\xe3o com Instagram"}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-md",children:C})]}):(0,a.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Teste de Integra\xe7\xe3o com Instagram"}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Conex\xe3o com Instagram"}),e&&(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(i.A,{userId:e,onConnectionChange:e=>{o(e)}})}),d&&e&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Sincronizar Postagens"}),(0,a.jsxs)("button",{onClick:k,disabled:m,className:"inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50",children:[(0,a.jsx)(j.DIg,{className:"mr-2 ".concat(m?"animate-spin":"")}),m?"Sincronizando...":"Sincronizar Agora"]})]}),h&&(0,a.jsx)("div",{className:"p-4 rounded-md ".concat(h.success?"bg-green-50 text-green-700":"bg-red-50 text-red-700"),children:h.success?(0,a.jsxs)("p",{children:["Sincroniza\xe7\xe3o conclu\xedda com sucesso! ",h.syncedCount," postagens sincronizadas."]}):(0,a.jsxs)("p",{children:["Erro ao sincronizar: ",h.error]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e&&(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Status da Conex\xe3o"}),(0,a.jsx)(p,{userId:e})]}),e&&(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Status da Sincroniza\xe7\xe3o"}),(0,a.jsx)(N,{userId:e})]})]}),e&&(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Sincroniza\xe7\xe3o Manual"}),(0,a.jsx)(b,{userId:e,onSyncComplete:()=>window.location.reload()})]}),t&&(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"M\xe9tricas do Instagram"}),(0,a.jsx)("div",{className:"max-w-xs",children:(0,a.jsx)(g,{restaurantId:t})})]}),d&&e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Resumo de Engajamento"}),(0,a.jsx)(f,{userId:e})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Gr\xe1fico de Engajamento"}),(0,a.jsx)(v,{userId:e,limit:10})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Postagens de Campanha"}),(0,a.jsx)(y,{campaignId:"",influencerId:e})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Feed do Instagram"}),(0,a.jsx)(u,{userId:e,limit:6})]})]})]})}},87780:(e,s,t)=>{Promise.resolve().then(t.bind(t,84536))}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,9724,3579,6874,7127,6766,5891,5593,8441,1684,7358],()=>s(87780)),_N_E=e.O()}]);