(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2242],{20208:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});var d=a(95155),t=a(12115),r=a(73579);function l(){let[e,s]=(0,t.useState)(!0),[a,l]=(0,t.useState)([]),[i,n]=(0,t.useState)([]),[o,c]=(0,t.useState)([]),[m,u]=(0,t.useState)(null),h=(0,r.createClientComponentClient)();return((0,t.useEffect)(()=>{(async function(){try{s(!0);let{data:e,error:a}=await h.from("profiles").select("id, role, full_name, email").in("email",["<EMAIL>","<EMAIL>"]);if(a)throw a;let{data:d,error:t}=await h.from("social_connections").select("*").in("user_id",(null==e?void 0:e.map(e=>e.id))||[]);if(t)throw t;let{data:r,error:i}=await h.from("campaigns").select("*, campaign_influencers(*)").in("restaurant_id",(null==e?void 0:e.filter(e=>"restaurant"===e.role).map(e=>e.id))||[]);if(i)throw i;l(e||[]),n(d||[]),c(r||[])}catch(e){console.error("Erro ao buscar dados de teste:",e),u(e.message)}finally{s(!1)}})()},[h]),e)?(0,d.jsxs)("div",{className:"p-4 bg-white rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Status dos Dados de Teste"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"}),(0,d.jsx)("span",{children:"Carregando dados de teste..."})]})]}):m?(0,d.jsxs)("div",{className:"p-4 bg-white rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Status dos Dados de Teste"}),(0,d.jsxs)("div",{className:"text-red-500",children:["Erro ao carregar dados de teste: ",m]})]}):(0,d.jsxs)("div",{className:"p-4 bg-white rounded-lg shadow",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Status dos Dados de Teste"}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("h4",{className:"font-medium mb-2",children:["Usu\xe1rios de Teste (",a.length,")"]}),0===a.length?(0,d.jsx)("p",{className:"text-yellow-600",children:"Nenhum usu\xe1rio de teste encontrado."}):(0,d.jsx)("ul",{className:"space-y-2",children:a.map(e=>(0,d.jsxs)("li",{className:"p-2 bg-gray-50 rounded",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Nome:"})," ",e.full_name]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Email:"})," ",e.email]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Fun\xe7\xe3o:"})," ",e.role]})]},e.id))})]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("h4",{className:"font-medium mb-2",children:["Conex\xf5es Sociais (",i.length,")"]}),0===i.length?(0,d.jsx)("p",{className:"text-yellow-600",children:"Nenhuma conex\xe3o social encontrada."}):(0,d.jsx)("ul",{className:"space-y-2",children:i.map(e=>(0,d.jsxs)("li",{className:"p-2 bg-gray-50 rounded",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Plataforma:"})," ",e.platform]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Usu\xe1rio:"})," ",e.username]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Ativo:"})," ",e.is_active?"Sim":"N\xe3o"]})]},e.id))})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"font-medium mb-2",children:["Campanhas (",o.length,")"]}),0===o.length?(0,d.jsx)("p",{className:"text-yellow-600",children:"Nenhuma campanha encontrada."}):(0,d.jsx)("ul",{className:"space-y-2",children:o.map(e=>{var s;return(0,d.jsxs)("li",{className:"p-2 bg-gray-50 rounded",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Nome:"})," ",e.name]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Status:"})," ",e.status]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:"Influenciadores:"})," ",(null===(s=e.campaign_influencers)||void 0===s?void 0:s.length)||0]})]},e.id)})})]}),(0,d.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Estes dados s\xe3o apenas para desenvolvimento e teste. Eles n\xe3o representam dados reais."})})]})}function i(){let[e,s]=(0,t.useState)(!1),[a,i]=(0,t.useState)(null),n=(0,r.createClientComponentClient)(),o=async()=>{try{s(!0),i(null);let{data:e,error:a}=await n.rpc("create_test_data");if(a)throw a;i({success:!0,message:"Dados de teste criados com sucesso!"}),setTimeout(()=>{window.location.reload()},2e3)}catch(e){console.error("Erro ao criar dados de teste:",e),i({success:!1,message:"Erro ao criar dados de teste: ".concat(e.message)})}finally{s(!1)}},c=async()=>{try{s(!0),i(null);let{data:e,error:a}=await n.rpc("clear_test_data");if(a)throw a;i({success:!0,message:"Dados de teste removidos com sucesso!"}),setTimeout(()=>{window.location.reload()},2e3)}catch(e){console.error("Erro ao limpar dados de teste:",e),i({success:!1,message:"Erro ao limpar dados de teste: ".concat(e.message)})}finally{s(!1)}};return(0,d.jsxs)("div",{className:"container mx-auto p-6 max-w-4xl",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Gerenciamento de Dados de Teste"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"A\xe7\xf5es"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("button",{onClick:o,disabled:e,className:"w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded transition duration-200 disabled:opacity-50",children:e?"Processando...":"Criar Dados de Teste"}),(0,d.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded transition duration-200 disabled:opacity-50",children:e?"Processando...":"Limpar Dados de Teste"})]}),a&&(0,d.jsx)("div",{className:"mt-4 p-3 rounded ".concat(a.success?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:a.message}),(0,d.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:[(0,d.jsxs)("p",{className:"mb-2",children:[(0,d.jsx)("strong",{children:"Criar Dados de Teste:"})," Cria usu\xe1rios de teste (restaurante e influenciador), conex\xf5es sociais simuladas e campanhas de exemplo."]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Limpar Dados de Teste:"})," Remove todos os dados de teste criados anteriormente."]})]})]}),(0,d.jsx)(l,{})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-medium text-yellow-800 mb-2",children:"Importante"}),(0,d.jsx)("p",{className:"text-yellow-700",children:"Esta p\xe1gina \xe9 apenas para desenvolvimento e teste. Ela permite criar e gerenciar dados fict\xedcios para testar a funcionalidade do aplicativo. N\xe3o use esta p\xe1gina em produ\xe7\xe3o."})]})]})}},87490:(e,s,a)=>{Promise.resolve().then(a.bind(a,20208))}},e=>{var s=s=>e(e.s=s);e.O(0,[9724,3579,8441,1684,7358],()=>s(87490)),_N_E=e.O()}]);