(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{18100:()=>{},18732:()=>{},30347:()=>{},33113:()=>{},39757:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,30347,23)),Promise.resolve().then(o.t.bind(o,80367,23)),Promise.resolve().then(o.t.bind(o,18732,23)),Promise.resolve().then(o.t.bind(o,33113,23)),Promise.resolve().then(o.bind(o,92289)),Promise.resolve().then(o.t.bind(o,65652,23)),Promise.resolve().then(o.t.bind(o,18100,23))},40283:(e,t,o)=>{"use strict";o.d(t,{A:()=>u,O:()=>a});var n=o(95155),s=o(12115),r=o(52643),l=o(44032);let i=(0,s.createContext)(void 0);function a(e){let{children:t}=e,[o,a]=(0,s.useState)(null),[u,d]=(0,s.useState)(null),[c,h]=(0,s.useState)(!0),[g,m]=(0,s.useState)(null),f=async()=>{try{var e;h(!0),m(null),console.log("AuthContext: Refreshing session...");let{data:t,error:o}=await r.N.auth.getSession();if(o){console.error("Erro ao verificar sess\xe3o atual:",o.message),m(o.message),h(!1);return}if(!t.session){console.log("AuthContext: No active session found"),a(null),d(null),h(!1);return}console.log("AuthContext: Active session found"),a(t.session),d((null===(e=t.session)||void 0===e?void 0:e.user)||null)}catch(e){console.error("Erro inesperado ao obter sess\xe3o:",e),m(e.message||"Erro desconhecido")}finally{h(!1)}},v=async()=>{try{h(!0),m(null),console.log("AuthContext: Signing out...");let{error:e}=await r.N.auth.signOut();e&&(console.error("Erro ao fazer logout no Supabase:",e.message),m(e.message)),a(null),d(null),console.log("AuthContext: Logout completed, redirecting to login"),window.location.href="/login"}catch(e){console.error("Erro inesperado ao fazer logout:",e),m(e.message||"Erro desconhecido"),a(null),d(null),window.location.href="/login"}finally{h(!1)}};return(0,s.useEffect)(()=>{f();let{data:e}=r.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e),"SIGNED_IN"===e?(console.log("User signed in, updating session"),a(t),d((null==t?void 0:t.user)||null)):"SIGNED_OUT"===e?(console.log("User signed out, clearing session"),a(null),d(null)):"TOKEN_REFRESHED"===e?(console.log("Token refreshed, updating session"),a(t),d((null==t?void 0:t.user)||null)):"USER_UPDATED"===e&&(console.log("User updated, updating session"),a(t),d((null==t?void 0:t.user)||null)),h(!1)});return()=>{e.subscription.unsubscribe()}},[]),(0,s.useEffect)(()=>{let e=e=>{(e.error instanceof l.lR||e.message&&(e.message.includes("Invalid Refresh Token")||e.message.includes("JWT expired")||e.message.includes("not authenticated")))&&(console.error("Erro de autentica\xe7\xe3o interceptado:",e),f())};return window.addEventListener("error",e),()=>{window.removeEventListener("error",e)}},[]),(0,n.jsx)(i.Provider,{value:{session:o,user:u,loading:c,error:g,signOut:v,refreshSession:f},children:t})}function u(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth deve ser usado dentro de um AuthProvider");return e}},52643:(e,t,o)=>{"use strict";o.d(t,{N:()=>s,b:()=>r});var n=o(73579);let s=(0,n.createClientComponentClient)();function r(){return(0,n.createClientComponentClient)()}},57740:(e,t,o)=>{"use strict";o.d(t,{D:()=>i,N:()=>l});var n=o(95155),s=o(12115);let r=(0,s.createContext)(void 0);function l(e){let{children:t,defaultTheme:o="light"}=e,[l,i]=(0,s.useState)(o);return(0,s.useEffect)(()=>{{let e=localStorage.getItem("theme");e?i(e):window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches&&i("dark");let t=window.matchMedia("(prefers-color-scheme: dark)"),o=e=>{localStorage.getItem("theme")||i(e.matches?"dark":"light")};return t.addEventListener("change",o),()=>t.removeEventListener("change",o)}},[]),(0,s.useEffect)(()=>{if("undefined"!=typeof document){let e=document.documentElement;e.classList.remove("light-theme","dark-theme"),e.classList.add("".concat(l,"-theme")),localStorage.setItem("theme",l)}},[l]),(0,n.jsx)(r.Provider,{value:{theme:l,setTheme:i,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")}},children:t})}function i(){let e=(0,s.useContext)(r);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},65652:()=>{},80367:()=>{},92289:(e,t,o)=>{"use strict";o.d(t,{default:()=>a});var n=o(95155),s=o(35695),r=o(13568),l=o(40283),i=o(57740);function a(e){let{children:t}=e;return(0,s.usePathname)(),(0,n.jsx)(i.N,{children:(0,n.jsx)(l.O,{children:(0,n.jsxs)("div",{className:"w-full max-w-full",children:[t,(0,n.jsx)(r.l$,{position:"top-right"})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8121,7690,7719,9724,3579,8006,8441,1684,7358],()=>t(39757)),_N_E=e.O()}]);