(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3003],{19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var n=t(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),s=e=>{let r=o(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:r,...l,width:a,height:a,stroke:t,strokeWidth:s?24*Number(o)/Number(a):o,className:i("lucide",c),...f},[...u.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),d=(e,r)=>{let t=(0,n.forwardRef)((t,o)=>{let{className:l,...d}=t;return(0,n.createElement)(c,{ref:o,iconNode:r,className:i("lucide-".concat(a(s(e))),"lucide-".concat(e),l),...d})});return t.displayName=s(e),t}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>i});var n=t(95155),a=t(12115),o=t(74466),s=t(59434);let i=(0,o.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,r)=>{let{className:t,variant:a,size:o,...l}=e;return(0,n.jsx)("button",{className:(0,s.cn)(i({variant:a,size:o}),t),ref:r,...l})});l.displayName="Button"},33545:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var n=t(95155),a=t(12115),o=t(66695),s=t(30285),i=t(62523),l=t(88262),c=t(51154),d=t(29911);function u(){let[e,r]=(0,a.useState)(""),[t,u]=(0,a.useState)(!1),[f,m]=(0,a.useState)(null);async function p(){if(!e){(0,l.o)({title:"Erro",description:"Por favor, informe um n\xfamero de telefone",variant:"destructive"});return}u(!0),m(null);try{let r=await fetch("/api/v1/whatsapp/test?phone=".concat(encodeURIComponent(e))),t=await r.json();if(!r.ok)throw Error(t.error||"Erro ao enviar mensagem de teste");m(t),(0,l.o)({title:"Sucesso",description:"Mensagem de teste enviada com sucesso",variant:"default"})}catch(e){console.error("Erro ao enviar mensagem de teste:",e),(0,l.o)({title:"Erro",description:e.message||"Erro ao enviar mensagem de teste",variant:"destructive"}),m({error:e.message})}finally{u(!1)}}return(0,n.jsxs)("div",{className:"container mx-auto py-8",children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold mb-6 flex items-center",children:[(0,n.jsx)(d.EcP,{className:"text-green-500 mr-3"}),"Teste da API do WhatsApp"]}),(0,n.jsxs)(o.Zp,{className:"max-w-md mx-auto",children:[(0,n.jsxs)(o.aR,{children:[(0,n.jsx)(o.ZB,{children:"Enviar Mensagem de Teste"}),(0,n.jsx)(o.BT,{children:"Este teste s\xf3 funciona para administradores e requer que a API do WhatsApp esteja configurada."})]}),(0,n.jsx)(o.Wu,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium mb-1 block",children:"N\xfamero de Telefone"}),(0,n.jsx)(i.p,{placeholder:"Ex: 5511999999999 (formato internacional sem +)",value:e,onChange:e=>r(e.target.value)}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Informe o n\xfamero no formato internacional sem o sinal de +"})]}),f&&(0,n.jsx)("div",{className:"mt-4 p-4 rounded-md bg-gray-50 overflow-auto",children:(0,n.jsx)("pre",{className:"text-xs",children:JSON.stringify(f,null,2)})})]})}),(0,n.jsx)(o.wL,{children:(0,n.jsx)(s.$,{onClick:p,disabled:t||!e,className:"w-full",children:t?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Enviando..."]}):"Enviar Mensagem de Teste"})})]})]})}},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52596:(e,r,t)=>{"use strict";function n(){for(var e,r,t=0,n="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,n,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r){if(Array.isArray(r)){var o=r.length;for(t=0;t<o;t++)r[t]&&(n=e(r[t]))&&(a&&(a+=" "),a+=n)}else for(n in r)r[n]&&(a&&(a+=" "),a+=n)}return a}(e))&&(n&&(n+=" "),n+=r);return n}t.d(r,{$:()=>n})},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var n=t(52596),a=t(39688);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>s});var n=t(95155),a=t(12115),o=t(59434);let s=a.forwardRef((e,r)=>{let{className:t,type:a,...s}=e;return(0,n.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...s})});s.displayName="Input"},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>i,wL:()=>u});var n=t(95155),a=t(12115),o=t(59434);let s=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});s.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("h3",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",t),...a})});d.displayName="CardContent";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,n.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"},74436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>d});var n=t(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=n.createContext&&n.createContext(a),s=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var n,a,o;n=e,a=r,o=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[a]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>n.createElement(u,i({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,c({key:t},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var t,{attr:a,size:o,title:l}=e,d=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)t=o[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,s),u=o||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,d,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),e.children)};return void 0!==o?n.createElement(o.Consumer,null,e=>r(e)):r(a)}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});var n=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,s=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:i}=r,l=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let o=a(r)||a(n);return s[e][o]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return o(e,l,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...c}[r]):({...i,...c})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},88262:(e,r,t)=>{"use strict";function n(e){let r=e.variant||"default",t=e.title||"",n=e.description||"";return console.log("[Toast - ".concat(r,"] ").concat(t,": ").concat(n)),alert("".concat(t,"\n").concat(n)),{id:Date.now(),dismiss:()=>{},update:e=>{}}}t.d(r,{o:()=>n})},95583:(e,r,t)=>{Promise.resolve().then(t.bind(t,33545))}},e=>{var r=r=>e(e.s=r);e.O(0,[6711,9688,8441,1684,7358],()=>r(95583)),_N_E=e.O()}]);