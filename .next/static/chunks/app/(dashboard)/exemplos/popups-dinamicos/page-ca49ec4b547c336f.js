(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4122],{5360:(e,s,a)=>{Promise.resolve().then(a.bind(a,85118))},35695:(e,s,a)=>{"use strict";var l=a(18999);a.o(l,"useParams")&&a.d(s,{useParams:function(){return l.useParams}}),a.o(l,"usePathname")&&a.d(s,{usePathname:function(){return l.usePathname}}),a.o(l,"useRouter")&&a.d(s,{useRouter:function(){return l.useRouter}}),a.o(l,"useSearchParams")&&a.d(s,{useSearchParams:function(){return l.useSearchParams}})},44345:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var l=a(95155),t=a(12115),i=a(17292),o=a(74489);function d(e){let{isOpen:s,onClose:a,title:d,children:n,id:r="basic-popup",size:c="medium",closeOnClickOutside:m=!0,closeOnEsc:u=!0,className:p="",headerContent:x,headerMenu:h,headerActions:b,stickyHeader:g=!1}=e;console.log("BasicPopup renderizado",{isOpen:s,title:d,id:r,size:c});let{isOpen:j,openPopup:N,closePopup:v}=(0,o.Y)({popupId:r,defaultIsOpen:s});(0,t.useEffect)(()=>{s?N():v()},[s,N,v]);let f=()=>{v(),a()},y=[{id:"content",label:"Conte\xfado",content:"function"==typeof n?n(f):(0,t.isValidElement)(n)&&t.Children.only(n)?(0,t.cloneElement)(n,{onClose:f}):n}];return(0,l.jsx)(i.A,{id:r,isOpen:j,onClose:f,title:d,tabs:y,size:"lg"===c?"large":c,closeOnClickOutside:m,closeOnEsc:u,className:p,headerContent:x,headerMenu:h,headerActions:b,stickyHeader:g})}},50996:(e,s,a)=>{"use strict";a.d(s,{P:()=>t});var l=a(12115);function t(e){let[s,a]=(0,l.useState)(!1),[t,i]=(0,l.useState)(e.defaultTitle||""),[o,d]=(0,l.useState)(e.defaultContent||null),[n,r]=(0,l.useState)(e.defaultSize||"medium");return{isOpen:s,title:t,content:o,size:n,openPopup:e=>{i(e.title),d(e.content),e.size&&r(e.size),a(!0)},closePopup:()=>{a(!1)},popupId:e.popupId}}},74489:(e,s,a)=>{"use strict";a.d(s,{Y:()=>i});var l=a(12115),t=a(35695);function i(e){let{popupId:s="default-popup-id",defaultTabId:a,queryParam:i="popup",closeOnNavigate:o=!0}=e||{};(0,t.useRouter)(),(0,t.usePathname)(),(0,t.useSearchParams)();let[d,n]=(0,l.useState)(!1),[r,c]=(0,l.useState)(a||"");return{isOpen:d,activeTabId:r,openPopup:e=>{c(e||a||""),n(!0)},closePopup:()=>{n(!1)},changeTab:e=>{c(e)}}}},85118:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>c});var l=a(95155),t=a(12115),i=a(50996),o=a(44345);function d(e){let{id:s,isOpen:a,onClose:t,title:i,content:d,size:n="medium",className:r=""}=e;return console.log("DynamicPopup renderizado",{isOpen:a,title:i,id:s,size:n}),(0,l.jsx)(o.A,{id:s,isOpen:a,onClose:t,title:i,size:n,className:"dynamic-popup ".concat(r),children:d})}var n=a(29911);function r(){let{isOpen:e,title:s,content:a,size:o,openPopup:r,closePopup:c,updateContent:m,updateTitle:u,updateSize:p}=(0,i.P)({popupId:"dynamic-popup"}),[x,h]=(0,t.useState)(0),b=()=>{r({title:"Visualizador de Imagem",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"bg-gray-100 rounded-lg overflow-hidden",children:(0,l.jsx)("img",{src:"https://images.unsplash.com/photo-1682687982501-1e58ab814714",alt:"Imagem de exemplo",className:"w-full h-auto"})}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"Foto por Unsplash"}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{onClick:()=>m((0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"bg-gray-100 rounded-lg overflow-hidden",children:(0,l.jsx)("img",{src:"https://images.unsplash.com/photo-1682687982501-1e58ab814714?q=80&w=1000",alt:"Imagem de exemplo em baixa qualidade",className:"w-full h-auto"})}),(0,l.jsx)("div",{className:"text-center text-gray-500",children:"Imagem em baixa qualidade"}),(0,l.jsx)("button",{onClick:()=>b(),className:"w-full px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Restaurar Qualidade Original"})]})),className:"px-3 py-1 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors",children:"Baixa Qualidade"}),(0,l.jsx)("button",{onClick:()=>p("fullscreen"),className:"px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Tela Cheia"})]})]})]}),size:"large"})},g=()=>{r({title:"Visualiza\xe7\xe3o de Dados",content:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200",children:[(0,l.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Dados de Vendas"}),(0,l.jsx)("div",{className:"h-64 flex items-end space-x-4 pb-4 border-b border-gray-200",children:[65,40,85,30,55,60,75].map((e,s)=>(0,l.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,l.jsx)("div",{className:"w-full bg-blue-500 rounded-t",style:{height:"".concat(e,"%")}}),(0,l.jsx)("div",{className:"text-xs mt-1",children:"Dia ".concat(s+1)})]},s))}),(0,l.jsxs)("div",{className:"mt-4 flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"\xdaltima semana"}),(0,l.jsx)("div",{className:"text-sm font-medium",children:"Total: R$ 12.450,00"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsx)("button",{onClick:()=>m((0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border border-gray-200",children:[(0,l.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Dados de Vendas (Mensal)"}),(0,l.jsx)("div",{className:"h-64 flex items-end space-x-4 pb-4 border-b border-gray-200",children:[45,60,35,80,55,70,50,65,75,40,90,55].map((e,s)=>(0,l.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,l.jsx)("div",{className:"w-full bg-purple-500 rounded-t",style:{height:"".concat(e,"%")}}),(0,l.jsx)("div",{className:"text-xs mt-1",children:"M".concat(s+1)})]},s))}),(0,l.jsxs)("div",{className:"mt-4 flex justify-between",children:[(0,l.jsx)("div",{className:"text-sm text-gray-500",children:"\xdaltimo ano"}),(0,l.jsx)("div",{className:"text-sm font-medium",children:"Total: R$ 145.320,00"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsx)("button",{onClick:()=>g(),className:"px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Visualiza\xe7\xe3o Semanal"}),(0,l.jsx)("button",{onClick:()=>u("Relat\xf3rio Anual de Vendas"),className:"px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Atualizar T\xedtulo"})]})]})),className:"px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Visualiza\xe7\xe3o Mensal"}),(0,l.jsx)("button",{onClick:()=>u("Relat\xf3rio Semanal de Vendas"),className:"px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Atualizar T\xedtulo"})]})]}),size:"medium"})};return(0,l.jsxs)("div",{className:"p-6 max-w-6xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Popups com Conte\xfado Din\xe2mico"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)(n.ELu,{className:"text-blue-500 mr-2",size:24}),(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Conte\xfado de Texto"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Exemplo de popup com conte\xfado de texto que pode ser atualizado dinamicamente."}),(0,l.jsx)("button",{onClick:()=>{r({title:"Conte\xfado de Texto",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{children:"Este \xe9 um exemplo de popup com conte\xfado de texto simples. O conte\xfado pode ser atualizado dinamicamente sem fechar o popup."}),(0,l.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:[(0,l.jsxs)("h4",{className:"font-medium text-blue-700 mb-2",children:["Contador: ",x]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{onClick:()=>h(e=>e-1),className:"px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Diminuir"}),(0,l.jsx)("button",{onClick:()=>h(e=>e+1),className:"px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Aumentar"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-3 mt-4",children:[(0,l.jsx)("button",{onClick:()=>u("T\xedtulo Atualizado"),className:"px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Atualizar T\xedtulo"}),(0,l.jsx)("button",{onClick:()=>p("large"),className:"px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Aumentar Tamanho"})]})]}),size:"medium"})},className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Abrir Popup"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)(n.dkL,{className:"text-green-500 mr-2",size:24}),(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Visualizador de Imagem"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Exemplo de popup para visualiza\xe7\xe3o de imagens com op\xe7\xf5es de tamanho."}),(0,l.jsx)("button",{onClick:b,className:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Abrir Popup"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)(n.v$b,{className:"text-purple-500 mr-2",size:24}),(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Visualiza\xe7\xe3o de Dados"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Exemplo de popup para visualiza\xe7\xe3o de dados com gr\xe1ficos interativos."}),(0,l.jsx)("button",{onClick:g,className:"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Abrir Popup"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm mb-8",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Como Funciona"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-600",children:["Os popups com conte\xfado din\xe2mico s\xe3o implementados usando o hook ",(0,l.jsx)("code",{children:"useDynamicPopup"}),", que permite:"]}),(0,l.jsxs)("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[(0,l.jsx)("li",{children:"Abrir um popup com conte\xfado inicial"}),(0,l.jsx)("li",{children:"Atualizar o conte\xfado do popup sem fech\xe1-lo"}),(0,l.jsx)("li",{children:"Atualizar o t\xedtulo do popup"}),(0,l.jsx)("li",{children:"Alterar o tamanho do popup"}),(0,l.jsx)("li",{children:"Manter o estado do popup entre atualiza\xe7\xf5es"})]}),(0,l.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4",children:[(0,l.jsx)("h3",{className:"font-medium text-blue-700 mb-2",children:"Casos de Uso"}),(0,l.jsxs)("ul",{className:"list-disc pl-5 space-y-1 text-blue-600",children:[(0,l.jsx)("li",{children:"Visualizadores de imagem com op\xe7\xf5es de zoom"}),(0,l.jsx)("li",{children:"Dashboards interativos com filtros"}),(0,l.jsx)("li",{children:"Formul\xe1rios com valida\xe7\xe3o em tempo real"}),(0,l.jsx)("li",{children:"Visualizadores de dados com diferentes visualiza\xe7\xf5es"})]})]})]})]}),(0,l.jsx)(d,{id:"dynamic-popup",isOpen:e,onClose:c,title:s,content:a,size:o})]})}function c(){return(0,l.jsx)(t.Suspense,{fallback:(0,l.jsx)("div",{children:"Carregando..."}),children:(0,l.jsx)(r,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,8264,535,7292,8441,1684,7358],()=>s(5360)),_N_E=e.O()}]);