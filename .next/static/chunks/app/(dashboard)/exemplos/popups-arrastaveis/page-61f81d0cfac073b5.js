(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1544],{37850:(e,t,r)=>{Promise.resolve().then(r.bind(r,57265))},57265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var o=r(95155),s=r(12115);function a(e){let{popupId:t,defaultIsOpen:r=!1,defaultPosition:o,onPositionChange:a}=e,[i,l]=(0,s.useState)(r),[n,c]=(0,s.useState)(o||{x:0,y:0});(0,s.useEffect)(()=>{if(!o){let e=localStorage.getItem("popup-position-".concat(t));if(e)try{let t=JSON.parse(e);c(t)}catch(e){console.error("Erro ao carregar posi\xe7\xe3o do popup:",e)}}},[t,o]);let d=(0,s.useCallback)(e=>{l(!0),e&&c(e)},[]),p=(0,s.useCallback)(()=>{l(!1)},[]);return{isOpen:i,position:n,openPopup:d,closePopup:p,updatePosition:(0,s.useCallback)(e=>{c(e),a&&a(e),localStorage.setItem("popup-position-".concat(t),JSON.stringify(e))},[t,a]),resetPosition:(0,s.useCallback)(()=>{c({x:0,y:0}),a&&a({x:0,y:0}),localStorage.removeItem("popup-position-".concat(t))},[t,a])}}var i=r(60760),l=r(63527),n=r(57740);function c(e){let{isOpen:t,onClose:r,title:a,children:c,id:d,size:p="medium",className:u="",showCloseButton:m=!0,closeOnClickOutside:h=!0,closeOnEsc:x=!0,initialPosition:b,onPositionChange:g}=e,{theme:v}=(0,n.D)(),f=(0,s.useRef)(null),[y,j]=(0,s.useState)(b||{x:0,y:0}),[w,N]=(0,s.useState)(!1),P={small:{width:"400px",maxWidth:"90vw",maxHeight:"80vh"},medium:{width:"600px",maxWidth:"90vw",maxHeight:"80vh"},large:{width:"800px",maxWidth:"90vw",maxHeight:"80vh"}};return(0,s.useEffect)(()=>{let e=e=>{x&&"Escape"===e.key&&r()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[r,x]),(0,s.useEffect)(()=>{if(t&&!b&&f.current){let e=window.innerWidth,t=window.innerHeight,r=f.current.offsetWidth;j({x:(e-r)/2,y:(t-f.current.offsetHeight)/2})}},[t,b]),(0,s.useEffect)(()=>{if(!b){let e=localStorage.getItem("popup-position-".concat(d));if(e)try{let t=JSON.parse(e);j(t)}catch(e){console.error("Erro ao carregar posi\xe7\xe3o do popup:",e)}}},[d,b]),(0,o.jsx)(i.N,{children:t&&(0,o.jsxs)(l.P.div,{className:"fixed inset-0 z-50 flex items-center justify-center",style:{backgroundColor:"transparent"},initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},children:[h&&(0,o.jsx)("div",{className:"absolute inset-0 bg-black/30",onClick:r}),(0,o.jsxs)(l.P.div,{ref:f,className:"absolute rounded-xl shadow-xl flex flex-col overflow-hidden ".concat(u," ").concat(w?"cursor-grabbing":"cursor-grab"),style:{width:P[p].width,maxWidth:P[p].maxWidth,maxHeight:P[p].maxHeight,backgroundColor:"rgb(var(--popup-background))",boxShadow:"0 10px 25px -5px rgba(var(--popup-shadow))",border:"1px solid rgb(var(--popup-border))",color:"rgb(var(--text-primary))",x:y.x,y:y.y,zIndex:60},drag:!0,dragMomentum:!1,dragElastic:0,onDragStart:()=>{N(!0)},onDrag:(e,t)=>{j(e=>({x:e.x+t.delta.x,y:e.y+t.delta.y}))},onDragEnd:()=>{N(!1),g&&g(y),localStorage.setItem("popup-position-".concat(d),JSON.stringify(y))},initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{type:"spring",damping:25,stiffness:300,mass:.5},children:[(0,o.jsxs)("div",{className:"p-4 flex items-center justify-between cursor-grab",style:{backgroundColor:"rgb(var(--popup-header-background))",borderBottom:"1px solid rgb(var(--popup-border))"},children:[(0,o.jsx)("h2",{className:"text-lg font-semibold flex items-center gap-2",style:{color:"rgb(var(--text-primary))"},children:a}),m&&(0,o.jsx)("button",{onClick:r,className:"rounded-full w-8 h-8 flex items-center justify-center transition-colors",style:{color:"rgb(var(--text-secondary))",":hover":{color:"rgb(var(--text-primary))",backgroundColor:"rgb(var(--background-tertiary))"}},"aria-label":"Fechar",children:"\xd7"})]}),(0,o.jsx)("div",{className:"p-4 overflow-y-auto",style:{backgroundColor:"rgb(var(--background-primary))"},children:c}),(0,o.jsx)("div",{className:"absolute bottom-2 right-2 text-xs opacity-50",style:{color:"rgb(var(--text-tertiary))"},children:"Arraste para mover"})]})]})})}var d=r(29911);function p(){let{isOpen:e,position:t,openPopup:r,closePopup:i,updatePosition:l,resetPosition:n}=a({popupId:"draggable-popup-1"}),{isOpen:p,position:u,openPopup:m,closePopup:h,updatePosition:x,resetPosition:b}=a({popupId:"draggable-popup-2"}),{isOpen:g,position:v,openPopup:f,closePopup:y,updatePosition:j,resetPosition:w}=a({popupId:"draggable-popup-3"}),[N,P]=(0,s.useState)(0);return(0,o.jsxs)("div",{className:"p-6 max-w-6xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Popups Arrast\xe1veis"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:[(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,o.jsxs)("div",{className:"flex items-center mb-4",children:[(0,o.jsx)(d.GF8,{className:"text-blue-500 mr-2",size:24}),(0,o.jsx)("h2",{className:"text-xl font-semibold",children:"Popup B\xe1sico"})]}),(0,o.jsx)("p",{className:"text-gray-600 mb-4",children:"Popup arrast\xe1vel b\xe1sico que pode ser movido para qualquer posi\xe7\xe3o na tela."}),(0,o.jsx)("button",{onClick:()=>r(),className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Abrir Popup"})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,o.jsxs)("div",{className:"flex items-center mb-4",children:[(0,o.jsx)(d.U_c,{className:"text-green-500 mr-2",size:24}),(0,o.jsx)("h2",{className:"text-xl font-semibold",children:"Popup Interativo"})]}),(0,o.jsx)("p",{className:"text-gray-600 mb-4",children:"Popup arrast\xe1vel com conte\xfado interativo e estado persistente."}),(0,o.jsx)("button",{onClick:()=>m(),className:"w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Abrir Popup"})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,o.jsxs)("div",{className:"flex items-center mb-4",children:[(0,o.jsx)(d.Wqt,{className:"text-purple-500 mr-2",size:24}),(0,o.jsx)("h2",{className:"text-xl font-semibold",children:"M\xfaltiplos Popups"})]}),(0,o.jsx)("p",{className:"text-gray-600 mb-4",children:"Abra m\xfaltiplos popups arrast\xe1veis e organize-os na tela."}),(0,o.jsx)("button",{onClick:()=>{r({x:100,y:100}),m({x:200,y:150}),f({x:300,y:200})},className:"w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Abrir Todos"})]})]}),(0,o.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm mb-8",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Como Funciona"}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("p",{className:"text-gray-600",children:["Os popups arrast\xe1veis s\xe3o implementados usando o hook ",(0,o.jsx)("code",{children:"useDraggablePopup"})," e o componente ",(0,o.jsx)("code",{children:"DraggablePopup"}),", que permitem:"]}),(0,o.jsxs)("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[(0,o.jsx)("li",{children:"Arrastar o popup para qualquer posi\xe7\xe3o na tela"}),(0,o.jsx)("li",{children:"Salvar a posi\xe7\xe3o do popup entre sess\xf5es"}),(0,o.jsx)("li",{children:"Abrir m\xfaltiplos popups simultaneamente"}),(0,o.jsx)("li",{children:"Interagir com o conte\xfado do popup sem fech\xe1-lo"})]}),(0,o.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4",children:[(0,o.jsx)("h3",{className:"font-medium text-blue-700 mb-2",children:"Casos de Uso"}),(0,o.jsxs)("ul",{className:"list-disc pl-5 space-y-1 text-blue-600",children:[(0,o.jsx)("li",{children:"Pain\xe9is de controle personaliz\xe1veis"}),(0,o.jsx)("li",{children:"Ferramentas flutuantes em aplica\xe7\xf5es de edi\xe7\xe3o"}),(0,o.jsx)("li",{children:"Janelas de chat ou notifica\xe7\xf5es"}),(0,o.jsx)("li",{children:"Interfaces de usu\xe1rio modulares"})]})]})]})]}),(0,o.jsx)(c,{id:"draggable-popup-1",isOpen:e,onClose:i,title:"Popup Arrast\xe1vel",size:"small",initialPosition:t,onPositionChange:l,children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("p",{children:"Este \xe9 um popup arrast\xe1vel b\xe1sico. Voc\xea pode mov\xea-lo para qualquer posi\xe7\xe3o na tela arrastando a barra de t\xedtulo."}),(0,o.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:(0,o.jsx)("p",{className:"text-blue-600",children:"A posi\xe7\xe3o deste popup \xe9 salva automaticamente e ser\xe1 restaurada quando voc\xea abri-lo novamente, mesmo ap\xf3s recarregar a p\xe1gina."})}),(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsx)("button",{onClick:n,className:"px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:"Resetar Posi\xe7\xe3o"})})]})}),(0,o.jsx)(c,{id:"draggable-popup-2",isOpen:p,onClose:h,title:"Popup Interativo",size:"medium",initialPosition:u,onPositionChange:x,children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("p",{children:"Este popup cont\xe9m elementos interativos. Voc\xea pode interagir com eles sem fechar o popup ou mov\xea-lo para outra posi\xe7\xe3o."}),(0,o.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg border border-green-200",children:[(0,o.jsxs)("h4",{className:"font-medium text-green-700 mb-2",children:["Contador: ",N]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsx)("button",{onClick:()=>P(e=>e-1),className:"px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Diminuir"}),(0,o.jsx)("button",{onClick:()=>P(e=>e+1),className:"px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Aumentar"})]})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("button",{onClick:b,className:"px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:"Resetar Posi\xe7\xe3o"}),(0,o.jsx)("button",{onClick:()=>P(0),className:"px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Resetar Contador"})]})]})}),(0,o.jsx)(c,{id:"draggable-popup-3",isOpen:g,onClose:y,title:"Informa\xe7\xf5es",size:"small",initialPosition:v,onPositionChange:j,children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("p",{children:"Este \xe9 o terceiro popup arrast\xe1vel. Voc\xea pode abrir m\xfaltiplos popups simultaneamente e organiz\xe1-los na tela."}),(0,o.jsx)("div",{className:"bg-purple-50 p-4 rounded-lg border border-purple-200",children:(0,o.jsx)("p",{className:"text-purple-600",children:"Experimente abrir todos os popups e organiz\xe1-los na tela para criar um painel de controle personalizado."})}),(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsx)("button",{onClick:w,className:"px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:"Resetar Posi\xe7\xe3o"})})]})})]})}},57740:(e,t,r)=>{"use strict";r.d(t,{D:()=>l,N:()=>i});var o=r(95155),s=r(12115);let a=(0,s.createContext)(void 0);function i(e){let{children:t,defaultTheme:r="light"}=e,[i,l]=(0,s.useState)(r);return(0,s.useEffect)(()=>{{let e=localStorage.getItem("theme");e?l(e):window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches&&l("dark");let t=window.matchMedia("(prefers-color-scheme: dark)"),r=e=>{localStorage.getItem("theme")||l(e.matches?"dark":"light")};return t.addEventListener("change",r),()=>t.removeEventListener("change",r)}},[]),(0,s.useEffect)(()=>{if("undefined"!=typeof document){let e=document.documentElement;e.classList.remove("light-theme","dark-theme"),e.classList.add("".concat(i,"-theme")),localStorage.setItem("theme",i)}},[i]),(0,o.jsx)(a.Provider,{value:{theme:i,setTheme:l,toggleTheme:()=>{l(e=>"light"===e?"dark":"light")}},children:t})}function l(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var o=r(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=o.createContext&&o.createContext(s),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,o)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var o,s,a;o=e,s=t,a=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in o?Object.defineProperty(o,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):o[s]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>o.createElement(p,l({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>o.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function p(e){var t=t=>{var r,{attr:s,size:a,title:n}=e,d=function(e,t){if(null==e)return{};var r,o,s=function(e,t){if(null==e)return{};var r={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,i),p=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),o.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:p,width:p,xmlns:"http://www.w3.org/2000/svg"}),n&&o.createElement("title",null,n),e.children)};return void 0!==a?o.createElement(a.Consumer,null,e=>t(e)):t(s)}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,8264,8441,1684,7358],()=>t(37850)),_N_E=e.O()}]);