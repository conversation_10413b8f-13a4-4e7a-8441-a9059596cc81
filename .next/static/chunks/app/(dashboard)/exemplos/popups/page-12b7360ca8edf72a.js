(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4332],{35229:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});var l=a(95155),r=a(57740);function t(e){let{popupId:s,tabId:a,children:t,className:i="",onClick:n,ariaLabel:d,style:c,disabled:o=!1,variant:p="link"}=e,{theme:u}=(0,r.D)(),m={..."link"===p?{color:"rgb(var(--primary-600))",textDecoration:"none",":hover":{textDecoration:"underline"}}:{padding:"0.5rem 1rem",backgroundColor:"rgb(var(--primary-600))",color:"white",borderRadius:"0.375rem",display:"inline-flex",alignItems:"center",justifyContent:"center",transition:"background-color 150ms ease",":hover":{backgroundColor:"rgb(var(--primary-700))"}},...o?{opacity:.5,cursor:"not-allowed"}:{},...c},x="".concat(o?"opacity-50 cursor-not-allowed":""," ").concat(i);return(0,l.jsx)("a",{href:(()=>{{let e=new URL(window.location.href);return e.searchParams.set("popup",s),a&&e.searchParams.set("tab",a),e.toString()}})(),className:x,onClick:e=>{if(e.preventDefault(),!o){{let e=new URL(window.location.href);e.searchParams.set("popup",s),a&&e.searchParams.set("tab",a),window.history.pushState({},"",e.toString())}n&&n()}},"aria-label":d,style:m,role:"button","aria-disabled":o,tabIndex:o?-1:0,children:t})}},35695:(e,s,a)=>{"use strict";var l=a(18999);a.o(l,"useParams")&&a.d(s,{useParams:function(){return l.useParams}}),a.o(l,"usePathname")&&a.d(s,{usePathname:function(){return l.usePathname}}),a.o(l,"useRouter")&&a.d(s,{useRouter:function(){return l.useRouter}}),a.o(l,"useSearchParams")&&a.d(s,{useSearchParams:function(){return l.useSearchParams}})},43854:(e,s,a)=>{Promise.resolve().then(a.bind(a,89230))},74489:(e,s,a)=>{"use strict";a.d(s,{Y:()=>t});var l=a(12115),r=a(35695);function t(e){let{popupId:s="default-popup-id",defaultTabId:a,queryParam:t="popup",closeOnNavigate:i=!0}=e||{};(0,r.useRouter)(),(0,r.usePathname)(),(0,r.useSearchParams)();let[n,d]=(0,l.useState)(!1),[c,o]=(0,l.useState)(a||"");return{isOpen:n,activeTabId:c,openPopup:e=>{o(e||a||""),d(!0)},closePopup:()=>{d(!1)},changeTab:e=>{o(e)}}}},89230:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var l=a(95155),r=a(29911),t=a(74489),i=a(17292),n=a(35229);function d(){let{isOpen:e,openPopup:s,closePopup:a}=(0,t.Y)({popupId:"simple-popup"}),{isOpen:d,activeTabId:c,openPopup:o,closePopup:p}=(0,t.Y)({popupId:"tabs-popup",defaultTabId:"info"}),{isOpen:u,activeTabId:m,openPopup:x,closePopup:h}=(0,t.Y)({popupId:"fullscreen-popup",defaultTabId:"dashboard"}),{isOpen:b,activeTabId:f,openPopup:g,closePopup:j}=(0,t.Y)({popupId:"settings-popup",defaultTabId:"profile"}),N=[{id:"info",label:"Informa\xe7\xf5es",icon:(0,l.jsx)(r.ELu,{className:"text-blue-500"}),color:"blue",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Informa\xe7\xf5es do Popup"}),(0,l.jsx)("p",{children:"Este \xe9 um exemplo de popup com m\xfaltiplas abas. Voc\xea pode navegar entre as abas clicando nos bot\xf5es acima."}),(0,l.jsx)("p",{children:"A URL \xe9 atualizada automaticamente para refletir a aba atual, permitindo navega\xe7\xe3o direta para abas espec\xedficas."})]})},{id:"user",label:"Usu\xe1rio",icon:(0,l.jsx)(r.x$1,{className:"text-green-500"}),color:"green",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Informa\xe7\xf5es do Usu\xe1rio"}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center",children:(0,l.jsx)(r.x$1,{className:"text-gray-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium",children:"Jo\xe3o Silva"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"<EMAIL>"})]})]})})]})},{id:"settings",label:"Configura\xe7\xf5es",icon:(0,l.jsx)(r.Pcn,{className:"text-purple-500"}),color:"purple",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Configura\xe7\xf5es"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("span",{children:"Notifica\xe7\xf5es por email"}),(0,l.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),(0,l.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("span",{children:"Tema escuro"}),(0,l.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",className:"sr-only peer"}),(0,l.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})}],y=[{id:"dashboard",label:"Dashboard",icon:(0,l.jsx)(r.ELu,{className:"text-blue-500"}),color:"blue",content:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("h3",{className:"text-xl font-medium",children:"Dashboard"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Usu\xe1rios"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"1,234"}),(0,l.jsx)("p",{className:"text-sm text-green-500",children:"+12% este m\xeas"})]}),(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Receita"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"R$ 56.789"}),(0,l.jsx)("p",{className:"text-sm text-green-500",children:"+8% este m\xeas"})]}),(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-2",children:"Convers\xf5es"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:"23%"}),(0,l.jsx)("p",{className:"text-sm text-red-500",children:"-2% este m\xeas"})]})]}),(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Atividade Recente"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 pb-3 border-b border-gray-100",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(r.x$1,{className:"text-blue-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm",children:"Novo usu\xe1rio registrado"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"H\xe1 5 minutos"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3 pb-3 border-b border-gray-100",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(r.SMR,{className:"text-green-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm",children:"Atualiza\xe7\xe3o de seguran\xe7a conclu\xedda"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"H\xe1 2 horas"})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,l.jsx)(r.jNV,{className:"text-yellow-500"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm",children:"Novo alerta do sistema"}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:"H\xe1 1 dia"})]})]})]})]})]})},{id:"analytics",label:"Analytics",icon:(0,l.jsx)(r.OKX,{className:"text-green-500"}),color:"green",content:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("h3",{className:"text-xl font-medium",children:"Analytics"}),(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Vis\xe3o Geral"}),(0,l.jsx)("div",{className:"h-64 bg-gray-100 rounded flex items-center justify-center",children:(0,l.jsx)("p",{className:"text-gray-500",children:"Gr\xe1fico de Analytics (Placeholder)"})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Tr\xe1fego por Fonte"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Google"}),(0,l.jsx)("span",{className:"text-sm",children:"45%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"45%"}})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Direto"}),(0,l.jsx)("span",{className:"text-sm",children:"30%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"30%"}})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Redes Sociais"}),(0,l.jsx)("span",{className:"text-sm",children:"15%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:"15%"}})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Outros"}),(0,l.jsx)("span",{className:"text-sm",children:"10%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-gray-500 h-2 rounded-full",style:{width:"10%"}})})]})]})]}),(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[(0,l.jsx)("h4",{className:"font-medium mb-4",children:"Dispositivos"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Mobile"}),(0,l.jsx)("span",{className:"text-sm",children:"65%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"65%"}})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Desktop"}),(0,l.jsx)("span",{className:"text-sm",children:"30%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"30%"}})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between mb-1",children:[(0,l.jsx)("span",{className:"text-sm",children:"Tablet"}),(0,l.jsx)("span",{className:"text-sm",children:"5%"})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:"5%"}})})]})]})]})]})]})}],v=[{id:"profile",label:"Perfil",icon:(0,l.jsx)(r.x$1,{className:"text-blue-500"}),color:"blue",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Configura\xe7\xf5es de Perfil"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome"}),(0,l.jsx)("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:"Jo\xe3o Silva"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,l.jsx)("input",{type:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:"<EMAIL>"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Telefone"}),(0,l.jsx)("input",{type:"tel",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:"(11) 98765-4321"})]}),(0,l.jsx)("div",{className:"pt-4",children:(0,l.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Salvar Altera\xe7\xf5es"})})]})]})},{id:"security",label:"Seguran\xe7a",icon:(0,l.jsx)(r.JhU,{className:"text-red-500"}),color:"red",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Configura\xe7\xf5es de Seguran\xe7a"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Senha Atual"}),(0,l.jsx)("input",{type:"password",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nova Senha"}),(0,l.jsx)("input",{type:"password",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirmar Nova Senha"}),(0,l.jsx)("input",{type:"password",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,l.jsx)("div",{className:"pt-4",children:(0,l.jsx)("button",{className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Alterar Senha"})})]})]})},{id:"notifications",label:"Notifica\xe7\xf5es",icon:(0,l.jsx)(r.jNV,{className:"text-yellow-500"}),color:"yellow",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Configura\xe7\xf5es de Notifica\xe7\xf5es"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Notifica\xe7\xf5es por email"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Receba atualiza\xe7\xf5es por email"})]}),(0,l.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),(0,l.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Notifica\xe7\xf5es push"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es no navegador"})]}),(0,l.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",className:"sr-only peer",defaultChecked:!0}),(0,l.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:"Notifica\xe7\xf5es de marketing"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Receba ofertas e promo\xe7\xf5es"})]}),(0,l.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",className:"sr-only peer"}),(0,l.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]})]})}];return(0,l.jsxs)("div",{className:"p-6 max-w-6xl mx-auto",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Exemplos de Popups"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-12",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Popup Simples"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Um popup b\xe1sico com uma \xfanica se\xe7\xe3o de conte\xfado."}),(0,l.jsx)("button",{onClick:()=>s(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Abrir Popup Simples"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Popup com Abas"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Um popup com m\xfaltiplas abas para organizar o conte\xfado."}),(0,l.jsxs)("div",{className:"space-x-2",children:[(0,l.jsx)("button",{onClick:()=>o("info"),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Abrir na Aba Info"}),(0,l.jsx)("button",{onClick:()=>o("user"),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Abrir na Aba Usu\xe1rio"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Popup Fullscreen"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Um popup que ocupa toda a tela, ideal para conte\xfados complexos."}),(0,l.jsx)("button",{onClick:()=>x("dashboard"),className:"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Abrir Popup Fullscreen"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Popup de Configura\xe7\xf5es"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Um popup para configura\xe7\xf5es com m\xfaltiplas se\xe7\xf5es."}),(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsx)(n.A,{popupId:"settings-popup",tabId:"profile",variant:"button",onClick:()=>g("profile"),children:"Perfil"}),(0,l.jsx)(n.A,{popupId:"settings-popup",tabId:"security",variant:"button",className:"bg-red-600 hover:bg-red-700",onClick:()=>g("security"),children:"Seguran\xe7a"}),(0,l.jsx)(n.A,{popupId:"settings-popup",tabId:"notifications",variant:"button",className:"bg-yellow-500 hover:bg-yellow-600",onClick:()=>g("notifications"),children:"Notifica\xe7\xf5es"})]})})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm mb-8",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Links Diretos para Popups"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Voc\xea pode criar links diretos para popups e abas espec\xedficas usando o componente PopupLink."}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium mb-2",children:"Variante Link"}),(0,l.jsxs)("div",{className:"space-x-4",children:[(0,l.jsx)(n.A,{popupId:"tabs-popup",tabId:"info",onClick:()=>o("info"),children:"Informa\xe7\xf5es"}),(0,l.jsx)(n.A,{popupId:"tabs-popup",tabId:"user",onClick:()=>o("user"),children:"Usu\xe1rio"}),(0,l.jsx)(n.A,{popupId:"tabs-popup",tabId:"settings",onClick:()=>o("settings"),children:"Configura\xe7\xf5es"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium mb-2",children:"Variante Bot\xe3o"}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,l.jsx)(n.A,{popupId:"fullscreen-popup",tabId:"dashboard",variant:"button",onClick:()=>x("dashboard"),children:"Dashboard"}),(0,l.jsx)(n.A,{popupId:"fullscreen-popup",tabId:"analytics",variant:"button",className:"bg-green-600 hover:bg-green-700",onClick:()=>x("analytics"),children:"Analytics"}),(0,l.jsx)(n.A,{popupId:"settings-popup",tabId:"profile",variant:"button",disabled:!0,onClick:()=>g("profile"),children:"Link Desabilitado"})]})]})]})]}),(0,l.jsx)(i.A,{id:"simple-popup",isOpen:e,onClose:a,title:"Popup Simples",tabs:[{id:"content",label:"Conte\xfado",content:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Conte\xfado do Popup"}),(0,l.jsx)("p",{children:"Este \xe9 um exemplo de popup simples com uma \xfanica se\xe7\xe3o de conte\xfado."}),(0,l.jsx)("p",{children:"O popup pode ser fechado clicando no bot\xe3o X, pressionando ESC, ou clicando fora da \xe1rea do popup."}),(0,l.jsx)("div",{className:"pt-4",children:(0,l.jsx)("button",{onClick:a,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Fechar Popup"})})]})}],size:"small",minContentHeight:"350px"}),(0,l.jsx)(i.A,{id:"tabs-popup",isOpen:d,onClose:p,title:"Popup com Abas",tabs:N,defaultTabId:c,size:"medium",minContentHeight:"400px"}),(0,l.jsx)(i.A,{id:"fullscreen-popup",isOpen:u,onClose:h,title:"Popup Fullscreen",tabs:y,defaultTabId:m,size:"fullscreen",minContentHeight:"500px"}),(0,l.jsx)(i.A,{id:"settings-popup",isOpen:b,onClose:j,title:"Configura\xe7\xf5es",tabs:v,defaultTabId:f,size:"large",minContentHeight:"450px"})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,8264,535,7292,8441,1684,7358],()=>s(43854)),_N_E=e.O()}]);