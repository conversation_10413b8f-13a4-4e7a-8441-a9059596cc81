(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3158],{12444:(e,r,a)=>{Promise.resolve().then(a.bind(a,93383))},35229:(e,r,a)=>{"use strict";a.d(r,{A:()=>l});var s=a(95155),o=a(57740);function l(e){let{popupId:r,tabId:a,children:l,className:t="",onClick:d,ariaLabel:i,style:n,disabled:c=!1,variant:m="link"}=e,{theme:u}=(0,o.D)(),p={..."link"===m?{color:"rgb(var(--primary-600))",textDecoration:"none",":hover":{textDecoration:"underline"}}:{padding:"0.5rem 1rem",backgroundColor:"rgb(var(--primary-600))",color:"white",borderRadius:"0.375rem",display:"inline-flex",alignItems:"center",justifyContent:"center",transition:"background-color 150ms ease",":hover":{backgroundColor:"rgb(var(--primary-700))"}},...c?{opacity:.5,cursor:"not-allowed"}:{},...n},b="".concat(c?"opacity-50 cursor-not-allowed":""," ").concat(t);return(0,s.jsx)("a",{href:(()=>{{let e=new URL(window.location.href);return e.searchParams.set("popup",r),a&&e.searchParams.set("tab",a),e.toString()}})(),className:b,onClick:e=>{if(e.preventDefault(),!c){{let e=new URL(window.location.href);e.searchParams.set("popup",r),a&&e.searchParams.set("tab",a),window.history.pushState({},"",e.toString())}d&&d()}},"aria-label":i,style:p,role:"button","aria-disabled":c,tabIndex:c?-1:0,children:l})}},93383:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c});var s=a(95155),o=a(12115),l=a(57740),t=a(29911);function d(e){let{className:r=""}=e,{theme:a,toggleTheme:o}=(0,l.D)();return(0,s.jsx)("button",{onClick:o,className:"p-2 rounded-full transition-colors ".concat(r),style:{backgroundColor:"dark"===a?"rgba(var(--background-tertiary), 0.5)":"rgba(var(--background-tertiary), 0.2)",color:"rgb(var(--text-secondary))"},"aria-label":"dark"===a?"Mudar para tema claro":"Mudar para tema escuro",children:"dark"===a?(0,s.jsx)(t.wQq,{className:"w-5 h-5"}):(0,s.jsx)(t.V6H,{className:"w-5 h-5"})})}var i=a(17292),n=a(35229);function c(){let{theme:e}=(0,l.D)(),[r,a]=(0,o.useState)(!1),c=[{id:"info",label:"Informa\xe7\xf5es",icon:(0,s.jsx)(t.ELu,{className:"text-blue-500 mr-1"}),color:"blue",content:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Sistema de Temas"}),(0,s.jsx)("p",{children:"O sistema de temas permite alternar entre os modos claro e escuro. Todos os componentes s\xe3o atualizados automaticamente para usar as cores do tema atual."}),(0,s.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,s.jsx)("h4",{className:"font-medium text-blue-700 dark:text-blue-300 mb-2",children:"Como funciona"}),(0,s.jsx)("p",{className:"text-blue-600 dark:text-blue-400",children:"O sistema de temas usa vari\xe1veis CSS para definir as cores de cada tema. Quando o tema \xe9 alterado, as vari\xe1veis s\xe3o atualizadas e todos os componentes que usam essas vari\xe1veis s\xe3o atualizados automaticamente."})]})]})},{id:"cores",label:"Cores",icon:(0,s.jsx)(t.lV_,{className:"text-purple-500 mr-1"}),color:"purple",content:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Paleta de Cores"}),(0,s.jsx)("p",{children:"A paleta de cores \xe9 definida por vari\xe1veis CSS que s\xe3o atualizadas quando o tema \xe9 alterado."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Cores de Fundo"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"p-4 rounded",style:{backgroundColor:"rgb(var(--background-primary))"},children:(0,s.jsx)("p",{style:{color:"rgb(var(--text-primary))"},children:"Background Primary"})}),(0,s.jsx)("div",{className:"p-4 rounded",style:{backgroundColor:"rgb(var(--background-secondary))"},children:(0,s.jsx)("p",{style:{color:"rgb(var(--text-primary))"},children:"Background Secondary"})}),(0,s.jsx)("div",{className:"p-4 rounded",style:{backgroundColor:"rgb(var(--background-tertiary))"},children:(0,s.jsx)("p",{style:{color:"rgb(var(--text-primary))"},children:"Background Tertiary"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Cores de Texto"}),(0,s.jsxs)("div",{className:"space-y-2 p-4 rounded",style:{backgroundColor:"rgb(var(--background-secondary))"},children:[(0,s.jsx)("p",{style:{color:"rgb(var(--text-primary))"},children:"Texto Prim\xe1rio"}),(0,s.jsx)("p",{style:{color:"rgb(var(--text-secondary))"},children:"Texto Secund\xe1rio"}),(0,s.jsx)("p",{style:{color:"rgb(var(--text-tertiary))"},children:"Texto Terci\xe1rio"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Cores de A\xe7\xe3o"}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsx)("button",{className:"px-4 py-2 rounded",style:{backgroundColor:"rgb(var(--primary-600))",color:"white"},children:"Prim\xe1ria"}),(0,s.jsx)("button",{className:"px-4 py-2 rounded",style:{backgroundColor:"rgb(var(--success-600))",color:"white"},children:"Sucesso"}),(0,s.jsx)("button",{className:"px-4 py-2 rounded",style:{backgroundColor:"rgb(var(--error-600))",color:"white"},children:"Erro"}),(0,s.jsx)("button",{className:"px-4 py-2 rounded",style:{backgroundColor:"rgb(var(--warning-600))",color:"white"},children:"Aviso"})]})]})]})}];return(0,s.jsxs)("div",{className:"p-6 max-w-6xl mx-auto",style:{color:"rgb(var(--text-primary))"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Sistema de Temas"}),(0,s.jsx)(d,{})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-12",children:[(0,s.jsxs)("div",{className:"rounded-xl p-6 shadow-sm",style:{backgroundColor:"rgb(var(--background-primary))",borderColor:"rgb(var(--border-light))",boxShadow:"0 4px 6px -1px rgba(var(--popup-shadow))"},children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold mb-4",children:["Tema Atual: ","dark"===e?"Escuro":"Claro"]}),(0,s.jsx)("p",{className:"mb-4",style:{color:"rgb(var(--text-secondary))"},children:"O sistema de temas permite alternar entre os modos claro e escuro. Clique no bot\xe3o no canto superior direito para alternar o tema."}),(0,s.jsx)("button",{onClick:()=>a(!0),className:"px-4 py-2 rounded-md transition-colors",style:{backgroundColor:"rgb(var(--primary-600))",color:"white"},children:"Abrir Popup"})]}),(0,s.jsxs)("div",{className:"rounded-xl p-6 shadow-sm",style:{backgroundColor:"rgb(var(--background-secondary))",borderColor:"rgb(var(--border-light))",boxShadow:"0 4px 6px -1px rgba(var(--popup-shadow))"},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Links de Popup"}),(0,s.jsx)("p",{className:"mb-4",style:{color:"rgb(var(--text-secondary))"},children:"Os links de popup tamb\xe9m s\xe3o atualizados automaticamente para usar as cores do tema atual."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Variante Link"}),(0,s.jsx)(n.A,{popupId:"theme-popup",tabId:"info",onClick:()=>a(!0),children:"Abrir popup (link)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Variante Bot\xe3o"}),(0,s.jsx)(n.A,{popupId:"theme-popup",tabId:"cores",variant:"button",onClick:()=>a(!0),children:"Ver cores"})]})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-6 shadow-sm mb-8",style:{backgroundColor:"rgb(var(--background-primary))",borderColor:"rgb(var(--border-light))",boxShadow:"0 4px 6px -1px rgba(var(--popup-shadow))"},children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Componentes com Suporte a Temas"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{style:{color:"rgb(var(--text-secondary))"},children:"Os seguintes componentes foram atualizados para suportar temas:"}),(0,s.jsxs)("ul",{className:"list-disc pl-5 space-y-2",style:{color:"rgb(var(--text-secondary))"},children:[(0,s.jsx)("li",{children:"StandardPopup - Popup padronizado com suporte para abas"}),(0,s.jsx)("li",{children:"PopupLink - Links para abrir popups"}),(0,s.jsx)("li",{children:"ThemeToggle - Bot\xe3o para alternar entre temas"})]}),(0,s.jsx)("p",{style:{color:"rgb(var(--text-secondary))"},children:"Todos esses componentes usam vari\xe1veis CSS para definir suas cores, o que permite que eles sejam atualizados automaticamente quando o tema \xe9 alterado."})]})]}),(0,s.jsx)(i.A,{id:"theme-popup",isOpen:r,onClose:()=>a(!1),title:"Popup com Suporte a Temas",tabs:c,defaultTabId:"info",size:"large",minContentHeight:"450px"})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6711,8264,535,7292,8441,1684,7358],()=>r(12444)),_N_E=e.O()}]);