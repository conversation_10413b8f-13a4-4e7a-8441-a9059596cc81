(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2578],{4720:(e,s,t)=>{Promise.resolve().then(t.bind(t,46700))},35695:(e,s,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},46700:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(95155),a=t(35695);function n(){let e=(0,a.useRouter)();return(0,r.jsxs)("div",{className:"flex justify-center items-center min-h-screen bg-white bg-opacity-40 p-4 relative",onClick:s=>{s.target===s.currentTarget&&e.push("/restaurante")},children:[(0,r.jsx)("button",{onClick:()=>e.push("/restaurante"),className:"absolute top-4 left-4 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})})}),(0,r.jsxs)("div",{onClick:e=>e.stopPropagation(),className:"w-[850px] h-[650px] bg-[#f7f7f7] rounded-2xl shadow-2xl flex flex-col overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-[#f0f0f0] border-b border-gray-300",children:[(0,r.jsxs)("div",{className:"px-6 pt-4 pb-2 flex items-center gap-2",children:[(0,r.jsx)("i",{className:"fas fa-cog text-gray-700"}),(0,r.jsx)("h2",{className:"text-lg font-bold",children:"Configura\xe7\xf5es"})]}),(0,r.jsxs)("div",{className:"flex px-5",children:[(0,r.jsxs)("div",{className:"tab-item active px-6 py-3 font-medium text-sm text-[#555] hover:text-[#333] relative flex items-center cursor-pointer",children:[(0,r.jsx)("i",{className:"fas fa-store mr-2 text-[#0071e3]"})," Informa\xe7\xf5es"]}),(0,r.jsxs)("div",{className:"tab-item px-6 py-3 font-medium text-sm text-[#555] hover:text-[#333] relative flex items-center cursor-pointer",children:[(0,r.jsx)("i",{className:"fas fa-credit-card mr-2 text-purple-500"})," Pagamentos"]}),(0,r.jsxs)("div",{className:"tab-item px-6 py-3 font-medium text-sm text-[#555] hover:text-[#333] relative flex items-center cursor-pointer",children:[(0,r.jsx)("i",{className:"fas fa-lock mr-2 text-red-500"})," Seguran\xe7a"]})]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,r.jsx)("p",{children:"Conte\xfado da aba ativa aparecer\xe1 aqui."})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8441,1684,7358],()=>s(4720)),_N_E=e.O()}]);