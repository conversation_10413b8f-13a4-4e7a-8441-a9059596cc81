(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2760],{17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>i});var t=s(95155),r=s(12115),n=s(36217),l=s(59434);let i=n.bL,o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});o.displayName=n.B8.displayName;let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});d.displayName=n.l9.displayName;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=n.UC.displayName},22964:(e,a,s)=>{"use strict";s.d(a,{eu:()=>o,q5:()=>c,BK:()=>d,Ay:()=>m});var t=s(95155),r=s(12115),n=s(54011),l=s(66766),i=s(59434);let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.bL,{ref:a,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});o.displayName=n.bL.displayName;let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n._V,{ref:a,className:(0,i.cn)("aspect-square h-full w-full",s),...r})});d.displayName=n._V.displayName;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.H4,{ref:a,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});c.displayName=n.H4.displayName;let m=function(e){let{userId:a,name:s,size:n=40,className:i="",customUrl:o}=e,[d,c]=r.useState(!1),m=o;return(!o||d)&&(m=a?function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];let a=Math.abs(e.split("").reduce((e,a)=>e+a.charCodeAt(0),0)%10);return"".concat("https://pbehloddlzwandfmpzbo.supabase.co/storage/v1/object/public","/avatars/default-").concat(a,".png")}(a,n):function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150,s=e.split(" ").map(e=>e.charAt(0)).join("").substring(0,2).toUpperCase(),t=e.split("").reduce((e,a)=>e+a.charCodeAt(0),0)%360,r='\n    <svg xmlns="http://www.w3.org/2000/svg" width="'.concat(a,'" height="').concat(a,'" viewBox="0 0 ').concat(a," ").concat(a,'">\n      <rect width="100%" height="100%" fill="').concat("hsl(".concat(t,", 70%, 60%)"),'" />\n      <text x="50%" y="50%" dy=".1em" \n        font-family="Arial, sans-serif" \n        font-size="').concat(.4*a,'" \n        fill="white" \n        text-anchor="middle" \n        dominant-baseline="middle">').concat(s,"</text>\n    </svg>\n  ");return"data:image/svg+xml;base64,".concat(btoa(r))}(s,n)),s.split(" ").map(e=>e.charAt(0)).join("").substring(0,2).toUpperCase(),(0,t.jsx)("div",{className:"relative overflow-hidden rounded-full ".concat(i),style:{width:n,height:n},children:m.startsWith("data:")?(0,t.jsx)("img",{src:m,alt:s,className:"w-full h-full object-cover"}):(0,t.jsx)(l.default,{src:m,alt:s,width:n,height:n,className:"object-cover",onError:()=>{c(!0)}})})}},26126:(e,a,s)=>{"use strict";s.d(a,{E:()=>i});var t=s(95155);s(12115);var r=s(74466),n=s(59434);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200/80"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:s,...r}=e;return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:s}),a),...r})}},35537:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>as});var t=s(95155),r=s(12115),n=s(73579),l=s(40283),i=s(86382),o=s(29911),d=s(60637),c=s(25175),m=s(35695),x=s(46102),u=s(30285),h=s(21399),f=s(59434);let p=h.bL,g=h.l9,j=r.forwardRef((e,a)=>{let{className:s,align:r="center",sideOffset:n=4,...l}=e;return(0,t.jsx)(h.ZL,{children:(0,t.jsx)(h.UC,{ref:a,align:r,sideOffset:n,className:(0,f.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})})});function v(e){let{campaignId:a,userId:s,userRole:n}=e,[l,i]=(0,r.useState)([]),[d,c]=(0,r.useState)(0),[m,x]=(0,r.useState)(!0),[h,f]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setTimeout(()=>{let e=function(e,a,s){try{let t=new Date,r=[],n=a||"default-user",l=e||"default-campaign";return"influencer"===s?(r.push({id:"1",user_id:n,campaign_id:l,type:"rank_change",title:"Sua posi\xe7\xe3o melhorou!",message:"Voc\xea subiu 2 posi\xe7\xf5es no ranking e agora est\xe1 em 3\xba lugar.",read:!1,created_at:new Date(t.getTime()-72e5).toISOString()}),r.push({id:"2",user_id:n,campaign_id:l,type:"engagement",title:"Aumento de engajamento",message:"Seu \xfaltimo post teve um aumento de 15% em salvamentos.",read:!0,created_at:new Date(t.getTime()-864e5).toISOString()}),r.push({id:"3",user_id:n,campaign_id:l,type:"campaign",title:"Campanha atualizada",message:"O restaurante adicionou novos requisitos \xe0 campanha.",read:!0,created_at:new Date(t.getTime()-2592e5).toISOString()})):(r.push({id:"1",user_id:n,campaign_id:l,type:"new_post",title:"Novo post na campanha",message:"O influenciador LuizVincenzi publicou um novo post.",read:!1,created_at:new Date(t.getTime()-36e5).toISOString()}),r.push({id:"2",user_id:n,campaign_id:l,type:"engagement",title:"Alto engajamento",message:"A campanha teve um aumento de 25% em salvamentos esta semana.",read:!1,created_at:new Date(t.getTime()-432e5).toISOString()}),r.push({id:"3",user_id:n,campaign_id:l,type:"rank_change",title:"Mudan\xe7a no ranking",message:"Gustavo Caliani subiu para o 1\xba lugar no ranking.",read:!0,created_at:new Date(t.getTime()-1728e5).toISOString()})),r}catch(e){return console.error("Erro ao gerar notifica\xe7\xf5es de exemplo:",e),[]}}(a,s,n);i(e),c(e.filter(e=>!e.read).length),x(!1)},500);return()=>clearTimeout(e)},[a,s,n]),(0,r.useEffect)(()=>{h&&d>0&&(i(e=>e.map(e=>({...e,read:!0}))),c(0))},[h]);let v=e=>{switch(e){case"rank_change":return(0,t.jsx)(o.SBv,{className:"text-yellow-500"});case"engagement":return(0,t.jsx)(o.U$b,{className:"text-green-500"});case"new_post":return(0,t.jsx)(o.uCC,{className:"text-blue-500"});default:return(0,t.jsx)(o.jNV,{className:"text-gray-500"})}},N=e=>{let a=new Date(e),s=Math.floor(Math.floor((new Date().getTime()-a.getTime())/1e3)/60),t=Math.floor(s/60),r=Math.floor(t/24);return r>0?"".concat(r," ").concat(1===r?"dia":"dias"," atr\xe1s"):t>0?"".concat(t," ").concat(1===t?"hora":"horas"," atr\xe1s"):s>0?"".concat(s," ").concat(1===s?"minuto":"minutos"," atr\xe1s"):"agora"};return(0,t.jsxs)(p,{open:h,onOpenChange:f,children:[(0,t.jsx)(g,{asChild:!0,children:(0,t.jsxs)(u.$,{variant:"ghost",size:"icon",className:"relative bg-white/10 backdrop-blur-sm rounded-full p-2","aria-label":"Notifica\xe7\xf5es",children:[(0,t.jsx)(o.jNV,{className:"h-5 w-5 text-white"}),d>0&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:d})]})}),(0,t.jsxs)(j,{className:"w-80 p-0 overflow-hidden",align:"end",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-900 to-indigo-900 p-3 text-white rounded-t-md",children:[(0,t.jsxs)("h3",{className:"font-semibold flex items-center",children:[(0,t.jsx)(o.jNV,{className:"mr-2 text-blue-300"}),"Notifica\xe7\xf5es"]}),(0,t.jsx)("p",{className:"text-xs text-blue-200",children:"Atualiza\xe7\xf5es da campanha"})]}),(0,t.jsx)("div",{className:"max-h-80 overflow-y-auto",children:m?(0,t.jsx)("div",{className:"flex justify-center items-center p-4",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"})}):0===l.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nenhuma notifica\xe7\xe3o"}):(0,t.jsx)("div",{className:"divide-y divide-gray-100",children:l.map(e=>(0,t.jsx)("div",{className:"p-3 hover:bg-gray-50 transition-colors ".concat(e.read?"":"bg-blue-50"),children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"mr-3 mt-0.5",children:v(e.type)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:N(e.created_at)})]}),(0,t.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:e.message})]})]})},e.id))})}),(0,t.jsx)("div",{className:"p-2 bg-gray-50 border-t border-gray-100 rounded-b-md",children:(0,t.jsx)(u.$,{variant:"ghost",size:"sm",className:"w-full text-xs text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>f(!1),children:(0,t.jsx)("span",{className:"flex items-center justify-center",children:"Ver todas as notifica\xe7\xf5es"})})})]})]})}function N(e){let{campaignId:a,userId:s,userRole:l,className:i=""}=e,[d,c]=(0,r.useState)(!0),[m,u]=(0,r.useState)(null),[h,f]=(0,r.useState)(null),[p,g]=(0,r.useState)([]),[j,N]=(0,r.useState)(!1),b=(0,n.createClientComponentClient)();if((0,r.useEffect)(()=>{async function e(){var e,t,r;try{if(c(!0),u(null),console.log("RankingHighlightFallback - Iniciando busca de dados para:",{campaignId:a,userId:s,userRole:l}),"influencer"===l){let{data:t,error:r}=await b.from("campaign_influencers").select("\n              id,\n              influencer_id,\n              campaign_id,\n              status,\n              created_at\n            ").eq("campaign_id",a);if(r)throw r;if(!(null==t?void 0:t.find(e=>e.influencer_id===s)))return console.log("Usu\xe1rio n\xe3o encontrado na campanha, retornando dados vazios"),{userRank:null,userPoints:0,totalParticipants:(null==t?void 0:t.length)||0,topInfluencers:[],userPreviousRank:null,userRankChange:0};let{data:n,error:l}=await b.from("profiles").select("id, full_name, profile_data").in("id",t.map(e=>e.influencer_id));if(l)throw l;let i=null==n?void 0:n.find(e=>e.id===s),o={influencer_id:s,rank:(null==t?void 0:t.length)||1,previous_rank:null,total_influencers:(null==t?void 0:t.length)||1,total_likes:0,total_comments:0,total_saves:0,total_points:0,engagement_rate:"0.0"};f(o);let d=t.map((e,a)=>{var s;let t=null==n?void 0:n.find(a=>a.id===e.influencer_id),r=(null==t?void 0:null===(s=t.profile_data)||void 0===s?void 0:s.instagram_username)||"username";return{influencer_id:e.influencer_id,influencer_name:(null==t?void 0:t.full_name)||"Influencer ".concat(a+1),username:r,rank:a+1,total_likes:0,total_comments:0,total_saves:0,total_points:0,engagement_rate:0}});d.sort((e,a)=>e.rank-a.rank);let c=d.slice(0,5);c.some(e=>e.influencer_id===s)||c.push({influencer_id:s,influencer_name:(null==i?void 0:i.full_name)||"Voc\xea",username:(null==i?void 0:null===(e=i.profile_data)||void 0===e?void 0:e.instagram_username)||"seu_username",rank:o.rank,total_likes:o.total_likes,total_comments:o.total_comments,total_saves:o.total_saves,total_points:o.total_points,engagement_rate:parseFloat(o.engagement_rate)}),g(c)}else{console.log("Buscando influenciadores aceitos para a campanha:",a);let{data:e,error:s}=await b.from("campaign_influencers").select("\n              id,\n              influencer_id,\n              status\n            ").eq("campaign_id",a).eq("status","accepted");if(s)throw console.error("Erro ao buscar influenciadores aceitos:",s),s;if(console.log("Influenciadores aceitos encontrados:",e),!e||0===e.length){console.log("Nenhum influenciador aceito encontrado para esta campanha");let{data:e,error:s}=await b.from("campaign_influencers").select("\n                id,\n                influencer_id,\n                status\n              ").eq("campaign_id",a);if(console.log("Todos os influenciadores da campanha (debug):",e),e&&e.length>0)console.log("Usando todos os influenciadores dispon\xedveis como fallback"),u(null);else{u("Nenhum influenciador aceito encontrado para esta campanha"),c(!1);return}}console.log("Buscando m\xe9tricas para os influenciadores aceitos");let r=e?e.map(e=>e.influencer_id):[];if(0===r.length){let{data:e}=await b.from("campaign_influencers").select("influencer_id").eq("campaign_id",a);e&&e.length>0&&(r=e.map(e=>e.influencer_id),console.log("Usando todos os influenciadores dispon\xedveis:",r))}let{data:n,error:l}=await b.from("campaign_influencers").select("\n              id,\n              influencer_id,\n              campaign_id,\n              status,\n              created_at\n            ").eq("campaign_id",a).in("influencer_id",r).order("created_at",{ascending:!0});if(l)throw console.error("Erro ao buscar m\xe9tricas dos influenciadores:",l),l;if(console.log("Dados de m\xe9tricas dos influenciadores:",n),n&&0!==n.length){console.log("Buscando perfis para os influenciadores:",n.map(e=>e.influencer_id));let{data:e,error:a}=await b.from("profiles").select("id, full_name, profile_data").in("id",n.map(e=>e.influencer_id));if(a)throw console.error("Erro ao buscar perfis:",a),a;console.log("Perfis encontrados:",e),console.log("Buscando posts dos influenciadores");let{data:s,error:t}=await b.from("posts").select("\n                id,\n                campaign_influencer_id,\n                likes_count,\n                comments_count,\n                saves_count,\n                engagement_rate\n              ").in("campaign_influencer_id",n.map(e=>e.id));t&&console.error("Erro ao buscar posts:",t),console.log("Posts encontrados:",s);let r=n.map((a,t)=>{var r;let n=null==e?void 0:e.find(e=>e.id===a.influencer_id);console.log("Perfil para influenciador ".concat(a.influencer_id,":"),n);let l=(null==s?void 0:s.filter(e=>e.campaign_influencer_id===a.id))||[];console.log("Posts para influenciador ".concat(a.influencer_id,":"),l);let i=l.reduce((e,a)=>e+(a.likes_count||0),0),o=l.reduce((e,a)=>e+(a.comments_count||0),0),d=l.reduce((e,a)=>e+(a.saves_count||0),0),c=i>0?i:a.previous_total_likes||0,m=o>0?o:a.previous_total_comments||0,x=d>0?d:a.previous_total_saves||0,u=(null==n?void 0:null===(r=n.profile_data)||void 0===r?void 0:r.instagram_username)||((null==n?void 0:n.profile_data)&&"instagram_username"in n.profile_data?n.profile_data.instagram_username:"influencer".concat(t+1));return{influencer_id:a.influencer_id,influencer_name:(null==n?void 0:n.full_name)||"Influencer ".concat(t+1),username:u,rank:t+1,total_likes:c,total_comments:m,total_saves:x,total_points:c+2*m+3*x||0,engagement_rate:parseFloat("0"),has_real_data:l.length>0}});console.log("Competidores criados com dados reais:",r),r.sort((e,a)=>a.total_points!==e.total_points?a.total_points-e.total_points:e.rank-a.rank),r.forEach((e,a)=>{e.rank=a+1}),console.log("Competidores ordenados:",r),g(r.slice(0,5))}else{console.warn("Nenhum dado real de m\xe9tricas encontrado para os influenciadores. Usando dados de exemplo."),N(!0);let{data:e}=await b.from("campaign_influencers").select("influencer_id").eq("campaign_id",a),s=(null==e?void 0:e.map(e=>e.influencer_id))||[];console.log("Usando todos os influenciadores dispon\xedveis para dados de exemplo:",s);let{data:r,error:n}=await b.from("profiles").select("id, full_name, profile_data").in("id",s);n&&console.error("Erro ao buscar perfis dos influenciadores:",n),console.log("Perfis dos influenciadores para dados de exemplo:",r);let l=[],i=Math.min(5,s.length||1);for(let e=0;e<i;e++){let a=s[e]||"mock-id",n=null==r?void 0:r.find(e=>e.id===a);l.push({influencer_id:a,influencer_name:(null==n?void 0:n.full_name)||"Influencer ".concat(e+1),username:(null==n?void 0:null===(t=n.profile_data)||void 0===t?void 0:t.instagram_username)||"influencer".concat(e+1),rank:e+1,total_likes:Math.floor(1e3*Math.random())+100,total_comments:Math.floor(200*Math.random())+20,total_saves:Math.floor(100*Math.random())+10,total_points:Math.floor(2e3*Math.random())+200,engagement_rate:5*Math.random()+1,is_mock_data:!0})}g(l)}}}catch(e){if(console.error("Erro ao buscar dados de ranking:",e),u("N\xe3o foi poss\xedvel carregar os dados de ranking"),"restaurant"===l){console.warn("Erro ao buscar dados reais. Usando dados de exemplo como fallback."),N(!0);try{let{data:e}=await b.from("campaign_influencers").select("id, influencer_id, status").eq("campaign_id",a);if(e&&e.length>0){let{data:a}=await b.from("profiles").select("id, full_name, profile_data").in("id",e.map(e=>e.influencer_id)),s=[];for(let t=0;t<Math.min(5,e.length);t++){let n=e[t].influencer_id,l=null==a?void 0:a.find(e=>e.id===n);s.push({influencer_id:n,influencer_name:(null==l?void 0:l.full_name)||"Influencer ".concat(t+1),username:(null==l?void 0:null===(r=l.profile_data)||void 0===r?void 0:r.instagram_username)||"influencer".concat(t+1),rank:t+1,total_likes:Math.floor(1e3*Math.random())+100,total_comments:Math.floor(200*Math.random())+20,total_saves:Math.floor(100*Math.random())+10,total_points:Math.floor(2e3*Math.random())+200,engagement_rate:5*Math.random()+1,is_mock_data:!0})}g(s);return}}catch(e){console.error("Erro ao tentar buscar dados para fallback:",e)}let e=[];for(let a=0;a<5;a++)e.push({influencer_id:"mock-id-".concat(a),influencer_name:"Influencer ".concat(a+1),username:"influencer".concat(a+1),rank:a+1,total_likes:Math.floor(1e3*Math.random())+100,total_comments:Math.floor(200*Math.random())+20,total_saves:Math.floor(100*Math.random())+10,total_points:Math.floor(2e3*Math.random())+200,engagement_rate:5*Math.random()+1,is_mock_data:!0});g(e)}}finally{c(!1)}}a&&s&&e()},[a,s,l,b]),d)return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 animate-pulse ".concat(i),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"})]}),(0,t.jsx)("div",{className:"h-24 bg-gray-200 rounded-lg mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full mr-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]},a))})]});if(m)return(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg ".concat(i),children:[m,(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium",children:"Tentar novamente"})})]});if("influencer"===l&&h){let{rank:e,total_influencers:a,previous_rank:r,engagement_rate:n,total_points:l,total_saves:d}=h,c=r?r-e:0;return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden ".concat(i),children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold flex items-center",children:[(0,t.jsx)(o.SBv,{className:"mr-2 text-yellow-300"}),"Seu Ranking na Campanha"]}),(0,t.jsxs)("span",{className:"text-sm bg-white/20 px-2 py-1 rounded",children:[a," participantes"]})]})}),(0,t.jsx)("div",{className:"p-6 bg-blue-50 border-b border-blue-100",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl ".concat(1===e?"bg-yellow-500":2===e?"bg-gray-400":3===e?"bg-amber-700":"bg-blue-600"),children:[e,"\xba"]}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700",children:"Sua posi\xe7\xe3o atual"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:1===e?"\uD83C\uDFC6 Primeiro Lugar!":2===e?"\uD83E\uDD48 Segundo Lugar!":3===e?"\uD83E\uDD49 Terceiro Lugar!":"".concat(e,"\xba Lugar")}),(0,t.jsx)("div",{className:"flex items-center mt-1",children:c>0?(0,t.jsxs)("span",{className:"text-green-600 flex items-center text-sm",children:[(0,t.jsx)(o.uCC,{className:"mr-1"})," Subiu ",c," ",1===c?"posi\xe7\xe3o":"posi\xe7\xf5es"]}):c<0?(0,t.jsxs)("span",{className:"text-red-600 flex items-center text-sm",children:[(0,t.jsx)(o.$TP,{className:"mr-1"})," Desceu ",Math.abs(c)," ",1===Math.abs(c)?"posi\xe7\xe3o":"posi\xe7\xf5es"]}):(0,t.jsxs)("span",{className:"text-gray-600 flex items-center text-sm",children:[(0,t.jsx)(o.iu5,{className:"mr-1"})," Manteve a posi\xe7\xe3o"]})})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700",children:"Taxa de Engajamento"}),(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[n,"%"]}),(0,t.jsxs)("div",{className:"flex items-center justify-end mt-1 space-x-3 text-sm",children:[(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center",children:[(0,t.jsx)(o.SBv,{className:"mr-1 text-blue-500"})," ",l," pts"]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de pontos acumulados"})})]})}),(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded flex items-center",children:[(0,t.jsx)(o.U$b,{className:"mr-1 text-green-500"})," ",d]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de salvamentos"})})]})})]})]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Seus Competidores Diretos"}),(0,t.jsx)("div",{className:"space-y-3",children:p.map(e=>{let a=e.influencer_id===s,r=e.rank<h.rank;return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg ".concat(a?"bg-blue-50 border border-blue-200":r?"bg-red-50":"bg-green-50"),children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ".concat(1===e.rank?"bg-yellow-500":2===e.rank?"bg-gray-400":3===e.rank?"bg-amber-700":"bg-gray-600"),children:e.rank}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsxs)("div",{className:"font-medium ".concat(a?"text-blue-700":"text-gray-800"),children:[e.influencer_name," ",a&&"(Voc\xea)"]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["@",e.username]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"font-medium text-gray-800",children:[e.engagement_rate.toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-2 text-xs",children:[(0,t.jsxs)("span",{className:"flex items-center text-red-600",children:[(0,t.jsx)(o.Mbv,{className:"mr-0.5"})," ",e.total_likes]}),(0,t.jsxs)("span",{className:"flex items-center text-blue-600",children:[(0,t.jsx)(o.j1Q,{className:"mr-0.5"})," ",e.total_comments]}),(0,t.jsxs)("span",{className:"flex items-center text-green-600",children:[(0,t.jsx)(o.U$b,{className:"mr-0.5"})," ",e.total_saves]})]})]})]},e.influencer_id)})}),(0,t.jsxs)("div",{className:"mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold text-yellow-800 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),"Dicas para Melhorar seu Ranking"]}),(0,t.jsxs)("ul",{className:"mt-2 text-xs text-yellow-700 space-y-1",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento."]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Responda aos coment\xe1rios para aumentar o engajamento geral."]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Crie conte\xfado que incentive o compartilhamento e salvamento."]})]})]})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500",children:["\xdaltima atualiza\xe7\xe3o: ",new Date().toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})]})]})}if("restaurant"===l&&p.length>0){console.log("Renderizando para restaurante com competidores:",p);let e=Math.max(...p.map(e=>e.total_points));return(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-xl overflow-hidden ".concat(i),children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-blue-900 to-indigo-900 p-5 relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute top-0 left-0 w-full h-full opacity-10",children:(0,t.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-[url('https://www.uefa.com/contentassets/c9b1b12d4c074c3ca3f03a7fdb018a2f/ucl-2021-24-starball-on-pitch-min.jpg')] bg-cover bg-center"})}),(0,t.jsxs)("div",{className:"relative flex justify-between items-center",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("h3",{className:"text-xl font-bold flex items-center text-white",children:[(0,t.jsx)(o.SBv,{className:"mr-3 text-yellow-300 text-2xl"}),(0,t.jsxs)("span",{children:[(0,t.jsx)("span",{className:"block",children:"Champions da Campanha"}),(0,t.jsx)("span",{className:"text-sm font-normal text-blue-200",children:"Ranking de Influenciadores"})]})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm",children:[(0,t.jsx)("span",{className:"font-bold",children:p.length})," ",(0,t.jsx)("span",{className:"text-blue-200",children:"influenciadores"})]}),(0,t.jsx)(v,{campaignId:a,userId:s,userRole:l})]})]})]}),j&&(0,t.jsxs)("div",{className:"bg-yellow-100 text-yellow-800 p-3 text-sm border-b border-yellow-200 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-yellow-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,t.jsx)("span",{children:"Exibindo dados de exemplo. Os influenciadores s\xe3o reais, mas as m\xe9tricas s\xe3o simuladas para fins de visualiza\xe7\xe3o."})]}),(0,t.jsxs)("div",{className:"p-0",children:[(0,t.jsxs)("div",{className:"overflow-hidden rounded-lg",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-gray-100 to-blue-50 p-3 grid grid-cols-12 text-xs font-semibold border-b border-gray-200",children:[(0,t.jsx)("div",{className:"col-span-1 text-center text-gray-600",children:"#"}),(0,t.jsx)("div",{className:"col-span-3 text-gray-600",children:"Influenciador"}),(0,t.jsx)("div",{className:"col-span-2 text-center text-red-500",children:"Curtidas"}),(0,t.jsx)("div",{className:"col-span-2 text-center text-blue-500",children:"Coment\xe1rios"}),(0,t.jsx)("div",{className:"col-span-2 text-center text-green-500",children:"Salvamentos"}),(0,t.jsx)("div",{className:"col-span-2 text-center",children:(0,t.jsx)("div",{className:"bg-blue-700 text-white py-1.5 px-3 rounded-lg shadow-sm inline-block font-bold",children:"PONTOS"})})]}),(0,t.jsx)("div",{className:"divide-y divide-gray-100",children:p.map((a,s)=>{let r=Math.round(a.total_points/e*100);return(0,t.jsxs)("div",{className:"relative p-3 grid grid-cols-12 items-center text-sm ".concat(s<3?"bg-blue-50/50":"hover:bg-gray-50"," ").concat(0===s?"border-l-4 border-yellow-400":1===s?"border-l-4 border-gray-400":2===s?"border-l-4 border-amber-700":""),children:[(0,t.jsx)("div",{className:"absolute left-0 top-0 h-full z-0 ".concat(0===s?"bg-yellow-50":1===s?"bg-gray-50":2===s?"bg-amber-50":"bg-blue-50"),style:{width:"".concat(r,"%")}}),(0,t.jsx)("div",{className:"relative z-10 col-span-1 flex justify-center",children:(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center font-bold ".concat(["bg-gradient-to-r from-yellow-500 to-yellow-400 text-white","bg-gradient-to-r from-gray-400 to-gray-300 text-white","bg-gradient-to-r from-amber-700 to-amber-600 text-white","bg-blue-50 text-blue-800"][s<3?s:3]),children:s+1})}),(0,t.jsx)("div",{className:"relative z-10 col-span-3 flex items-center",children:(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"font-semibold text-gray-900",children:a.influencer_name||"Influencer ".concat(s+1)}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["@",a.username||"influencer".concat(s+1)]})]})}),(0,t.jsx)("div",{className:"relative z-10 col-span-2 text-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-red-500 font-semibold",children:[(0,t.jsx)(o.Mbv,{className:"mr-1"}),(0,t.jsx)("span",{children:a.total_likes})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.round(a.total_likes/(a.total_points||1)*100)||0,"% do total"]})]})}),(0,t.jsx)("div",{className:"relative z-10 col-span-2 text-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-blue-500 font-semibold",children:[(0,t.jsx)(o.j1Q,{className:"mr-1"}),(0,t.jsx)("span",{children:a.total_comments})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.round(a.total_comments/(a.total_points||1)*100)||0,"% do total"]})]})}),(0,t.jsx)("div",{className:"relative z-10 col-span-2 text-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsxs)("div",{className:"flex items-center text-green-500 font-semibold",children:[(0,t.jsx)(o.U$b,{className:"mr-1"}),(0,t.jsx)("span",{children:a.total_saves})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.round(a.total_saves/(a.total_points||1)*100)||0,"% do total"]})]})}),(0,t.jsx)("div",{className:"relative z-10 col-span-2 text-center",children:(0,t.jsxs)("div",{className:"".concat(s<3?"bg-gradient-to-r from-blue-600 to-blue-700":"bg-blue-600"," rounded-lg py-2 px-4 inline-block shadow-md border ").concat(0===s?"border-yellow-300":1===s?"border-gray-300":2===s?"border-amber-300":"border-blue-500"),children:[(0,t.jsx)("div",{className:"font-bold text-white text-xl",children:a.total_points}),(0,t.jsxs)("div",{className:"text-xs text-blue-100 flex items-center justify-center mt-1",children:[(0,t.jsx)(o.YYR,{className:"mr-1"}),a.engagement_rate.toFixed(1),"% taxa"]}),(0,t.jsx)("div",{className:"mt-1 text-xs",children:(0,t.jsxs)("span",{className:"bg-blue-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center",children:[(0,t.jsx)(o.x$1,{className:"mr-1",size:10}),"Participante"]})})]})})]},a.influencer_id)})})]}),(0,t.jsxs)("div",{className:"mt-4 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-5 text-white",children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold flex items-center mb-3",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-blue-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"An\xe1lise de Desempenho da Campanha"]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-3",children:[(0,t.jsx)("div",{className:"text-xs text-blue-200",children:"Engajamento Total"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[p.reduce((e,a)=>e+a.total_points,0)," pts"]})]}),(0,t.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-3",children:[(0,t.jsx)("div",{className:"text-xs text-blue-200",children:"Salvamentos"}),(0,t.jsx)("div",{className:"text-xl font-bold",children:p.reduce((e,a)=>e+a.total_saves,0)})]}),(0,t.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-3",children:[(0,t.jsx)("div",{className:"text-xs text-blue-200",children:"Taxa M\xe9dia"}),(0,t.jsxs)("div",{className:"text-xl font-bold",children:[(p.reduce((e,a)=>e+a.engagement_rate,0)/p.length).toFixed(1),"%"]})]})]}),(0,t.jsxs)("div",{className:"text-sm text-blue-100 mb-4",children:["Os salvamentos representam ",(0,t.jsxs)("span",{className:"font-bold text-white",children:[Math.round(p.reduce((e,a)=>e+a.total_saves,0)/p.reduce((e,a)=>e+a.total_likes+a.total_comments+a.total_saves,0)*100),"%"]})," do engajamento total desta campanha, demonstrando alto interesse do p\xfablico em guardar o conte\xfado para refer\xeancia futura."]}),(0,t.jsxs)("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[(0,t.jsxs)("h5",{className:"text-sm font-semibold mb-2 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1 text-blue-300",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"F\xf3rmula de C\xe1lculo de Pontos"]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-2 text-xs",children:[(0,t.jsxs)("div",{className:"bg-red-500/20 p-2 rounded flex flex-col items-center",children:[(0,t.jsx)(o.Mbv,{className:"text-red-400 mb-1"}),(0,t.jsx)("span",{className:"font-semibold",children:"Curtidas"}),(0,t.jsx)("span",{className:"text-blue-200",children:"1 ponto cada"})]}),(0,t.jsxs)("div",{className:"bg-blue-500/20 p-2 rounded flex flex-col items-center",children:[(0,t.jsx)(o.j1Q,{className:"text-blue-400 mb-1"}),(0,t.jsx)("span",{className:"font-semibold",children:"Coment\xe1rios"}),(0,t.jsx)("span",{className:"text-blue-200",children:"2 pontos cada"})]}),(0,t.jsxs)("div",{className:"bg-green-500/20 p-2 rounded flex flex-col items-center",children:[(0,t.jsx)(o.U$b,{className:"text-green-400 mb-1"}),(0,t.jsx)("span",{className:"font-semibold",children:"Salvamentos"}),(0,t.jsx)("span",{className:"text-blue-200",children:"3 pontos cada"})]}),(0,t.jsxs)("div",{className:"bg-yellow-500/20 p-2 rounded flex flex-col items-center",children:[(0,t.jsx)(o.SBv,{className:"text-yellow-400 mb-1"}),(0,t.jsx)("span",{className:"font-semibold",children:"Total"}),(0,t.jsx)("span",{className:"text-blue-200",children:"Soma ponderada"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:["\xdaltima atualiza\xe7\xe3o: ",new Date().toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.SBv,{className:"text-yellow-500 mr-1"}),(0,t.jsx)("span",{children:"Champions League Ranking"})]})]})]})}return(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-6 ".concat(i),children:j?(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("div",{className:"bg-yellow-100 text-yellow-800 p-3 text-sm rounded-lg mb-4 inline-flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2 text-yellow-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,t.jsx)("span",{children:"Exibindo dados de exemplo. As m\xe9tricas s\xe3o simuladas para fins de visualiza\xe7\xe3o."})]})}):(0,t.jsx)("div",{className:"text-center text-gray-500",children:"Nenhum dado de ranking dispon\xedvel para esta campanha."})})}function b(e){let{campaignId:a,userId:s,userRole:l,className:i=""}=e,[d,c]=(0,r.useState)(!0),[m,u]=(0,r.useState)(null),[h,f]=(0,r.useState)(null),[p,g]=(0,r.useState)([]);if((0,n.createClientComponentClient)(),(0,r.useEffect)(()=>{console.log("RankingHighlight - Redirecionando para fallback"),u("Fun\xe7\xf5es RPC n\xe3o dispon\xedveis"),c(!1)},[a,s,l]),d)return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 animate-pulse ".concat(i),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"})]}),(0,t.jsx)("div",{className:"h-24 bg-gray-200 rounded-lg mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0].map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full mr-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"})]},a))})]});if(m)return(0,t.jsx)(N,{campaignId:a,userId:s,userRole:l,className:i});if("influencer"===l&&h){let{rank:e,total_influencers:a,previous_rank:r,engagement_rate:n,total_points:l,total_saves:d}=h,c=r?r-e:0;return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden ".concat(i),children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold flex items-center",children:[(0,t.jsx)(o.SBv,{className:"mr-2 text-yellow-300"}),"Seu Ranking na Campanha"]}),(0,t.jsxs)("span",{className:"text-sm bg-white/20 px-2 py-1 rounded",children:[a," participantes"]})]})}),(0,t.jsx)("div",{className:"p-6 bg-blue-50 border-b border-blue-100",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl ".concat(1===e?"bg-yellow-500":2===e?"bg-gray-400":3===e?"bg-amber-700":"bg-blue-600"),children:[e,"\xba"]}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700",children:"Sua posi\xe7\xe3o atual"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:1===e?"\uD83C\uDFC6 Primeiro Lugar!":2===e?"\uD83E\uDD48 Segundo Lugar!":3===e?"\uD83E\uDD49 Terceiro Lugar!":"".concat(e,"\xba Lugar")}),(0,t.jsx)("div",{className:"flex items-center mt-1",children:c>0?(0,t.jsxs)("span",{className:"text-green-600 flex items-center text-sm",children:[(0,t.jsx)(o.uCC,{className:"mr-1"})," Subiu ",c," ",1===c?"posi\xe7\xe3o":"posi\xe7\xf5es"]}):c<0?(0,t.jsxs)("span",{className:"text-red-600 flex items-center text-sm",children:[(0,t.jsx)(o.$TP,{className:"mr-1"})," Desceu ",Math.abs(c)," ",1===Math.abs(c)?"posi\xe7\xe3o":"posi\xe7\xf5es"]}):(0,t.jsxs)("span",{className:"text-gray-600 flex items-center text-sm",children:[(0,t.jsx)(o.iu5,{className:"mr-1"})," Manteve a posi\xe7\xe3o"]})})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700",children:"Taxa de Engajamento"}),(0,t.jsxs)("div",{className:"text-2xl font-bold text-blue-900",children:[n.toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"flex items-center justify-end mt-1 space-x-3 text-sm",children:[(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center",children:[(0,t.jsx)(o.SBv,{className:"mr-1 text-blue-500"})," ",l," pts"]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de pontos acumulados"})})]})}),(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded flex items-center",children:[(0,t.jsx)(o.U$b,{className:"mr-1 text-green-500"})," ",d]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de salvamentos"})})]})})]})]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Seus Competidores Diretos"}),(0,t.jsx)("div",{className:"space-y-3",children:p.map(e=>{let a=e.influencer_id===s,r=e.rank<h.rank;return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg ".concat(a?"bg-blue-50 border border-blue-200":r?"bg-red-50":"bg-green-50"),children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ".concat(1===e.rank?"bg-yellow-500":2===e.rank?"bg-gray-400":3===e.rank?"bg-amber-700":"bg-gray-600"),children:e.rank}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsxs)("div",{className:"font-medium ".concat(a?"text-blue-700":"text-gray-800"),children:[e.influencer_name," ",a&&"(Voc\xea)"]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["@",e.username]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"font-medium text-gray-800",children:[e.engagement_rate.toFixed(1),"%"]}),(0,t.jsxs)("div",{className:"flex items-center justify-end space-x-2 text-xs",children:[(0,t.jsxs)("span",{className:"flex items-center text-red-600",children:[(0,t.jsx)(o.Mbv,{className:"mr-0.5"})," ",e.total_likes]}),(0,t.jsxs)("span",{className:"flex items-center text-blue-600",children:[(0,t.jsx)(o.j1Q,{className:"mr-0.5"})," ",e.total_comments]}),(0,t.jsxs)("span",{className:"flex items-center text-green-600",children:[(0,t.jsx)(o.U$b,{className:"mr-0.5"})," ",e.total_saves]})]})]})]},e.influencer_id)})}),(0,t.jsxs)("div",{className:"mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold text-yellow-800 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),"Dicas para Melhorar seu Ranking"]}),(0,t.jsxs)("ul",{className:"mt-2 text-xs text-yellow-700 space-y-1",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento."]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Responda aos coment\xe1rios para aumentar o engajamento geral."]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1 mt-0.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Crie conte\xfado que incentive o compartilhamento e salvamento."]})]})]})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500",children:["\xdaltima atualiza\xe7\xe3o: ",new Date().toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})]})]})}return"restaurant"===l&&p.length>0?(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden ".concat(i),children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-indigo-700 p-4 text-white",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h3",{className:"text-lg font-bold flex items-center",children:[(0,t.jsx)(o.SBv,{className:"mr-2 text-yellow-300"}),"Top Influenciadores da Campanha"]}),(0,t.jsxs)("span",{className:"text-sm bg-white/20 px-2 py-1 rounded",children:[p.length," influenciadores"]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("div",{className:"space-y-4",children:p.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg ".concat(0===a?"bg-yellow-50 border border-yellow-200":1===a?"bg-gray-50 border border-gray-200":2===a?"bg-amber-50 border border-amber-200":"bg-white border border-gray-100"),children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-xl ".concat(0===a?"bg-yellow-500":1===a?"bg-gray-400":2===a?"bg-amber-700":"bg-gray-600"),children:a+1}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("div",{className:"font-medium text-gray-800",children:e.influencer_name}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["@",e.username]}),(0,t.jsxs)("div",{className:"mt-1 flex items-center space-x-2 text-xs",children:[(0,t.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded flex items-center",children:[(0,t.jsx)(o.YYR,{className:"mr-0.5"})," ",e.engagement_rate.toFixed(1),"%"]}),(0,t.jsxs)("span",{className:"bg-green-100 text-green-800 px-1.5 py-0.5 rounded flex items-center",children:[(0,t.jsx)(o.U$b,{className:"mr-0.5"})," ",e.total_saves]})]})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"flex flex-col items-end",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-1",children:"M\xe9tricas de Engajamento"}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(o.Mbv,{className:"text-red-500 mb-1"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:e.total_likes})]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de curtidas"})})]})}),(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(o.j1Q,{className:"text-blue-500 mb-1"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:e.total_comments})]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de coment\xe1rios"})})]})}),(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)(o.U$b,{className:"text-green-500 mb-1"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:e.total_saves})]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsx)("p",{children:"Total de salvamentos"})})]})})]})]})})]},e.influencer_id))}),(0,t.jsxs)("div",{className:"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold text-blue-800 flex items-center mb-2",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"Insights da Campanha"]}),(0,t.jsxs)("div",{className:"text-xs text-blue-700 space-y-2",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Engajamento Total:"})," ",p.reduce((e,a)=>e+a.total_points,0)," pontos"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Salvamentos Totais:"})," ",p.reduce((e,a)=>e+a.total_saves,0)," salvamentos"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Taxa M\xe9dia de Engajamento:"})," ",(p.reduce((e,a)=>e+a.engagement_rate,0)/p.length).toFixed(1),"%"]}),(0,t.jsxs)("p",{className:"mt-2 text-blue-800 font-medium",children:["Os salvamentos est\xe3o contribuindo significativamente para o engajamento desta campanha, representando ",Math.round(p.reduce((e,a)=>e+a.total_saves,0)/p.reduce((e,a)=>e+a.total_likes+a.total_comments+a.total_saves,0)*100),"% do engajamento total."]})]})]})]}),(0,t.jsxs)("div",{className:"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500",children:["\xdaltima atualiza\xe7\xe3o: ",new Date().toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})]})]}):(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-6 ".concat(i),children:(0,t.jsx)("div",{className:"text-center text-gray-500",children:"Nenhum dado de ranking dispon\xedvel para esta campanha."})})}var y=s(88262),w=s(87712),_=s(33109),C=s(68500),k=s(55595),E=s(18186),A=s(51154),S=s(17313),M=s(66695),T=s(22964),P=s(55863);let R=r.forwardRef((e,a)=>{let{className:s,value:r,...n}=e;return(0,t.jsx)(P.bL,{ref:a,className:(0,f.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...n,children:(0,t.jsx)(P.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});function I(e){let{campaignId:a,className:s}=e;(0,n.createClientComponentClient)();let[l,i]=(0,r.useState)(!0),[o,d]=(0,r.useState)([]),[c,m]=(0,r.useState)(null),[x,h]=(0,r.useState)("ranking");(0,r.useEffect)(()=>{f()},[a]);let f=async()=>{i(!0);try{let e=await fetch("/api/v1/campaigns/leaderboard?campaignId=".concat(a),{method:"GET",headers:{"Content-Type":"application/json"}}),s=await e.json();if(!e.ok)throw Error(s.error||"Erro ao carregar leaderboard");d(s.leaderboard||[]),m(s.campaign||null)}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel carregar o leaderboard",variant:"destructive"})}finally{i(!1)}},p=o.length>0?Math.max(...o.map(e=>e.total_points||0)):100,g=(e,a)=>a?e<a?(0,t.jsx)(_.A,{className:"h-4 w-4 text-green-500"}):e>a?(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-500"}):(0,t.jsx)(w.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(w.A,{className:"h-4 w-4 text-gray-400"}),j=e=>1===e?(0,t.jsx)(k.A,{className:"h-5 w-5 text-yellow-500"}):2===e?(0,t.jsx)(k.A,{className:"h-5 w-5 text-gray-400"}):3===e?(0,t.jsx)(k.A,{className:"h-5 w-5 text-amber-700"}):null;return(0,t.jsxs)(M.Zp,{className:"overflow-hidden ".concat(s),children:[(0,t.jsx)(M.aR,{className:"bg-[#f5f5f5] pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(M.ZB,{className:"text-lg font-medium",children:[(0,t.jsx)(E.A,{className:"h-5 w-5 inline-block mr-2 text-amber-500"}),"Ranking da Campanha"]}),c&&(0,t.jsx)(M.BT,{children:c.name})]}),(0,t.jsx)(u.$,{variant:"ghost",size:"sm",onClick:f,disabled:l,className:"text-gray-500 hover:text-gray-700",children:l?(0,t.jsx)(A.A,{className:"h-4 w-4 animate-spin"}):"Atualizar"})]})}),(0,t.jsxs)(S.tU,{defaultValue:"ranking",value:x,onValueChange:e=>h(e),children:[(0,t.jsx)("div",{className:"px-4 pt-2",children:(0,t.jsxs)(S.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(S.Xi,{value:"ranking",children:"Ranking"}),(0,t.jsx)(S.Xi,{value:"metrics",children:"M\xe9tricas"})]})}),(0,t.jsx)(M.Wu,{className:"p-0",children:l?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(A.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):0===o.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nenhum influenciador participando desta campanha ainda."}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.av,{value:"ranking",className:"m-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Posi\xe7\xe3o"}),(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Influenciador"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pontos"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progresso"})]})}),(0,t.jsx)("tbody",{className:"divide-y divide-gray-200",children:o.map(e=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:e.current_rank}),g(e.current_rank,e.previous_rank),j(e.current_rank)]})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)(T.eu,{className:"h-8 w-8 mr-2",children:[(0,t.jsx)(T.BK,{src:e.profiles.profile_image_url||"",alt:e.profiles.full_name}),(0,t.jsx)(T.q5,{children:e.profiles.full_name.charAt(0)})]}),(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.profiles.full_name})]})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.total_points||0})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,t.jsx)(R,{value:e.total_points/p*100,className:"h-2"})})]},e.id))})]})})}),(0,t.jsx)(S.av,{value:"metrics",className:"m-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-200",children:[(0,t.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Influenciador"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Likes"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Coment\xe1rios"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Salvos"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Compartilhamentos"}),(0,t.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Visualiza\xe7\xf5es"})]})}),(0,t.jsx)("tbody",{className:"divide-y divide-gray-200",children:o.map(e=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)(T.eu,{className:"h-8 w-8 mr-2",children:[(0,t.jsx)(T.BK,{src:e.profiles.profile_image_url||"",alt:e.profiles.full_name}),(0,t.jsx)(T.q5,{children:e.profiles.full_name.charAt(0)})]}),(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.profiles.full_name})]})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right",children:(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.likes_count||0})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right",children:(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.comments_count||0})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right",children:(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.saves_count||0})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right",children:(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.shares_count||0})}),(0,t.jsx)("td",{className:"px-4 py-3 whitespace-nowrap text-right",children:(0,t.jsx)("div",{className:"text-sm text-gray-900",children:e.views_count||0})})]},e.id))})]})})})]})})]})]})}R.displayName=P.bL.displayName;var D=s(69037),L=s(32919),z=s(26126);function B(e){let{criadorId:a,className:s}=e,l=(0,n.createClientComponentClient)(),[i,o]=(0,r.useState)(!0),[d,c]=(0,r.useState)([]);(0,r.useEffect)(()=>{m()},[a]);let m=async()=>{o(!0);try{let{data:e,error:s}=await l.from("achievements").select("*").order("points",{ascending:!1});if(s)throw Error("Erro ao carregar conquistas: ".concat(s.message));let{data:t,error:r}=await l.from("user_achievements").select("*").eq("user_id",a);if(r)throw Error("Erro ao carregar conquistas do usu\xe1rio: ".concat(r.message));let n=e.map(e=>{let a=null==t?void 0:t.find(a=>a.achievement_id===e.id);return{...e,earned:!!a,earned_at:null==a?void 0:a.earned_at}});c(n)}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel carregar as conquistas",variant:"destructive"})}finally{o(!1)}},u=(e,a)=>a?(0,t.jsx)(D.A,{className:"h-8 w-8 text-amber-500"}):(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(D.A,{className:"h-8 w-8 text-gray-300"}),(0,t.jsx)(L.A,{className:"h-4 w-4 text-gray-400 absolute -bottom-1 -right-1"})]}),h=d.length,f=d.filter(e=>e.earned).length,p=d.filter(e=>e.earned).reduce((e,a)=>e+(a.points||0),0);return(0,t.jsxs)(M.Zp,{className:"overflow-hidden ".concat(s),children:[(0,t.jsx)(M.aR,{className:"bg-[#f5f5f5] pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(M.ZB,{className:"text-lg font-medium",children:[(0,t.jsx)(D.A,{className:"h-5 w-5 inline-block mr-2 text-amber-500"}),"Conquistas"]}),(0,t.jsxs)(M.BT,{children:[f," de ",h," conquistas desbloqueadas"]})]}),(0,t.jsxs)(z.E,{variant:"outline",className:"bg-white",children:[p," pontos"]})]})}),(0,t.jsx)(M.Wu,{className:"p-4",children:i?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(A.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):0===d.length?(0,t.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Nenhuma conquista dispon\xedvel."}):(0,t.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4",children:d.map(e=>(0,t.jsx)(x.Bc,{children:(0,t.jsxs)(x.m_,{children:[(0,t.jsx)(x.k$,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex flex-col items-center p-3 rounded-lg ".concat(e.earned?"bg-amber-50 border border-amber-200":"bg-gray-50 border border-gray-200"),children:[u(e.icon,e.earned),(0,t.jsx)("span",{className:"mt-2 text-sm font-medium text-center ".concat(e.earned?"text-gray-900":"text-gray-500"),children:e.name}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 mt-1",children:[e.points," pts"]})]})}),(0,t.jsx)(x.ZI,{children:(0,t.jsxs)("div",{className:"max-w-xs",children:[(0,t.jsx)("p",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm",children:e.description}),e.earned&&e.earned_at&&(0,t.jsxs)("p",{className:"text-xs mt-1 text-green-600",children:["Desbloqueado em ",new Date(e.earned_at).toLocaleDateString("pt-BR")]})]})})]})},e.id))})})]})}var O=s(14186);function U(e){let{campaignCriadorId:a,className:s}=e,l=(0,n.createClientComponentClient)(),[i,o]=(0,r.useState)(!0),[d,c]=(0,r.useState)([]),[m,x]=(0,r.useState)(0),[h,f]=(0,r.useState)(1),[p,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{j()},[a,h]);let j=async()=>{o(!0);try{let{data:e,error:s,count:t}=await l.from("points_history").select("*",{count:"exact"}).eq("campaign_criador_id",a).order("created_at",{ascending:!1}).range((h-1)*10,10*h-1);if(s)throw Error("Erro ao carregar hist\xf3rico de pontos: ".concat(s.message));c(e||[]),g(!!t&&t>10*h);let{data:r,error:n}=await l.from("campaign_criadors").select("total_points").eq("id",a).single();!n&&r&&x(r.total_points||0)}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel carregar o hist\xf3rico de pontos",variant:"destructive"})}finally{o(!1)}},v=e=>new Date(e).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return(0,t.jsxs)(M.Zp,{className:"overflow-hidden ".concat(s),children:[(0,t.jsx)(M.aR,{className:"bg-[#f5f5f5] pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(M.ZB,{className:"text-lg font-medium",children:[(0,t.jsx)(_.A,{className:"h-5 w-5 inline-block mr-2 text-green-500"}),"Hist\xf3rico de Pontos"]}),(0,t.jsx)(M.BT,{children:"Registro de pontos ganhos"})]}),(0,t.jsxs)(z.E,{variant:"outline",className:"bg-white",children:[m," pontos totais"]})]})}),(0,t.jsx)(M.Wu,{className:"p-0",children:i&&1===h?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(A.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):0===d.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nenhum registro de pontos encontrado."}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:d.map(e=>{var a,s;return(0,t.jsxs)("div",{className:"p-4 hover:bg-gray-50",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.reason}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,t.jsx)(O.A,{className:"h-3 w-3 mr-1"}),v(e.created_at)]}),(null===(a=e.details)||void 0===a?void 0:a.post_id)&&(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Post ID: ",e.details.post_id]})]}),(0,t.jsxs)(z.E,{variant:e.points>=0?"success":"destructive",className:"ml-2",children:[e.points>=0?"+":"",e.points," pts"]})]}),(null===(s=e.details)||void 0===s?void 0:s.metrics)&&(0,t.jsxs)("div",{className:"mt-2 grid grid-cols-5 gap-2 text-xs text-gray-500",children:[void 0!==e.details.metrics.likes_count&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Likes:"})," ",e.details.metrics.likes_count]}),void 0!==e.details.metrics.comments_count&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Coment\xe1rios:"})," ",e.details.metrics.comments_count]}),void 0!==e.details.metrics.shares_count&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Compartilhamentos:"})," ",e.details.metrics.shares_count]}),void 0!==e.details.metrics.saves_count&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Salvos:"})," ",e.details.metrics.saves_count]}),void 0!==e.details.metrics.views_count&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Visualiza\xe7\xf5es:"})," ",e.details.metrics.views_count]})]})]},e.id)})}),(0,t.jsxs)("div",{className:"flex justify-between p-4 border-t border-gray-200",children:[(0,t.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>{h>1&&f(h-1)},disabled:1===h||i,children:[i&&1!==h?(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2 animate-spin"}):null,"Anterior"]}),(0,t.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>{f(h+1)},disabled:!p||i,children:[i&&1!==h?(0,t.jsx)(A.A,{className:"h-4 w-4 mr-2 animate-spin"}):null,"Pr\xf3ximo"]})]})]})})]})}var F=s(43453),V=s(84616),H=s(85339),W=s(69796),G=s(63008),q=s(4573),$=s(81497),Y=s(40646),Z=s(69074),J=s(13052),K=s(2675),X=s(5196);let Q=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(K.bL,{ref:a,className:(0,f.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...r,children:(0,t.jsx)(K.C1,{className:(0,f.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(X.A,{className:"h-4 w-4"})})})});Q.displayName=K.bL.displayName;var ee=s(71007),ea=s(12486),es=s(88539),et=s(54165),er=s(87489);let en=r.forwardRef((e,a)=>{let{className:s,orientation:r="horizontal",decorative:n=!0,...l}=e;return(0,t.jsx)(er.b,{ref:a,decorative:n,orientation:r,className:(0,f.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...l})});function el(e){var a;let{open:s,onOpenChange:l,task:i,onComplete:o}=e;(0,n.createClientComponentClient)();let[d,c]=(0,r.useState)([]),[m,x]=(0,r.useState)(""),[h,f]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{s&&(null==i?void 0:i.id)&&j()},[s,null==i?void 0:i.id]);let j=async()=>{f(!0);try{let e=await fetch("/api/v1/tasks/".concat(i.id,"/comments"),{method:"GET",headers:{"Content-Type":"application/json"}}),a=await e.json();if(!e.ok)throw Error(a.error||"Erro ao carregar coment\xe1rios");c(a.comments||[])}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel carregar os coment\xe1rios",variant:"destructive"})}finally{f(!1)}},v=async e=>{if(e.preventDefault(),m.trim()){g(!0);try{let e=await fetch("/api/v1/tasks/".concat(i.id,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({comment:m})}),a=await e.json();if(!e.ok)throw Error(a.error||"Erro ao adicionar coment\xe1rio");c([...d,a.comment]),x("")}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel adicionar o coment\xe1rio",variant:"destructive"})}finally{g(!1)}}},N=(null==i?void 0:i.status)===W.e1.OVERDUE||(null==i?void 0:i.status)===W.e1.PENDING&&(null==i?void 0:i.due_date)&&new Date(i.due_date)<new Date,b=e=>(0,G.GP)(new Date(e),"dd/MM/yyyy '\xe0s' HH:mm",{locale:q.F});return(0,t.jsx)(et.lG,{open:s,onOpenChange:l,children:(0,t.jsxs)(et.Cf,{className:"sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{className:N?"text-red-700":"",children:null==i?void 0:i.title}),(0,t.jsx)(et.rr,{children:"Detalhes da tarefa e coment\xe1rios"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(null==i?void 0:i.description)&&(0,t.jsx)("p",{className:"text-gray-700",children:i.description}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(z.E,{variant:"outline",className:"bg-gray-100",children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)($.A,{className:"h-3 w-3"}),(e=>{if(!e)return"Desconhecido";switch(e){case W.wP.CONTENT_CREATION:return"Cria\xe7\xe3o de Conte\xfado";case W.wP.CONTENT_APPROVAL:return"Aprova\xe7\xe3o de Conte\xfado";case W.wP.CONTENT_PUBLICATION:return"Publica\xe7\xe3o de Conte\xfado";case W.wP.SCHEDULE_VISIT:return"Agendar Visita";case W.wP.ADMINISTRATIVE:return"Administrativo";case W.wP.PAYMENT:return"Pagamento";default:return"Outro"}})(null==i?void 0:i.task_type)]})}),(0,t.jsxs)(z.E,{variant:"outline",className:(e=>{if(!e)return"bg-blue-100 text-blue-800";switch(e){case W.W6.LOW:return"bg-gray-100 text-gray-800";case W.W6.MEDIUM:return"bg-blue-100 text-blue-800";case W.W6.HIGH:return"bg-orange-100 text-orange-800";case W.W6.URGENT:return"bg-red-100 text-red-800";default:return"bg-blue-100 text-blue-800"}})(null==i?void 0:i.priority),children:["Prioridade: ",(e=>{if(!e)return"M\xe9dia";switch(e){case W.W6.LOW:return"Baixa";case W.W6.MEDIUM:return"M\xe9dia";case W.W6.HIGH:return"Alta";case W.W6.URGENT:return"Urgente";default:return"M\xe9dia"}})(null==i?void 0:i.priority)]}),(null==i?void 0:i.due_date)&&(0,t.jsx)(z.E,{variant:"outline",className:N?"bg-red-100 text-red-800 border-red-200":"bg-blue-50 text-blue-700 border-blue-100",children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(Z.A,{className:"h-3 w-3"}),"Vencimento: ",(a=i.due_date)?(0,G.GP)(new Date(a),"dd 'de' MMMM 'de' yyyy",{locale:q.F}):null,N&&" (Atrasada)"]})}),(0,t.jsx)(z.E,{variant:"outline",className:(null==i?void 0:i.status)===W.e1.COMPLETED?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(Y.A,{className:"h-3 w-3"}),(null==i?void 0:i.status)===W.e1.COMPLETED?"Conclu\xedda":"Pendente"]})})]}),(null==i?void 0:i.status)!==W.e1.COMPLETED&&(0,t.jsxs)(u.$,{variant:"outline",className:"gap-2",onClick:o,children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),"Marcar como Conclu\xedda"]})]}),(0,t.jsx)(en,{className:"my-4"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-sm font-medium mb-3 flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"h-4 w-4"}),"Coment\xe1rios"]}),(0,t.jsx)("div",{className:"space-y-4 max-h-[300px] overflow-y-auto pr-2",children:h?(0,t.jsx)("div",{className:"flex justify-center py-4",children:(0,t.jsx)(A.A,{className:"h-6 w-6 animate-spin text-gray-400"})}):0===d.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Nenhum coment\xe1rio ainda. Seja o primeiro a comentar!"}):d.map(e=>{var a,s,r,n,l;return(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(T.eu,{className:"h-8 w-8",children:[(0,t.jsx)(T.BK,{src:(null===(a=e.user)||void 0===a?void 0:a.profile_image_url)||"",alt:(null===(s=e.user)||void 0===s?void 0:s.full_name)||"Usu\xe1rio"}),(0,t.jsx)(T.q5,{children:(null===(n=e.user)||void 0===n?void 0:null===(r=n.full_name)||void 0===r?void 0:r.charAt(0))||"U"})]}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"bg-gray-100 rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:(null===(l=e.user)||void 0===l?void 0:l.full_name)||"Usu\xe1rio"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:b(e.created_at)})]}),(0,t.jsx)("p",{className:"text-gray-700 text-sm whitespace-pre-wrap",children:e.comment})]})})]},e.id)})})]})]}),(0,t.jsx)("form",{onSubmit:v,className:"mt-4",children:(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(T.eu,{className:"h-8 w-8",children:[(0,t.jsx)(T.BK,{src:"",alt:"Voc\xea"}),(0,t.jsx)(T.q5,{children:(0,t.jsx)(ee.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(es.T,{placeholder:"Adicione um coment\xe1rio...",value:m,onChange:e=>x(e.target.value),className:"resize-none",rows:2}),(0,t.jsx)("div",{className:"flex justify-end mt-2",children:(0,t.jsxs)(u.$,{type:"submit",size:"sm",disabled:!m.trim()||p,className:"gap-1",children:[p?(0,t.jsx)(A.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(ea.A,{className:"h-4 w-4"}),"Enviar"]})})]})]})})]})})}function ei(e){var a;let{task:s,onComplete:n}=e,[l,i]=(0,r.useState)(!1),o=s.status===W.e1.OVERDUE||s.status===W.e1.PENDING&&s.due_date&&new Date(s.due_date)<new Date,d=(e=>{switch(e){case W.wP.CONTENT_CREATION:return{icon:(0,t.jsx)($.A,{className:"h-4 w-4"}),color:"bg-blue-100 text-blue-800"};case W.wP.CONTENT_APPROVAL:return{icon:(0,t.jsx)(Y.A,{className:"h-4 w-4"}),color:"bg-green-100 text-green-800"};case W.wP.CONTENT_PUBLICATION:return{icon:(0,t.jsx)($.A,{className:"h-4 w-4"}),color:"bg-purple-100 text-purple-800"};case W.wP.SCHEDULE_VISIT:return{icon:(0,t.jsx)(Z.A,{className:"h-4 w-4"}),color:"bg-amber-100 text-amber-800"};case W.wP.ADMINISTRATIVE:return{icon:(0,t.jsx)($.A,{className:"h-4 w-4"}),color:"bg-gray-100 text-gray-800"};case W.wP.PAYMENT:return{icon:(0,t.jsx)($.A,{className:"h-4 w-4"}),color:"bg-emerald-100 text-emerald-800"};default:return{icon:(0,t.jsx)($.A,{className:"h-4 w-4"}),color:"bg-gray-100 text-gray-800"}}})(s.task_type);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"p-3 rounded-lg border ".concat(o?"border-red-200 bg-red-50":"border-gray-200 bg-white"," hover:bg-gray-50 cursor-pointer transition-colors"),onClick:()=>i(!0),children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"pt-0.5",children:(0,t.jsx)(Q,{checked:s.status===W.e1.COMPLETED,onCheckedChange:()=>n(s.id),onClick:e=>e.stopPropagation(),disabled:s.status===W.e1.COMPLETED})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium ".concat(s.status===W.e1.COMPLETED?"line-through text-gray-500":o?"text-red-700":"text-gray-900"),children:s.title}),(0,t.jsx)(J.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0"})]}),s.description&&(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1 line-clamp-1",children:s.description}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mt-2",children:[(0,t.jsx)(z.E,{variant:"outline",className:d.color,children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[d.icon,(e=>{switch(e){case W.wP.CONTENT_CREATION:return"Cria\xe7\xe3o de Conte\xfado";case W.wP.CONTENT_APPROVAL:return"Aprova\xe7\xe3o de Conte\xfado";case W.wP.CONTENT_PUBLICATION:return"Publica\xe7\xe3o de Conte\xfado";case W.wP.SCHEDULE_VISIT:return"Agendar Visita";case W.wP.ADMINISTRATIVE:return"Administrativo";case W.wP.PAYMENT:return"Pagamento";default:return"Outro"}})(s.task_type)]})}),s.due_date&&(0,t.jsx)(z.E,{variant:"outline",className:o?"bg-red-100 text-red-800 border-red-200":"bg-blue-50 text-blue-700 border-blue-100",children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(O.A,{className:"h-3 w-3"}),(e=>{if(!e)return null;let a=new Date(e),s=new Date;s.setHours(0,0,0,0);let t=new Date(s);return(t.setDate(t.getDate()+1),a.getTime()===s.getTime())?"Hoje":a.getTime()===t.getTime()?"Amanh\xe3":(0,G.GP)(a,"dd/MM/yyyy",{locale:q.F})})(s.due_date),o&&" (Atrasada)"]})}),(null===(a=s.comments)||void 0===a?void 0:a.length)>0&&(0,t.jsx)(z.E,{variant:"outline",className:"bg-gray-100 text-gray-800",children:(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)($.A,{className:"h-3 w-3"}),s.comments.length]})})]})]})]})}),(0,t.jsx)(el,{open:l,onOpenChange:i,task:s,onComplete:e=>{e.stopPropagation(),n(s.id)}})]})}en.displayName=er.b.displayName;var eo=s(42355),ed=s(53490);r.forwardRef((e,a)=>{let{className:s,variant:n="filled",label:l,helperText:i,error:o,startIcon:d,endIcon:c,counter:m,disabled:x,required:u,id:h,...p}=e,g=r.useId(),j=h||g,[v,N]=r.useState(!!p.value||!!p.defaultValue),[b,y]=r.useState(!1);r.useEffect(()=>{N(!!p.value)},[p.value]);let w=r.useCallback(e=>{var a;y(!0),null===(a=p.onFocus)||void 0===a||a.call(p,e)},[p.onFocus]),_=r.useCallback(e=>{var a;y(!1),null===(a=p.onBlur)||void 0===a||a.call(p,e)},[p.onBlur]),C=r.useCallback(e=>{var a;N(!!e.target.value),null===(a=p.onChange)||void 0===a||a.call(p,e)},[p.onChange]),k={filled:(0,f.cn)("bg-md-surface-variant/20 border-b-2 rounded-t-md px-3 pt-6 pb-2",o?"border-md-error focus-within:border-md-error":"border-md-on-surface-variant/50 focus-within:border-md-primary",x&&"bg-md-surface-variant/10 border-md-on-surface-variant/30"),outlined:(0,f.cn)("border-2 rounded-md px-3 pt-6 pb-2",o?"border-md-error focus-within:border-md-error":"border-md-on-surface-variant/50 focus-within:border-md-primary",x&&"border-md-on-surface-variant/30")},E=(0,f.cn)("absolute pointer-events-none transition-all duration-200 left-3",b||v?"text-xs top-2":"text-md-on-surface-variant top-1/2 -translate-y-1/2",b&&!o&&"text-md-primary",o&&"text-md-error",x&&"text-md-on-surface-variant/50");return(0,t.jsxs)("div",{className:(0,f.cn)("w-full space-y-1.5",s),children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:(0,f.cn)("flex items-center w-full transition-colors relative",k[n]),children:[d&&(0,t.jsx)("div",{className:(0,f.cn)("mr-2 text-md-on-surface-variant",o&&"text-md-error",x&&"opacity-50"),children:d}),(0,t.jsx)("input",{id:j,className:(0,f.cn)("flex h-full w-full bg-transparent","text-md-on-surface placeholder:text-transparent","focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed","peer"),ref:a,"aria-invalid":o,disabled:x,required:u,onFocus:w,onBlur:_,onChange:C,...p}),l&&(0,t.jsxs)("label",{htmlFor:j,className:E,children:[l,u&&(0,t.jsx)("span",{className:"text-md-error ml-0.5",children:"*"})]}),c&&(0,t.jsx)("div",{className:(0,f.cn)("ml-2 text-md-on-surface-variant",o&&"text-md-error",x&&"opacity-50"),children:c})]})}),(i||m)&&(0,t.jsxs)("div",{className:"flex justify-between items-center px-3",children:[i&&(0,t.jsx)("p",{className:(0,f.cn)("text-xs",o?"text-md-error":"text-md-on-surface-variant",x&&"opacity-50"),children:i}),m&&(0,t.jsxs)("p",{className:(0,f.cn)("text-xs ml-auto",m.count>m.max?"text-md-error":"text-md-on-surface-variant",x&&"opacity-50"),children:[m.count,"/",m.max]})]})]})}).displayName="TextField",r.forwardRef((e,a)=>{let{className:s,variant:n="filled",label:l,helperText:i,error:o,counter:d,disabled:c,required:m,id:x,rows:u=3,...h}=e,p=r.useId(),g=x||p,[j,v]=r.useState(!!h.value||!!h.defaultValue),[N,b]=r.useState(!1);r.useEffect(()=>{v(!!h.value)},[h.value]);let y=r.useCallback(e=>{var a;b(!0),null===(a=h.onFocus)||void 0===a||a.call(h,e)},[h.onFocus]),w=r.useCallback(e=>{var a;b(!1),null===(a=h.onBlur)||void 0===a||a.call(h,e)},[h.onBlur]),_=r.useCallback(e=>{var a;v(!!e.target.value),null===(a=h.onChange)||void 0===a||a.call(h,e)},[h.onChange]),C={filled:(0,f.cn)("bg-md-surface-variant/20 border-b-2 rounded-t-md px-3 pt-6 pb-2",o?"border-md-error focus-within:border-md-error":"border-md-on-surface-variant/50 focus-within:border-md-primary",c&&"bg-md-surface-variant/10 border-md-on-surface-variant/30"),outlined:(0,f.cn)("border-2 rounded-md px-3 pt-6 pb-2",o?"border-md-error focus-within:border-md-error":"border-md-on-surface-variant/50 focus-within:border-md-primary",c&&"border-md-on-surface-variant/30")},k=(0,f.cn)("absolute pointer-events-none transition-all duration-200 left-3",N||j?"text-xs top-2":"text-md-on-surface-variant top-4",N&&!o&&"text-md-primary",o&&"text-md-error",c&&"text-md-on-surface-variant/50");return(0,t.jsxs)("div",{className:(0,f.cn)("w-full space-y-1.5",s),children:[(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:(0,f.cn)("w-full transition-colors relative",C[n]),children:[(0,t.jsx)("textarea",{id:g,className:(0,f.cn)("flex w-full bg-transparent resize-none","text-md-on-surface placeholder:text-transparent","focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed","peer"),ref:a,"aria-invalid":o,disabled:c,required:m,rows:u,onFocus:y,onBlur:w,onChange:_,...h}),l&&(0,t.jsxs)("label",{htmlFor:g,className:k,children:[l,m&&(0,t.jsx)("span",{className:"text-md-error ml-0.5",children:"*"})]})]})}),(i||d)&&(0,t.jsxs)("div",{className:"flex justify-between items-center px-3",children:[i&&(0,t.jsx)("p",{className:(0,f.cn)("text-xs",o?"text-md-error":"text-md-on-surface-variant",c&&"opacity-50"),children:i}),d&&(0,t.jsxs)("p",{className:(0,f.cn)("text-xs ml-auto",d.count>d.max?"text-md-error":"text-md-on-surface-variant",c&&"opacity-50"),children:[d.count,"/",d.max]})]})]})}).displayName="TextArea",r.forwardRef((e,a)=>{let{className:s,children:r,...n}=e;return(0,t.jsx)("button",{className:(0,f.cn)("inline-flex items-center justify-center rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background","h-10 w-10",s),ref:a,...n,children:r})}).displayName="IconButton";let ec=(0,s(74466).F)("inline-flex items-center justify-center rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",extended:"bg-primary text-primary-foreground hover:bg-primary/90 px-6"},size:{default:"h-14 w-14",sm:"h-10 w-10",lg:"h-24 w-24"}},defaultVariants:{variant:"default",size:"default"}});function em(e){let{className:a,classNames:s,showOutsideDays:r=!0,...n}=e;return(0,t.jsx)(ed.h,{showOutsideDays:r,className:(0,f.cn)("p-3",a),locale:q.F,classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,f.cn)((0,u.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,f.cn)((0,u.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...s},components:{IconLeft:()=>(0,t.jsx)(eo.A,{className:"h-4 w-4"}),IconRight:()=>(0,t.jsx)(J.A,{className:"h-4 w-4"})},...n})}r.forwardRef((e,a)=>{let{className:s,variant:r,size:n,children:l,...i}=e;return(0,t.jsx)("button",{className:(0,f.cn)(ec({variant:r,size:n}),s),ref:a,...i,children:l})}).displayName="FAB",em.displayName="Calendar";var ex=s(99708),eu=s(62177),eh=s(85057);let ef=eu.Op,ep=r.createContext({}),eg=e=>{let{...a}=e;return(0,t.jsx)(ep.Provider,{value:{name:a.name},children:(0,t.jsx)(eu.xI,{...a})})},ej=()=>{let e=r.useContext(ep),a=r.useContext(ev),{getFieldState:s,formState:t}=(0,eu.xW)(),n=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=a;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},ev=r.createContext({}),eN=r.forwardRef((e,a)=>{let{className:s,...n}=e,l=r.useId();return(0,t.jsx)(ev.Provider,{value:{id:l},children:(0,t.jsx)("div",{ref:a,className:(0,f.cn)("space-y-2",s),...n})})});eN.displayName="FormItem";let eb=r.forwardRef((e,a)=>{let{className:s,...r}=e,{error:n,formItemId:l}=ej();return(0,t.jsx)(eh.J,{ref:a,className:(0,f.cn)(n&&"text-destructive",s),htmlFor:l,...r})});eb.displayName="FormLabel";let ey=r.forwardRef((e,a)=>{let{...s}=e,{error:r,formItemId:n,formDescriptionId:l,formMessageId:i}=ej();return(0,t.jsx)(ex.DX,{ref:a,id:n,"aria-describedby":r?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!r,...s})});ey.displayName="FormControl",r.forwardRef((e,a)=>{let{className:s,...r}=e,{formDescriptionId:n}=ej();return(0,t.jsx)("p",{ref:a,id:n,className:(0,f.cn)("text-sm text-muted-foreground",s),...r})}).displayName="FormDescription";let ew=r.forwardRef((e,a)=>{let{className:s,children:r,...n}=e,{error:l,formMessageId:i}=ej(),o=l?String(null==l?void 0:l.message):r;return o?(0,t.jsx)("p",{ref:a,id:i,className:(0,f.cn)("text-sm font-medium text-destructive",s),...n,children:o}):null});ew.displayName="FormMessage";var e_=s(62523),eC=s(42980),ek=s(66474),eE=s(47863);let eA=eC.bL;eC.YJ;let eS=eC.WT,eM=r.forwardRef((e,a)=>{let{className:s,children:r,...n}=e;return(0,t.jsxs)(eC.l9,{ref:a,className:(0,f.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...n,children:[r,(0,t.jsx)(eC.In,{asChild:!0,children:(0,t.jsx)(ek.A,{className:"h-4 w-4 opacity-50"})})]})});eM.displayName=eC.l9.displayName;let eT=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eC.PP,{ref:a,className:(0,f.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,t.jsx)(eE.A,{className:"h-4 w-4"})})});eT.displayName=eC.PP.displayName;let eP=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eC.wn,{ref:a,className:(0,f.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,t.jsx)(ek.A,{className:"h-4 w-4"})})});eP.displayName=eC.wn.displayName;let eR=r.forwardRef((e,a)=>{let{className:s,children:r,position:n="popper",...l}=e;return(0,t.jsx)(eC.ZL,{children:(0,t.jsxs)(eC.UC,{ref:a,className:(0,f.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...l,children:[(0,t.jsx)(eT,{}),(0,t.jsx)(eC.LM,{className:(0,f.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(eP,{})]})})});eR.displayName=eC.UC.displayName,r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eC.JU,{ref:a,className:(0,f.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=eC.JU.displayName;let eI=r.forwardRef((e,a)=>{let{className:s,children:r,...n}=e;return(0,t.jsxs)(eC.q7,{ref:a,className:(0,f.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(eC.VF,{children:(0,t.jsx)(X.A,{className:"h-4 w-4"})})}),(0,t.jsx)(eC.p4,{children:r})]})});eI.displayName=eC.q7.displayName,r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eC.wv,{ref:a,className:(0,f.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=eC.wv.displayName;var eD=s(90221),eL=s(55594);let ez=eL.Ik({title:eL.Yj().min(3,{message:"O t\xedtulo deve ter pelo menos 3 caracteres"}),description:eL.Yj().optional(),task_type:eL.fc(W.wP),priority:eL.fc(W.W6),due_date:eL.p6().optional()});function eB(e){let{open:a,onOpenChange:s,onSubmit:r,campaignId:n}=e,l=(0,eu.mN)({resolver:(0,eD.u)(ez),defaultValues:{title:"",description:"",task_type:W.wP.CONTENT_CREATION,priority:W.W6.MEDIUM}});return(0,t.jsx)(et.lG,{open:a,onOpenChange:s,children:(0,t.jsxs)(et.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:"Nova Tarefa"}),(0,t.jsx)(et.rr,{children:"Crie uma nova tarefa para acompanhar suas atividades."})]}),(0,t.jsx)(ef,{...l,children:(0,t.jsxs)("form",{onSubmit:l.handleSubmit(e=>{r({...e,campaign_id:n})}),className:"space-y-4",children:[(0,t.jsx)(eg,{control:l.control,name:"title",render:e=>{let{field:a}=e;return(0,t.jsxs)(eN,{children:[(0,t.jsx)(eb,{children:"T\xedtulo"}),(0,t.jsx)(ey,{children:(0,t.jsx)(e_.p,{placeholder:"Digite o t\xedtulo da tarefa",...a})}),(0,t.jsx)(ew,{})]})}}),(0,t.jsx)(eg,{control:l.control,name:"description",render:e=>{let{field:a}=e;return(0,t.jsxs)(eN,{children:[(0,t.jsx)(eb,{children:"Descri\xe7\xe3o"}),(0,t.jsx)(ey,{children:(0,t.jsx)(es.T,{placeholder:"Descreva os detalhes da tarefa",className:"resize-none",...a})}),(0,t.jsx)(ew,{})]})}}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsx)(eg,{control:l.control,name:"task_type",render:e=>{let{field:a}=e;return(0,t.jsxs)(eN,{children:[(0,t.jsx)(eb,{children:"Tipo"}),(0,t.jsxs)(eA,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,t.jsx)(ey,{children:(0,t.jsx)(eM,{children:(0,t.jsx)(eS,{placeholder:"Selecione o tipo"})})}),(0,t.jsxs)(eR,{children:[(0,t.jsx)(eI,{value:W.wP.CONTENT_CREATION,children:"Cria\xe7\xe3o de Conte\xfado"}),(0,t.jsx)(eI,{value:W.wP.CONTENT_APPROVAL,children:"Aprova\xe7\xe3o de Conte\xfado"}),(0,t.jsx)(eI,{value:W.wP.CONTENT_PUBLICATION,children:"Publica\xe7\xe3o de Conte\xfado"}),(0,t.jsx)(eI,{value:W.wP.SCHEDULE_VISIT,children:"Agendar Visita"}),(0,t.jsx)(eI,{value:W.wP.ADMINISTRATIVE,children:"Administrativo"}),(0,t.jsx)(eI,{value:W.wP.PAYMENT,children:"Pagamento"}),(0,t.jsx)(eI,{value:W.wP.OTHER,children:"Outro"})]})]}),(0,t.jsx)(ew,{})]})}}),(0,t.jsx)(eg,{control:l.control,name:"priority",render:e=>{let{field:a}=e;return(0,t.jsxs)(eN,{children:[(0,t.jsx)(eb,{children:"Prioridade"}),(0,t.jsxs)(eA,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,t.jsx)(ey,{children:(0,t.jsx)(eM,{children:(0,t.jsx)(eS,{placeholder:"Selecione a prioridade"})})}),(0,t.jsxs)(eR,{children:[(0,t.jsx)(eI,{value:W.W6.LOW,children:"Baixa"}),(0,t.jsx)(eI,{value:W.W6.MEDIUM,children:"M\xe9dia"}),(0,t.jsx)(eI,{value:W.W6.HIGH,children:"Alta"}),(0,t.jsx)(eI,{value:W.W6.URGENT,children:"Urgente"})]})]}),(0,t.jsx)(ew,{})]})}})]}),(0,t.jsx)(eg,{control:l.control,name:"due_date",render:e=>{let{field:a}=e;return(0,t.jsxs)(eN,{className:"flex flex-col",children:[(0,t.jsx)(eb,{children:"Data de Vencimento"}),(0,t.jsxs)(p,{children:[(0,t.jsx)(g,{asChild:!0,children:(0,t.jsx)(ey,{children:(0,t.jsxs)(u.$,{variant:"outline",className:"w-full pl-3 text-left font-normal ".concat(a.value?"":"text-muted-foreground"),children:[a.value?(0,G.GP)(a.value,"PPP",{locale:q.F}):(0,t.jsx)("span",{children:"Selecione uma data"}),(0,t.jsx)(Z.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,t.jsx)(j,{className:"w-auto p-0",align:"start",children:(0,t.jsx)(em,{mode:"single",selected:a.value,onSelect:a.onChange,disabled:e=>e<new Date,initialFocus:!0})})]}),(0,t.jsx)(ew,{})]})}}),(0,t.jsxs)(et.Es,{children:[(0,t.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>s(!1),children:"Cancelar"}),(0,t.jsx)(u.$,{type:"submit",children:"Criar Tarefa"})]})]})})]})})}function eO(e){let{userId:a,campaignId:s,className:l}=e;(0,n.createClientComponentClient)();let[i,o]=(0,r.useState)(!0),[d,c]=(0,r.useState)([]),[m,x]=(0,r.useState)("pending"),[h,f]=(0,r.useState)(!1);(0,r.useEffect)(()=>{p()},[a,s,m]);let p=async()=>{o(!0);try{let e="/api/v1/tasks?";s&&(e+="campaignId=".concat(s,"&")),"pending"===m?e+="status=".concat(W.e1.PENDING,",").concat(W.e1.IN_PROGRESS,",").concat(W.e1.OVERDUE,"&"):"completed"===m&&(e+="status=".concat(W.e1.COMPLETED,"&"));let a=await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}}),t=await a.json();if(!a.ok)throw Error(t.error||"Erro ao carregar tarefas");c(t.tasks||[])}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel carregar as tarefas",variant:"destructive"})}finally{o(!1)}},g=async e=>{try{let a=await fetch("/api/v1/tasks/".concat(e,"/complete"),{method:"POST",headers:{"Content-Type":"application/json"}}),s=await a.json();if(!a.ok)throw Error(s.error||"Erro ao concluir tarefa");(0,y.o)({title:"Sucesso",description:"Tarefa conclu\xedda com sucesso",variant:"default"}),p()}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel concluir a tarefa",variant:"destructive"})}},j=async e=>{try{let a=await fetch("/api/v1/tasks",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,campaign_id:s})}),t=await a.json();if(!a.ok)throw Error(t.error||"Erro ao criar tarefa");(0,y.o)({title:"Sucesso",description:"Tarefa criada com sucesso",variant:"default"}),p(),f(!1)}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel criar a tarefa",variant:"destructive"})}},v=(e=>{let a={[W.W6.URGENT]:[],[W.W6.HIGH]:[],[W.W6.MEDIUM]:[],[W.W6.LOW]:[]};return e.forEach(e=>{a[e.priority].push(e)}),a})(d),N=d.some(e=>e.status===W.e1.OVERDUE||e.status===W.e1.PENDING&&e.due_date&&new Date(e.due_date)<new Date);return(0,t.jsxs)(M.Zp,{className:"overflow-hidden ".concat(l),children:[(0,t.jsx)(M.aR,{className:"bg-[#f5f5f5] pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(M.ZB,{className:"text-lg font-medium",children:[(0,t.jsx)(F.A,{className:"h-5 w-5 inline-block mr-2 text-green-500"}),"Tarefas"]}),(0,t.jsx)(M.BT,{children:"Gerencie suas tarefas e prazos"})]}),(0,t.jsxs)(u.$,{onClick:()=>f(!0),size:"sm",className:"gap-1",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),"Nova Tarefa"]})]})}),(0,t.jsxs)(S.tU,{defaultValue:"pending",value:m,onValueChange:e=>x(e),children:[(0,t.jsx)("div",{className:"px-4 pt-2",children:(0,t.jsxs)(S.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsxs)(S.Xi,{value:"pending",className:"relative",children:["Pendentes",N&&(0,t.jsxs)("span",{className:"absolute -top-1 -right-1 flex h-3 w-3",children:[(0,t.jsx)("span",{className:"animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"}),(0,t.jsx)("span",{className:"relative inline-flex rounded-full h-3 w-3 bg-red-500"})]})]}),(0,t.jsx)(S.Xi,{value:"completed",children:"Conclu\xeddas"}),(0,t.jsx)(S.Xi,{value:"all",children:"Todas"})]})}),(0,t.jsx)(M.Wu,{className:"p-0",children:i?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(A.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):0===d.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nenhuma tarefa encontrada."}):(0,t.jsxs)("div",{className:"divide-y divide-gray-100",children:[v[W.W6.URGENT].length>0&&(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("h3",{className:"text-sm font-medium text-red-600 mb-2 flex items-center",children:[(0,t.jsx)(H.A,{className:"h-4 w-4 mr-1"}),"Urgente"]}),(0,t.jsx)("div",{className:"space-y-2",children:v[W.W6.URGENT].map(e=>(0,t.jsx)(ei,{task:e,onComplete:g},e.id))})]}),v[W.W6.HIGH].length>0&&(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-orange-600 mb-2",children:"Alta Prioridade"}),(0,t.jsx)("div",{className:"space-y-2",children:v[W.W6.HIGH].map(e=>(0,t.jsx)(ei,{task:e,onComplete:g},e.id))})]}),v[W.W6.MEDIUM].length>0&&(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-600 mb-2",children:"M\xe9dia Prioridade"}),(0,t.jsx)("div",{className:"space-y-2",children:v[W.W6.MEDIUM].map(e=>(0,t.jsx)(ei,{task:e,onComplete:g},e.id))})]}),v[W.W6.LOW].length>0&&(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:"Baixa Prioridade"}),(0,t.jsx)("div",{className:"space-y-2",children:v[W.W6.LOW].map(e=>(0,t.jsx)(ei,{task:e,onComplete:g},e.id))})]})]})})]}),(0,t.jsx)(eB,{open:h,onOpenChange:f,onSubmit:j,campaignId:s})]})}var eU=s(4516),eF=s(98794),eV=s(3898),eH=s(82672),eW=s(54416),eG=s(1243),eq=s(66937);let e$=eq.bL;eq.l9;let eY=eq.ZL,eZ=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eq.hJ,{className:(0,f.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r,ref:a})});eZ.displayName=eq.hJ.displayName;let eJ=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsxs)(eY,{children:[(0,t.jsx)(eZ,{}),(0,t.jsx)(eq.UC,{ref:a,className:(0,f.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...r})]})});eJ.displayName=eq.UC.displayName;let eK=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,f.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...s})};eK.displayName="AlertDialogHeader";let eX=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,f.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};eX.displayName="AlertDialogFooter";let eQ=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eq.hE,{ref:a,className:(0,f.cn)("text-lg font-semibold",s),...r})});eQ.displayName=eq.hE.displayName;let e0=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eq.VY,{ref:a,className:(0,f.cn)("text-sm text-muted-foreground",s),...r})});e0.displayName=eq.VY.displayName;let e1=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eq.rc,{ref:a,className:(0,f.cn)((0,u.r)(),s),...r})});e1.displayName=eq.rc.displayName;let e2=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(eq.ZD,{ref:a,className:(0,f.cn)((0,u.r)({variant:"outline"}),"mt-2 sm:mt-0",s),...r})});function e5(e){var a,s;let{open:n,onOpenChange:l,schedule:i,onScheduleUpdated:o}=e,[d,c]=(0,r.useState)(!1),[m,x]=(0,r.useState)(!1),[h,f]=(0,r.useState)(""),[p,g]=(0,r.useState)(""),[j,v]=(0,r.useState)(!1),N=async()=>{if(!h.trim()){(0,y.o)({title:"Erro",description:"Por favor, informe o motivo do cancelamento",variant:"destructive"});return}v(!0);try{let e=await fetch("/api/v1/schedule/cancel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({scheduleId:i.id,reason:h})}),a=await e.json();if(!e.ok)throw Error(a.error||"Erro ao cancelar agendamento");(0,y.o)({title:"Sucesso",description:"Agendamento cancelado com sucesso",variant:"default"}),c(!1),o()}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel cancelar o agendamento",variant:"destructive"})}finally{v(!1)}},b=async()=>{v(!0);try{let e=await fetch("/api/v1/schedule/complete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({scheduleId:i.id,notes:p})}),a=await e.json();if(!e.ok)throw Error(a.error||"Erro ao concluir agendamento");(0,y.o)({title:"Sucesso",description:"Agendamento conclu\xeddo com sucesso",variant:"default"}),x(!1),o()}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel concluir o agendamento",variant:"destructive"})}finally{v(!1)}},w=(null==i?void 0:i.status)===eH.Y.PENDING||(null==i?void 0:i.status)===eH.Y.CONFIRMED,_=(null==i?void 0:i.status)===eH.Y.CONFIRMED;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(et.lG,{open:n,onOpenChange:l,children:(0,t.jsxs)(et.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(et.c7,{children:[(0,t.jsx)(et.L3,{children:"Detalhes do Agendamento"}),(0,t.jsx)(et.rr,{children:"Informa\xe7\xf5es sobre o agendamento"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Status"}),(e=>{switch(e){case eH.Y.PENDING:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-yellow-100 text-yellow-800",children:"Pendente"});case eH.Y.CONFIRMED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-blue-100 text-blue-800",children:"Confirmado"});case eH.Y.COMPLETED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-green-100 text-green-800",children:"Conclu\xeddo"});case eH.Y.CANCELLED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-red-100 text-red-800",children:"Cancelado"});case eH.Y.RESCHEDULED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-purple-100 text-purple-800",children:"Reagendado"});default:return(0,t.jsx)(z.E,{variant:"outline",children:"Desconhecido"})}})(null==i?void 0:i.status)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Campanha"}),(0,t.jsx)("p",{className:"text-gray-700",children:(null==i?void 0:null===(a=i.campaign)||void 0===a?void 0:a.name)||"N/A"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),"Data e Hora"]}),(0,t.jsx)("p",{className:"text-gray-700",children:(e=>{let a=(0,eF.H)(e);return(0,G.GP)(a,"dd 'de' MMMM 'de' yyyy '\xe0s' HH:mm",{locale:q.F})})(null==i?void 0:i.scheduled_date)})]}),(null==i?void 0:null===(s=i.campaign)||void 0===s?void 0:s.restaurants)&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(eU.A,{className:"h-4 w-4"}),"Local"]}),(0,t.jsxs)("p",{className:"text-gray-700",children:[i.campaign.restaurants.address,", ",i.campaign.restaurants.city,", ",i.campaign.restaurants.state]})]}),(null==i?void 0:i.influencer)&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(ee.A,{className:"h-4 w-4"}),"Influenciador"]}),(0,t.jsx)("p",{className:"text-gray-700",children:i.influencer.full_name}),i.influencer.phone&&(0,t.jsxs)("p",{className:"text-gray-500 text-sm",children:["Telefone: ",i.influencer.phone]})]}),(null==i?void 0:i.notes)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium",children:"Notas"}),(0,t.jsx)("p",{className:"text-gray-700",children:i.notes})]}),(null==i?void 0:i.status)===eH.Y.CANCELLED&&(null==i?void 0:i.cancellation_reason)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-red-600",children:"Motivo do Cancelamento"}),(0,t.jsx)("p",{className:"text-gray-700",children:i.cancellation_reason})]})]}),(0,t.jsxs)(et.Es,{className:"gap-2",children:[w&&(0,t.jsxs)(u.$,{variant:"outline",className:"gap-2 text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>c(!0),children:[(0,t.jsx)(eW.A,{className:"h-4 w-4"}),"Cancelar Agendamento"]}),_&&(0,t.jsxs)(u.$,{variant:"default",className:"gap-2",onClick:()=>x(!0),children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),"Marcar como Conclu\xeddo"]})]})]})}),(0,t.jsx)(e$,{open:d,onOpenChange:c,children:(0,t.jsxs)(eJ,{children:[(0,t.jsxs)(eK,{children:[(0,t.jsx)(eQ,{children:"Cancelar Agendamento"}),(0,t.jsx)(e0,{children:"Tem certeza que deseja cancelar este agendamento? Esta a\xe7\xe3o n\xe3o pode ser desfeita."})]}),(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsx)("label",{htmlFor:"cancel-reason",className:"block text-sm font-medium mb-2",children:"Motivo do Cancelamento"}),(0,t.jsx)(es.T,{id:"cancel-reason",placeholder:"Informe o motivo do cancelamento",value:h,onChange:e=>f(e.target.value),className:"resize-none",rows:3})]}),(0,t.jsxs)(eX,{children:[(0,t.jsx)(e2,{disabled:j,children:"Voltar"}),(0,t.jsxs)(e1,{onClick:e=>{e.preventDefault(),N()},disabled:j,className:"bg-red-600 hover:bg-red-700 text-white",children:[j?(0,t.jsx)(A.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eG.A,{className:"h-4 w-4 mr-2"}),"Cancelar Agendamento"]})]})]})}),(0,t.jsx)(e$,{open:m,onOpenChange:x,children:(0,t.jsxs)(eJ,{children:[(0,t.jsxs)(eK,{children:[(0,t.jsx)(eQ,{children:"Concluir Agendamento"}),(0,t.jsx)(e0,{children:"Confirme que a visita foi realizada com sucesso."})]}),(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsx)("label",{htmlFor:"complete-notes",className:"block text-sm font-medium mb-2",children:"Observa\xe7\xf5es (opcional)"}),(0,t.jsx)(es.T,{id:"complete-notes",placeholder:"Adicione observa\xe7\xf5es sobre a visita",value:p,onChange:e=>g(e.target.value),className:"resize-none",rows:3})]}),(0,t.jsxs)(eX,{children:[(0,t.jsx)(e2,{disabled:j,children:"Voltar"}),(0,t.jsxs)(e1,{onClick:e=>{e.preventDefault(),b()},disabled:j,className:"bg-green-600 hover:bg-green-700 text-white",children:[j?(0,t.jsx)(A.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Concluir Agendamento"]})]})]})})]})}function e4(e){let{campaignId:a,className:s}=e;(0,n.createClientComponentClient)();let[l,i]=(0,r.useState)(!0),[o,d]=(0,r.useState)([]),[c,m]=(0,r.useState)(new Date),[x,h]=(0,r.useState)(null),[f,p]=(0,r.useState)(!1);(0,r.useEffect)(()=>{g()},[a]);let g=async()=>{i(!0);try{let e="";e=a?"/api/v1/schedule/campaign/".concat(a):"/api/v1/schedule/criador";let s=await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}}),t=await s.json();if(!s.ok)throw Error(t.error||"Erro ao carregar agendamentos");d(t.schedules||[])}catch(e){(0,y.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel carregar os agendamentos",variant:"destructive"})}finally{i(!1)}},j=e=>{switch(e){case eH.Y.PENDING:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-yellow-100 text-yellow-800",children:"Pendente"});case eH.Y.CONFIRMED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-blue-100 text-blue-800",children:"Confirmado"});case eH.Y.COMPLETED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-green-100 text-green-800",children:"Conclu\xeddo"});case eH.Y.CANCELLED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-red-100 text-red-800",children:"Cancelado"});case eH.Y.RESCHEDULED:return(0,t.jsx)(z.E,{variant:"outline",className:"bg-purple-100 text-purple-800",children:"Reagendado"});default:return(0,t.jsx)(z.E,{variant:"outline",children:"Desconhecido"})}},v=e=>{let a=(0,eF.H)(e);return(0,G.GP)(a,"dd 'de' MMMM '\xe0s' HH:mm",{locale:q.F})},N=e=>{h(e),p(!0)},b=c?o.filter(e=>(0,eV.r)((0,eF.H)(e.scheduled_date),c)):[];return(0,t.jsxs)(M.Zp,{className:"overflow-hidden ".concat(s),children:[(0,t.jsx)(M.aR,{className:"bg-[#f5f5f5] pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(M.ZB,{className:"text-lg font-medium",children:[(0,t.jsx)(Z.A,{className:"h-5 w-5 inline-block mr-2 text-blue-500"}),"Agenda"]}),(0,t.jsx)(M.BT,{children:"Gerencie seus agendamentos"})]}),(0,t.jsx)(u.$,{variant:"outline",size:"sm",onClick:g,disabled:l,children:l?(0,t.jsx)(A.A,{className:"h-4 w-4 animate-spin"}):"Atualizar"})]})}),(0,t.jsx)(M.Wu,{className:"p-0",children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 divide-x divide-gray-200",children:[(0,t.jsx)("div",{className:"p-4",children:(0,t.jsx)(em,{mode:"single",selected:c,onSelect:m,className:"rounded-md border",locale:q.F,modifiers:{booked:o.map(e=>(0,eF.H)(e.scheduled_date))},modifiersStyles:{booked:{fontWeight:"bold",backgroundColor:"#e0f2fe",color:"#0369a1"}}})}),(0,t.jsxs)("div",{className:"p-4 max-h-[400px] overflow-y-auto",children:[(0,t.jsx)("h3",{className:"font-medium mb-3",children:c?(0,t.jsxs)(t.Fragment,{children:["Agendamentos para ",(0,G.GP)(c,"dd 'de' MMMM",{locale:q.F})]}):"Selecione uma data"}),l?(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(A.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):0===b.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nenhum agendamento para esta data."}):(0,t.jsx)("div",{className:"space-y-3",children:b.map(e=>{var a,s;return(0,t.jsx)("div",{className:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>N(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:(null===(a=e.campaign)||void 0===a?void 0:a.name)||"Campanha"}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,t.jsx)(O.A,{className:"h-3 w-3 mr-1"}),v(e.scheduled_date)]}),(null===(s=e.campaign)||void 0===s?void 0:s.restaurants)&&(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,t.jsx)(eU.A,{className:"h-3 w-3 mr-1"}),e.campaign.restaurants.address,", ",e.campaign.restaurants.city]}),e.influencer&&(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center mt-1",children:[(0,t.jsx)(ee.A,{className:"h-3 w-3 mr-1"}),e.influencer.full_name]})]}),(0,t.jsx)("div",{children:j(e.status)})]})},e.id)})})]})]})}),x&&(0,t.jsx)(e5,{open:f,onOpenChange:p,schedule:x,onScheduleUpdated:()=>{g(),p(!1)}})]})}e2.displayName=eq.ZD.displayName;let e3=[{city:"S\xe3o Paulo",state:"SP"},{city:"Rio de Janeiro",state:"RJ"},{city:"Bras\xedlia",state:"DF"},{city:"Salvador",state:"BA"},{city:"Fortaleza",state:"CE"},{city:"Belo Horizonte",state:"MG"},{city:"Manaus",state:"AM"},{city:"Curitiba",state:"PR"},{city:"Recife",state:"PE"},{city:"Porto Alegre",state:"RS"},{city:"Bel\xe9m",state:"PA"},{city:"Goi\xe2nia",state:"GO"},{city:"Guarulhos",state:"SP"},{city:"Campinas",state:"SP"},{city:"S\xe3o Lu\xeds",state:"MA"},{city:"S\xe3o Gon\xe7alo",state:"RJ"},{city:"Macei\xf3",state:"AL"},{city:"Duque de Caxias",state:"RJ"},{city:"Natal",state:"RN"},{city:"Teresina",state:"PI"},{city:"Florian\xf3polis",state:"SC"},{city:"Joinville",state:"SC"},{city:"Blumenau",state:"SC"},{city:"Balne\xe1rio Cambori\xfa",state:"SC"},{city:"Itaja\xed",state:"SC"}],e6=[{id:"rest-1",name:"Restaurante Italiano Bella Pasta",logo_url:"https://example.com/logos/restaurant1.jpg",city:"S\xe3o Paulo",state:"SP"},{id:"rest-2",name:"Churrascaria Fogo & Brasa",logo_url:"https://example.com/logos/restaurant2.jpg",city:"Rio de Janeiro",state:"RJ"},{id:"rest-3",name:"Sushi Koi",logo_url:"https://example.com/logos/restaurant3.jpg",city:"Curitiba",state:"PR"},{id:"rest-4",name:"Pizzaria Napolitana",logo_url:"https://example.com/logos/restaurant4.jpg",city:"Belo Horizonte",state:"MG"},{id:"rest-5",name:"Cantina do Chef",logo_url:"https://example.com/logos/restaurant5.jpg",city:"Porto Alegre",state:"RS"},{id:"rest-6",name:"Sabor Nordestino",logo_url:"https://example.com/logos/restaurant6.jpg",city:"Recife",state:"PE"},{id:"rest-7",name:"Bistr\xf4 Franc\xeas",logo_url:"https://example.com/logos/restaurant7.jpg",city:"Bras\xedlia",state:"DF"},{id:"rest-8",name:"Hamburgueria Artesanal",logo_url:"https://example.com/logos/restaurant8.jpg",city:"Salvador",state:"BA"},{id:"rest-9",name:"Caf\xe9 & Bistr\xf4",logo_url:"https://example.com/logos/restaurant9.jpg",city:"Florian\xf3polis",state:"SC"},{id:"rest-10",name:"Restaurante Mediterr\xe2neo",logo_url:"https://example.com/logos/restaurant10.jpg",city:"Fortaleza",state:"CE"},{id:"rest-11",name:"Doceria Gourmet",logo_url:"https://example.com/logos/restaurant11.jpg",city:"Manaus",state:"AM"},{id:"rest-12",name:"Padaria Artesanal",logo_url:"https://example.com/logos/restaurant12.jpg",city:"Goi\xe2nia",state:"GO"},{id:"rest-13",name:"Sorveteria Gelato",logo_url:"https://example.com/logos/restaurant13.jpg",city:"Natal",state:"RN"},{id:"rest-14",name:"Restaurante Vegano",logo_url:"https://example.com/logos/restaurant14.jpg",city:"Joinville",state:"SC"},{id:"rest-15",name:"Choperia Artesanal",logo_url:"https://example.com/logos/restaurant15.jpg",city:"Blumenau",state:"SC"}];e6[0],e6[1],e6[2],e6[3],e6[4],e6[5],e6[6],e6[7],e6[8],e6[9],e6[0],e6[1],e6[2],e6[3];let e8={async getInfluencerCampaigns(e){let a=(0,n.createClientComponentClient)();try{let{data:s,error:t}=await a.from("campaign_participants").select("\n          id, \n          status, \n          campaigns(\n            id, \n            name, \n            description, \n            start_date, \n            end_date, \n            status, \n            restaurant_id, \n            restaurant_profiles(id, business_name)\n          )\n        ").eq("profile_id",e);if(t)return console.error("Error fetching campaign participants:",t),[];if(!s||0===s.length)return[];return(await Promise.all(s.map(async s=>{let t,r;let n=s.campaigns,l=Array.isArray(n)?n[0]:n;if(!l)return null;let i=l.restaurant_profiles,o=Array.isArray(i)?i[0]:i,{data:d,error:c}=await a.from("points_history").select("points").eq("campaign_participant_id",s.id),m=0;!c&&d&&(m=d.reduce((e,a)=>e+(a.points||0),0));let{data:x,error:u}=await a.from("campaign_videos").select("status").eq("campaign_participant_id",s.id).order("created_at",{ascending:!1}).limit(1).single();return!u&&x&&(t=x.status),{id:s.id,campaign_id:l.id,influencer_id:e,status:s.status,total_points:m,video_status:t,rank:r,campaigns:{id:l.id,name:l.name,description:l.description,start_date:l.start_date,end_date:l.end_date,status:l.status,restaurant_id:l.restaurant_id,restaurants:o?{id:o.id,name:o.business_name}:null}}}))).filter(Boolean)}catch(e){return console.error("Error in getInfluencerCampaigns:",e),[]}},async getAvailableCampaigns(e){let a=(0,n.createClientComponentClient)();try{console.log("Fetching available campaigns with filters:",e);let s=a.from("campaigns").select("\n          id,\n          name,\n          description,\n          start_date,\n          end_date,\n          status,\n          restaurant_id,\n          restaurant_profiles(id, business_name, city, state),\n          requirements\n        ");(null==e?void 0:e.city)&&(s=s.eq("city",e.city)),(null==e?void 0:e.state)&&(s=s.eq("state",e.state));let{data:t,error:r}=await s;if(r)return console.error("Error fetching available campaigns:",r),[];if(console.log("Raw campaigns data from database:",t),!t||0===t.length)return console.log("No campaigns found with the current filters"),[];let n=t.map(e=>{let a=e.restaurant_profiles,s=Array.isArray(a)?a[0]:a,t=e.requirements||{},r=t.hashtags||[],n=t.mentions||[];return{id:e.id,name:e.name,description:e.description,start_date:e.start_date,end_date:e.end_date,status:e.status,restaurant_id:e.restaurant_id,restaurants:s?{id:s.id,name:s.business_name,city:s.city,state:s.state}:null,hashtags:r,mentions:n,city:null==s?void 0:s.city,state:null==s?void 0:s.state}});return console.log("Transformed campaigns:",n),n}catch(e){return console.error("Error in getAvailableCampaigns:",e),[]}},async applyForCampaign(e,a){let s=(0,n.createClientComponentClient)();try{console.log("Attempting to apply for campaign: ".concat(a," by influencer: ").concat(e));let{data:{user:t}}=await s.auth.getUser();if(!t)return{success:!1,message:"Usu\xe1rio n\xe3o autenticado. Por favor, fa\xe7a login e tente novamente."};if(console.log("InfluencerId: ".concat(e)),console.log("Auth.uid(): ".concat(t.id)),e!==t.id)return{success:!1,message:"Erro de autentica\xe7\xe3o. Por favor, fa\xe7a login novamente e tente mais uma vez."};let{data:r,error:n}=await s.from("campaign_influencers").select("id, status").eq("influencer_id",t.id).eq("campaign_id",a).single();if(n&&"PGRST116"!==n.code)return console.error("Error checking existing participation:",n),{success:!1,message:"Erro ao verificar participa\xe7\xe3o existente. Tente novamente."};if(r)return{success:!1,message:"Voc\xea j\xe1 se candidatou a esta campanha."};let{data:l,error:i}=await s.from("influencer_profiles").select("id").eq("id",t.id).single();if(i||!l)return console.error("Influencer profile not found:",i),{success:!1,message:"Perfil de influenciador n\xe3o encontrado. Por favor, complete seu perfil antes de se candidatar a campanhas."};let{data:o,error:d}=await s.from("campaigns").select("\n          id,\n          name,\n          description,\n          start_date,\n          end_date,\n          status,\n          restaurant_id,\n          restaurant_profiles(id, business_name)\n        ").eq("id",a).single();if(d)return console.error("Error fetching campaign details:",d),{success:!1,message:"Erro ao buscar detalhes da campanha. Tente novamente."};let{data:c,error:m}=await s.from("campaign_influencers").insert({influencer_id:t.id,campaign_id:a,status:"invited"}).select("id").single();if(m){if(console.error("Error creating participation:",m),"23503"===m.code)return{success:!1,message:"Perfil de influenciador n\xe3o encontrado. Por favor, complete seu perfil antes de se candidatar a campanhas."};return{success:!1,message:"Erro ao se candidatar para a campanha: ".concat(m.message,". Tente novamente.")}}console.log("Successfully applied to campaign:",c);let x=o.restaurant_profiles,u=Array.isArray(x)?x[0]:x,h={id:c.id,campaign_id:a,influencer_id:t.id,status:"invited",total_points:0,campaigns:{id:o.id,name:o.name,description:o.description,start_date:o.start_date,end_date:o.end_date,status:o.status,restaurant_id:o.restaurant_id,restaurants:u?{id:u.id,name:u.business_name}:null}};return{success:!0,message:"Candidatura enviada com sucesso! Aguarde a aprova\xe7\xe3o do restaurante.",campaignInfluencer:h}}catch(e){return console.error("Error in applyForCampaign:",e),{success:!1,message:"Erro ao se candidatar para a campanha. Tente novamente."}}}};function e9(e){var a,s,n,l,i,d,c;let{influencer:x}=e,u=(0,m.useSearchParams)(),h=null==u?void 0:u.get("campaign"),[f,p]=(0,r.useState)([]),[g,j]=(0,r.useState)([]),[v,N]=(0,r.useState)(!0),[y,w]=(0,r.useState)("all"),[_,C]=(0,r.useState)(""),[k,E]=(0,r.useState)(""),[A,S]=(0,r.useState)(null),[M,T]=(0,r.useState)(null),[P,R]=(0,r.useState)(null);(0,r.useEffect)(()=>{(null==x?void 0:x.id)&&(D(x.id),L())},[x]),(0,r.useEffect)(()=>{if(h&&f.length>0){let e=f.find(e=>e.campaigns.id===h);e&&S(e.campaigns)}},[h,f]);let D=async e=>{N(!0);try{let a=await e8.getInfluencerCampaigns(e);console.log("Fetched campaigns from database:",a),p(a)}catch(e){console.error("Error fetching campaigns:",e)}finally{N(!1)}},L=async e=>{try{let a=await e8.getAvailableCampaigns(e?{city:e}:void 0);if(console.log("Fetched available campaigns from database:",a),f.length>0){let e=f.map(e=>e.campaigns.id),s=a.filter(a=>!e.includes(a.id));j(s)}else j(a)}catch(e){console.error("Error fetching available campaigns:",e)}},z=async e=>{if(null==x?void 0:x.id){T(e),R(null);try{let a=await e8.applyForCampaign(x.id,e);a.success?(R({type:"success",text:a.message}),a.campaignInfluencer&&p(e=>[...e,a.campaignInfluencer]),j(a=>a.filter(a=>a.id!==e))):R({type:"error",text:a.message})}catch(e){console.error("Error applying for campaign:",e),R({type:"error",text:"Erro ao se candidatar para a campanha. Tente novamente."})}finally{T(null)}}},O=e=>{E(e),L(e)},F=e=>e?new Date(e).toLocaleDateString("pt-BR"):"",V=e=>{if(!e)return 0;let a=new Date(e),s=new Date;return Math.ceil((a.getTime()-s.getTime())/864e5)},H=f.filter(e=>{var a;let s="all"===y||e.status===y,t=!_||e.campaigns.name.toLowerCase().includes(_.toLowerCase())||((null===(a=e.campaigns.restaurants)||void 0===a?void 0:a.name)||"").toLowerCase().includes(_.toLowerCase());return s&&t}),W=e=>{S(e);let a=new URL(window.location.href);a.searchParams.set("campaign",e.id),window.history.pushState({},"",a.toString())};return(0,t.jsx)("div",{children:A?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50 mb-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("button",{onClick:()=>{S(null);let e=new URL(window.location.href);e.searchParams.delete("campaign"),window.history.pushState({},"",e.toString())},className:"text-blue-600 hover:text-blue-700 flex items-center",children:[(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Voltar para campanhas"]})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center mr-3",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:A.name}),(0,t.jsx)("p",{className:"text-gray-600",children:null===(a=A.restaurants)||void 0===a?void 0:a.name})]})]})})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"md:col-span-2 space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.bfZ,{className:"text-blue-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Per\xedodo"})]}),(0,t.jsxs)("p",{className:"text-gray-700",children:[F(A.start_date)," a ",F(A.end_date)]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:V(A.end_date)>0?"Faltam ".concat(V(A.end_date)," dias"):"Encerrada"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-red-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.vq8,{className:"text-red-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Restaurante"})]}),(0,t.jsx)("p",{className:"text-gray-700",children:null===(s=A.restaurants)||void 0===s?void 0:s.name})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.amN,{className:"text-purple-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Hashtags"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:(null===(n=A.hashtags)||void 0===n?void 0:n.map((e,a)=>(0,t.jsxs)("span",{className:"bg-gray-50 border border-gray-100 px-2 py-1 rounded-full text-sm text-gray-700",children:["#",e]},a)))||(0,t.jsx)("p",{className:"text-gray-500",children:"Nenhuma hashtag definida"})})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.T35,{className:"text-blue-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Men\xe7\xf5es"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:(null===(l=A.mentions)||void 0===l?void 0:l.map((e,a)=>(0,t.jsxs)("span",{className:"bg-gray-50 border border-gray-100 px-2 py-1 rounded-full text-sm text-gray-700",children:["@",e]},a)))||(0,t.jsx)("p",{className:"text-gray-500",children:"Nenhuma men\xe7\xe3o definida"})})]})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-50 flex items-center justify-center mr-3",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Descri\xe7\xe3o"})]}),(0,t.jsx)("p",{className:"text-gray-700",children:A.description||"Nenhuma descri\xe7\xe3o dispon\xedvel para esta campanha."})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-yellow-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.SBv,{className:"text-yellow-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Ranking Detalhado"})]}),(0,t.jsx)(I,{campaignId:A.id})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-amber-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.Z0L,{className:"text-amber-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Suas Conquistas"})]}),(0,t.jsx)(B,{influencerId:(null==x?void 0:x.id)||"",className:"border-0 shadow-none"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"})})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Hist\xf3rico de Pontos"})]}),(0,t.jsx)(U,{campaignInfluencerId:(null===(i=f.find(e=>e.campaigns.id===A.id))||void 0===i?void 0:i.id)||"",className:"border-0 shadow-none"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.Hcz,{className:"text-green-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Tarefas da Campanha"})]}),(0,t.jsx)(eO,{campaignId:A.id,userId:null==x?void 0:x.id,className:"border-0 shadow-none"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.gUT,{className:"text-blue-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Agenda da Campanha"})]}),(0,t.jsx)(e4,{campaignId:A.id,className:"border-0 shadow-none"})]})]}),(0,t.jsxs)("div",{className:"md:col-span-1",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50 sticky top-24",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-yellow-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.SBv,{className:"text-yellow-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Seu Ranking"})]}),(0,t.jsx)(b,{campaignId:A.id,userId:(null==x?void 0:x.id)||"",userRole:"influencer"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50 mt-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-amber-50 flex items-center justify-center mr-3",children:(0,t.jsx)(o.Z0L,{className:"text-amber-500 h-4 w-4"})}),(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:"Estat\xedsticas de Gamifica\xe7\xe3o"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Total de Pontos"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:(null===(d=f.find(e=>e.campaigns.id===A.id))||void 0===d?void 0:d.total_points)||0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Posi\xe7\xe3o Atual"}),(0,t.jsx)("p",{className:"text-xl font-bold text-gray-900",children:(null===(c=f.find(e=>e.campaigns.id===A.id))||void 0===c?void 0:c.rank)||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Conquistas Desbloqueadas"}),(0,t.jsxs)("p",{className:"text-xl font-bold text-gray-900",children:[Math.floor(5*Math.random())," de 12"]})]})]})]})]})]})})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Filtrar Campanhas"})]}),(0,t.jsx)("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-4 w-full",children:[(0,t.jsxs)("select",{className:"px-4 py-2 rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 border border-gray-100",value:y,onChange:e=>w(e.target.value),children:[(0,t.jsx)("option",{value:"all",children:"Todas as campanhas"}),(0,t.jsx)("option",{value:"accepted",children:"Ativas"}),(0,t.jsx)("option",{value:"pending",children:"Pendentes"}),(0,t.jsx)("option",{value:"rejected",children:"Rejeitadas"}),(0,t.jsx)("option",{value:"completed",children:"Conclu\xeddas"})]}),(0,t.jsxs)("select",{className:"px-4 py-2 rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 border border-gray-100",value:k,onChange:e=>O(e.target.value),children:[(0,t.jsx)("option",{value:"",children:"Todas as cidades"}),e3.map((e,a)=>(0,t.jsxs)("option",{value:e.city,children:[e.city," - ",e.state]},a))]}),(0,t.jsx)("div",{className:"relative flex-grow",children:(0,t.jsx)("input",{type:"text",placeholder:"Buscar campanhas...",className:"px-4 py-2 w-full rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 border border-gray-100",value:_,onChange:e=>C(e.target.value)})})]})})]}),P&&(0,t.jsx)("div",{className:"mt-4 p-4 rounded-xl shadow-sm border border-gray-50 ".concat((P.type,"bg-white text-gray-700")),children:P.text})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50 mb-8",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Minhas Campanhas"})]})}),v?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"})}):0===H.length?(0,t.jsxs)("div",{className:"text-center p-8 bg-gray-50 rounded-lg",children:[(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Nenhuma campanha encontrada com os filtros atuais."}),"all"!==y&&(0,t.jsx)("button",{onClick:()=>w("all"),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Ver todas as campanhas"})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:H.map(e=>{var a;return(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50 cursor-pointer hover:shadow-md transition-shadow",onClick:()=>W(e.campaigns),children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:e.campaigns.name}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("accepted"===e.status?"bg-green-50 text-green-700":"pending"===e.status?"bg-yellow-50 text-yellow-700":"rejected"===e.status?"bg-red-50 text-red-700":"bg-gray-50 text-gray-700"),children:"accepted"===e.status?"Ativa":"pending"===e.status?"Pendente":"rejected"===e.status?"Rejeitada":"completed"===e.status?"Conclu\xedda":"Desconhecido"})]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.campaigns.description||"Sem descri\xe7\xe3o dispon\xedvel"}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-500",children:"Restaurante"}),(0,t.jsx)("p",{className:"font-medium",children:null===(a=e.campaigns.restaurants)||void 0===a?void 0:a.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-500",children:"T\xe9rmino"}),(0,t.jsx)("p",{className:"font-medium",children:F(e.campaigns.end_date)}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:V(e.campaigns.end_date)>0?"Faltam ".concat(V(e.campaigns.end_date)," dias"):"Encerrada"})]})]})]},e.id)})})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3",children:(0,t.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z",clipRule:"evenodd"})})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Campanhas Dispon\xedveis"})]}),k&&(0,t.jsxs)("span",{className:"text-sm bg-gray-50 px-3 py-1 rounded-full flex items-center border border-gray-100",children:[k,(0,t.jsx)("button",{onClick:()=>O(""),className:"ml-2 text-gray-500 hover:text-gray-900 focus:outline-none",children:"\xd7"})]})]}),0===g.length?(0,t.jsxs)("div",{className:"text-center p-8 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("p",{className:"text-gray-500 mb-2",children:["Nenhuma campanha dispon\xedvel ",k?"em ".concat(k):"no momento","."]}),k&&(0,t.jsx)("button",{onClick:()=>O(""),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Ver todas as cidades"})]}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{className:"border-b border-gray-100",children:[(0,t.jsx)("th",{className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campanha"}),(0,t.jsx)("th",{className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Restaurante"}),(0,t.jsx)("th",{className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Localiza\xe7\xe3o"}),(0,t.jsx)("th",{className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pagamento"}),(0,t.jsx)("th",{className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"T\xe9rmino"}),(0,t.jsx)("th",{className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7\xf5es"})]})}),(0,t.jsx)("tbody",{className:"divide-y divide-gray-100",children:g.map(e=>{var a;return(0,t.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,t.jsxs)("td",{className:"py-4 pr-4",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.current_influencers,"/",e.max_influencers," criadores"]})]}),(0,t.jsx)("td",{className:"py-4 pr-4",children:(0,t.jsx)("div",{className:"text-sm text-gray-500",children:null===(a=e.restaurants)||void 0===a?void 0:a.name})}),(0,t.jsx)("td",{className:"py-4 pr-4",children:(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e.city,", ",e.state]})}),(0,t.jsx)("td",{className:"py-4 pr-4",children:(0,t.jsxs)("div",{className:"text-sm font-medium",children:["R$ ",e.payment_amount]})}),(0,t.jsxs)("td",{className:"py-4 pr-4",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:F(e.end_date)}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:V(e.end_date)>0?"".concat(V(e.end_date)," dias"):"Encerrada"})]}),(0,t.jsx)("td",{className:"py-4 text-right text-sm font-medium",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("button",{onClick:()=>W(e),className:"px-4 py-1 rounded-lg bg-gray-50 text-gray-700 text-xs hover:bg-gray-100 transition-colors border border-gray-100",children:"Detalhes"}),(0,t.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),z(e.id)},disabled:M===e.id,className:"px-4 py-1 rounded-lg bg-blue-600 text-white text-xs hover:bg-blue-700 transition-colors ".concat(M===e.id?"opacity-50 cursor-not-allowed":""),children:M===e.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Enviando..."]}):"Candidatar-se"})]})})]},e.id)})})]})}),g.length>0&&(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Mostrando ",g.length," campanhas dispon\xedveis",k?" em ".concat(k):""]})})]})]})})}var e7=s(48067),ae=s(81757),aa=s(74489);function as(){return(0,t.jsx)(i.A,{requiredRole:"influencer",children:(0,t.jsx)(at,{})})}function at(){let{user:e}=(0,l.A)(),a=(0,n.createClientComponentClient)(),[s,i]=(0,r.useState)(null),[m,x]=(0,r.useState)(!1),{isOpen:u,openPopup:h,closePopup:f}=(0,aa.Y)({popupId:"settings-modal",defaultTabId:"info"});(0,r.useEffect)(()=>{(null==e?void 0:e.id)&&p(e.id)},[e]);let p=async e=>{x(!0);try{let{data:s,error:t}=await a.from("influencers").select("*").eq("id",e).single();if(t)throw t;i(s)}catch(e){console.error("Error fetching influencer data:",e)}finally{x(!1)}};return(0,t.jsxs)("div",{className:"h-full bg-[#f5f5f5] flex flex-col font-sans relative overflow-hidden",children:[(0,t.jsxs)("header",{className:"fixed top-0 left-0 right-0 flex justify-between items-center px-6 py-1.5 bg-[#f5f5f5] z-50 h-12",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.default,{size:"medium",textClassName:"text-xl font-bold"}),m&&(0,t.jsxs)("div",{className:"ml-3 flex items-center text-xs text-gray-500",children:[(0,t.jsxs)("svg",{className:"animate-spin h-3 w-3 mr-1 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,t.jsx)("span",{children:"Sincronizando..."})]})]}),(0,t.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2",children:(0,t.jsx)(e7.A,{})}),(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)(c.A,{trigger:(null==s?void 0:s.name)||(null==e?void 0:e.email)||"Usu\xe1rio",items:[{label:"Configura\xe7\xf5es",icon:(0,t.jsx)(o.Pcn,{className:"text-gray-500"}),onClick:()=>h("info")},{label:"Ajuda",icon:(0,t.jsx)(o.gZZ,{className:"text-gray-500"}),onClick:()=>window.open("https://help.connectcity.com.br","_blank")},{label:"Logout",icon:(0,t.jsx)(o.axc,{className:"text-gray-500"}),onClick:()=>{a.auth.signOut().then(()=>{window.location.href="/login"})}}]})})]}),(0,t.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-white overflow-y-auto flex-1 flex flex-col shadow-md m-1 mt-[3.25rem]",style:{minHeight:"calc(100vh - 5rem)",maxHeight:"calc(100vh - 5rem)"},children:(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(e9,{influencer:s})})}),(0,t.jsx)("a",{href:"https://wa.me/5547999543437",target:"_blank",rel:"noopener noreferrer",className:"fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg flex items-center justify-center",children:(0,t.jsx)(o.EcP,{size:24})}),(0,t.jsx)(ae.A,{isOpen:u,onClose:f,onSaved:()=>{p(null==e?void 0:e.id)},userType:"influencer",defaultTab:"info"})]})}},40283:(e,a,s)=>{"use strict";s.d(a,{A:()=>d,O:()=>o});var t=s(95155),r=s(12115),n=s(52643),l=s(44032);let i=(0,r.createContext)(void 0);function o(e){let{children:a}=e,[s,o]=(0,r.useState)(null),[d,c]=(0,r.useState)(null),[m,x]=(0,r.useState)(!0),[u,h]=(0,r.useState)(null),f=async()=>{try{var e;x(!0),h(null),console.log("AuthContext: Refreshing session...");let{data:a,error:s}=await n.N.auth.getSession();if(s){console.error("Erro ao verificar sess\xe3o atual:",s.message),h(s.message),x(!1);return}if(!a.session){console.log("AuthContext: No active session found"),o(null),c(null),x(!1);return}console.log("AuthContext: Active session found"),o(a.session),c((null===(e=a.session)||void 0===e?void 0:e.user)||null)}catch(e){console.error("Erro inesperado ao obter sess\xe3o:",e),h(e.message||"Erro desconhecido")}finally{x(!1)}},p=async()=>{try{x(!0),h(null),console.log("AuthContext: Signing out...");let{error:e}=await n.N.auth.signOut();e&&(console.error("Erro ao fazer logout no Supabase:",e.message),h(e.message)),o(null),c(null),console.log("AuthContext: Logout completed, redirecting to login"),window.location.href="/login"}catch(e){console.error("Erro inesperado ao fazer logout:",e),h(e.message||"Erro desconhecido"),o(null),c(null),window.location.href="/login"}finally{x(!1)}};return(0,r.useEffect)(()=>{f();let{data:e}=n.N.auth.onAuthStateChange(async(e,a)=>{console.log("Auth state changed:",e),"SIGNED_IN"===e?(console.log("User signed in, updating session"),o(a),c((null==a?void 0:a.user)||null)):"SIGNED_OUT"===e?(console.log("User signed out, clearing session"),o(null),c(null)):"TOKEN_REFRESHED"===e?(console.log("Token refreshed, updating session"),o(a),c((null==a?void 0:a.user)||null)):"USER_UPDATED"===e&&(console.log("User updated, updating session"),o(a),c((null==a?void 0:a.user)||null)),x(!1)});return()=>{e.subscription.unsubscribe()}},[]),(0,r.useEffect)(()=>{let e=e=>{(e.error instanceof l.lR||e.message&&(e.message.includes("Invalid Refresh Token")||e.message.includes("JWT expired")||e.message.includes("not authenticated")))&&(console.error("Erro de autentica\xe7\xe3o interceptado:",e),f())};return window.addEventListener("error",e),()=>{window.removeEventListener("error",e)}},[]),(0,t.jsx)(i.Provider,{value:{session:s,user:d,loading:m,error:u,signOut:p,refreshSession:f},children:a})}function d(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth deve ser usado dentro de um AuthProvider");return e}},46102:(e,a,s)=>{"use strict";s.d(a,{Bc:()=>i,ZI:()=>c,k$:()=>d,m_:()=>o});var t=s(95155),r=s(12115),n=s(83e3),l=s(59434);let i=n.Kq,o=n.bL,d=n.l9,c=r.forwardRef((e,a)=>{let{className:s,sideOffset:r=4,...i}=e;return(0,t.jsx)(n.UC,{ref:a,sideOffset:r,className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})});c.displayName=n.UC.displayName},48067:(e,a,s)=>{"use strict";s.d(a,{A:()=>o});var t=s(95155),r=s(6874),n=s.n(r),l=s(35695),i=s(29911);function o(){let e=(0,l.usePathname)();return(0,t.jsxs)("div",{className:"flex items-center navigation-tabs",style:{backgroundColor:"#f5f5f5"},children:[(0,t.jsx)(n(),{href:"/criador",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-2 text-sm font-medium border-b-2 !bg-[#f5f5f5] ".concat("/criador"===e?"border-gray-800 text-gray-800":"border-[#f5f5f5] text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,t.jsx)(i.rQ8,{className:"mr-1.5 bg-[#f5f5f5] text-xs"}),(0,t.jsx)("span",{className:"bg-[#f5f5f5]",children:"Home"})]})}),(0,t.jsx)(n(),{href:"/criador/campanhas",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-2 text-sm font-medium border-b-2 !bg-[#f5f5f5] ".concat(e.includes("/criador/campanhas")?"border-gray-800 text-gray-800":"border-[#f5f5f5] text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,t.jsx)(i.sdT,{className:"mr-1.5 bg-[#f5f5f5] text-xs"}),(0,t.jsx)("span",{className:"bg-[#f5f5f5]",children:"Campanhas"})]})})]})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>o,rr:()=>f});var t=s(95155),r=s(12115),n=s(11662),l=s(54416),i=s(59434);let o=n.bL;n.l9;let d=n.ZL;n.bm;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-white/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});c.displayName=n.hJ.displayName;let m=r.forwardRef((e,a)=>{let{className:s,children:r,...o}=e;return(0,t.jsxs)(d,{children:[(0,t.jsx)(c,{}),(0,t.jsxs)(n.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-0 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...o,children:[r,(0,t.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.UC.displayName;let x=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...s})};x.displayName="DialogHeader";let u=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};u.displayName="DialogFooter";let h=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});h.displayName=n.hE.displayName;let f=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(n.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});f.displayName=n.VY.displayName},60637:(e,a,s)=>{"use strict";s.d(a,{default:()=>l});var t=s(95155),r=s(6874),n=s.n(r);function l(e){let{className:a="",textClassName:s="text-xl font-semibold",showText:r=!0,size:l="medium",href:i="/"}=e;return i?(0,t.jsx)(n(),{href:i,className:"flex items-center ".concat(a),children:(0,t.jsx)("span",{className:s,children:"crIAdores"})}):(0,t.jsx)("div",{className:"flex items-center ".concat(a),children:(0,t.jsx)("span",{className:s,children:"crIAdores"})})}},86382:(e,a,s)=>{"use strict";s.d(a,{A:()=>c});var t=s(95155),r=s(12115),n=s(35695),l=s(40283),i=s(52643),o=s(44032);function d(e,a){if(!e)return!1;if(e instanceof o.lR){console.error("Erro de autentica\xe7\xe3o:",e.message);let s=window.location.pathname;if(s.includes("/login")||s.includes("/registro"))return console.log("J\xe1 estamos na p\xe1gina de login/registro, n\xe3o redirecionando"),!0;let t="/login";return(a&&!a.includes("/login")&&""!==a&&(t+="?redirect=".concat(encodeURIComponent(a))),e instanceof o.lR&&(e.message.includes("Invalid Refresh Token")||e.message.includes("Refresh Token Not Found")))?t+=(t.includes("?")?"&":"?")+"error=invalid_refresh_token":e instanceof o.lR&&(e.message.includes("JWT expired")||e.message.includes("Token expired"))?t+=(t.includes("?")?"&":"?")+"error=expired_session":t+=(t.includes("?")?"&":"?")+"error=auth_error",console.log("Redirecionando para: ".concat(t)),window.location.href=t,!0}return!1}function c(e){let{children:a,requiredRole:s,redirectTo:o="/login"}=e,{session:c,user:m,loading:x,refreshSession:u}=(0,l.A)(),[h,f]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!0),j=(0,n.useRouter)();return((0,r.useEffect)(()=>{(async()=>{if(!x){if(!c||!m){console.log("RouteGuard: No session found, redirecting to login"),window.location.href="".concat(o,"?redirect=").concat(encodeURIComponent(window.location.pathname));return}if(!s){f(!0),g(!1);return}try{let{data:e,error:a}=await i.N.from("profiles").select("role").eq("id",m.id).single();if(a){console.error("RouteGuard: Error fetching user role:",a),await u(),d(a,window.location.pathname);return}if(!e||!e.role){console.error("RouteGuard: User has no role"),window.location.href="".concat(o,"?error=profile_error");return}if(e.role===s)console.log('RouteGuard: User role "'.concat(e.role,'" matches required role "').concat(s,'"')),f(!0);else{console.warn('RouteGuard: User role "'.concat(e.role,'" does not match required role "').concat(s,'"'));let a="/login";"influencer"===e.role?a="/influenciador":"restaurant"===e.role?a="/restaurante-novo":"admin"===e.role&&(a="/admin"),window.location.href=a}}catch(e){console.error("RouteGuard: Unexpected error:",e),d(e,window.location.pathname)}finally{g(!1)}}})()},[c,m,x,s,j,o,u]),x||p)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Verificando autentica\xe7\xe3o..."})]})}):h?(0,t.jsx)(t.Fragment,{children:a}):null}},88539:(e,a,s)=>{"use strict";s.d(a,{T:()=>l});var t=s(95155),r=s(12115),n=s(59434);let l=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:a,...r})});l.displayName="Textarea"},93998:(e,a,s)=>{Promise.resolve().then(s.bind(s,35537))}},e=>{var a=a=>e(e.s=a);e.O(0,[6711,9724,3579,9688,6874,8264,535,7084,2177,7127,3035,6766,5532,5514,4665,7292,7326,5593,5999,5358,8441,1684,7358],()=>a(93998)),_N_E=e.O()}]);