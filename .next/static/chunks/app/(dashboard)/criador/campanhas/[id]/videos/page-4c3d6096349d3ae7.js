(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5879],{35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40283:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,O:()=>d});var a=r(95155),n=r(12115),s=r(52643),o=r(44032);let i=(0,n.createContext)(void 0);function d(e){let{children:t}=e,[r,d]=(0,n.useState)(null),[c,l]=(0,n.useState)(null),[u,m]=(0,n.useState)(!0),[p,f]=(0,n.useState)(null),g=async()=>{try{var e;m(!0),f(null),console.log("AuthContext: Refreshing session...");let{data:t,error:r}=await s.N.auth.getSession();if(r){console.error("Erro ao verificar sess\xe3o atual:",r.message),f(r.message),m(!1);return}if(!t.session){console.log("AuthContext: No active session found"),d(null),l(null),m(!1);return}console.log("AuthContext: Active session found"),d(t.session),l((null===(e=t.session)||void 0===e?void 0:e.user)||null)}catch(e){console.error("Erro inesperado ao obter sess\xe3o:",e),f(e.message||"Erro desconhecido")}finally{m(!1)}},h=async()=>{try{m(!0),f(null),console.log("AuthContext: Signing out...");let{error:e}=await s.N.auth.signOut();e&&(console.error("Erro ao fazer logout no Supabase:",e.message),f(e.message)),d(null),l(null),console.log("AuthContext: Logout completed, redirecting to login"),window.location.href="/login"}catch(e){console.error("Erro inesperado ao fazer logout:",e),f(e.message||"Erro desconhecido"),d(null),l(null),window.location.href="/login"}finally{m(!1)}};return(0,n.useEffect)(()=>{g();let{data:e}=s.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e),"SIGNED_IN"===e?(console.log("User signed in, updating session"),d(t),l((null==t?void 0:t.user)||null)):"SIGNED_OUT"===e?(console.log("User signed out, clearing session"),d(null),l(null)):"TOKEN_REFRESHED"===e?(console.log("Token refreshed, updating session"),d(t),l((null==t?void 0:t.user)||null)):"USER_UPDATED"===e&&(console.log("User updated, updating session"),d(t),l((null==t?void 0:t.user)||null)),m(!1)});return()=>{e.subscription.unsubscribe()}},[]),(0,n.useEffect)(()=>{let e=e=>{(e.error instanceof o.lR||e.message&&(e.message.includes("Invalid Refresh Token")||e.message.includes("JWT expired")||e.message.includes("not authenticated")))&&(console.error("Erro de autentica\xe7\xe3o interceptado:",e),g())};return window.addEventListener("error",e),()=>{window.removeEventListener("error",e)}},[]),(0,a.jsx)(i.Provider,{value:{session:r,user:c,loading:u,error:p,signOut:h,refreshSession:g},children:t})}function c(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth deve ser usado dentro de um AuthProvider");return e}},52642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),n=r(12115),s=r(35695),o=r(73579),i=r(40283),d=r(86382),c=r(29911);let l=[{id:"ci-1",campaign_id:"camp-1",influencer_id:"inf-1",status:"accepted",total_points:1250,video_status:"pending",campaigns:{id:"camp-1",name:"Campanha Instagram Test",description:"Campanha para testar integra\xe7\xe3o com Instagram",start_date:"2023-01-01",end_date:"2023-12-31",status:"active",restaurant_id:"rest-1",restaurants:{id:"rest-1",name:"Restaurante Demo"}}},{id:"ci-2",campaign_id:"camp-2",influencer_id:"inf-1",status:"accepted",total_points:980,video_status:"recorded",campaigns:{id:"camp-2",name:"Campanha TikTok Ver\xe3o",description:"Campanha de ver\xe3o para o TikTok",start_date:"2023-06-01",end_date:"2023-08-31",status:"active",restaurant_id:"rest-2",restaurants:{id:"rest-2",name:"Restaurante Praia"}}},{id:"ci-3",campaign_id:"camp-3",influencer_id:"inf-1",status:"accepted",total_points:1500,video_status:"submitted",campaigns:{id:"camp-3",name:"Campanha Dia dos Namorados",description:"Campanha especial para o Dia dos Namorados",start_date:"2023-06-01",end_date:"2023-06-12",status:"active",restaurant_id:"rest-3",restaurants:{id:"rest-3",name:"Restaurante Rom\xe2ntico"}}},{id:"ci-4",campaign_id:"camp-4",influencer_id:"inf-1",status:"accepted",total_points:2100,video_status:"approved",campaigns:{id:"camp-4",name:"Campanha Natal",description:"Campanha especial de Natal",start_date:"2023-12-01",end_date:"2023-12-25",status:"active",restaurant_id:"rest-4",restaurants:{id:"rest-4",name:"Restaurante Festivo"}}},{id:"ci-5",campaign_id:"camp-5",influencer_id:"inf-1",status:"accepted",total_points:750,video_status:"rejected",campaigns:{id:"camp-5",name:"Campanha Ano Novo",description:"Campanha especial de Ano Novo",start_date:"2023-12-26",end_date:"2024-01-05",status:"active",restaurant_id:"rest-5",restaurants:{id:"rest-5",name:"Restaurante Celebra\xe7\xe3o"}}}],u=[{id:"video-1",campaign_influencer_id:"ci-1",status:"pending",created_at:"2023-01-01T00:00:00Z",updated_at:"2023-01-01T00:00:00Z"},{id:"video-2",campaign_influencer_id:"ci-2",status:"recorded",created_at:"2023-06-01T00:00:00Z",updated_at:"2023-06-05T00:00:00Z",recorded_at:"2023-06-05T00:00:00Z"},{id:"video-3",campaign_influencer_id:"ci-3",status:"submitted",video_url:"https://example.com/video3.mp4",created_at:"2023-06-01T00:00:00Z",updated_at:"2023-06-10T00:00:00Z",recorded_at:"2023-06-05T00:00:00Z",submitted_at:"2023-06-10T00:00:00Z"},{id:"video-4",campaign_influencer_id:"ci-4",status:"approved",video_url:"https://example.com/video4.mp4",feedback:"\xd3timo v\xeddeo! Aprovado.",created_at:"2023-12-01T00:00:00Z",updated_at:"2023-12-15T00:00:00Z",recorded_at:"2023-12-05T00:00:00Z",submitted_at:"2023-12-10T00:00:00Z",approved_at:"2023-12-15T00:00:00Z"},{id:"video-5",campaign_influencer_id:"ci-5",status:"rejected",video_url:"https://example.com/video5.mp4",feedback:"O v\xeddeo n\xe3o atende aos requisitos. Por favor, grave novamente.",created_at:"2023-12-26T00:00:00Z",updated_at:"2024-01-02T00:00:00Z",recorded_at:"2023-12-28T00:00:00Z",submitted_at:"2023-12-30T00:00:00Z",rejected_at:"2024-01-02T00:00:00Z"}],m={getInfluencerVideos:async e=>(await new Promise(e=>setTimeout(e,500)),u),getVideoById:async e=>(await new Promise(e=>setTimeout(e,300)),u.find(t=>t.id===e)||null),getVideoByCampaignInfluencerId:async e=>(await new Promise(e=>setTimeout(e,300)),u.find(t=>t.campaign_influencer_id===e)||null),async createVideo(e){await new Promise(e=>setTimeout(e,800));let t={id:"video-".concat(Date.now()),campaign_influencer_id:e,status:"pending",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};return u.push(t),t},async updateVideoStatus(e,t,r,a){await new Promise(e=>setTimeout(e,800));let n=u.findIndex(t=>t.id===e);if(-1===n)return null;let s={...u[n]};s.status=t,s.updated_at=new Date().toISOString(),"recorded"===t?s.recorded_at=new Date().toISOString():"submitted"===t?(s.submitted_at=new Date().toISOString(),r&&(s.video_url=r)):"approved"===t?(s.approved_at=new Date().toISOString(),a&&(s.feedback=a)):"rejected"===t&&(s.rejected_at=new Date().toISOString(),a&&(s.feedback=a)),u[n]=s;let o=l.findIndex(e=>e.id===s.campaign_influencer_id);return -1!==o&&(l[o].video_status=t),s},getCampaignsWithVideoStatus:async e=>(await new Promise(e=>setTimeout(e,1e3)),l.filter(t=>t.influencer_id===e).map(e=>{let t=u.find(t=>t.campaign_influencer_id===e.id);return{...e,videos:t?[t]:[]}}))};function p(e){let{campaignInfluencerId:t,videoId:r,currentStatus:s="pending",onStatusChange:i}=e,[d,l]=(0,n.useState)(null),[u,p]=(0,n.useState)(!1),[f,g]=(0,n.useState)(0),[h,v]=(0,n.useState)(null),[x,b]=(0,n.useState)(!1),[j,w]=(0,n.useState)(s),_=(0,n.useRef)(null);(0,o.createClientComponentClient)();let y=async()=>{try{v(null);let e=r;if(!e){let r=await m.createVideo(t);if(!r)throw Error("Erro ao criar registro de v\xeddeo");e=r.id}if(!await m.updateVideoStatus(e,"recorded"))throw Error("Erro ao atualizar status do v\xeddeo");w("recorded"),i&&i("recorded"),b(!0),setTimeout(()=>b(!1),3e3)}catch(e){v(e.message||"Erro ao registrar v\xeddeo")}},N=async()=>{if(!d){v("Por favor, selecione um arquivo de v\xeddeo.");return}try{p(!0),g(0),v(null);let e=r;if(!e){let r=await m.createVideo(t);if(!r)throw Error("Erro ao criar registro de v\xeddeo");e=r.id}for(let e=0;e<=100;e+=10)g(e),await new Promise(e=>setTimeout(e,200));let a="https://example.com/videos/".concat(t,"_").concat(Date.now(),".mp4");if(!await m.updateVideoStatus(e,"submitted",a))throw Error("Erro ao atualizar status do v\xeddeo");w("submitted"),i&&i("submitted"),b(!0),setTimeout(()=>b(!1),3e3)}catch(e){v(e.message||"Erro ao fazer upload do v\xeddeo")}finally{p(!1)}};return(0,a.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Gerenciamento de V\xeddeo"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Status atual:",(0,a.jsx)("span",{className:"ml-2 font-medium ".concat("approved"===j?"text-green-600":"rejected"===j?"text-red-600":"submitted"===j?"text-purple-600":"recorded"===j?"text-blue-600":"text-yellow-600"),children:"pending"===j?"Pendente":"recorded"===j?"Gravado":"submitted"===j?"Enviado":"approved"===j?"Aprovado":"Rejeitado"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:(()=>{switch(j){case"pending":return"Voc\xea ainda n\xe3o gravou um v\xeddeo para esta campanha.";case"recorded":return"V\xeddeo gravado! Agora fa\xe7a o upload para aprova\xe7\xe3o.";case"submitted":return"V\xeddeo enviado! Aguardando aprova\xe7\xe3o.";case"approved":return"V\xeddeo aprovado! Voc\xea pode public\xe1-lo nas suas redes sociais.";case"rejected":return"V\xeddeo rejeitado. Por favor, grave um novo v\xeddeo.";default:return""}})()})]}),("pending"===j||"rejected"===j)&&(0,a.jsxs)("button",{onClick:y,className:"flex items-center justify-center w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mb-4",children:[(0,a.jsx)(c.HiP,{className:"mr-2"}),"Marcar como Gravado"]}),"recorded"===j&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Selecione o v\xeddeo para upload"}),(0,a.jsx)("input",{type:"file",accept:"video/*",onChange:e=>{if(e.target.files&&e.target.files.length>0){let t=e.target.files[0];if(!t.type.startsWith("video/")){v("Por favor, selecione um arquivo de v\xeddeo v\xe1lido.");return}if(t.size>0x6400000){v("O arquivo \xe9 muito grande. O tamanho m\xe1ximo \xe9 100MB.");return}l(t),v(null)}},ref:_,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"})]}),(0,a.jsx)("button",{onClick:N,disabled:!d||u,className:"flex items-center justify-center w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ".concat(!d||u?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"),children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.hW,{className:"animate-spin mr-2"}),"Enviando... ",f,"%"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.HVe,{className:"mr-2"}),"Enviar V\xeddeo"]})})]}),h&&(0,a.jsx)("div",{className:"mt-4 p-2 bg-red-50 border border-red-200 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-red-600 flex items-center",children:[(0,a.jsx)(c._Hm,{className:"mr-2"}),h]})}),x&&(0,a.jsx)("div",{className:"mt-4 p-2 bg-green-50 border border-green-200 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-green-600 flex items-center",children:[(0,a.jsx)(c.A7C,{className:"mr-2"}),"recorded"===j?"V\xeddeo marcado como gravado com sucesso!":"V\xeddeo enviado com sucesso!"]})})]})}function f(e){let{campaignInfluencerId:t,onStatusChange:r}=e,[s,o]=(0,n.useState)(null),[i,d]=(0,n.useState)(!0),[l,u]=(0,n.useState)(null);return((0,n.useEffect)(()=>{(async()=>{try{d(!0);let e=await m.getVideoByCampaignInfluencerId(t);o(e)}catch(e){u(e.message||"Erro ao carregar v\xeddeo")}finally{d(!1)}})()},[t]),i)?(0,a.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}):l?(0,a.jsx)("div",{className:"p-4 border border-red-200 rounded-lg bg-red-50",children:(0,a.jsx)("p",{className:"text-red-600",children:l})}):s?(0,a.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-4 ".concat((e=>{switch(e){case"pending":return"text-yellow-700 bg-yellow-50 border-yellow-200";case"recorded":return"text-blue-700 bg-blue-50 border-blue-200";case"submitted":return"text-purple-700 bg-purple-50 border-purple-200";case"approved":return"text-green-700 bg-green-50 border-green-200";case"rejected":return"text-red-700 bg-red-50 border-red-200";default:return"text-gray-700 bg-gray-50 border-gray-200"}})(s.status)),children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(e=>{switch(e){case"pending":return(0,a.jsx)(c.BS8,{className:"text-yellow-500"});case"recorded":return(0,a.jsx)(c.HiP,{className:"text-blue-500"});case"submitted":return(0,a.jsx)(c.HVe,{className:"text-purple-500"});case"approved":return(0,a.jsx)(c.A7C,{className:"text-green-500"});case"rejected":return(0,a.jsx)(c._Hm,{className:"text-red-500"});default:return(0,a.jsx)(c.BS8,{className:"text-gray-500"})}})(s.status),(0,a.jsxs)("h3",{className:"text-lg font-medium ml-2",children:["V\xeddeo ",(e=>{switch(e){case"pending":return"Pendente";case"recorded":return"Gravado";case"submitted":return"Enviado";case"approved":return"Aprovado";case"rejected":return"Rejeitado";default:return"Desconhecido"}})(s.status)]})]}),(0,a.jsxs)("div",{className:"text-sm",children:["pending"===s.status&&(0,a.jsx)("p",{children:"Voc\xea ainda n\xe3o gravou um v\xeddeo para esta campanha."}),"recorded"===s.status&&(0,a.jsx)("p",{children:"Voc\xea marcou o v\xeddeo como gravado. Agora fa\xe7a o upload para aprova\xe7\xe3o."}),"submitted"===s.status&&(0,a.jsxs)("p",{children:["V\xeddeo enviado em ",new Date(s.submitted_at||"").toLocaleDateString(),". Aguardando aprova\xe7\xe3o."]}),"approved"===s.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{children:["V\xeddeo aprovado em ",new Date(s.approved_at||"").toLocaleDateString(),"!"]}),s.feedback&&(0,a.jsxs)("p",{className:"mt-2",children:["Feedback: ",s.feedback]})]}),"rejected"===s.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{children:["V\xeddeo rejeitado em ",new Date(s.rejected_at||"").toLocaleDateString(),"."]}),s.feedback&&(0,a.jsxs)("p",{className:"mt-2",children:["Motivo: ",s.feedback]}),(0,a.jsx)("p",{className:"mt-2",children:"Por favor, grave um novo v\xeddeo seguindo as orienta\xe7\xf5es."})]})]})]}),s.video_url&&"rejected"!==s.status&&(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Pr\xe9via do v\xeddeo:"}),(0,a.jsx)("video",{controls:!0,className:"w-full rounded-md",src:s.video_url,poster:"/video-thumbnail.jpg",children:"Seu navegador n\xe3o suporta a reprodu\xe7\xe3o de v\xeddeos."})]})]}):(0,a.jsx)("div",{className:"p-4 border border-gray-200 rounded-lg bg-gray-50",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Nenhum v\xeddeo encontrado para esta campanha."})})}function g(){return(0,a.jsx)(d.A,{requiredRole:"influencer",children:(0,a.jsx)(h,{})})}function h(){let e=(0,s.useParams)(),t=(0,s.useRouter)(),{user:r}=(0,i.A)(),d=(0,o.createClientComponentClient)(),[l,u]=(0,n.useState)(null),[g,h]=(0,n.useState)(null),[v,x]=(0,n.useState)(null),[b,j]=(0,n.useState)(!0),[w,_]=(0,n.useState)(null),y=e.id;(0,n.useEffect)(()=>{(null==r?void 0:r.id)&&y&&N()},[r,y]);let N=async()=>{try{var e;j(!0);let{data:t,error:a}=await d.from("campaigns").select("\n          id,\n          name,\n          description,\n          restaurants(name)\n        ").eq("id",y).single();if(a)throw a;let n={id:t.id,name:t.name,description:t.description,restaurant_name:null===(e=t.restaurants)||void 0===e?void 0:e.name};u(n);let{data:s,error:o}=await d.from("campaign_influencers").select("id, campaign_id, influencer_id, status, video_status").eq("campaign_id",y).eq("influencer_id",null==r?void 0:r.id).single();if(o)throw o;if(h(s),s){let e=await m.getVideoByCampaignInfluencerId(s.id);x(e)}}catch(e){console.error("Erro ao buscar dados da campanha:",e),_(e.message||"Erro ao carregar dados da campanha")}finally{j(!1)}},S=async e=>{v&&x({...v,status:e}),N()};return b?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"})}):w?(0,a.jsxs)("div",{className:"p-6 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-red-700 mb-2",children:"Erro"}),(0,a.jsx)("p",{className:"text-red-600",children:w}),(0,a.jsxs)("button",{onClick:()=>t.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)(c.QVr,{className:"mr-2"}),"Voltar"]})]}):l&&g?(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("button",{onClick:()=>t.back(),className:"inline-flex items-center text-blue-600 hover:text-blue-800",children:[(0,a.jsx)(c.QVr,{className:"mr-2"}),"Voltar"]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(c.HiP,{className:"text-blue-500 mr-3 text-xl"}),(0,a.jsx)("h1",{className:"text-2xl font-bold",children:l.name})]}),l.restaurant_name&&(0,a.jsxs)("p",{className:"text-gray-600 mb-2",children:["Restaurante: ",l.restaurant_name]}),l.description&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-md",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Descri\xe7\xe3o da campanha:"}),(0,a.jsx)("p",{className:"text-gray-700",children:l.description})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Status do V\xeddeo"}),(0,a.jsx)(f,{campaignInfluencerId:g.id,onStatusChange:S})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Gerenciar V\xeddeo"}),(0,a.jsx)(p,{campaignInfluencerId:g.id,videoId:null==v?void 0:v.id,currentStatus:(null==v?void 0:v.status)||"pending",onStatusChange:S})]})]})]}):(0,a.jsxs)("div",{className:"p-6 bg-yellow-50 border border-yellow-200 rounded-lg",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-yellow-700 mb-2",children:"Campanha n\xe3o encontrada"}),(0,a.jsx)("p",{className:"text-yellow-600",children:"N\xe3o foi poss\xedvel encontrar a campanha solicitada ou voc\xea n\xe3o tem acesso a ela."}),(0,a.jsxs)("button",{onClick:()=>t.push("/criador/campanhas"),className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,a.jsx)(c.QVr,{className:"mr-2"}),"Voltar para campanhas"]})]})}},52643:(e,t,r)=>{"use strict";r.d(t,{N:()=>n,b:()=>s});var a=r(73579);let n=(0,a.createClientComponentClient)();function s(){return(0,a.createClientComponentClient)()}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>l});var a=r(12115),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=a.createContext&&a.createContext(n),o=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var a,n,s;a=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function l(e){return t=>a.createElement(u,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:n,size:s,title:d}=e,l=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,o),u=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,l,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),d&&a.createElement("title",null,d),e.children)};return void 0!==s?a.createElement(s.Consumer,null,e=>t(e)):t(n)}},86382:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(95155),n=r(12115),s=r(35695),o=r(40283),i=r(52643),d=r(44032);function c(e,t){if(!e)return!1;if(e instanceof d.lR){console.error("Erro de autentica\xe7\xe3o:",e.message);let r=window.location.pathname;if(r.includes("/login")||r.includes("/registro"))return console.log("J\xe1 estamos na p\xe1gina de login/registro, n\xe3o redirecionando"),!0;let a="/login";return(t&&!t.includes("/login")&&""!==t&&(a+="?redirect=".concat(encodeURIComponent(t))),e instanceof d.lR&&(e.message.includes("Invalid Refresh Token")||e.message.includes("Refresh Token Not Found")))?a+=(a.includes("?")?"&":"?")+"error=invalid_refresh_token":e instanceof d.lR&&(e.message.includes("JWT expired")||e.message.includes("Token expired"))?a+=(a.includes("?")?"&":"?")+"error=expired_session":a+=(a.includes("?")?"&":"?")+"error=auth_error",console.log("Redirecionando para: ".concat(a)),window.location.href=a,!0}return!1}function l(e){let{children:t,requiredRole:r,redirectTo:d="/login"}=e,{session:l,user:u,loading:m,refreshSession:p}=(0,o.A)(),[f,g]=(0,n.useState)(!1),[h,v]=(0,n.useState)(!0),x=(0,s.useRouter)();return((0,n.useEffect)(()=>{(async()=>{if(!m){if(!l||!u){console.log("RouteGuard: No session found, redirecting to login"),window.location.href="".concat(d,"?redirect=").concat(encodeURIComponent(window.location.pathname));return}if(!r){g(!0),v(!1);return}try{let{data:e,error:t}=await i.N.from("profiles").select("role").eq("id",u.id).single();if(t){console.error("RouteGuard: Error fetching user role:",t),await p(),c(t,window.location.pathname);return}if(!e||!e.role){console.error("RouteGuard: User has no role"),window.location.href="".concat(d,"?error=profile_error");return}if(e.role===r)console.log('RouteGuard: User role "'.concat(e.role,'" matches required role "').concat(r,'"')),g(!0);else{console.warn('RouteGuard: User role "'.concat(e.role,'" does not match required role "').concat(r,'"'));let t="/login";"influencer"===e.role?t="/influenciador":"restaurant"===e.role?t="/restaurante-novo":"admin"===e.role&&(t="/admin"),window.location.href=t}}catch(e){console.error("RouteGuard: Unexpected error:",e),c(e,window.location.pathname)}finally{v(!1)}}})()},[l,u,m,r,x,d,p]),m||h)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Verificando autentica\xe7\xe3o..."})]})}):f?(0,a.jsx)(a.Fragment,{children:t}):null}},98481:(e,t,r)=>{Promise.resolve().then(r.bind(r,52642))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,9724,3579,8441,1684,7358],()=>t(98481)),_N_E=e.O()}]);