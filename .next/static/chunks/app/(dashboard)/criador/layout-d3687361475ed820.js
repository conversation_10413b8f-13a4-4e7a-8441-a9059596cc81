(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7234],{4220:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>a});var r=s(95155),t=s(12115);function a(e){let{children:l}=e;return(0,t.useEffect)(()=>{document.body.classList.add("criador-page");{let e=localStorage.getItem("criador_last_visit"),l=Date.now();(!e||l-parseInt(e,10)>6e4)&&localStorage.setItem("criador_last_visit",l.toString())}return()=>{document.body.classList.remove("criador-page")}},[]),(0,r.jsx)("div",{className:"flex flex-col min-h-screen h-screen overflow-hidden",children:(0,r.jsxs)("div",{className:"pt-8 px-4 pb-2 w-full flex flex-col",children:[" ",(0,r.jsx)("div",{className:"w-full h-full flex flex-col flex-grow overflow-hidden",children:l})]})})}},85606:(e,l,s)=>{Promise.resolve().then(s.bind(s,4220))}},e=>{var l=l=>e(e.s=l);e.O(0,[8441,1684,7358],()=>l(85606)),_N_E=e.O()}]);