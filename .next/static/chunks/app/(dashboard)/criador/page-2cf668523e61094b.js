(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3793],{5845:(e,t,r)=>{"use strict";r.d(t,{i:()=>n});var a=r(12115),s=r(39033);function n({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,o]=function({defaultProp:e,onChange:t}){let r=a.useState(e),[n]=r,o=a.useRef(n),l=(0,s.c)(t);return a.useEffect(()=>{o.current!==n&&(l(n),o.current=n)},[n,o,l]),r}({defaultProp:t,onChange:r}),l=void 0!==e,i=l?e:n,c=(0,s.c)(r);return[i,a.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&c(r)}else o(t)},[l,e,o,c])]}},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>n});var a=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}function o(...e){return a.useCallback(n(...e),e)}},9577:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var a=r(95155),s=r(12115),n=r(73579),o=r(40283),l=r(86382),i=r(29911),c=r(60637),d=r(25175),u=r(6874),m=r.n(u);function p(e){let{campaigns:t,loading:r}=e,[n,o]=(0,s.useState)(null),l=e=>e?new Date(e).toLocaleDateString("pt-BR"):"",i=e=>{if(!e)return 0;let t=new Date(e),r=new Date;return Math.ceil((t.getTime()-r.getTime())/864e5)},c=e=>{switch(e){case"accepted":return"Ativa";case"pending":return"Pendente";case"rejected":return"Rejeitada";case"completed":return"Conclu\xedda";default:return"Desconhecido"}},d=e=>{switch(e){case"pending":default:return"Pendente";case"recorded":return"Gravado";case"submitted":return"Enviado";case"approved":return"Aprovado";case"rejected":return"Rejeitado";case"accepted":return"Aceito"}},u=e=>{n===e?o(null):o(e)};return r?(0,a.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"})}):0===t.length?(0,a.jsxs)("div",{className:"text-center py-8 border rounded-lg",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Nenhuma campanha ativa no momento"}),(0,a.jsx)(m(),{href:"/influenciador/campanhas",className:"mt-2 inline-block text-blue-500 hover:underline",children:"Procurar novas campanhas"})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campanha"}),(0,a.jsx)("th",{scope:"col",className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Restaurante"}),(0,a.jsx)("th",{scope:"col",className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{scope:"col",className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"T\xe9rmino"}),(0,a.jsx)("th",{scope:"col",className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pontos"}),(0,a.jsx)("th",{scope:"col",className:"py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7\xf5es"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-100",children:t.map(e=>{var t;return e.campaigns?(0,a.jsxs)(s.Fragment,{children:[(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors ".concat(n===e.id?"bg-gray-50":""),children:[(0,a.jsx)("td",{className:"py-4 pr-4",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.campaigns.name})}),(0,a.jsx)("td",{className:"py-4 pr-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-500",children:null===(t=e.campaigns.restaurants)||void 0===t?void 0:t.name})}),(0,a.jsx)("td",{className:"py-4 pr-4",children:(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[c(e.status)," / ",d(e.video_status)]})}),(0,a.jsxs)("td",{className:"py-4 pr-4",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:l(e.campaigns.end_date)}),(0,a.jsx)("div",{className:"text-xs text-gray-400",children:i(e.campaigns.end_date)>0?"".concat(i(e.campaigns.end_date)," dias"):"Encerrada"})]}),(0,a.jsx)("td",{className:"py-4 pr-4",children:(0,a.jsx)("div",{className:"text-sm font-medium",children:e.total_points||0})}),(0,a.jsxs)("td",{className:"py-4 text-right text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>u(e.id),className:"text-gray-500 hover:text-gray-900 mr-3",children:n===e.id?"Ocultar":"Detalhes"}),(0,a.jsx)(m(),{href:"/influenciador/campanhas/".concat(e.campaigns.id,"/videos"),className:"text-gray-500 hover:text-gray-900 mr-3",children:"V\xeddeo"}),(0,a.jsx)(m(),{href:"/influenciador/campanhas?campaign=".concat(e.campaigns.id),className:"text-gray-500 hover:text-gray-900",children:"Ver"})]})]}),n===e.id&&(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:6,className:"py-4 bg-gray-50",children:(0,a.jsxs)("div",{className:"text-sm text-gray-700 px-4 py-3 rounded-lg bg-white shadow-sm mx-2",children:[(0,a.jsx)("p",{className:"font-medium mb-2",children:"Descri\xe7\xe3o:"}),(0,a.jsx)("p",{className:"mb-4 text-gray-600",children:e.campaigns.description||"Sem descri\xe7\xe3o dispon\xedvel"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Per\xedodo:"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[l(e.campaigns.start_date)," a ",l(e.campaigns.end_date)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Pr\xf3ximos passos:"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[(!e.video_status||"pending"===e.video_status)&&"Grave o v\xeddeo para esta campanha","recorded"===e.video_status&&"Envie o v\xeddeo para aprova\xe7\xe3o","submitted"===e.video_status&&"Aguarde a aprova\xe7\xe3o do v\xeddeo","approved"===e.video_status&&"V\xeddeo aprovado! Publique nas suas redes","rejected"===e.video_status&&"V\xeddeo rejeitado. Grave um novo v\xeddeo","accepted"===e.video_status&&"Campanha aceita! Grave um v\xeddeo para come\xe7ar"]})]})]})]})})})]},e.id):null})})]})})}var f=r(79794);function h(e){let{influencer:t}=e,r=(0,n.createClientComponentClient)(),[o,l]=(0,s.useState)([]),[i,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)({rank:(null==t?void 0:t.rank)||0,total_points:(null==t?void 0:t.total_points)||0,total_earnings:(null==t?void 0:t.total_earnings)||0,percentIncrease:(null==t?void 0:t.percentIncrease)||0});(0,s.useEffect)(()=>{(null==t?void 0:t.id)&&(console.log("HomeContent useEffect triggered with influencerId:",t.id),x(t.id,r),h(t.id,r))},[t,r]);let h=async(e,r)=>{console.log("fetchInfluencerStats called with influencerId:",e);try{let{data:a,error:s}=await r.from("campaign_participants").select("current_rank").eq("profile_id",e);s&&console.error("Erro ao buscar dados de rank do criador:",s);let n=[];a&&(n=a.map(e=>e.current_rank).filter(e=>"number"==typeof e));let o=n.length>0?Math.round(n.reduce((e,t)=>e+t,0)/n.length):(null==t?void 0:t.rank)||0,l=0;try{let{data:t,error:a}=await r.from("points_history").select("points").eq("influencer_id",e);a?console.error("Erro ao buscar hist\xf3rico de pontos para stats:",a):t&&(l=t.reduce((e,t)=>e+(t.points||0),0))}catch(e){console.error("Erro ao acessar points_history para total de pontos em stats:",e)}let i=new Date,c=new Date(i.getFullYear(),i.getMonth(),1).toISOString(),d=new Date(i.getFullYear(),i.getMonth()-1,1).toISOString(),m=new Date(i.getFullYear(),i.getMonth(),0).toISOString(),p=0;try{let{data:t,error:a}=await r.from("points_history").select("points").eq("influencer_id",e).gte("created_at",c);a?console.error("Erro ao buscar pontos do m\xeas atual:",a):t&&(p=t.reduce((e,t)=>e+(t.points||0),0))}catch(e){console.error("Erro ao acessar points_history para pontos do m\xeas atual:",e)}let f=0;try{let{data:t,error:a}=await r.from("points_history").select("points").eq("influencer_id",e).gte("created_at",d).lte("created_at",m);a?console.error("Erro ao buscar pontos do m\xeas anterior:",a):t&&(f=t.reduce((e,t)=>e+(t.points||0),0))}catch(e){console.error("Erro ao acessar points_history para pontos do m\xeas anterior:",e)}let h=0;f>0?h=Math.round((p-f)/f*100):p>0&&(h=100);let x=0;try{let{data:t,error:a}=await r.from("campaign_participants").select("payment_amount").eq("profile_id",e).in("payment_status",["paid","completed"]);a?console.error("Erro ao buscar dados de pagamentos de participantes:",a):t&&(x=t.reduce((e,t)=>e+(parseFloat(t.payment_amount)||0),0))}catch(e){console.error("Erro geral ao buscar dados de pagamentos de participantes:",e)}u({rank:o,total_points:l,total_earnings:x,percentIncrease:h})}catch(e){console.error("Erro geral ao buscar estat\xedsticas do criador:",e)}},x=async(e,t)=>{console.log("fetchCampaigns called with influencerId:",e),c(!0);try{let{data:r,error:a}=await t.from("campaign_participants").select("id, status, campaigns(id, name, description, start_date, end_date, status, restaurant_id, restaurant_profiles(id, business_name))").eq("profile_id",e);if(a){console.error("Erro ao buscar participa\xe7\xf5es em campanhas:",a),l([]),c(!1);return}if(!r||0===r.length){console.log("Nenhuma participa\xe7\xe3o em campanha encontrada para o criador."),l([]),c(!1);return}let s=function(e){return Array.isArray(e)?e.length>0?e[0]:null:e||null},n=r.map(async e=>{let r=null;try{let{data:a,error:s}=await t.from("campaign_videos").select("status").eq("campaign_participant_id",e.id).order("created_at",{ascending:!1}).limit(1).single();s&&"PGRST116"!==s.code?console.error("Erro ao buscar video para participa\xe7\xe3o ".concat(e.id,":"),s):a&&(r=a)}catch(t){console.error("Exce\xe7\xe3o ao buscar video para participa\xe7\xe3o ".concat(e.id,":"),t)}let a=s(e.campaigns);if(!a)return null;let n=s(a.restaurant_profiles),o=a?{id:a.id,name:a.name,description:a.description||void 0,start_date:a.start_date,end_date:a.end_date,status:a.status,restaurant_id:a.restaurant_id,restaurants:n?{id:n.id,name:n.business_name}:null}:null,l=0;try{let{data:r,error:a}=await t.from("points_history").select("points").eq("campaign_participant_id",e.id);a?console.error("Erro ao buscar pontos para participa\xe7\xe3o ".concat(e.id,":"),a):r&&(l=r.reduce((e,t)=>e+(t.points||0),0))}catch(t){console.error("Erro ao acessar points_history para participa\xe7\xe3o ".concat(e.id,":"),t)}return{id:e.id,status:e.status,total_points:l,campaigns:o,video_status:(null==r?void 0:r.status)||"pending"}}),o=(await Promise.all(n)).filter(Boolean);console.log("Campanhas mapeadas:",o),l(o)}catch(e){console.error("Erro geral ao buscar campanhas:",e),l([])}finally{c(!1)}};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-yellow-50 flex items-center justify-center mr-3",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Classifica\xe7\xe3o"})]}),(0,a.jsxs)("div",{className:"flex items-end justify-between",children:[(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("span",{className:"text-3xl font-light text-gray-900",children:d.rank}),(0,a.jsx)("span",{className:"text-xl font-light text-gray-400 ml-1",children:"\xba"})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[d.total_points," pontos"]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,a.jsx)("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"})]})}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Ganhos totais"})]}),(0,a.jsxs)("div",{className:"flex items-end justify-between",children:[(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsxs)("span",{className:"text-3xl font-light text-gray-900",children:["R$ ",d.total_earnings]})}),(0,a.jsxs)("span",{className:"text-sm flex items-center ".concat(d.percentIncrease>=0?"text-green-500":"text-red-500"),children:[d.percentIncrease>=0?(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 mr-1",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414l4.293 4.293A1 1 0 0012 13z",clipRule:"evenodd"})}),Math.abs(d.percentIncrease),"% este m\xeas"]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-3",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-purple-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})})}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Restaurantes"})]}),(0,a.jsxs)("div",{className:"flex items-end justify-between",children:[(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsx)("span",{className:"text-3xl font-light text-gray-900",children:o.length})}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"parcerias ativas"})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"})})}),(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Pr\xf3ximas a\xe7\xf5es"})]}),(0,a.jsx)(f.A,{userId:null==t?void 0:t.id,className:"mt-0",compact:!0})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z"})})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Minhas Campanhas"})]}),(0,a.jsxs)(m(),{href:"/criador/campanhas",className:"text-blue-600 hover:text-blue-700 text-sm flex items-center group",children:["Ver todas",(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1 group-hover:translate-x-0.5 transition-transform",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 5l7 7-7 7"})})]})]}),(0,a.jsx)(p,{campaigns:o.filter(e=>"accepted"===e.status||"active_in_campaign"===e.status),loading:i})]}),(0,a.jsx)("div",{className:"hidden grid grid-cols-1 lg:grid-cols-2 gap-6"})]})}var x=r(48067),g=r(81757),v=r(74489);function y(){return(0,a.jsx)(l.A,{requiredRole:"influencer",children:(0,a.jsx)(j,{})})}function j(){let{user:e}=(0,o.A)(),t=(0,n.createClientComponentClient)(),[r,l]=(0,s.useState)(null),[u,m]=(0,s.useState)(!1),{isOpen:p,openPopup:f,closePopup:y}=(0,v.Y)({popupId:"settings-modal",defaultTabId:"info"});(0,s.useEffect)(()=>{(null==e?void 0:e.id)&&j(e.id)},[e]);let j=async e=>{m(!0);try{let{data:r,error:a}=await t.from("profiles").select("*").eq("id",e).single();if(a){console.error("Error fetching profile data:",a);return}let s=[];try{let{data:r,error:a}=await t.from("campaign_participants").select("\n            id,\n            current_rank,\n            campaign_id\n          ").eq("profile_id",e);a?console.error("Error fetching campaign participant data:",a):s=r||[]}catch(e){console.error("Error checking campaign_participants table:",e)}let n=(null==s?void 0:s.filter(e=>e.current_rank).map(e=>e.current_rank))||[],o=n.length>0?Math.round(n.reduce((e,t)=>e+t,0)/n.length):0,i=0;try{let{data:r,error:a}=await t.from("points_history").select("points").eq("influencer_id",e);a?console.error("Error fetching points_history for influencer stats:",a):r&&(i=r.reduce((e,t)=>e+(t.points||0),0))}catch(e){console.error("Error accessing points_history for influencer stats:",e)}let c=0;try{let{data:r,error:a}=await t.from("payments").select("amount").eq("payment_type","influencer_payout").eq("payout_participant_id",e);!a&&r&&(c=r.reduce((e,t)=>e+(parseFloat(t.amount)||0),0))}catch(e){console.log("Erro ao buscar dados de pagamentos:",e)}let d={id:e,full_name:r.full_name,profile_image_url:r.profile_image_url,total_points:i,rank:o,total_earnings:c};l(d),console.log("Dados do criador carregados do banco de dados:",d)}catch(e){console.error("Error fetching influencer data:",e)}finally{m(!1)}};return(0,a.jsxs)("div",{className:"h-full bg-[#f5f5f5] flex flex-col font-sans relative overflow-hidden",children:[(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 flex justify-between items-center px-6 py-1 bg-[#f5f5f5] z-50 h-10",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.default,{size:"medium",textClassName:"text-xl font-bold"}),u&&(0,a.jsxs)("div",{className:"ml-3 flex items-center text-xs text-gray-500",children:[(0,a.jsxs)("svg",{className:"animate-spin h-3 w-3 mr-1 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{children:"Sincronizando..."})]})]}),(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2",children:(0,a.jsx)(x.A,{})}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(d.A,{trigger:(null==r?void 0:r.name)||(null==e?void 0:e.email)||"Usu\xe1rio",items:[{label:"Configura\xe7\xf5es",icon:(0,a.jsx)(i.Pcn,{className:"text-gray-500"}),onClick:()=>f("info")},{label:"Ajuda",icon:(0,a.jsx)(i.gZZ,{className:"text-gray-500"}),onClick:()=>window.open("https://help.connectcity.com.br","_blank")},{label:"Logout",icon:(0,a.jsx)(i.axc,{className:"text-gray-500"}),onClick:()=>{t.auth.signOut().then(()=>{window.location.href="/login"})}}]})})]}),(0,a.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-white overflow-y-auto flex-1 flex flex-col shadow-md m-1 mt-[2rem]",style:{minHeight:"calc(100vh - 4rem)",maxHeight:"calc(100vh - 4rem)"},children:(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(h,{influencer:r})})}),(0,a.jsx)("a",{href:"https://wa.me/5543991049779",target:"_blank",rel:"noopener noreferrer",className:"fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg flex items-center justify-center",children:(0,a.jsx)(i.EcP,{size:24})}),(0,a.jsx)(g.A,{isOpen:p,onClose:y,onSaved:()=>{j(null==e?void 0:e.id)},userType:"influencer",defaultTab:"info"})]})}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...i,width:s,height:s,stroke:r,strokeWidth:o?24*Number(n)/Number(s):n,className:l("lucide",c),...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:i,...d}=r;return(0,a.createElement)(c,{ref:n,iconNode:t,className:l("lucide-".concat(s(o(e))),"lucide-".concat(e),i),...d})});return r.displayName=o(e),r}},30335:(e,t,r)=>{Promise.resolve().then(r.bind(r,9577))},39033:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var a=r(12115);function s(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}},40283:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,O:()=>i});var a=r(95155),s=r(12115),n=r(52643),o=r(44032);let l=(0,s.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),[u,m]=(0,s.useState)(!0),[p,f]=(0,s.useState)(null),h=async()=>{try{var e;m(!0),f(null),console.log("AuthContext: Refreshing session...");let{data:t,error:r}=await n.N.auth.getSession();if(r){console.error("Erro ao verificar sess\xe3o atual:",r.message),f(r.message),m(!1);return}if(!t.session){console.log("AuthContext: No active session found"),i(null),d(null),m(!1);return}console.log("AuthContext: Active session found"),i(t.session),d((null===(e=t.session)||void 0===e?void 0:e.user)||null)}catch(e){console.error("Erro inesperado ao obter sess\xe3o:",e),f(e.message||"Erro desconhecido")}finally{m(!1)}},x=async()=>{try{m(!0),f(null),console.log("AuthContext: Signing out...");let{error:e}=await n.N.auth.signOut();e&&(console.error("Erro ao fazer logout no Supabase:",e.message),f(e.message)),i(null),d(null),console.log("AuthContext: Logout completed, redirecting to login"),window.location.href="/login"}catch(e){console.error("Erro inesperado ao fazer logout:",e),f(e.message||"Erro desconhecido"),i(null),d(null),window.location.href="/login"}finally{m(!1)}};return(0,s.useEffect)(()=>{h();let{data:e}=n.N.auth.onAuthStateChange(async(e,t)=>{console.log("Auth state changed:",e),"SIGNED_IN"===e?(console.log("User signed in, updating session"),i(t),d((null==t?void 0:t.user)||null)):"SIGNED_OUT"===e?(console.log("User signed out, clearing session"),i(null),d(null)):"TOKEN_REFRESHED"===e?(console.log("Token refreshed, updating session"),i(t),d((null==t?void 0:t.user)||null)):"USER_UPDATED"===e&&(console.log("User updated, updating session"),i(t),d((null==t?void 0:t.user)||null)),m(!1)});return()=>{e.subscription.unsubscribe()}},[]),(0,s.useEffect)(()=>{let e=e=>{(e.error instanceof o.lR||e.message&&(e.message.includes("Invalid Refresh Token")||e.message.includes("JWT expired")||e.message.includes("not authenticated")))&&(console.error("Erro de autentica\xe7\xe3o interceptado:",e),h())};return window.addEventListener("error",e),()=>{window.removeEventListener("error",e)}},[]),(0,a.jsx)(l.Provider,{value:{session:r,user:c,loading:u,error:p,signOut:x,refreshSession:h},children:t})}function c(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth deve ser usado dentro de um AuthProvider");return e}},46081:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,q:()=>n});var a=r(12115),s=r(95155);function n(e,t){let r=a.createContext(t),n=e=>{let{children:t,...n}=e,o=a.useMemo(()=>n,Object.values(n));return(0,s.jsx)(r.Provider,{value:o,children:t})};return n.displayName=e+"Provider",[n,function(s){let n=a.useContext(r);if(n)return n;if(void 0!==t)return t;throw Error(`\`${s}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],n=()=>{let t=r.map(e=>a.createContext(e));return function(r){let s=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return n.scopeName=e,[function(t,n){let o=a.createContext(n),l=r.length;r=[...r,n];let i=t=>{let{scope:r,children:n,...i}=t,c=r?.[e]?.[l]||o,d=a.useMemo(()=>i,Object.values(i));return(0,s.jsx)(c.Provider,{value:d,children:n})};return i.displayName=t+"Provider",[i,function(r,s){let i=s?.[e]?.[l]||o,c=a.useContext(i);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((t,{useScope:r,scopeName:a})=>{let s=r(e)[`__scope${a}`];return{...t,...s}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}(n,...t)]}},48067:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(6874),n=r.n(s),o=r(35695),l=r(29911);function i(){let e=(0,o.usePathname)();return(0,a.jsxs)("div",{className:"flex items-center navigation-tabs",style:{backgroundColor:"#f5f5f5"},children:[(0,a.jsx)(n(),{href:"/criador",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-2 text-sm font-medium border-b-2 !bg-[#f5f5f5] ".concat("/criador"===e?"border-gray-800 text-gray-800":"border-[#f5f5f5] text-gray-500 hover:text-gray-700"),children:(0,a.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,a.jsx)(l.rQ8,{className:"mr-1.5 bg-[#f5f5f5] text-xs"}),(0,a.jsx)("span",{className:"bg-[#f5f5f5]",children:"Home"})]})}),(0,a.jsx)(n(),{href:"/criador/campanhas",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-2 text-sm font-medium border-b-2 !bg-[#f5f5f5] ".concat(e.includes("/criador/campanhas")?"border-gray-800 text-gray-800":"border-[#f5f5f5] text-gray-500 hover:text-gray-700"),children:(0,a.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,a.jsx)(l.sdT,{className:"mr-1.5 bg-[#f5f5f5] text-xs"}),(0,a.jsx)("span",{className:"bg-[#f5f5f5]",children:"Campanhas"})]})})]})}},52596:(e,t,r)=>{"use strict";function a(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,a,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t){if(Array.isArray(t)){var n=t.length;for(r=0;r<n;r++)t[r]&&(a=e(t[r]))&&(s&&(s+=" "),s+=a)}else for(a in t)t[a]&&(s&&(s+=" "),s+=a)}return s}(e))&&(a&&(a+=" "),a+=t);return a}r.d(t,{$:()=>a})},52712:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});var a=r(12115),s=globalThis?.document?a.useLayoutEffect:()=>{}},60637:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(95155),s=r(6874),n=r.n(s);function o(e){let{className:t="",textClassName:r="text-xl font-semibold",showText:s=!0,size:o="medium",href:l="/"}=e;return l?(0,a.jsx)(n(),{href:l,className:"flex items-center ".concat(t),children:(0,a.jsx)("span",{className:r,children:"crIAdores"})}):(0,a.jsx)("div",{className:"flex items-center ".concat(t),children:(0,a.jsx)("span",{className:r,children:"crIAdores"})})}},85185:(e,t,r)=>{"use strict";function a(e,t,{checkForDefaultPrevented:r=!0}={}){return function(a){if(e?.(a),!1===r||!a.defaultPrevented)return t?.(a)}}r.d(t,{m:()=>a})},86382:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(95155),s=r(12115),n=r(35695),o=r(40283),l=r(52643),i=r(44032);function c(e,t){if(!e)return!1;if(e instanceof i.lR){console.error("Erro de autentica\xe7\xe3o:",e.message);let r=window.location.pathname;if(r.includes("/login")||r.includes("/registro"))return console.log("J\xe1 estamos na p\xe1gina de login/registro, n\xe3o redirecionando"),!0;let a="/login";return(t&&!t.includes("/login")&&""!==t&&(a+="?redirect=".concat(encodeURIComponent(t))),e instanceof i.lR&&(e.message.includes("Invalid Refresh Token")||e.message.includes("Refresh Token Not Found")))?a+=(a.includes("?")?"&":"?")+"error=invalid_refresh_token":e instanceof i.lR&&(e.message.includes("JWT expired")||e.message.includes("Token expired"))?a+=(a.includes("?")?"&":"?")+"error=expired_session":a+=(a.includes("?")?"&":"?")+"error=auth_error",console.log("Redirecionando para: ".concat(a)),window.location.href=a,!0}return!1}function d(e){let{children:t,requiredRole:r,redirectTo:i="/login"}=e,{session:d,user:u,loading:m,refreshSession:p}=(0,o.A)(),[f,h]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!0),v=(0,n.useRouter)();return((0,s.useEffect)(()=>{(async()=>{if(!m){if(!d||!u){console.log("RouteGuard: No session found, redirecting to login"),window.location.href="".concat(i,"?redirect=").concat(encodeURIComponent(window.location.pathname));return}if(!r){h(!0),g(!1);return}try{let{data:e,error:t}=await l.N.from("profiles").select("role").eq("id",u.id).single();if(t){console.error("RouteGuard: Error fetching user role:",t),await p(),c(t,window.location.pathname);return}if(!e||!e.role){console.error("RouteGuard: User has no role"),window.location.href="".concat(i,"?error=profile_error");return}if(e.role===r)console.log('RouteGuard: User role "'.concat(e.role,'" matches required role "').concat(r,'"')),h(!0);else{console.warn('RouteGuard: User role "'.concat(e.role,'" does not match required role "').concat(r,'"'));let t="/login";"influencer"===e.role?t="/influenciador":"restaurant"===e.role?t="/restaurante-novo":"admin"===e.role&&(t="/admin"),window.location.href=t}}catch(e){console.error("RouteGuard: Unexpected error:",e),c(e,window.location.pathname)}finally{g(!1)}}})()},[d,u,m,r,v,i,p]),m||x)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Verificando autentica\xe7\xe3o..."})]})}):f?(0,a.jsx)(a.Fragment,{children:t}):null}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>o});var a=r(12115),s=r(6101),n=r(95155);function o(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var o;let e,l;let i=(o=r,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=(...e)=>{n(...e),s(...e)}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(c.ref=t?(0,s.t)(t,i):i),a.cloneElement(r,c)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...o}=e,l=a.Children.toArray(s),i=l.find(d);if(i){let e=i.props.children,s=l.map(t=>t!==i?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...o,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...o,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=o("Slot"),i=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function d(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,9724,3579,9688,6874,8264,535,7127,5532,7292,7326,5593,5999,5358,9794,8441,1684,7358],()=>t(30335)),_N_E=e.O()}]);