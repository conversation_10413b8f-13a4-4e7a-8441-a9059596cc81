(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3478],{9330:(e,a,t)=>{"use strict";async function s(e,a){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];t&&console.log("getRestaurantData: Starting restaurant data retrieval for user ID:",a);try{t&&console.log("getRestaurantData: Checking restaurant_profiles table...");let{data:s,error:r}=await e.from("restaurant_profiles").select("*").eq("id",a).maybeSingle();r&&t&&console.error("getRestaurantData: Error fetching from restaurant_profiles:",r),t&&console.log("getRestaurantData: Checking restaurants table by owner_id...");let{data:n,error:l}=await e.from("restaurants").select("*").eq("owner_id",a).maybeSingle();l&&t&&console.error("getRestaurantData: Error fetching from restaurants by owner_id:",l),t&&console.log("getRestaurantData: Checking restaurants table by direct id match...");let{data:i,error:o}=await e.from("restaurants").select("*").eq("id",a).maybeSingle();o&&t&&console.error("getRestaurantData: Error fetching from restaurants by id:",o);let c=null;if(s?(t&&console.log("getRestaurantData: Found data in restaurant_profiles"),c={...s,name:s.business_name||s.name,source:"restaurant_profiles"}):n?(t&&console.log("getRestaurantData: Found data in restaurants by owner_id"),c={...n,source:"restaurants"}):i&&(t&&console.log("getRestaurantData: Found data in restaurants by direct id match"),c={...i,source:"restaurants"}),c&&"restaurants"===c.source&&!s){t&&console.log("getRestaurantData: Attempting to combine data from both tables");let{data:s,error:r}=await e.from("profiles").select("*").eq("id",a).maybeSingle();r&&t&&console.error("getRestaurantData: Error fetching profile:",r),s&&(t&&console.log("getRestaurantData: Found profile data, combining with restaurant data"),c={...c,...s,name:c.name||s.full_name,source:"combined"})}return t&&(c?console.log("getRestaurantData: Final restaurant data:",c):console.log("getRestaurantData: No restaurant data found for user ID:",a)),c}catch(e){return console.error("getRestaurantData: Unexpected error:",e),null}}async function r(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Novo Restaurante",s=arguments.length>3&&void 0!==arguments[3]&&arguments[3];s&&console.log("createRestaurantProfile: Creating new restaurant profile for user ID:",a);try{let{data:r,error:n}=await e.from("profiles").select("full_name, role").eq("id",a).maybeSingle();if(n)return console.error("createRestaurantProfile: Error fetching user profile:",n),null;if(r&&"restaurant"!==r.role){s&&console.log("createRestaurantProfile: Updating user role to restaurant");let{error:t}=await e.from("profiles").update({role:"restaurant"}).eq("id",a);t&&console.error("createRestaurantProfile: Error updating user role:",t)}let l={id:a,business_name:(null==r?void 0:r.full_name)||t,city:"Cidade n\xe3o especificada",state:"Estado n\xe3o especificado",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};s&&console.log("createRestaurantProfile: Creating restaurant profile with data:",l);let{data:i,error:o}=await e.from("restaurant_profiles").insert(l).select().single();if(o){console.error("createRestaurantProfile: Error creating restaurant profile. Message:",o.message,"Details:",o.details,"Hint:",o.hint,"Code:",o.code,"Full Error:",JSON.stringify(o,null,2)),s&&console.log("createRestaurantProfile: Attempting to create record in restaurants table instead");let n={owner_id:a,name:(null==r?void 0:r.full_name)||t},{data:l,error:i}=await e.from("restaurants").insert(n).select().single();if(i)return console.error("createRestaurantProfile: Error creating restaurant record. Message:",i.message,"Details:",i.details,"Hint:",i.hint,"Code:",i.code,"Full Error:",JSON.stringify(i,null,2)),null;return s&&console.log("createRestaurantProfile: Created restaurant record:",l),{...l,source:"restaurants"}}return s&&console.log("createRestaurantProfile: Created restaurant profile:",i),{...i,name:i.business_name,source:"restaurant_profiles"}}catch(e){return console.error("createRestaurantProfile: Unexpected error:",e),null}}t.d(a,{E:()=>s,J:()=>r})},14900:(e,a,t)=>{"use strict";t.d(a,{A:()=>c});var s=t(95155),r=t(12115);function n(e){let{icon:a,value:t,change:r,isPositive:n=!0,description:l,className:i=""}=e;return(0,s.jsxs)("div",{className:"p-2 flex flex-col items-center ".concat(i),children:[(0,s.jsx)("div",{className:"text-base mb-1",children:a}),l&&(0,s.jsx)("div",{className:"text-xs text-gray-700 mb-1 font-medium tracking-wide",children:l}),(0,s.jsx)("div",{className:"text-base font-semibold tracking-wide",children:t}),r&&(0,s.jsxs)("div",{className:"text-xs mt-1 ".concat(n?"text-green-600":"text-red-500"," font-medium tracking-wide"),children:[n?"↑":"↓"," ",r]})]})}var l=t(29911),i=t(59432),o=t(75740);function c(e){var a,t;let{restaurantId:c,onCardClick:d,compact:m=!1}=e,[x,u]=(0,r.useState)(null),[g,h]=(0,r.useState)(!0),[p,f]=(0,r.useState)(null),v=e=>null===e?"N/A":e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),j=(e,a)=>{if(null===e||null===a||0===a)return;let t=e-a;return Math.abs(t)>=1e3?v(Math.abs(t)):Math.abs(t).toString()};return(0,r.useEffect)(()=>{(async function(){if(!c){h(!1);return}h(!0),f(null);try{let e=await (0,o.yk)(c);u(e)}catch(e){console.error("Erro ao carregar presen\xe7a digital:",e),f("N\xe3o foi poss\xedvel carregar os dados de presen\xe7a digital.")}finally{h(!1)}})()},[c]),m?(0,s.jsx)("div",{className:"cursor-pointer",onClick:d,children:g?(0,s.jsx)("div",{className:"flex justify-center items-center h-16",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"})}):p?(0,s.jsx)("div",{className:"text-red-500 text-xs",children:p}):x?(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"flex items-center mb-1",children:[(0,s.jsx)(l.ao$,{className:"text-pink-500 h-3 w-3 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-gray-600",children:"Instagram"})]}),(0,s.jsx)("div",{className:"text-sm font-medium",children:v(x.instagram_followers||0)})]}),(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"flex items-center mb-1",children:[(0,s.jsx)(l.DSS,{className:"text-blue-500 h-3 w-3 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-gray-600",children:"Google"})]}),(0,s.jsx)("div",{className:"text-sm font-medium",children:(null===(a=x.google_rating)||void 0===a?void 0:a.toFixed(1))||"N/A"})]}),(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"flex items-center mb-1",children:[(0,s.jsx)(i.e$j,{className:"text-green-600 h-3 w-3 mr-1"}),(0,s.jsx)("span",{className:"text-xs text-gray-600",children:"TripAdvisor"})]}),(0,s.jsxs)("div",{className:"text-sm font-medium",children:["#",x.tripadvisor_ranking||"N/A"]})]})]}):(0,s.jsx)("div",{className:"text-gray-500 text-xs",children:"Sem dados dispon\xedveis."})}):(0,s.jsxs)("div",{className:"bg-white rounded-xl p-5 shadow-sm cursor-pointer transition-all h-full hover:shadow-md hover:bg-gray-50",onClick:d,children:[(0,s.jsx)("h3",{className:"text-base font-medium text-gray-700 mb-2",children:"Presen\xe7a Digital"}),g?(0,s.jsx)("div",{className:"flex justify-center items-center h-32",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):p?(0,s.jsx)("div",{className:"text-red-500 text-sm",children:p}):x?(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,s.jsx)(n,{icon:(0,s.jsx)(l.ao$,{className:"text-pink-500"}),description:"Seguidores",value:v(x.instagram_followers||0),change:j(x.instagram_followers,x.instagram_previous_followers),isPositive:(x.instagram_followers||0)>(x.instagram_previous_followers||0)}),(0,s.jsx)(n,{icon:(0,s.jsx)(l.DSS,{className:"text-blue-500"}),description:"Avalia\xe7\xe3o",value:(null===(t=x.google_rating)||void 0===t?void 0:t.toFixed(1))||"N/A",change:j(x.google_rating,x.google_previous_rating),isPositive:(x.google_rating||0)>(x.google_previous_rating||0)}),(0,s.jsx)(n,{icon:(0,s.jsx)(i.e$j,{className:"text-green-600"}),description:"Ranking",value:"#".concat(x.tripadvisor_ranking||"N/A"),change:x.tripadvisor_total_places?"de ".concat(x.tripadvisor_total_places):void 0,isPositive:!0})]}):(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"Nenhum dado de presen\xe7a digital dispon\xedvel."})]})}},15190:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var s=t(95155);t(12115);var r=t(29911);function n(e){let{ticketMedio:a,ticketMedioAnterior:t,novosClientes:n,cac:l,cacComparativo:i,faturamentoAtribuido:o,onCardClick:c,compact:d=!1}=e,m=a-t,x=m/t*100,u=m>=0,g=e=>e.toLocaleString("pt-BR",{style:"currency",currency:"BRL",minimumFractionDigits:0,maximumFractionDigits:0});return d?(0,s.jsxs)("div",{className:"cursor-pointer",onClick:c,children:[(0,s.jsx)("div",{className:"flex justify-between items-center mb-2",children:(0,s.jsxs)("div",{className:"flex items-baseline",children:[(0,s.jsx)("span",{className:"text-sm font-medium mr-2",children:g(a)}),(0,s.jsxs)("span",{className:"text-xs ".concat(u?"text-green-600":"text-red-500"),children:[u?(0,s.jsx)(r.uCC,{className:"inline mr-0.5 h-2 w-2"}):(0,s.jsx)(r.$TP,{className:"inline mr-0.5 h-2 w-2"}),Math.abs(x).toFixed(1),"%"]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-0.5 flex items-center",children:[(0,s.jsx)(r.YXz,{className:"mr-1 text-blue-500",size:8}),(0,s.jsx)("span",{children:"Novos Clientes"})]}),(0,s.jsx)("div",{className:"text-sm font-medium",children:n})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-0.5 flex items-center",children:[(0,s.jsx)(r.MxO,{className:"mr-1 text-blue-500",size:8}),(0,s.jsx)("span",{children:"CAC"})]}),(0,s.jsx)("div",{className:"text-sm font-medium",children:g(l)})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-100 pt-1",children:[(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"Faturamento"}),(0,s.jsx)("div",{className:"text-sm font-bold text-green-600",children:g(o)})]})]}):(0,s.jsxs)("div",{className:"bg-white rounded-xl p-3 shadow-sm cursor-pointer transition-all h-full hover:shadow-md hover:bg-gray-50 flex flex-col",onClick:c,children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h3",{className:"text-base font-medium text-gray-700",children:"Impacto no Faturamento"}),(0,s.jsx)(r.YYR,{className:"text-green-600"})]}),(0,s.jsxs)("div",{className:"flex flex-col justify-between flex-grow",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Ticket M\xe9dio"}),(0,s.jsxs)("div",{className:"text-xs font-medium ".concat(u?"text-green-600":"text-red-500"),children:[u?(0,s.jsx)(r.uCC,{className:"inline mr-1"}):(0,s.jsx)(r.$TP,{className:"inline mr-1"}),Math.abs(x).toFixed(1),"%"]})]}),(0,s.jsxs)("div",{className:"flex items-baseline",children:[(0,s.jsx)("span",{className:"text-2xl font-bold mr-2",children:g(a)}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["vs ",g(t)]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-1 flex items-center",children:[(0,s.jsx)(r.YXz,{className:"mr-1 text-blue-500",size:10}),(0,s.jsx)("span",{children:"Novos Clientes"})]}),(0,s.jsx)("div",{className:"font-semibold",children:n})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-xs text-gray-500 mb-1 flex items-center",children:[(0,s.jsx)(r.MxO,{className:"mr-1 text-blue-500",size:10}),(0,s.jsx)("span",{children:"CAC"})]}),(0,s.jsx)("div",{className:"font-semibold",children:g(l)})]})]})]}),(0,s.jsxs)("div",{className:"pt-1 mt-1 border-t border-gray-100",children:[(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"Faturamento Atribu\xeddo"}),(0,s.jsx)("div",{className:"text-lg font-bold text-green-600 truncate",children:g(o)})]})]})]})}},21834:(e,a,t)=>{"use strict";t.d(a,{A:()=>m});var s=t(95155),r=t(12115),n=t(73579),l=t(17895),i=t(8469),o=t(27906),c=t(29911),d=t(62972);function m(e){let{plan:a,totalAvailable:t,used:m,restaurantId:x,startDate:u,endDate:g,onViewAllRatingsClick:h,onUpgradeClick:p,compact:f=!1}=e,[v,j]=(0,r.useState)(0),[b,N]=(0,r.useState)(0),[y,w]=(0,r.useState)(!1),_=(0,n.createClientComponentClient)(),C=()=>{w(!0)};return(0,r.useEffect)(()=>{(async function(){if(x)try{let{data:e,error:a}=await _.from("restaurant_ratings_summary").select("average_rating, total_ratings").eq("restaurant_id",x).single();!a&&e&&(j(e.average_rating),N(e.total_ratings))}catch(e){console.error("Erro ao carregar estat\xedsticas de avalia\xe7\xe3o:",e)}})()},[x,_]),(0,s.jsxs)(s.Fragment,{children:[f?(0,s.jsxs)("div",{className:"cursor-pointer",onClick:C,children:[(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Utiliza\xe7\xe3o"}),(0,s.jsx)(i.A,{plan:a})]}),(0,s.jsx)(l.A,{value:m,max:t,color:"#407662",className:"mb-1"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[m," de ",t," (",Math.round(m/t*100),"%)"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("div",{className:"flex items-center",children:b>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-yellow-50 px-1.5 py-0.5 rounded-md mr-1.5",children:(0,s.jsx)("span",{className:"text-sm font-bold text-yellow-600",children:v.toFixed(1)})}),(0,s.jsx)(o.A,{value:v,readOnly:!0,size:"xs"}),(0,s.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",b,")"]})]}):(0,s.jsx)("p",{className:"text-xs text-gray-500 italic",children:"Sem avalia\xe7\xf5es"})}),(0,s.jsxs)("button",{className:"text-xs text-blue-600 hover:underline flex items-center",onClick:e=>{e.stopPropagation(),h&&h()},children:["Ver todas",(0,s.jsx)(c.X6T,{className:"ml-1 h-2 w-2"})]})]})]}):(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-sm h-full p-4 cursor-pointer hover:shadow-md transition-shadow",onClick:C,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,s.jsx)("h3",{className:"text-base font-medium text-gray-700",children:"Seu Plano"}),(0,s.jsx)(i.A,{plan:a})]}),(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Utiliza\xe7\xe3o"}),(0,s.jsx)(l.A,{value:m,max:t,color:"#407662",className:"mb-1"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[m," de ",t," (",Math.round(m/t*100),"%)"]})]}),(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(c.gt3,{className:"text-yellow-500 mr-1"}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Avalia\xe7\xf5es"})]}),(0,s.jsxs)("button",{className:"text-xs text-blue-600 hover:underline flex items-center",onClick:e=>{e.stopPropagation(),h&&h()},children:["Ver todas",(0,s.jsx)(c.X6T,{className:"ml-1 h-3 w-3"})]})]}),b>0?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"bg-yellow-50 px-2 py-1 rounded-md mr-2",children:(0,s.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:v.toFixed(1)})}),(0,s.jsx)(o.A,{value:v,readOnly:!0,size:"sm"}),(0,s.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",b,")"]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 italic",children:"Nenhuma avalia\xe7\xe3o recebida ainda"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"As avalia\xe7\xf5es aparecer\xe3o aqui quando seu restaurante for avaliado"})]})]})]}),(0,s.jsx)(d.A,{isOpen:y,onClose:()=>{w(!1)},plan:a,totalAvailable:t,used:m,startDate:u,endDate:g,restaurantId:x,onUpgradeClick:p,onViewAllRatingsClick:h})]})}},35638:(e,a,t)=>{Promise.resolve().then(t.bind(t,69081))},50996:(e,a,t)=>{"use strict";t.d(a,{P:()=>r});var s=t(12115);function r(e){let[a,t]=(0,s.useState)(!1),[r,n]=(0,s.useState)(e.defaultTitle||""),[l,i]=(0,s.useState)(e.defaultContent||null),[o,c]=(0,s.useState)(e.defaultSize||"medium");return{isOpen:a,title:r,content:l,size:o,openPopup:e=>{n(e.title),i(e.content),e.size&&c(e.size),t(!0)},closePopup:()=>{t(!1)},popupId:e.popupId}}},51493:(e,a,t)=>{"use strict";t.d(a,{C:()=>l,y:()=>i});var s=t(95155),r=t(12115);let n=(0,r.createContext)(void 0),l=e=>{let{children:a}=e,[t,l]=(0,r.useState)("crIAdores"),[i,o]=(0,r.useState)(null);return(0,s.jsx)(n.Provider,{value:{appBarTitle:t,setAppBarTitle:l,appBarTrailing:i,setAppBarTrailing:o},children:a})},i=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAppBar must be used within an AppBarProvider");return e}},58502:(e,a,t)=>{"use strict";t.d(a,{$v:()=>r,WP:()=>n});var s=t(73579);async function r(e){if(!e)return 0;let a=(0,s.createClientComponentClient)();try{let{data:t,error:s}=await a.from("campaign_influencers").select("id").eq("campaign_id",e).eq("status","accepted");if(s)return console.log("Erro ao buscar influenciadores para campanha ".concat(e,": ").concat(s.message)),0;return(null==t?void 0:t.length)||0}catch(a){return console.log("Erro ao processar contagem de influenciadores para campanha ".concat(e)),0}}async function n(e){if(!e)return 0;let a=(0,s.createClientComponentClient)();try{let{data:t,error:s}=await a.from("campaign_influencers").select("total_points").eq("campaign_id",e).eq("status","accepted");if(s)return console.log("Erro ao buscar pontos para campanha ".concat(e,": ").concat(s.message)),0;let r=(null==t?void 0:t.reduce((e,a)=>e+(a.total_points||0),0))||0;return r>0?r:1500}catch(a){return console.log("Erro ao processar pontos para campanha ".concat(e)),1500}}},69081:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>S});var s=t(95155),r=t(12115),n=t(35695),l=t(73579),i=t(29911),o=t(50996),c=t(74489),d=t(9330),m=t(51493),x=t(25175),u=t(81757),g=t(21834),h=t(14900),p=t(15190),f=t(64065),v=t(32502);function j(e){let{title:a,icon:t,children:n,defaultOpen:l=!1}=e,[o,c]=(0,r.useState)(l);return(0,s.jsxs)("div",{className:"mb-6 border border-gray-200 rounded-lg overflow-hidden",children:[(0,s.jsxs)("button",{onClick:()=>c(!o),className:"w-full flex justify-between items-center p-4 bg-gray-50 hover:bg-gray-100 transition-colors text-left",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-green-600 mr-3",children:t}),(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-800",children:a})]}),o?(0,s.jsx)(i.Ucs,{className:"text-gray-500"}):(0,s.jsx)(i.Vr3,{className:"text-gray-500"})]}),o&&(0,s.jsx)("div",{className:"p-4 bg-white",children:n})]})}function b(e){let{restaurantId:a,ticketMedio:t,ticketMedioAnterior:n,novosClientes:o,cac:c,cacComparativos:d,faturamentoAtribuido:m,onSave:x}=e,u=(0,l.createClientComponentClient)(),[g,h]=(0,r.useState)(!1),[p,v]=(0,r.useState)([]),[b,N]=(0,r.useState)(t),[y,w]=(0,r.useState)(o),[_,C]=(0,r.useState)(c),k=t-n,A=k>=0,S=e=>e.toLocaleString("pt-BR",{style:"currency",currency:"BRL",minimumFractionDigits:0,maximumFractionDigits:0});(0,r.useEffect)(()=>{(async()=>{if(a){h(!0);try{let{data:e,error:t}=await u.from("campaigns").select("id, name").eq("restaurant_id",a).eq("status","active").order("created_at",{ascending:!1});if(t)console.error("Erro ao buscar campanhas:",t);else if(e){let a=e.map((e,a)=>({id:e.id,nome:e.name,faturamentoAtribuido:Math.floor(1e4*Math.random())+5e3,novosClientes:Math.floor(50*Math.random())+10,cac:Math.floor(30*Math.random())+5,cor:a%2==0?"#4ade80":"#60a5fa"}));v(a)}}catch(e){console.error("Erro ao buscar dados de campanhas:",e)}finally{h(!1)}}})()},[a,u]);let D={labels:p.map(e=>e.nome),datasets:[{label:"Faturamento Atribu\xeddo",data:p.map(e=>e.faturamentoAtribuido),backgroundColor:p.map(e=>e.cor||"#4ade80"),borderColor:"rgba(0, 0, 0, 0.1)",borderWidth:1}]},R={responsive:!0,plugins:{legend:{display:!1},tooltip:{callbacks:{label:function(e){return S(e.raw)}}}},scales:{y:{ticks:{callback:function(e){return S(e)}}}}},P={labels:d.map(e=>e.canal),datasets:[{label:"Custo de Aquisi\xe7\xe3o por Cliente",data:d.map(e=>e.valor),backgroundColor:["#4ade80","#f87171","#60a5fa","#fbbf24"],borderColor:"rgba(0, 0, 0, 0.1)",borderWidth:1}]},E=()=>{x&&x({ticketMedio:b,novosClientes:y,cac:_})};return g?(0,s.jsxs)("div",{className:"bg-white rounded-xl p-8 flex flex-col items-center justify-center min-h-[300px]",children:[(0,s.jsx)(i.hW,{className:"text-green-600 text-3xl animate-spin mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Carregando dados de impacto no faturamento..."})]}):(0,s.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden max-w-3xl mx-auto",children:[(0,s.jsxs)("div",{className:"bg-gray-100 p-6 border-b",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:"Impacto no Faturamento"})}),(0,s.jsx)(i.YYR,{className:"text-green-600 text-xl"})]}),(0,s.jsx)("p",{className:"text-gray-600",children:"An\xe1lise detalhada do impacto financeiro das suas campanhas de marketing com influenciadores."})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-green-800 mb-2",children:"Resumo de Impacto"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"bg-white p-3 rounded shadow-sm",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Faturamento Atribu\xeddo"}),(0,s.jsx)("div",{className:"text-xl font-bold text-green-600",children:S(m)})]}),(0,s.jsxs)("div",{className:"bg-white p-3 rounded shadow-sm",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"Novos Clientes"}),(0,s.jsx)("div",{className:"text-xl font-bold text-blue-600",children:o})]}),(0,s.jsxs)("div",{className:"bg-white p-3 rounded shadow-sm",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-1",children:"CAC M\xe9dio"}),(0,s.jsx)("div",{className:"text-xl font-bold text-purple-600",children:S(c)})]})]})]}),(0,s.jsx)(j,{title:"Ticket M\xe9dio",icon:(0,s.jsx)(i.wJQ,{size:18}),defaultOpen:!0,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:S(t)}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Ticket m\xe9dio atual"})]}),(0,s.jsxs)("div",{className:"text-sm font-medium ".concat(A?"text-green-600":"text-red-500"," flex items-center"),children:[A?(0,s.jsx)(i.uCC,{className:"mr-1"}):(0,s.jsx)(i.$TP,{className:"mr-1"}),Math.abs(k/n*100).toFixed(1),"%",(0,s.jsx)("span",{className:"text-gray-500 ml-1",children:"vs per\xedodo anterior"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h5",{className:"font-medium mb-2",children:"Atualizar Ticket M\xe9dio"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"number",value:b,onChange:e=>N(Number(e.target.value)),className:"border rounded px-3 py-2 w-full",min:"0",step:"1"}),(0,s.jsx)("button",{onClick:E,className:"ml-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition",children:"Salvar"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Atualize o ticket m\xe9dio com base nos dados mais recentes do seu sistema de PDV."})]})]})}),(0,s.jsx)(j,{title:"Faturamento por Campanha",icon:(0,s.jsx)(i.MxO,{size:18}),defaultOpen:!0,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-64",children:p.length>0?(0,s.jsx)(f.yP,{data:D,options:R}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full bg-gray-50 rounded-lg",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Nenhuma campanha ativa encontrada"})})}),p.length>0&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h5",{className:"font-medium mb-2",children:"Detalhamento por Campanha"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campanha"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Faturamento"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Novos Clientes"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"CAC"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-4 py-2 whitespace-nowrap",children:(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.nome})}),(0,s.jsx)("td",{className:"px-4 py-2 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-gray-900",children:S(e.faturamentoAtribuido)})}),(0,s.jsx)("td",{className:"px-4 py-2 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-gray-900",children:e.novosClientes})}),(0,s.jsx)("td",{className:"px-4 py-2 whitespace-nowrap",children:(0,s.jsx)("div",{className:"text-gray-900",children:S(e.cac)})})]},e.id))})]})})]})]})}),(0,s.jsx)(j,{title:"Comparativo de CAC",icon:(0,s.jsx)(i.YXz,{size:18}),defaultOpen:!0,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-64",children:(0,s.jsx)(f.yP,{data:P,options:{...R,indexAxis:"y"}})}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h5",{className:"font-medium mb-2",children:"Atualizar CAC"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"number",value:_,onChange:e=>C(Number(e.target.value)),className:"border rounded px-3 py-2 w-full",min:"0",step:"1"}),(0,s.jsx)("button",{onClick:E,className:"ml-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition",children:"Salvar"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Atualize o CAC (Custo de Aquisi\xe7\xe3o por Cliente) com base nos seus dados mais recentes."})]})]})}),(0,s.jsx)(j,{title:"Novos Clientes",icon:(0,s.jsx)(i.IoZ,{size:18}),children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:o}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:"Novos clientes atribu\xeddos \xe0s campanhas"})]})}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h5",{className:"font-medium mb-2",children:"Atualizar Contagem de Novos Clientes"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"number",value:y,onChange:e=>w(Number(e.target.value)),className:"border rounded px-3 py-2 w-full",min:"0",step:"1"}),(0,s.jsx)("button",{onClick:E,className:"ml-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition",children:"Salvar"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Atualize a contagem de novos clientes com base nos dados do seu sistema de CRM ou PDV."})]}),(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-800 mb-2",children:"Dica de Rastreamento"}),(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Para melhorar a precis\xe3o do rastreamento de novos clientes, considere:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-sm text-blue-700 mt-2",children:[(0,s.jsx)("li",{children:"Usar c\xf3digos de cupom exclusivos para cada influenciador"}),(0,s.jsx)("li",{children:"Implementar UTM tags em links compartilhados"}),(0,s.jsx)("li",{children:"Perguntar aos clientes como conheceram seu restaurante"}),(0,s.jsx)("li",{children:"Monitorar check-ins e men\xe7\xf5es no Instagram"})]})]})]})})]})]})}v.t1.register(v.PP,v.kc,v.E8,v.hE,v.m_,v.s$);var N=t(52643),y=t(58502);function w(e){let{restaurantId:a}=e,[t,n]=(0,r.useState)([]),[l,o]=(0,r.useState)(!0),[c,d]=(0,r.useState)(null),[m,x]=(0,r.useState)(null),[u,g]=(0,r.useState)({}),[h,p]=(0,r.useState)({}),f=e=>{try{let a=new Date(e);return new Intl.DateTimeFormat("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric"}).format(a)}catch(e){return"Data inv\xe1lida"}},v=e=>void 0===e?"0":new Intl.NumberFormat("pt-BR").format(e),j=e=>{if(!e)return{days:0,status:"expired"};let a=new Date,t=Math.ceil((new Date(e).getTime()-a.getTime())/864e5),s="active";return t<0?s="expired":t<7&&(s="ending"),{days:t,status:s}},b=e=>{switch(e){case"active":return"bg-green-100 text-green-800";case"ending":return"bg-amber-100 text-amber-800";case"expired":return"bg-red-100 text-red-800";case"draft":return"bg-gray-100 text-gray-800";default:return"bg-blue-100 text-blue-800"}};(0,r.useEffect)(()=>{!async function(){try{o(!0),d(null),console.log("[PremiumCampanhasTable] Fetching campaigns for restaurantId: ".concat(a));let e=N.N.from("campaigns").select("*, restaurant_profiles(id, business_name)");a&&(e=e.eq("restaurant_id",a));let{data:t,error:s}=await e.order("created_at",{ascending:!1});if(s)throw console.error("[PremiumCampanhasTable] Error directly from Supabase campaign fetch:",s),s;if(console.log("[PremiumCampanhasTable] Initial campaigns data fetched (before metrics):",t),t&&t.length>0){let e=t.map(async e=>{let a=0,t=0,s=e.end_date;console.log("[PremiumCampanhasTable] Processing metrics for campaign ID: ".concat(e.id,", Name: ").concat(e.name));try{a=await (0,y.$v)(e.id)}catch(a){console.error("[PremiumCampanhasTable] Error in getCampaignInfluencerCount for campaign ".concat(e.id," (").concat(e.name,"):"),a)}try{t=await (0,y.WP)(e.id)}catch(a){console.error("[PremiumCampanhasTable] Error in getCampaignTotalPoints for campaign ".concat(e.id," (").concat(e.name,"):"),a)}if(!s&&e.created_at)try{let a=new Date(e.created_at);a.setDate(a.getDate()+30),s=a.toISOString()}catch(a){console.error("[PremiumCampanhasTable] Error calculating end_date for campaign ".concat(e.id,":"),a)}return{...e,influencer_count:a,total_points:t,end_date:s}}),a=await Promise.all(e);console.log("[PremiumCampanhasTable] Campaigns data after metrics processing:",a),n(a)}else n([])}catch(e){console.error("Erro ao buscar campanhas:",e),d("Erro ao buscar campanhas")}finally{o(!1)}}()},[a]);let w=async e=>{if(!u[e]){p(a=>({...a,[e]:!0}));try{let{data:a,error:t}=await N.N.from("campaign_influencers").select("\n          *,\n          influencer:influencers(\n            name,\n            instagram_username,\n            profile_image_url\n          )\n        ").eq("campaign_id",e).eq("status","accepted");if(t)throw t;g(t=>({...t,[e]:a||[]}))}catch(a){console.error("Erro ao buscar criadores da campanha ".concat(e,":"),a)}finally{p(a=>({...a,[e]:!1}))}}},_=e=>{m===e?x(null):(x(e),w(e))};return l?(0,s.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-50",children:(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"})})}):c?(0,s.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-50",children:(0,s.jsx)("div",{className:"bg-red-50 p-4 rounded-md text-red-700",children:c})}):(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-indigo-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-900",children:"Suas Campanhas"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Gerencie suas campanhas ativas e veja o desempenho de cada uma"})]})]}),0===t.length?(0,s.jsx)("div",{className:"text-center py-8 bg-gray-50 rounded-xl",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Nenhuma campanha encontrada"})}):(0,s.jsx)("div",{className:"overflow-hidden rounded-xl border border-gray-200",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Campanha"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Criadores"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pontos"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"T\xe9rmino"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"A\xe7\xf5es"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>{var a,t;let{days:n,status:l}=j(e.end_date),o=m===e.id;return(0,s.jsxs)(r.Fragment,{children:[(0,s.jsxs)("tr",{className:"".concat(o?"bg-gray-50":"hover:bg-gray-50"," transition-colors"),children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 font-bold",children:e.name.charAt(0).toUpperCase()}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:null===(a=e.restaurant_profiles)||void 0===a?void 0:a.business_name})]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(b(l)),children:"active"===l?"Ativa":"ending"===l?"Finalizando":"expired"===l?"Encerrada":"draft"===l?"Rascunho":"Desconhecido"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(i.YXz,{className:"text-indigo-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:v(e.influencer_count)})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(i.YYR,{className:"text-green-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:v(e.total_points)})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(i.bfZ,{className:"text-gray-400 mr-2"}),(0,s.jsx)("span",{className:"text-sm text-gray-900",children:f(e.end_date||"")})]}),n>=0?(0,s.jsx)("span",{className:"text-xs ".concat(n<7?"text-amber-600":"text-green-600"),children:0===n?"Termina hoje":"".concat(n," dia").concat(1!==n?"s":""," restante").concat(1!==n?"s":"")}):(0,s.jsxs)("span",{className:"text-xs text-red-600",children:["Encerrada h\xe1 ",Math.abs(n)," dia",1!==Math.abs(n)?"s":""]})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,s.jsx)("button",{onClick:()=>_(e.id),className:"text-indigo-600 hover:text-indigo-900 flex items-center justify-end w-full",children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.mx3,{className:"mr-1"})," Ocultar"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Ny1,{className:"mr-1"})," Detalhes"]})})})]}),o&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:6,className:"px-6 py-4 bg-gray-50 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"py-2",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Criadores na campanha"}),h[e.id]?(0,s.jsx)("div",{className:"flex justify-center py-4",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"})}):(null===(t=u[e.id])||void 0===t?void 0:t.length)?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:u[e.id].map(e=>{var a,t,r,n,l;return(0,s.jsxs)("div",{className:"flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 overflow-hidden",children:(null===(a=e.influencer)||void 0===a?void 0:a.profile_image_url)?(0,s.jsx)("img",{src:e.influencer.profile_image_url,alt:(null===(t=e.influencer)||void 0===t?void 0:t.name)||"Influencer",className:"h-full w-full object-cover",onError:e=>{let a=e.target;a.onerror=null,a.src="https://via.placeholder.com/40"}}):(0,s.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-indigo-100 text-indigo-600 font-bold",children:((null===(r=e.influencer)||void 0===r?void 0:r.name)||"A").charAt(0).toUpperCase()})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:(null===(n=e.influencer)||void 0===n?void 0:n.name)||"Nome n\xe3o dispon\xedvel"}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["@",(null===(l=e.influencer)||void 0===l?void 0:l.instagram_username)||"username"]})]}),(0,s.jsxs)("div",{className:"ml-auto",children:[(0,s.jsx)("div",{className:"text-xs font-medium text-gray-500",children:"Pontos"}),(0,s.jsx)("div",{className:"text-sm font-semibold text-indigo-600",children:v(e.points||0)})]})]},e.id)})}):(0,s.jsx)("div",{className:"text-center py-4 bg-white rounded-lg",children:(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"Nenhum criador encontrado para esta campanha"})}),e.description&&(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Descri\xe7\xe3o da campanha"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),e.briefing&&(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Briefing"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:e.briefing})]})]})})})]},e.id)})})]})})]})}var _=t(59432);function C(e){let{userId:a,userRole:t}=e,[n,o]=(0,r.useState)([]),[c,d]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null),[u,g]=(0,r.useState)(null),h=(0,l.createClientComponentClient)();(0,r.useEffect)(()=>{!async function(){try{if(d(!0),x(null),!a)throw Error("ID do usu\xe1rio n\xe3o fornecido");let e=[{id:"1",platform:"google",rating:4.7,review_count:128,date:"2023-12-15",platform_icon:(0,s.jsx)(i.DSS,{className:"text-blue-500"})},{id:"2",platform:"tripadvisor",rating:4.5,review_count:87,date:"2023-12-10",platform_icon:(0,s.jsx)(i.hSX,{className:"text-green-500"})},{id:"3",platform:"ifood",rating:4.8,review_count:215,date:"2023-12-20",platform_icon:(0,s.jsx)(_.ycs,{className:"text-red-500"})}];o(e)}catch(e){console.error("Erro ao buscar avalia\xe7\xf5es:",e),x("N\xe3o foi poss\xedvel carregar as avalia\xe7\xf5es")}finally{d(!1)}}()},[a,h]);let p=e=>{let a=[],t=Math.floor(e),r=e%1>=.5;for(let e=0;e<5;e++)e<t?a.push((0,s.jsx)(i.gt3,{className:"text-yellow-400"},e)):e===t&&r?a.push((0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.gt3,{className:"text-gray-300"}),(0,s.jsx)("div",{className:"absolute top-0 left-0 overflow-hidden w-1/2",children:(0,s.jsx)(i.gt3,{className:"text-yellow-400"})})]},e)):a.push((0,s.jsx)(i.gt3,{className:"text-gray-300"},e));return a};return c?(0,s.jsx)("div",{className:"p-4 flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):m?(0,s.jsx)("div",{className:"p-4 text-red-500",children:(0,s.jsx)("p",{children:m})}):(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Filtrar por plataforma"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>g(null),className:"px-4 py-2 rounded-md ".concat(null===u?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:"Todas"}),(0,s.jsxs)("button",{onClick:()=>g("google"),className:"px-4 py-2 rounded-md flex items-center space-x-2 ".concat("google"===u?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:[(0,s.jsx)(i.DSS,{})," ",(0,s.jsx)("span",{children:"Google"})]}),(0,s.jsxs)("button",{onClick:()=>g("tripadvisor"),className:"px-4 py-2 rounded-md flex items-center space-x-2 ".concat("tripadvisor"===u?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:[(0,s.jsx)(i.hSX,{})," ",(0,s.jsx)("span",{children:"TripAdvisor"})]}),(0,s.jsxs)("button",{onClick:()=>g("ifood"),className:"px-4 py-2 rounded-md flex items-center space-x-2 ".concat("ifood"===u?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:[(0,s.jsx)(_.ycs,{})," ",(0,s.jsx)("span",{children:"iFood"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[n.filter(e=>!u||e.platform===u).map(e=>(0,s.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.platform_icon,(0,s.jsx)("span",{className:"font-medium capitalize",children:"google"===e.platform?"Google":"tripadvisor"===e.platform?"TripAdvisor":"iFood"})]}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["Atualizado em ",new Date(e.date).toLocaleDateString("pt-BR")]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("div",{className:"flex",children:p(e.rating)}),(0,s.jsx)("span",{className:"font-bold",children:e.rating.toFixed(1)}),(0,s.jsxs)("span",{className:"text-gray-500",children:["(",e.review_count," avalia\xe7\xf5es)"]})]}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("button",{className:"text-blue-500 hover:text-blue-700 text-sm font-medium",onClick:()=>{alert("Ver detalhes das avalia\xe7\xf5es do ".concat(e.platform))},children:"Ver detalhes"})})]},e.id)),0===n.filter(e=>!u||e.platform===u).length&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nenhuma avalia\xe7\xe3o encontrada para esta plataforma."})]})]})}var k=t(79794);function A(){let e=(0,n.useRouter)(),{setAppBarTitle:a,setAppBarTrailing:t}=(0,m.y)();(0,n.useSearchParams)();let f=(0,l.createClientComponentClient)(),{isOpen:v,title:j,content:N,openPopup:y,closePopup:_}=(0,o.P)({popupId:"restaurant-popup"}),{isOpen:A,openPopup:S,closePopup:D}=(0,c.Y)({popupId:"settings-modal",defaultTabId:"info"}),[R,P]=(0,r.useState)(null),[E,F]=(0,r.useState)(null),[M,z]=(0,r.useState)(null),[I,T]=(0,r.useState)(!0),[q,O]=(0,r.useState)(!1),U=r.useCallback(e=>{e&&(a(e.name||"Restaurante"),t((0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(x.A,{trigger:e.name||"Restaurante",items:[{label:"Configura\xe7\xf5es",icon:(0,s.jsx)(i.Pcn,{className:"text-gray-500"}),onClick:()=>S("info")},{label:"Ajuda",icon:(0,s.jsx)(i.gZZ,{className:"text-gray-500"}),onClick:()=>window.open("https://wa.me/5547999543437","_blank")},{label:"Logout",icon:(0,s.jsx)(i.axc,{className:"text-gray-500"}),onClick:()=>{f.auth.signOut().then(()=>{window.location.href="/login"})}}]})})))},[]),V=(e,a)=>{y({title:e,content:a})},B=r.useCallback(async e=>{if(!R)return;console.log("Dados a serem salvos:",e);let{error:a}=await f.from("restaurants").update({roi:parseFloat((e.ticketMedio/e.cac).toFixed(1))}).eq("id",R.id);a||P(a=>a?{...a,roi:parseFloat((e.ticketMedio/e.cac).toFixed(1))}:null),_()},[null==R?void 0:R.id,f,P,_]),Y=r.useCallback(()=>{V("Impacto no Faturamento",(0,s.jsx)(b,{restaurantId:null==R?void 0:R.id,ticketMedio:52,ticketMedioAnterior:45,novosClientes:128,cac:8,cacComparativos:[{canal:"Influenciadores",valor:8},{canal:"Google Ads",valor:22},{canal:"Facebook Ads",valor:18},{canal:"Instagram Ads",valor:15}],faturamentoAtribuido:12500,onSave:B}))},[V,null==R?void 0:R.id,B]),$=r.useCallback(async e=>{try{console.log("Fetching digital presence data for restaurant ID:",e.id);let{data:a,error:t}=await f.from("digital_presence").select("*").eq("restaurant_id",e.id).order("snapshot_date",{ascending:!1}).limit(1).maybeSingle();if(t)console.error("Error fetching digital presence data:",t);else if(a){console.log("Digital presence data found:",a);let e={instagram:{followers:a.instagram_followers,previous_followers:a.instagram_previous_followers,engagement_rate:a.instagram_engagement_rate},google_reviews:{rating:a.google_rating,previous_rating:a.google_previous_rating,total_reviews:a.google_total_reviews,new_reviews:a.google_new_reviews},tripadvisor:{ranking:a.tripadvisor_ranking,total_places:a.tripadvisor_total_places,rating:a.tripadvisor_rating,reviews:a.tripadvisor_reviews}};F(e)}else console.log("No digital presence data found")}catch(e){console.error("Error fetching additional data:",e)}},[f]),H=r.useCallback(async()=>{console.log("[getUserAndData] Function called"),console.log("Fetching session...");let{data:{session:a},error:t}=await f.auth.getSession();if(t){console.error("Error fetching session:",t),e.push("/login");return}if(!a){console.error("No session found"),e.push("/login");return}try{T(!0),z(null),console.log("Session found:",a),console.log("Fetching restaurant data for user ID:",a.user.id);let e=await (0,d.E)(f,a.user.id,!0);if(console.log("[getUserAndData] Fetched restaurantData:",e),e||(console.log("No restaurant data found. Creating new restaurant profile..."),e=await (0,d.J)(f,a.user.id,void 0,!0),console.log("[getUserAndData] Created new restaurantData:",e),e?console.log("New restaurant profile created:",e):(console.error("Failed to create restaurant profile"),z("Erro ao criar perfil do restaurante."))),e){console.log("Restaurant data found:",e);let a={id:e.id,name:e.name||e.business_name||"Restaurante",instagram_handle:e.instagram_handle,roi:e.roi};console.log("[getUserAndData] currentRestaurant object created:",a),R&&R.id===a.id&&R.name===a.name&&R.instagram_handle===a.instagram_handle&&R.roi===a.roi?console.log("[getUserAndData] Restaurant data unchanged, not updating state."):(console.log("[getUserAndData] Updating restaurant state:",a),P(a))}else z("N\xe3o foi poss\xedvel encontrar ou criar dados do restaurante.")}catch(e){console.error("Unexpected error in dashboard:",e),e&&"object"==typeof e&&0===Object.keys(e).length?console.error("Erro desconhecido no dashboard (objeto vazio)"):console.error("Detalhes do erro: ".concat((null==e?void 0:e.message)||JSON.stringify(e))),z("Ocorreu um erro inesperado. Por favor, tente novamente.")}finally{T(!1)}},[f,e,$]);return((0,r.useEffect)(()=>{console.log("[useEffect] Running initial data fetch effect."),H()},[H]),(0,r.useEffect)(()=>{console.log("[useEffect] Running additional data fetch effect. Restaurant:",R),R&&$(R)},[R,$]),(0,r.useEffect)(()=>{R?U(R):(a("crIAdores"),t(null))},[R]),I)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Carregando dashboard..."})]})}):M&&!R?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md w-full",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-red-600 mb-4",children:"Erro"}),(0,s.jsx)("p",{className:"text-gray-700 mb-6",children:M}),(0,s.jsx)("button",{onClick:()=>e.push("/login"),className:"w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition duration-200",children:"Voltar para Login"})]})}):(0,s.jsxs)("div",{className:"h-full bg-f5f5f5 flex flex-col font-sans relative overflow-hidden pb-4",children:[M&&(0,s.jsx)("div",{className:"bg-red-100  bg-f5f5f5  border-l-4 border-red-500 text-red-700 p-4 mb-4 mx-6",children:(0,s.jsx)("p",{children:M})}),(0,s.jsx)("main",{className:"p-6 rounded-xl bg-white overflow-y-auto flex-1 flex flex-col shadow-md min-h-0",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,s.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-purple-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{d:"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"})})}),(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Seu Plano"})]}),(0,s.jsx)(g.A,{plan:"premium",totalAvailable:8,used:4,startDate:"2025-03-01",endDate:"2025-05-01",restaurantId:null==R?void 0:R.id,onUpgradeClick:()=>{window.open("https://wa.me/5547999543437?text=Ol\xe1!%20Gostaria%20de%20fazer%20upgrade%20do%20meu%20plano.","_blank")},onViewAllRatingsClick:()=>{V("Avalia\xe7\xf5es do Restaurante",(0,s.jsx)(C,{userId:(null==R?void 0:R.id)||"",userRole:"restaurant"}))},compact:!0})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z",clipRule:"evenodd"})})}),(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Presen\xe7a Digital"})]}),(0,s.jsx)(h.A,{restaurantId:(null==R?void 0:R.id)||"",onCardClick:()=>{O(!0)},compact:!0})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,s.jsx)("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"})]})}),(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Impacto no Faturamento"})]}),(0,s.jsx)(p.A,{ticketMedio:52,ticketMedioAnterior:45,novosClientes:128,cac:8,cacComparativo:{canal:"Google Ads",valor:22},faturamentoAtribuido:12500,onCardClick:Y,compact:!0})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm border border-gray-50",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"})})}),(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Pr\xf3ximas a\xe7\xf5es"})]}),(0,s.jsx)(k.A,{userId:null==R?void 0:R.id,className:"mt-0",compact:!0})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-6",children:(0,s.jsx)(w,{restaurantId:null==R?void 0:R.id})}),(0,s.jsx)("div",{className:"hidden md:col-span-4 grid grid-cols-1 md:grid-cols-2 gap-4"})]})}),(0,s.jsx)("a",{href:"https://wa.me/5543991049779",target:"_blank",rel:"noopener noreferrer",className:"fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg flex items-center justify-center",children:(0,s.jsx)(i.EcP,{size:24})}),(0,s.jsx)(u.A,{isOpen:A,onClose:D,onSaved:H,userType:"restaurant",defaultTab:"info"}),v&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-xl p-6 max-w-3xl w-full max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:j}),(0,s.jsx)("button",{onClick:_,className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),N]})})]})}function S(){return(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)("div",{className:"flex justify-center items-center h-screen",children:"Loading..."}),children:(0,s.jsx)(A,{})})}},75740:(e,a,t)=>{"use strict";t.d(a,{bI:()=>n,yk:()=>r});let s=(0,t(73579).createClientComponentClient)();async function r(e){console.log("Buscando presen\xe7a digital para o restaurante:",e);try{let{data:a,error:t}=await s.from("metrics").select("*").eq("profile_id",e).eq("metric_type","digital_presence").order("snapshot_date",{ascending:!1}).limit(1);if(t)return console.error("Erro ao buscar presen\xe7a digital:",t),null;if(!a||0===a.length)return console.log("Nenhum dado de presen\xe7a digital encontrado"),null;let r=a[0],n={restaurant_id:r.profile_id,instagram_followers:r.data.instagram_followers,instagram_previous_followers:r.data.instagram_previous_followers,instagram_engagement_rate:r.data.instagram_engagement_rate,google_rating:r.data.google_rating,google_previous_rating:r.data.google_previous_rating,google_total_reviews:r.data.google_total_reviews,google_new_reviews:r.data.google_new_reviews,tripadvisor_ranking:r.data.tripadvisor_ranking,tripadvisor_total_places:r.data.tripadvisor_total_places,tripadvisor_rating:r.data.tripadvisor_rating,tripadvisor_reviews:r.data.tripadvisor_reviews,snapshot_date:r.snapshot_date,created_at:r.created_at,updated_at:r.updated_at};return console.log("Dados de presen\xe7a digital convertidos:",n),n}catch(e){return console.error("Exce\xe7\xe3o ao buscar presen\xe7a digital:",e),null}}async function n(e){console.log("Buscando contagem de conte\xfado para o restaurante:",e);try{let{data:a,error:t}=await s.from("metrics").select("*").eq("profile_id",e).eq("metric_type","content_count").order("snapshot_date",{ascending:!1}).limit(1);if(t)return console.error("Erro ao buscar contagem de conte\xfado:",t),null;if(!a||0===a.length)return console.log("Nenhum dado de contagem de conte\xfado encontrado"),null;let r=a[0],n={restaurant_id:r.profile_id,total_content:r.data.total_content||0,monthly_content:r.data.monthly_content||0,last_month_content:r.data.last_month_content||0,last_updated:r.snapshot_date,created_at:r.created_at,updated_at:r.updated_at};return console.log("Dados de contagem de conte\xfado convertidos:",n),n}catch(e){return console.error("Exce\xe7\xe3o ao buscar contagem de conte\xfado:",e),null}}}},e=>{var a=a=>e(e.s=a);e.O(0,[722,6711,2362,5647,9724,3579,9688,8264,535,7084,7127,3035,5891,5532,6693,7292,7326,5593,2972,5999,5358,9794,8441,1684,7358],()=>a(35638)),_N_E=e.O()}]);