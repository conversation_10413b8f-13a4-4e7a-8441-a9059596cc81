(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[358],{2410:(e,t,r)=>{Promise.resolve().then(r.bind(r,4492))},4492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(95155),a=r(12115),n=r(29911),i=r(66766),l=r(73579),o=r(26126),d=r(46102);function c(e){let{userId:t,showTooltip:r=!0,className:n=""}=e,i=(0,l.createClientComponentClient)(),[c,u]=(0,a.useState)(!1),[m,f]=(0,a.useState)(!0);if((0,a.useEffect)(()=>{(async function(){try{let e=t;if(!e){let{data:{session:t}}=await i.auth.getSession();if(!t)return;e=t.user.id}let{data:r,error:s}=await i.from("profiles").select("phone_verified").eq("id",e).single();if(s)throw s;u((null==r?void 0:r.phone_verified)||!1)}catch(e){console.error("Erro ao verificar status do WhatsApp:",e)}finally{f(!1)}})()},[i,t]),m||!c)return null;let x=(0,s.jsxs)(o.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 flex items-center gap-1 ".concat(n),children:[(0,s.jsx)("svg",{viewBox:"0 0 24 24",width:"14",height:"14",fill:"#25D366",children:(0,s.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"})}),"WhatsApp"]});return r?(0,s.jsx)(d.Bc,{children:(0,s.jsxs)(d.m_,{children:[(0,s.jsx)(d.k$,{asChild:!0,children:x}),(0,s.jsx)(d.ZI,{children:(0,s.jsx)("p",{children:"Conectado ao WhatsApp"})})]})}):x}function u(e){let{name:t,username:r,points:a,avatarUrl:n,onClick:l,userId:o}=e;return(0,s.jsxs)("div",{className:"flex items-center justify-between py-2 ".concat(l?"cursor-pointer hover:bg-gray-50 rounded-md transition-colors":""),onClick:l,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mr-3",children:n?(0,s.jsx)(i.default,{src:n,alt:t,width:36,height:36,className:"rounded-full object-cover"}):(0,s.jsx)("div",{className:"w-9 h-9 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium",children:t.charAt(0)})}),(0,s.jsxs)("div",{className:"flex-grow min-w-0",children:[(0,s.jsxs)("div",{className:"font-medium truncate flex items-center gap-2",children:[t,o&&(0,s.jsx)(c,{userId:o,className:"text-xs py-0.5 px-1.5"})]}),(0,s.jsx)("div",{className:"text-xs text-gray-500 truncate",children:r})]})]}),(0,s.jsxs)("div",{className:"flex-shrink-0 ml-3 font-bold text-sm",children:[a.toLocaleString()," pts"]})]})}var m=r(60864);function f(e){let{totalContent:t=30,onViewAllClick:r,onCriadorClick:i}=e,[l,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),f=e=>{c(e),o(!0),i&&i(e)};return(0,s.jsxs)("div",{className:"bg-white rounded-xl p-5 shadow-sm h-full",children:[(0,s.jsx)("h3",{className:"text-base font-medium text-gray-700 mb-2",children:"Criadores"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-8 flex items-center",children:[(0,s.jsxs)("div",{className:"relative mr-5",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-3xl font-bold text-white",children:t})}),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md",children:(0,s.jsx)(n.dkL,{className:"text-indigo-600",size:16})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-xl font-bold text-gray-800",children:"Conte\xfados"}),(0,s.jsx)("p",{className:"text-gray-600",children:"criados pelos influenciadores"})]})]}),(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[(0,s.jsx)(n.SBv,{className:"text-yellow-500 mr-2",size:20}),(0,s.jsx)("h4",{className:"text-base font-medium text-gray-700",children:"Melhores Influenciadores"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-3",children:"Os influenciadores que mais trouxeram resultados"}),(0,s.jsx)("div",{className:"space-y-2",children:[{id:"1",name:"Ana Silva",username:"anasilva",points:450,avatarUrl:"https://i.pravatar.cc/150?u=1"},{id:"2",name:"Pedro Santos",username:"pedrosantos",points:380,avatarUrl:"https://i.pravatar.cc/150?u=2"},{id:"3",name:"Carla Mendes",username:"carlamendes",points:320,avatarUrl:"https://i.pravatar.cc/150?u=3"},{id:"4",name:"Lucas Oliveira",username:"lucasoliveira",points:520,avatarUrl:"https://i.pravatar.cc/150?u=4"},{id:"5",name:"Mariana Costa",username:"marianacosta",points:420,avatarUrl:"https://i.pravatar.cc/150?u=5"}].map(e=>(0,s.jsx)(u,{name:e.name,username:e.username,points:e.points,avatarUrl:e.avatarUrl,onClick:()=>f(e.id),userId:e.id},e.id))})]})]}),(0,s.jsx)("div",{className:"mt-3 pt-2 border-t border-gray-100",children:(0,s.jsxs)("button",{onClick:r,className:"text-sm text-blue-600 hover:text-blue-800 flex items-center justify-end w-full font-medium",children:["Ver Todos Criadores",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})}),(0,s.jsx)(m.A,{isOpen:l,onClose:()=>o(!1),criadorId:d})]})}var x=r(52643);function h(e){let{totalCriadors:t,activeCriadors:r,targetCriadors:i,topCriadors:l,totalContent:o=30,monthlyContent:d=8,onViewAllClick:c,onCriadorClick:f}=e,[h,p]=(0,a.useState)(!1),[v,b]=(0,a.useState)(null),[g,j]=(0,a.useState)([]),[y,N]=(0,a.useState)(!1),w=e=>{b(e),p(!0),f&&f(e)};(0,a.useEffect)(()=>{(async()=>{N(!0);try{let{data:e,error:t}=await x.N.from("criadors").select("*").order("points",{ascending:!1}).limit(5);if(t)console.error("Erro ao buscar criadores:",t);else if(e){let t=e.map(e=>({id:e.id,name:e.name,username:e.username,points:e.points,avatarUrl:e.avatar_url}));j(t)}}catch(e){console.error("Erro ao buscar criadores:",e)}finally{N(!1)}})()},[]);let C=g.length>0?g:l;return(0,s.jsxs)("div",{className:"bg-white rounded-xl p-5 shadow-sm h-full",children:[(0,s.jsx)("h3",{className:"text-base font-medium text-gray-700 mb-4",children:"Criadores"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-8 bg-gradient-to-r from-indigo-50 to-blue-50 p-4 rounded-xl",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"mr-4",children:(0,s.jsx)("div",{className:"text-5xl font-bold text-indigo-600",children:o})}),(0,s.jsxs)("div",{className:"border-l-2 border-indigo-200 pl-4",children:[(0,s.jsx)("div",{className:"text-indigo-900 font-medium",children:"Conte\xfados"}),(0,s.jsx)("div",{className:"text-indigo-700 font-medium",children:"criados"})]})]})}),(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[(0,s.jsx)(n.SBv,{className:"text-yellow-500 mr-2",size:20}),(0,s.jsx)("h4",{className:"text-base font-medium text-gray-700",children:"Melhores Criadores"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-3",children:"Os criadores que mais trouxeram resultados"}),(0,s.jsx)("div",{className:"space-y-2",children:y?(0,s.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"})}):C.length>0?C.map(e=>(0,s.jsx)(u,{name:e.name,username:e.username,points:e.points,avatarUrl:e.avatarUrl,onClick:()=>w(e.id)},e.id)):(0,s.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Nenhum criador encontrado"})})]})]}),(0,s.jsx)("div",{className:"mt-3 pt-2 border-t border-gray-100",children:(0,s.jsxs)("button",{onClick:c,className:"text-sm text-blue-600 hover:text-blue-800 flex items-center justify-end w-full font-medium",children:["Ver Todos Criadores",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})}),(0,s.jsx)(m.A,{isOpen:h,onClose:()=>{p(!1),b(null)},criadorId:v})]})}function p(e){let{totalCriadors:t,activeCriadors:r,targetCriadors:i,topCriadors:l,totalContent:o=30,monthlyContent:d=8,onViewAllClick:c,onCriadorClick:f}=e,[h,p]=(0,a.useState)(!1),[v,b]=(0,a.useState)(null),[g,j]=(0,a.useState)([]),[y,N]=(0,a.useState)(!1),w=e=>{b(e),p(!0),f&&f(e)};(0,a.useEffect)(()=>{(async()=>{N(!0);try{let{data:e,error:t}=await x.N.from("criadors").select("*").order("points",{ascending:!1}).limit(5);if(t)console.error("Erro ao buscar influenciadores:",t);else if(e){let t=e.map(e=>({id:e.id,name:e.name,username:e.username,points:e.points,avatarUrl:e.avatar_url}));j(t)}}catch(e){console.error("Erro ao buscar influenciadores:",e)}finally{N(!1)}})()},[]);let C=g.length>0?g:l;return(0,s.jsxs)("div",{className:"bg-white rounded-xl p-5 shadow-sm h-full",children:[(0,s.jsx)("h3",{className:"text-base font-medium text-gray-700 mb-4",children:"Criadores"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-8 flex items-center",children:[(0,s.jsxs)("div",{className:"relative mr-5",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-3xl font-bold text-white",children:o})}),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-md",children:(0,s.jsx)(n.dkL,{className:"text-indigo-600",size:16})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-xl font-bold text-gray-800",children:"Conte\xfados"}),(0,s.jsx)("p",{className:"text-gray-600",children:"criados pelos influenciadores"})]})]}),(0,s.jsxs)("div",{className:"mt-5",children:[(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[(0,s.jsx)(n.SBv,{className:"text-yellow-500 mr-2",size:20}),(0,s.jsx)("h4",{className:"text-base font-medium text-gray-700",children:"Melhores Influenciadores"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500 mb-3",children:"Os influenciadores que mais trouxeram resultados"}),(0,s.jsx)("div",{className:"space-y-2",children:y?(0,s.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"})}):C.length>0?C.map(e=>(0,s.jsx)(u,{name:e.name,username:e.username,points:e.points,avatarUrl:e.avatarUrl,onClick:()=>w(e.id)},e.id)):(0,s.jsx)("div",{className:"text-center py-4 text-gray-500",children:"Nenhum influenciador encontrado"})})]})]}),(0,s.jsx)("div",{className:"mt-3 pt-2 border-t border-gray-100",children:(0,s.jsxs)("button",{onClick:c,className:"text-sm text-blue-600 hover:text-blue-800 flex items-center justify-end w-full font-medium",children:["Ver Todos Influenciadores",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})}),(0,s.jsx)(m.A,{isOpen:h,onClose:()=>{p(!1),b(null)},criadorId:v})]})}var v=r(35695);let b=[{id:"1",name:"Ana Silva",username:"@gourmetanasita",points:1250,avatarUrl:"https://i.pravatar.cc/150?u=ana_silva"},{id:"2",name:"Pedro Santos",username:"@pedrosantos",points:980,avatarUrl:"https://i.pravatar.cc/150?u=pedro_santos"},{id:"3",name:"Carla Mendes",username:"@carlamendes",points:840,avatarUrl:"https://i.pravatar.cc/150?u=carla_mendes"},{id:"4",name:"Marcos Oliveira",username:"@marcosoliveira",points:720,avatarUrl:"https://i.pravatar.cc/150?u=marcos_oliveira"},{id:"5",name:"Julia Ferreira",username:"@juliaferreira",points:650,avatarUrl:"https://i.pravatar.cc/150?u=julia_ferreira"}];function g(){let e=(0,v.useRouter)(),t=()=>{e.push("/restaurante/criadores")};return(0,s.jsxs)("div",{className:"container mx-auto p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Op\xe7\xf5es de Design para o Card de Criadores"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"Design 1 (Atual)"}),(0,s.jsx)(f,{totalInfluencers:30,activeInfluencers:8,targetInfluencers:12,totalContent:30,monthlyContent:8,topInfluencers:b,onViewAllClick:t,onInfluencerClick:handleInfluencerClick})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"Design 2"}),(0,s.jsx)(h,{totalInfluencers:30,activeInfluencers:8,targetInfluencers:12,totalContent:30,monthlyContent:8,topInfluencers:b,onViewAllClick:t,onInfluencerClick:handleInfluencerClick})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-3",children:"Design 3"}),(0,s.jsx)(p,{totalInfluencers:30,activeInfluencers:8,targetInfluencers:12,totalContent:30,monthlyContent:8,topInfluencers:b,onViewAllClick:t,onInfluencerClick:handleInfluencerClick})]})]}),(0,s.jsx)("div",{className:"mt-8",children:(0,s.jsx)("button",{onClick:()=>e.push("/restaurante"),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Voltar para o Dashboard"})})]})}},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let s=r(12115);function a(e,t){let r=(0,s.useRef)(null),a=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=r.current;e&&(r.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(r.current=n(e,s)),t&&(a.current=n(t,s))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11275:(e,t,r)=>{"use strict";r.d(t,{X:()=>n});var s=r(12115),a=r(52712);function n(e){let[t,r]=s.useState(void 0);return(0,a.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,a=t.blockSize}else s=e.offsetWidth,a=e.offsetHeight;r({width:s,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},17313:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var s=r(95155),a=r(12115),n=r(36217),i=r(59434);let l=n.bL,o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...a})});o.displayName=n.B8.displayName;let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...a})});d.displayName=n.l9.displayName;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...a})});c.displayName=n.UC.displayName},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(95155);r(12115);var a=r(74466),n=r(59434);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200/80"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:r}),t),...a})}},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},46102:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>l,ZI:()=>c,k$:()=>d,m_:()=>o});var s=r(95155),a=r(12115),n=r(83e3),i=r(59434);let l=n.Kq,o=n.bL,d=n.l9,c=a.forwardRef((e,t)=>{let{className:r,sideOffset:a=4,...l}=e;return(0,s.jsx)(n.UC,{ref:t,sideOffset:a,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...l})});c.displayName=n.UC.displayName},52643:(e,t,r)=>{"use strict";r.d(t,{N:()=>a,b:()=>n});var s=r(73579);let a=(0,s.createClientComponentClient)();function n(){return(0,s.createClientComponentClient)()}},54165:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>f,L3:()=>x,c7:()=>m,lG:()=>o,rr:()=>h});var s=r(95155),a=r(12115),n=r(11662),i=r(54416),l=r(59434);let o=n.bL;n.l9;let d=n.ZL;n.bm;let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-white/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...a})});c.displayName=n.hJ.displayName;let u=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(n.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-0 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...o,children:[a,(0,s.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=n.UC.displayName;let m=e=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};m.displayName="DialogHeader";let f=e=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};f.displayName="DialogFooter";let x=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",r),...a})});x.displayName=n.hE.displayName;let h=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});h.displayName=n.VY.displayName},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>c});var s=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(a),i=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var s,a,n;s=e,a=t,n=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){return t=>s.createElement(u,l({attr:d({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,d({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:n,title:o}=e,c=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,i),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,c,{className:r,style:d(d({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>t(e)):t(a)}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let n=a(t)||a(s);return i[e][n]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},e=>{var t=t=>e(e.s=t);e.O(0,[6330,6711,9724,3579,9688,7084,3035,6766,5514,864,8441,1684,7358],()=>t(2410)),_N_E=e.O()}]);