"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5612],{25175:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(95155),s=r(12115),n=r(29911);function o(e){let{trigger:t,items:r,className:o=""}=e,[l,c]=(0,s.useState)(!1),i=(0,s.useRef)(null);return(0,s.useEffect)(()=>{function e(e){i.current&&!i.current.contains(e.target)&&c(!1)}return l&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[l]),(0,a.jsxs)("div",{className:"relative ".concat(o),ref:i,children:[(0,a.jsxs)("button",{onClick:()=>c(!l),className:"flex items-center gap-2 px-4 py-1.5 bg-white rounded-full hover:bg-gray-50 transition-all duration-200 border border-gray-200 shadow-sm font-medium",children:[(0,a.jsx)("span",{className:"text-gray-800",children:t}),(0,a.jsx)("div",{className:"flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 transition-all duration-200 ".concat(l?"bg-blue-100":""),children:(0,a.jsx)(n.Vr3,{className:"transition-transform duration-200 ".concat(l?"rotate-180 text-blue-500":"text-gray-500"),size:10})})]}),l&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-10 overflow-hidden border border-gray-100 animate-fadeIn",children:[(0,a.jsx)("div",{className:"absolute w-3 h-3 bg-white transform rotate-45",style:{top:"-6px",right:"20px",boxShadow:"-2px -2px 2px rgba(0, 0, 0, 0.05)"}}),(0,a.jsx)("div",{className:"relative z-10 bg-white rounded-lg overflow-hidden py-1",children:r.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>{c(!1),e.onClick()},className:"w-full text-left px-4 py-2.5 hover:bg-gray-50 flex items-center gap-3 transition-colors duration-150",children:[(0,a.jsx)("span",{className:"text-gray-500",children:e.icon}),(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:e.label})]},t))})]})]})}},52643:(e,t,r)=>{r.d(t,{N:()=>s,b:()=>n});var a=r(73579);let s=(0,a.createClientComponentClient)();function n(){return(0,a.createClientComponentClient)()}},60440:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(95155),s=r(12115),n=r(35695),o=r(52643),l=r(81452),c=r(13568);function i(e){let{children:t}=e,r=(0,n.useRouter)(),[i,f]=(0,s.useState)(!0),[d,u]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await o.N.auth.getSession();if(t)throw t;if(!e){r.push("/login?redirect=/admin/campaigns");return}let a=e.user.email;a&&(0,l.K)(a)?u(!0):(c.oR.error("Voc\xea n\xe3o tem permiss\xe3o para acessar esta \xe1rea"),r.push("/"))}catch(e){console.error("Erro ao verificar status de administrador:",e),c.oR.error("Erro ao verificar suas permiss\xf5es"),r.push("/")}finally{f(!1)}})()},[r]),i)?(0,a.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):d?(0,a.jsx)(a.Fragment,{children:t}):null}},60637:(e,t,r)=>{r.d(t,{default:()=>o});var a=r(95155),s=r(6874),n=r.n(s);function o(e){let{className:t="",textClassName:r="text-xl font-semibold",showText:s=!0,size:o="medium",href:l="/"}=e;return l?(0,a.jsx)(n(),{href:l,className:"flex items-center ".concat(t),children:(0,a.jsx)("span",{className:r,children:"crIAdores"})}):(0,a.jsx)("div",{className:"flex items-center ".concat(t),children:(0,a.jsx)("span",{className:r,children:"crIAdores"})})}},61257:(e,t,r)=>{r.d(t,{A:()=>x});var a=r(95155);r(12115);var s=r(35695),n=r(29911),o=r(52643),l=r(13568),c=r(60440),i=r(60637),f=r(6874),d=r.n(f);function u(){let e=(0,s.usePathname)(),t=t=>e===t||e.startsWith("".concat(t,"/"));return(0,a.jsxs)("div",{className:"flex items-center overflow-x-auto bg-[#f5f5f5] navigation-tabs",style:{backgroundColor:"#f5f5f5"},children:[(0,a.jsx)(d(),{href:"/admin/negocios",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ".concat(t("/admin/negocios")?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,a.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,a.jsx)(n.ymh,{className:"mr-2 bg-[#f5f5f5]"}),(0,a.jsx)("span",{className:"bg-[#f5f5f5]",children:"Neg\xf3cios"})]})}),(0,a.jsx)(d(),{href:"/admin/campaigns",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ".concat(t("/admin/campaigns")?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,a.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,a.jsx)(n.sdT,{className:"mr-2 bg-[#f5f5f5]"}),(0,a.jsx)("span",{className:"bg-[#f5f5f5]",children:"Campanhas"})]})}),(0,a.jsx)(d(),{href:"/admin/criadores",style:{backgroundColor:"#f5f5f5"},className:"px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ".concat(t("/admin/criadores")?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,a.jsxs)("div",{className:"flex items-center bg-[#f5f5f5]",children:[(0,a.jsx)(n.YXz,{className:"mr-2 bg-[#f5f5f5]"}),(0,a.jsx)("span",{className:"bg-[#f5f5f5]",children:"Criadores"})]})})]})}var m=r(25175);function x(e){let{children:t,title:r,backLink:f}=e,d=(0,s.useRouter)(),x=async()=>{try{let{error:e}=await o.N.auth.signOut();if(e)throw e;l.oR.success("Logout realizado com sucesso"),d.push("/login")}catch(e){console.error("Erro ao fazer logout:",e),l.oR.error("Erro ao fazer logout")}};return(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 flex justify-between items-center px-6 py-1.5 bg-[#f5f5f5] z-50",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(i.default,{size:"medium",textClassName:"text-xl font-bold"})}),(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2",children:(0,a.jsx)(u,{})}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(m.A,{trigger:"Admin",items:[{label:"Configura\xe7\xf5es",icon:(0,a.jsx)(n.Pcn,{className:"text-gray-500"}),onClick:()=>d.push("/admin/settings")},{label:"Ajuda",icon:(0,a.jsx)(n.gZZ,{className:"text-gray-500"}),onClick:()=>window.open("https://help.connectcity.com.br","_blank")},{label:"Logout",icon:(0,a.jsx)(n.axc,{className:"text-gray-500"}),onClick:x}]})})]}),f&&(0,a.jsx)("div",{className:"mt-2 mb-4",children:(0,a.jsx)("button",{onClick:()=>d.push(f),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,a.jsx)(n.QVr,{className:"text-gray-600"})})}),(0,a.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-white overflow-y-auto flex-1 flex flex-col shadow-md",style:{minHeight:"calc(100vh - 6rem)",maxHeight:"calc(100vh - 6rem)"},children:(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:r}),t]})})]})})}},74436:(e,t,r)=>{r.d(t,{k5:()=>f});var a=r(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(s),o=["attr","size","title"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var a,s,n;a=e,s=t,n=r[t],(s=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(s))in a?Object.defineProperty(a,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e){return t=>a.createElement(d,l({attr:i({},e.attr)},t),function e(t){return t&&t.map((t,r)=>a.createElement(t.tag,i({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:s,size:n,title:c}=e,f=function(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,o),d=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",l({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,f,{className:r,style:i(i({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&a.createElement("title",null,c),e.children)};return void 0!==n?a.createElement(n.Consumer,null,e=>t(e)):t(s)}},81452:(e,t,r)=>{r.d(t,{I:()=>a,K:()=>s});let a=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];function s(e){return a.includes(e.toLowerCase())}}}]);