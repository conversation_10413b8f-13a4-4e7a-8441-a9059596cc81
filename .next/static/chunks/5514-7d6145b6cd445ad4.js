"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5514],{22436:(e,t,n)=>{var r=n(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,l=r.useEffect,a=r.useLayoutEffect,u=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),i=r[0].inst,c=r[1];return a(function(){i.value=n,i.getSnapshot=t,s(i)&&c({inst:i})},[e,n,t]),l(function(){return s(i)&&c({inst:i}),e(function(){s(i)&&c({inst:i})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},22475:(e,t,n)=>{n.d(t,{UE:()=>ed,ll:()=>el,rD:()=>em,UU:()=>es,jD:()=>ef,ER:()=>ep,cY:()=>ea,BN:()=>eu,Ej:()=>ec});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,a=Math.floor,u=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(d(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>c[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function b(e,t,n){let r,{reference:i,floating:o}=e,l=g(t),a=m(g(t)),u=h(a),s=d(t),c="y"===l,f=i.x+i.width/2-o.width/2,v=i.y+i.height/2-o.height/2,y=i[u]/2-o[u]/2;switch(s){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:v};break;case"left":r={x:i.x-o.width,y:v};break;default:r={x:i.x,y:i.y}}switch(p(t)){case"start":r[a]-=y*(n&&c?-1:1);break;case"end":r[a]+=y*(n&&c?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=b(s,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:o,fn:h}=a[n],{x:g,y:v,data:y,reset:w}=await h({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[o]:{...p[o],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:f}=b(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=w(m),g=a[p?"floating"===d?"reference":"floating":d],v=x(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),E=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},R=x(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:b,strategy:u}):y);return{top:(v.top-R.top+h.top)/E.y,bottom:(R.bottom-v.bottom+h.bottom)/E.y,left:(v.left-R.left+h.left)/E.x,right:(R.right-v.right+h.right)/E.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function C(e){return r.some(t=>e[t]>=0)}async function A(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),a=p(n),u="y"===g(n),s=["left","top"].includes(l)?-1:1,c=o&&u?-1:1,m=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:y}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),u?{x:v*c,y:h*s}:{x:h*s,y:v*c}}function L(){return"undefined"!=typeof window}function N(e){return P(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function S(e){var t;return null==(t=(P(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function P(e){return!!L()&&(e instanceof Node||e instanceof O(e).Node)}function j(e){return!!L()&&(e instanceof Element||e instanceof O(e).Element)}function D(e){return!!L()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function k(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}function M(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=U(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=F(),n=j(e)?U(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(N(e))}function U(e){return O(e).getComputedStyle(e)}function W(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function B(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||k(e)&&e.host||S(e);return k(t)?t.host:t}function V(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=B(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&M(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=O(i);if(o){let e=Y(l);return t.concat(l,l.visualViewport||[],M(i)?i:[],e&&n?V(e):[])}return t.concat(i,V(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function z(e){let t=U(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=D(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,u=l(n)!==o||l(r)!==a;return u&&(n=o,r=a),{width:n,height:r,$:u}}function X(e){return j(e)?e:e.contextElement}function $(e){let t=X(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=z(t),a=(o?l(n.width):n.width)/r,s=(o?l(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let q=u(0);function G(e){let t=O(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:q}function K(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=X(e),a=u(1);t&&(r?j(r)&&(a=$(r)):a=$(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===O(l))&&i)?G(l):u(0),c=(o.left+s.x)/a.x,f=(o.top+s.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=O(l),t=r&&j(r)?O(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=$(i),t=i.getBoundingClientRect(),r=U(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=o,f+=l,i=Y(n=O(i))}}return x({width:d,height:p,x:c,y:f})}function Z(e,t){let n=W(e).scrollLeft;return t?t.left+n:K(S(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Z(e,r)),y:r.top+t.scrollTop}}function Q(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=O(e),r=S(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,u=0;if(i){o=i.width,l=i.height;let e=F();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=S(e),n=W(e),r=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Z(e),u=-n.scrollTop;return"rtl"===U(r).direction&&(a+=o(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:a,y:u}}(S(e));else if(j(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=D(e)?$(e):u(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=G(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function ee(e){return"static"===U(e).position}function et(e,t){if(!D(e)||"fixed"===U(e).position)return null;if(t)return t(e);let n=e.offsetParent;return S(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=O(e);if(I(e))return n;if(!D(e)){let t=B(e);for(;t&&!H(t);){if(j(t)&&!ee(t))return t;t=B(t)}return n}let r=et(e,t);for(;r&&["table","td","th"].includes(N(r))&&ee(r);)r=et(r,t);return r&&H(r)&&ee(r)&&!_(r)?n:r||function(e){let t=B(e);for(;D(t)&&!H(t);){if(_(t))return t;if(I(t))break;t=B(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),i=S(t),o="fixed"===n,l=K(e,!0,o,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!o){if(("body"!==N(t)||M(i))&&(a=W(t)),r){let e=K(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=Z(i))}let c=!i||r||o?u(0):J(i,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=S(r),a=!!t&&I(t.floating);if(r===l||a&&o)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),f=u(0),d=D(r);if((d||!d&&!o)&&(("body"!==N(r)||M(l))&&(s=W(r)),D(r))){let e=K(r);c=$(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||o?u(0):J(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+f.x+p.x,y:n.y*c.y-s.scrollTop*c.y+f.y+p.y}},getDocumentElement:S,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:l}=e,a=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=V(e,[],!1).filter(e=>j(e)&&"body"!==N(e)),i=null,o="fixed"===U(e).position,l=o?B(e):e;for(;j(l)&&!H(l);){let t=U(l),n=_(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||M(l)&&!n&&function e(t,n){let r=B(t);return!(r===n||!j(r)||H(r))&&("fixed"===U(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=B(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=Q(t,n,l);return e.top=o(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=o(r.left,e.left),e},Q(t,u,l));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:en,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=z(e);return{width:t,height:n}},getScale:$,isElement:j,isRTL:function(e){return"rtl"===U(e).direction}};function eo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function el(e,t,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=X(e),m=u||s?[...p?V(p):[],...V(t)]:[];m.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,l=S(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),u();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:g}=d;if(c||t(),!h||!g)return;let v=a(m),y=a(l.clientWidth-(p+h)),w={rootMargin:-v+"px "+-y+"px "+-a(l.clientHeight-(m+g))+"px "+-a(p)+"px",threshold:o(0,i(1,f))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==f){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||eo(d,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),u}(p,n):null,g=-1,v=null;c&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let y=d?K(e):null;return d&&function t(){let r=K(e);y&&!eo(y,r)&&n(),y=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{u&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(l)}}let ea=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,u=await A(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}},eu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:l}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),p={x:n,y:r},h=await R(t,c),v=g(d(l)),y=m(v),w=p[y],x=p[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=o(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=o(n,i(x,r))}let b=s.fn({...t,[y]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[v]:u}}}}}},es=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:w,elements:x}=t,{mainAxis:b=!0,crossAxis:E=!0,fallbackPlacements:T,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:L=!0,...N}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let O=d(a),S=g(c),P=d(c)===c,j=await (null==w.isRTL?void 0:w.isRTL(x.floating)),D=T||(P||!L?[y(c)]:function(e){let t=y(e);return[v(e),t,v(t)]}(c)),k="none"!==A;!T&&k&&D.push(...function(e,t,n,r){let i=p(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(v)))),o}(c,L,A,j));let M=[c,...D],I=await R(t,N),_=[],F=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&_.push(I[O]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),i=m(g(e)),o=h(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=y(l)),[l,y(l)]}(a,s,j);_.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=M[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(o=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let e=null==(l=F.filter(e=>{if(k){let t=g(e.placement);return t===S||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}},ec=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let l,a;let{placement:u,rects:s,platform:c,elements:m}=t,{apply:h=()=>{},...v}=f(e,t),y=await R(t,v),w=d(u),x=p(u),b="y"===g(u),{width:E,height:T}=s.floating;"top"===w||"bottom"===w?(l=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(m.floating))?"start":"end")?"left":"right"):(a=w,l="end"===x?"top":"bottom");let C=T-y.top-y.bottom,A=E-y.left-y.right,L=i(T-y[l],C),N=i(E-y[a],A),O=!t.middlewareData.shift,S=L,P=N;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=C),O&&!x){let e=o(y.left,0),t=o(y.right,0),n=o(y.top,0),r=o(y.bottom,0);b?P=E-2*(0!==e||0!==t?e+t:o(y.left,y.right)):S=T-2*(0!==n||0!==r?n+r:o(y.top,y.bottom))}await h({...t,availableWidth:P,availableHeight:S});let j=await c.getDimensions(m.floating);return E!==j.width||T!==j.height?{reset:{rects:!0}}:{}}}},ef=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=f(e,t);switch(r){case"referenceHidden":{let e=T(await R(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:C(e)}}}case"escaped":{let e=T(await R(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:C(e)}}}default:return{}}}}},ed=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:l,rects:a,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:v=0}=f(e,t)||{};if(null==d)return{};let y=w(v),x={x:n,y:r},b=m(g(l)),E=h(b),R=await u.getDimensions(d),T="y"===b,C=T?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[b]-x[b]-a.floating[E],L=x[b]-a.reference[b],N=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),O=N?N[C]:0;O&&await (null==u.isElement?void 0:u.isElement(N))||(O=s.floating[C]||a.floating[E]);let S=O/2-R[E]/2-1,P=i(y[T?"top":"left"],S),j=i(y[T?"bottom":"right"],S),D=O-R[E]-j,k=O/2-R[E]/2+(A/2-L/2),M=o(P,i(k,D)),I=!c.arrow&&null!=p(l)&&k!==M&&a.reference[E]/2-(k<P?P:j)-R[E]/2<0,_=I?k<P?k-P:k-D:0;return{[b]:x[b]+_,data:{[b]:M,centerOffset:k-M-_,...I&&{alignmentOffset:_}},reset:I}}}),ep=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},p=g(i),h=m(p),v=c[h],y=c[p],w=f(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var b,E;let e="y"===h?"width":"height",t=["top","left"].includes(d(i)),n=o.reference[p]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[p])||0)+(t?0:x.crossAxis),r=o.reference[p]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[p])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[p]:y}}}},em=(e,t,n)=>{let r=new Map,i={platform:ei,...n},o={...i.platform,_c:r};return E(e,t,{...i,platform:o})}},49033:(e,t,n)=>{e.exports=n(22436)},54011:(e,t,n)=>{n.d(t,{H4:()=>T,_V:()=>R,bL:()=>E});var r=n(12115),i=n(46081),o=n(39033),l=n(52712),a=n(63540),u=n(49033);function s(){return()=>{}}var c=n(95155),f="Avatar",[d,p]=(0,i.A)(f),[m,h]=d(f),g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...i}=e,[o,l]=r.useState("idle");return(0,c.jsx)(m,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:l,children:(0,c.jsx)(a.sG.span,{...i,ref:t})})});g.displayName=f;var v="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:i,onLoadingStatusChange:f=()=>{},...d}=e,p=h(v,n),m=function(e,t){let{referrerPolicy:n,crossOrigin:i}=t,o=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),a=r.useRef(null),c=o?(a.current||(a.current=new window.Image),a.current):null,[f,d]=r.useState(()=>b(c,e));return(0,l.N)(()=>{d(b(c,e))},[c,e]),(0,l.N)(()=>{let e=e=>()=>{d(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),n&&(c.referrerPolicy=n),"string"==typeof i&&(c.crossOrigin=i),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,i,n]),f}(i,d),g=(0,o.c)(e=>{f(e),p.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==m&&g(m)},[m,g]),"loaded"===m?(0,c.jsx)(a.sG.img,{...d,ref:t,src:i}):null});y.displayName=v;var w="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:i,...o}=e,l=h(w,n),[u,s]=r.useState(void 0===i);return r.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>s(!0),i);return()=>window.clearTimeout(e)}},[i]),u&&"loaded"!==l.imageLoadingStatus?(0,c.jsx)(a.sG.span,{...o,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=w;var E=g,R=y,T=x},63540:(e,t,n)=>{n.d(t,{sG:()=>s,hO:()=>c});var r=n(12115),i=n(47650),o=n(6101),l=n(95155),a=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{var n,i,l;let a,u;let{children:s,...c}=e,f=r.isValidElement(s)?(u=(a=null===(i=Object.getOwnPropertyDescriptor((n=s).props,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in a&&a.isReactWarning)?n.ref:(u=(a=null===(l=Object.getOwnPropertyDescriptor(n,"ref"))||void 0===l?void 0:l.get)&&"isReactWarning"in a&&a.isReactWarning)?n.props.ref:n.props.ref||n.ref:void 0,d=(0,o.s)(f,t);if(r.isValidElement(s)){let e=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=o(...t);return i(...t),r}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(c,s.props);return s.type!==r.Fragment&&(e.ref=d),r.cloneElement(s,e)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=r.forwardRef((e,n)=>{let{children:i,...o}=e,a=r.Children.toArray(i),s=a.find(u);if(s){let e=s.props.children,i=a.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,l.jsx)(t,{...o,ref:n,children:i})});return n.displayName="".concat(e,".Slot"),n}(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function c(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},83e3:(e,t,n)=>{n.d(t,{UC:()=>ep,Kq:()=>ec,bL:()=>ef,l9:()=>ed});var r=n(12115),i=n(85185),o=n(6101),l=n(46081),a=n(94903),u=n(61285),s=n(84945),c=n(22475);n(47650);var f=n(99708),d=n(95155),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,f.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),m=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,d.jsx)(p.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});m.displayName="Arrow";var h=n(39033),g=n(52712),v=n(11275),y="Popper",[w,x]=(0,l.A)(y),[b,E]=w(y),R=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,d.jsx)(b,{scope:t,anchor:i,onAnchorChange:o,children:n})};R.displayName=y;var T="PopperAnchor",C=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...l}=e,a=E(T,n),u=r.useRef(null),s=(0,o.s)(t,u);return r.useEffect(()=>{a.onAnchorChange((null==i?void 0:i.current)||u.current)}),i?null:(0,d.jsx)(p.div,{...l,ref:s})});C.displayName=T;var A="PopperContent",[L,N]=w(A),O=r.forwardRef((e,t)=>{var n,i,l,a,u,f,m,y;let{__scopePopper:w,side:x="bottom",sideOffset:b=0,align:R="center",alignOffset:T=0,arrowPadding:C=0,avoidCollisions:N=!0,collisionBoundary:O=[],collisionPadding:S=0,sticky:P="partial",hideWhenDetached:j=!1,updatePositionStrategy:I="optimized",onPlaced:_,...F}=e,H=E(A,w),[U,W]=r.useState(null),B=(0,o.s)(t,e=>W(e)),[V,Y]=r.useState(null),z=(0,v.X)(V),X=null!==(m=null==z?void 0:z.width)&&void 0!==m?m:0,$=null!==(y=null==z?void 0:z.height)&&void 0!==y?y:0,q="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},G=Array.isArray(O)?O:[O],K=G.length>0,Z={padding:q,boundary:G.filter(D),altBoundary:K},{refs:J,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:en}=(0,s.we)({strategy:"fixed",placement:x+("center"!==R?"-"+R:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,c.ll)(...t,{animationFrame:"always"===I})},elements:{reference:H.anchor},middleware:[(0,s.cY)({mainAxis:b+$,alignmentAxis:T}),N&&(0,s.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===P?(0,s.ER)():void 0,...Z}),N&&(0,s.UU)({...Z}),(0,s.Ej)({...Z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),V&&(0,s.UE)({element:V,padding:C}),k({arrowWidth:X,arrowHeight:$}),j&&(0,s.jD)({strategy:"referenceHidden",...Z})]}),[er,ei]=M(ee),eo=(0,h.c)(_);(0,g.N)(()=>{et&&(null==eo||eo())},[et,eo]);let el=null===(n=en.arrow)||void 0===n?void 0:n.x,ea=null===(i=en.arrow)||void 0===i?void 0:i.y,eu=(null===(l=en.arrow)||void 0===l?void 0:l.centerOffset)!==0,[es,ec]=r.useState();return(0,g.N)(()=>{U&&ec(window.getComputedStyle(U).zIndex)},[U]),(0,d.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:es,"--radix-popper-transform-origin":[null===(a=en.transformOrigin)||void 0===a?void 0:a.x,null===(u=en.transformOrigin)||void 0===u?void 0:u.y].join(" "),...(null===(f=en.hide)||void 0===f?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(L,{scope:w,placedSide:er,onArrowChange:Y,arrowX:el,arrowY:ea,shouldHideArrow:eu,children:(0,d.jsx)(p.div,{"data-side":er,"data-align":ei,...F,ref:B,style:{...F.style,animation:et?void 0:"none"}})})})});O.displayName=A;var S="PopperArrow",P={top:"bottom",right:"left",bottom:"top",left:"right"},j=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=N(S,n),o=P[i.placedSide];return(0,d.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(m,{...r,ref:t,style:{...r.style,display:"block"}})})});function D(e){return null!==e}j.displayName=S;var k=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,m]=M(a),h={start:"0%",center:"50%",end:"100%"}[m],g=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+f/2,v=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,y="",w="";return"bottom"===p?(y=c?h:"".concat(g,"px"),w="".concat(-d,"px")):"top"===p?(y=c?h:"".concat(g,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?h:"".concat(v,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=c?h:"".concat(v,"px")),{data:{x:y,y:w}}}});function M(e){let[t,n="center"]=e.split("-");return[t,n]}n(3727);var I=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[i,o]=r.useState(),l=r.useRef({}),a=r.useRef(e),u=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=_(l.current);u.current="mounted"===s?e:"none"},[s]),(0,g.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,i=_(t);e?c("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,g.N)(()=>{if(i){var e;let t;let n=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=_(l.current).includes(e.animationName);if(e.target===i&&r&&(c("ANIMATION_END"),!a.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(u.current=_(l.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}c("ANIMATION_END")},[i,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),a=(0,o.s)(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:a}):null};function _(e){return(null==e?void 0:e.animationName)||"none"}I.displayName="Presence";var F=n(5845),H=r.forwardRef((e,t)=>(0,d.jsx)(p.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));H.displayName="VisuallyHidden";var[U,W]=(0,l.A)("Tooltip",[x]),B=x(),V="TooltipProvider",Y="tooltip.open",[z,X]=U(V),$=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:l}=e,a=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,d.jsx)(z,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.current=!0,i)},[i]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:o,children:l})};$.displayName=V;var q="Tooltip",[G,K]=U(q),Z=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:s}=e,c=X(q,e.__scopeTooltip),f=B(t),[p,m]=r.useState(null),h=(0,u.B)(),g=r.useRef(0),v=null!=a?a:c.disableHoverableContent,y=null!=s?s:c.delayDuration,w=r.useRef(!1),[x=!1,b]=(0,F.i)({prop:i,defaultProp:o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(Y))):c.onClose(),null==l||l(e)}}),E=r.useMemo(()=>x?w.current?"delayed-open":"instant-open":"closed",[x]),T=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,w.current=!1,b(!0)},[b]),C=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,b(!1)},[b]),A=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{w.current=!0,b(!0),g.current=0},y)},[y,b]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,d.jsx)(R,{...f,children:(0,d.jsx)(G,{scope:t,contentId:h,open:x,stateAttribute:E,trigger:p,onTriggerChange:m,onTriggerEnter:r.useCallback(()=>{c.isOpenDelayedRef.current?A():T()},[c.isOpenDelayedRef,A,T]),onTriggerLeave:r.useCallback(()=>{v?C():(window.clearTimeout(g.current),g.current=0)},[C,v]),onOpen:T,onClose:C,disableHoverableContent:v,children:n})})};Z.displayName=q;var J="TooltipTrigger",Q=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=K(J,n),u=X(J,n),s=B(n),c=r.useRef(null),f=(0,o.s)(t,c,a.onTriggerChange),m=r.useRef(!1),h=r.useRef(!1),g=r.useCallback(()=>m.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,d.jsx)(C,{asChild:!0,...s,children:(0,d.jsx)(p.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||u.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{a.open&&a.onClose(),m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{m.current||a.onOpen()}),onBlur:(0,i.m)(e.onBlur,a.onClose),onClick:(0,i.m)(e.onClick,a.onClose)})})});Q.displayName=J;var[ee,et]=U("TooltipPortal",{forceMount:void 0}),en="TooltipContent",er=r.forwardRef((e,t)=>{let n=et(en,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,l=K(en,e.__scopeTooltip);return(0,d.jsx)(I,{present:r||l.open,children:l.disableHoverableContent?(0,d.jsx)(eu,{side:i,...o,ref:t}):(0,d.jsx)(ei,{side:i,...o,ref:t})})}),ei=r.forwardRef((e,t)=>{let n=K(en,e.__scopeTooltip),i=X(en,e.__scopeTooltip),l=r.useRef(null),a=(0,o.s)(t,l),[u,s]=r.useState(null),{trigger:c,onClose:f}=n,p=l.current,{onPointerInTransitChange:m}=i,h=r.useCallback(()=>{s(null),m(!1)},[m]),g=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),m(!0)},[m]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&p){let e=e=>g(e,p),t=e=>g(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,g,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let l=t[e].x,a=t[e].y,u=t[o].x,s=t[o].y;a>r!=s>r&&n<(u-l)*(r-a)/(s-a)+l&&(i=!i)}return i}(n,u);r?h():i&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,u,f,h]),(0,d.jsx)(eu,{...e,ref:a})}),[eo,el]=U(q,{isInside:!1}),ea=(0,f.Dc)("TooltipContent"),eu=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:l,onPointerDownOutside:u,...s}=e,c=K(en,n),f=B(n),{onClose:p}=c;return r.useEffect(()=>(document.addEventListener(Y,p),()=>document.removeEventListener(Y,p)),[p]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,p]),(0,d.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,d.jsxs)(O,{"data-state":c.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,d.jsx)(ea,{children:i}),(0,d.jsx)(eo,{scope:n,isInside:!0,children:(0,d.jsx)(H,{id:c.contentId,role:"tooltip",children:o||i})})]})})});er.displayName=en;var es="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=B(n);return el(es,n).isInside?null:(0,d.jsx)(j,{...i,...r,ref:t})}).displayName=es;var ec=$,ef=Z,ed=Q,ep=er},84945:(e,t,n)=>{n.d(t,{BN:()=>m,ER:()=>h,Ej:()=>v,UE:()=>w,UU:()=>g,cY:()=>p,jD:()=>y,we:()=>f});var r=n(22475),i=n(12115),o=n(47650),l="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function a(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function u(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function s(e,t){let n=u(e);return Math.round(t*n)/n}function c(e){let t=i.useRef(e);return l(()=>{t.current=e}),t}function f(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:f=[],platform:d,elements:{reference:p,floating:m}={},transform:h=!0,whileElementsMounted:g,open:v}=e,[y,w]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[x,b]=i.useState(f);a(x,f)||b(f);let[E,R]=i.useState(null),[T,C]=i.useState(null),A=i.useCallback(e=>{e!==S.current&&(S.current=e,R(e))},[]),L=i.useCallback(e=>{e!==P.current&&(P.current=e,C(e))},[]),N=p||E,O=m||T,S=i.useRef(null),P=i.useRef(null),j=i.useRef(y),D=null!=g,k=c(g),M=c(d),I=c(v),_=i.useCallback(()=>{if(!S.current||!P.current)return;let e={placement:t,strategy:n,middleware:x};M.current&&(e.platform=M.current),(0,r.rD)(S.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==I.current};F.current&&!a(j.current,t)&&(j.current=t,o.flushSync(()=>{w(t)}))})},[x,t,n,M,I]);l(()=>{!1===v&&j.current.isPositioned&&(j.current.isPositioned=!1,w(e=>({...e,isPositioned:!1})))},[v]);let F=i.useRef(!1);l(()=>(F.current=!0,()=>{F.current=!1}),[]),l(()=>{if(N&&(S.current=N),O&&(P.current=O),N&&O){if(k.current)return k.current(N,O,_);_()}},[N,O,_,k,D]);let H=i.useMemo(()=>({reference:S,floating:P,setReference:A,setFloating:L}),[A,L]),U=i.useMemo(()=>({reference:N,floating:O}),[N,O]),W=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!U.floating)return e;let t=s(U.floating,y.x),r=s(U.floating,y.y);return h?{...e,transform:"translate("+t+"px, "+r+"px)",...u(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,h,U.floating,y.x,y.y]);return i.useMemo(()=>({...y,update:_,refs:H,elements:U,floatingStyles:W}),[y,_,H,U,W])}let d=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:i}).fn(t):{}:n?(0,r.UE)({element:n,padding:i}).fn(t):{}}}),p=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),m=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),h=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),g=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),v=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),y=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),w=(e,t)=>({...d(e),options:[e,t]})}}]);