"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5999],{25175:(e,a,s)=>{s.d(a,{A:()=>i});var r=s(95155),t=s(12115),l=s(29911);function i(e){let{trigger:a,items:s,className:i=""}=e,[n,o]=(0,t.useState)(!1),c=(0,t.useRef)(null);return(0,t.useEffect)(()=>{function e(e){c.current&&!c.current.contains(e.target)&&o(!1)}return n&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[n]),(0,r.jsxs)("div",{className:"relative ".concat(i),ref:c,children:[(0,r.jsxs)("button",{onClick:()=>o(!n),className:"flex items-center gap-2 px-4 py-1.5 bg-white rounded-full hover:bg-gray-50 transition-all duration-200 border border-gray-200 shadow-sm font-medium",children:[(0,r.jsx)("span",{className:"text-gray-800",children:a}),(0,r.jsx)("div",{className:"flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 transition-all duration-200 ".concat(n?"bg-blue-100":""),children:(0,r.jsx)(l.Vr3,{className:"transition-transform duration-200 ".concat(n?"rotate-180 text-blue-500":"text-gray-500"),size:10})})]}),n&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-10 overflow-hidden border border-gray-100 animate-fadeIn",children:[(0,r.jsx)("div",{className:"absolute w-3 h-3 bg-white transform rotate-45",style:{top:"-6px",right:"20px",boxShadow:"-2px -2px 2px rgba(0, 0, 0, 0.05)"}}),(0,r.jsx)("div",{className:"relative z-10 bg-white rounded-lg overflow-hidden py-1",children:s.map((e,a)=>(0,r.jsxs)("button",{onClick:()=>{o(!1),e.onClick()},className:"w-full text-left px-4 py-2.5 hover:bg-gray-50 flex items-center gap-3 transition-colors duration-150",children:[(0,r.jsx)("span",{className:"text-gray-500",children:e.icon}),(0,r.jsx)("span",{className:"text-gray-700 font-medium",children:e.label})]},a))})]})]})}},73970:(e,a,s)=>{s.d(a,{N:()=>r});let r=(0,s(73579).createClientComponentClient)()},74489:(e,a,s)=>{s.d(a,{Y:()=>l});var r=s(12115),t=s(35695);function l(e){let{popupId:a="default-popup-id",defaultTabId:s,queryParam:l="popup",closeOnNavigate:i=!0}=e||{};(0,t.useRouter)(),(0,t.usePathname)(),(0,t.useSearchParams)();let[n,o]=(0,r.useState)(!1),[c,d]=(0,r.useState)(s||"");return{isOpen:n,activeTabId:c,openPopup:e=>{d(e||s||""),o(!0)},closePopup:()=>{o(!1)},changeTab:e=>{d(e)}}}},81757:(e,a,s)=>{s.d(a,{A:()=>_});var r=s(95155),t=s(12115),l=s(29911),i=s(73970),n=s(17292),o=s(35695);function c(e){let{userId:a}=e,[s,l]=(0,t.useState)({passwordChange:!1,emailVerification:!1,profileIncomplete:!1}),[n,c]=(0,t.useState)({show_password_alert:!0,show_email_alert:!0,show_profile_alert:!0}),d=(0,o.useRouter)();(0,t.useEffect)(()=>{(async()=>{try{if(!a)return;let{data:e,error:s}=await i.N.from("profiles").select("password_changed, email_verified, full_name, phone, alerts_preferences").eq("id",a).single();if(s){console.error("Erro ao verificar alertas do usu\xe1rio:",s);return}e.alerts_preferences&&c(e.alerts_preferences),l({passwordChange:!1===e.password_changed,emailVerification:!1===e.email_verified,profileIncomplete:!e.full_name||!e.phone})}catch(e){console.error("Erro ao verificar alertas do usu\xe1rio:",e)}})()},[a]);let m=async()=>{try{let{data:{session:e}}=await i.N.auth.getSession();if(!e){console.error("Usu\xe1rio n\xe3o est\xe1 autenticado");return}let{error:a}=await i.N.auth.resetPasswordForEmail(e.user.email||"");if(a){console.error("Erro ao enviar email de verifica\xe7\xe3o:",a);return}alert("Email de verifica\xe7\xe3o enviado. Por favor, verifique sua caixa de entrada.")}catch(e){console.error("Erro ao enviar email de verifica\xe7\xe3o:",e)}},u=e=>{l(a=>({...a,[e]:!1}))};return s.passwordChange&&n.show_password_alert||s.emailVerification&&n.show_email_alert||s.profileIncomplete&&n.show_profile_alert?(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Avisos"}),s.passwordChange&&n.show_password_alert&&(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:"Voc\xea est\xe1 usando uma senha tempor\xe1ria. Por favor, altere sua senha para garantir a seguran\xe7a da sua conta."}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("div",{className:"-mx-2 -my-1.5 flex",children:[(0,r.jsx)("button",{type:"button",onClick:()=>{d.push("/change-password")},className:"bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600",children:"Alterar Senha"}),(0,r.jsx)("button",{type:"button",onClick:()=>u("passwordChange"),className:"ml-3 bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600",children:"Lembrar Depois"})]})})]})]})}),s.emailVerification&&n.show_email_alert&&(0,r.jsx)("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-blue-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"Seu email ainda n\xe3o foi verificado. Por favor, verifique seu email para garantir o acesso completo \xe0 sua conta."}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("div",{className:"-mx-2 -my-1.5 flex",children:[(0,r.jsx)("button",{type:"button",onClick:m,className:"bg-blue-50 px-2 py-1.5 rounded-md text-sm font-medium text-blue-800 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-50 focus:ring-blue-600",children:"Reenviar Email"}),(0,r.jsx)("button",{type:"button",onClick:()=>u("emailVerification"),className:"ml-3 bg-blue-50 px-2 py-1.5 rounded-md text-sm font-medium text-blue-800 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-50 focus:ring-blue-600",children:"Lembrar Depois"})]})})]})]})}),s.profileIncomplete&&n.show_profile_alert&&(0,r.jsx)("div",{className:"bg-gray-50 border-l-4 border-gray-400 p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm text-gray-700",children:"Seu perfil est\xe1 incompleto. Por favor, preencha todas as informa\xe7\xf5es para melhorar sua experi\xeancia."}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("button",{type:"button",onClick:()=>u("profileIncomplete"),className:"bg-gray-50 px-2 py-1.5 rounded-md text-sm font-medium text-gray-800 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-gray-600",children:"Entendi"})})]})]})})]}):null}function d(e){let{userId:a}=e,[s,l]=(0,t.useState)({show_password_alert:!0,show_email_alert:!0,show_profile_alert:!0}),[n,o]=(0,t.useState)(!1),[c,d]=(0,t.useState)(!1);(0,t.useEffect)(()=>{(async()=>{try{if(!a)return;let{data:e,error:s}=await i.N.from("profiles").select("alerts_preferences").eq("id",a).single();if(s){console.error("Erro ao buscar prefer\xeancias de alertas:",s);return}e&&e.alerts_preferences&&l(e.alerts_preferences)}catch(e){console.error("Erro ao buscar prefer\xeancias de alertas:",e)}})()},[a]);let m=e=>{l(a=>({...a,[e]:!a[e]}))},u=async()=>{try{o(!0);let{error:e}=await i.N.from("profiles").update({alerts_preferences:s}).eq("id",a);if(e){console.error("Erro ao salvar prefer\xeancias de alertas:",e);return}d(!0),setTimeout(()=>d(!1),3e3)}catch(e){console.error("Erro ao salvar prefer\xeancias de alertas:",e)}finally{o(!1)}};return(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Configura\xe7\xf5es de Avisos"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Escolha quais avisos voc\xea deseja ver na p\xe1gina de configura\xe7\xf5es."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"password-alert",type:"checkbox",checked:s.show_password_alert,onChange:()=>m("show_password_alert"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"password-alert",className:"ml-2 block text-sm text-gray-700",children:"Mostrar aviso de senha tempor\xe1ria"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"email-alert",type:"checkbox",checked:s.show_email_alert,onChange:()=>m("show_email_alert"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"email-alert",className:"ml-2 block text-sm text-gray-700",children:"Mostrar aviso de verifica\xe7\xe3o de email"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"profile-alert",type:"checkbox",checked:s.show_profile_alert,onChange:()=>m("show_profile_alert"),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"profile-alert",className:"ml-2 block text-sm text-gray-700",children:"Mostrar aviso de perfil incompleto"})]})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center",children:[(0,r.jsx)("button",{onClick:u,disabled:n,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:n?"Salvando...":"Salvar Prefer\xeancias"}),c&&(0,r.jsx)("span",{className:"ml-3 text-sm text-green-600",children:"Prefer\xeancias salvas com sucesso!"})]})]})}var m=s(25593),u=s(38135),x=s(22422),h=s(73579),p=s(30285),f=s(62523),g=s(88262),v=s(51154),j=s(40646);function b(){let e=(0,h.createClientComponentClient)(),[a,s]=(0,t.useState)(""),[l,i]=(0,t.useState)(""),[n,o]=(0,t.useState)(!1),[c,d]=(0,t.useState)(!1),[m,u]=(0,t.useState)(!1),[x,b]=(0,t.useState)(!0);async function w(){if(!a){(0,g.o)({title:"Erro",description:"Por favor, informe seu n\xfamero de telefone",variant:"destructive"});return}u(!0);try{let e=await fetch("/api/v1/whatsapp/mock-verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phone:a})}),s=await e.json();if(!e.ok)throw Error(s.error||"Erro ao iniciar verifica\xe7\xe3o");o(!0),(0,g.o)({title:"C\xf3digo enviado",description:"Um c\xf3digo de verifica\xe7\xe3o foi enviado para o seu WhatsApp"})}catch(e){(0,g.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel enviar o c\xf3digo",variant:"destructive"})}finally{u(!1)}}async function N(){if(!l){(0,g.o)({title:"Erro",description:"Por favor, informe o c\xf3digo de verifica\xe7\xe3o",variant:"destructive"});return}u(!0);try{let e=await fetch("/api/v1/whatsapp/mock-verify",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({phone:a,code:l})}),s=await e.json();if(!e.ok)throw Error(s.error||"C\xf3digo inv\xe1lido");d(!0),o(!1),(0,g.o)({title:"N\xfamero verificado",description:"Seu n\xfamero de WhatsApp foi verificado com sucesso"})}catch(e){(0,g.o)({title:"Erro",description:e.message||"N\xe3o foi poss\xedvel verificar o c\xf3digo",variant:"destructive"})}finally{u(!1)}}return((0,t.useEffect)(()=>{(async function(){try{let{data:{session:a}}=await e.auth.getSession();if(a){let{data:r,error:t}=await e.from("profiles").select("phone, phone_verified").eq("id",a.user.id).single();if(t)throw t;r&&(s(r.phone||""),d(r.phone_verified||!1))}}catch(e){console.error("Erro ao carregar dados do usu\xe1rio:",e)}finally{b(!1)}})()},[e]),x)?(0,r.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,r.jsx)(v.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"mb-4",children:"Conecte seu WhatsApp para receber notifica\xe7\xf5es importantes sobre suas campanhas, novos influenciadores e relat\xf3rios de desempenho."}),c?(0,r.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 text-green-500 mr-2"}),(0,r.jsxs)("p",{className:"text-green-700 font-medium",children:["Seu n\xfamero de WhatsApp est\xe1 verificado: ",a]})]}),(0,r.jsx)(p.$,{variant:"outline",className:"mt-2",onClick:function(){d(!1),s(""),i(""),o(!1)},children:"Alterar n\xfamero"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Seu n\xfamero de WhatsApp"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(f.p,{type:"tel",placeholder:"Ex: +5511999999999",value:a,onChange:e=>s(e.target.value),className:"flex-1",disabled:n||m}),(0,r.jsxs)(p.$,{onClick:w,disabled:!a||n||m,children:[m&&!n?(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,m&&!n?"Enviando...":"Verificar"]})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Informe seu n\xfamero no formato internacional com o c\xf3digo do pa\xeds"})]}),n&&(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-blue-700 mb-2",children:"Enviamos um c\xf3digo de verifica\xe7\xe3o para o seu WhatsApp. Por favor, informe o c\xf3digo abaixo:"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(f.p,{type:"text",placeholder:"C\xf3digo de verifica\xe7\xe3o",value:l,onChange:e=>i(e.target.value),className:"flex-1",disabled:m}),(0,r.jsxs)(p.$,{onClick:N,disabled:!l||m,children:[m?(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}):null,m?"Verificando...":"Confirmar"]})]})]})]})]})})}var w=s(80333),N=s(85057);function y(e){let{whatsappOnly:a=!1}=e,s=(0,h.createClientComponentClient)(),[l,i]=(0,t.useState)({enable_notifications:!0,whatsapp_enabled:!0,whatsapp_new_influencer_joined:!0,whatsapp_new_post_created:!0,whatsapp_weekly_report:!0,email_enabled:!0,email_new_influencer_joined:!0,email_new_post_created:!0,email_weekly_report:!0}),[n,o]=(0,t.useState)(!0),[c,d]=(0,t.useState)(!1);async function m(){d(!0);try{if(!(await fetch("/api/v1/notifications/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)})).ok)throw Error("Erro ao salvar prefer\xeancias");(0,g.o)({title:"Prefer\xeancias salvas",description:"Suas prefer\xeancias de notifica\xe7\xe3o foram atualizadas com sucesso"})}catch(e){console.error("Erro ao salvar prefer\xeancias:",e),(0,g.o)({title:"Erro",description:"N\xe3o foi poss\xedvel salvar suas prefer\xeancias",variant:"destructive"})}finally{d(!1)}}function u(e,a){i(s=>({...s,[e]:a}))}return((0,t.useEffect)(()=>{(async function(){try{let{data:{session:e}}=await s.auth.getSession();if(!e)return;let a=await fetch("/api/v1/notifications/settings");if(!a.ok)throw Error("Erro ao carregar prefer\xeancias");let r=await a.json();i(e=>({...e,...r}))}catch(e){console.error("Erro ao carregar prefer\xeancias:",e)}finally{o(!1)}})()},[s]),n)?(0,r.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,r.jsx)(v.A,{className:"h-6 w-6 animate-spin text-gray-500"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[!a&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"enable_notifications",className:"text-base font-medium",children:"Ativar notifica\xe7\xf5es"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es sobre campanhas, influenciadores e relat\xf3rios"})]}),(0,r.jsx)(w.d,{id:"enable_notifications",checked:l.enable_notifications,onCheckedChange:e=>u("enable_notifications",e)})]}),l.enable_notifications&&(0,r.jsxs)(r.Fragment,{children:[!a&&(0,r.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Canais de notifica\xe7\xe3o"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"whatsapp_enabled",className:"font-medium",children:"WhatsApp"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es via WhatsApp"})]}),(0,r.jsx)(w.d,{id:"whatsapp_enabled",checked:l.whatsapp_enabled,onCheckedChange:e=>u("whatsapp_enabled",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"email_enabled",className:"font-medium",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es via email"})]}),(0,r.jsx)(w.d,{id:"email_enabled",checked:l.email_enabled,onCheckedChange:e=>u("email_enabled",e)})]})]})]}),(l.whatsapp_enabled||a)&&(0,r.jsxs)("div",{className:a?"":"border-t pt-4 mt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Notifica\xe7\xf5es via WhatsApp"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"whatsapp_new_influencer_joined",className:"font-medium",children:"Novos influenciadores"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es quando um influenciador aceitar participar da sua campanha"})]}),(0,r.jsx)(w.d,{id:"whatsapp_new_influencer_joined",checked:l.whatsapp_new_influencer_joined,onCheckedChange:e=>u("whatsapp_new_influencer_joined",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"whatsapp_new_post_created",className:"font-medium",children:"Novos posts"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es quando um influenciador criar um novo post"})]}),(0,r.jsx)(w.d,{id:"whatsapp_new_post_created",checked:l.whatsapp_new_post_created,onCheckedChange:e=>u("whatsapp_new_post_created",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"whatsapp_weekly_report",className:"font-medium",children:"Relat\xf3rios semanais"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba relat\xf3rios semanais sobre o desempenho das suas campanhas"})]}),(0,r.jsx)(w.d,{id:"whatsapp_weekly_report",checked:l.whatsapp_weekly_report,onCheckedChange:e=>u("whatsapp_weekly_report",e)})]})]})]}),l.email_enabled&&!a&&(0,r.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Notifica\xe7\xf5es via Email"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"email_new_influencer_joined",className:"font-medium",children:"Novos influenciadores"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es quando um influenciador aceitar participar da sua campanha"})]}),(0,r.jsx)(w.d,{id:"email_new_influencer_joined",checked:l.email_new_influencer_joined,onCheckedChange:e=>u("email_new_influencer_joined",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"email_new_post_created",className:"font-medium",children:"Novos posts"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba notifica\xe7\xf5es quando um influenciador criar um novo post"})]}),(0,r.jsx)(w.d,{id:"email_new_post_created",checked:l.email_new_post_created,onCheckedChange:e=>u("email_new_post_created",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N.J,{htmlFor:"email_weekly_report",className:"font-medium",children:"Relat\xf3rios semanais"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receba relat\xf3rios semanais sobre o desempenho das suas campanhas"})]}),(0,r.jsx)(w.d,{id:"email_weekly_report",checked:l.email_weekly_report,onCheckedChange:e=>u("email_weekly_report",e)})]})]})]})]}),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)(p.$,{onClick:m,disabled:c,children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):"Salvar prefer\xeancias"})})]})}function _(e){let{isOpen:a,onClose:s,onSaved:o,userType:h,defaultTab:p="info"}=e,[f,g]=(0,t.useState)(null),[v,j]=(0,t.useState)(!1),[w,N]=(0,t.useState)(null),[_,C]=(0,t.useState)(""),[k,S]=(0,t.useState)(""),[E,A]=(0,t.useState)(""),[z,R]=(0,t.useState)(""),[P,F]=(0,t.useState)(""),[q,M]=(0,t.useState)(""),[V,I]=(0,t.useState)(""),[W,J]=(0,t.useState)(!0),[L,B]=(0,t.useState)(p);(0,t.useEffect)(()=>{a&&(async()=>{var e,a,s,r,t,l,n,o,c,d;let{data:{session:m}}=await i.N.auth.getSession();if(m){if(g(m.user.id),"restaurant"===h){let{data:l}=await i.N.from("restaurants").select("*").eq("owner_id",m.user.id).maybeSingle();l&&(N(l.id),C(null!==(e=l.name)&&void 0!==e?e:""),S(null!==(a=l.responsible)&&void 0!==a?a:""),A(null!==(s=l.email)&&void 0!==s?s:""),R(null!==(r=l.phone)&&void 0!==r?r:""),F(null!==(t=l.location)&&void 0!==t?t:""))}else{let{data:e}=await i.N.from("profiles").select("*").eq("id",m.user.id).maybeSingle();e&&(C(null!==(l=e.full_name)&&void 0!==l?l:""),A(null!==(n=e.email)&&void 0!==n?n:""),R(null!==(o=e.phone)&&void 0!==o?o:""));let{data:a}=await i.N.from("influencer_profiles").select("*").eq("id",m.user.id).maybeSingle();a&&(M(null!==(c=a.bio)&&void 0!==c?c:""),I(null!==(d=a.instagram_username)&&void 0!==d?d:""))}}})()},[a,h]);let T=async()=>{if(w){j(!0);try{let{data:e,error:a}=await i.N.from("restaurants").update({name:_,responsible:k,email:E,phone:z,location:P}).eq("id",w).select();!a&&e&&e.length>0?(null==o||o(e[0]),s()):a&&alert("Erro ao salvar: "+a.message)}catch(e){console.error("Unexpected error saving restaurant info:",e),alert("Erro inesperado ao salvar.")}finally{j(!1)}}},U=async()=>{try{if(j(!0),!f){console.error("Usu\xe1rio n\xe3o encontrado");return}let{error:e}=await i.N.from("profiles").update({full_name:_,email:E,phone:z,updated_at:new Date().toISOString()}).eq("id",f);if(e)throw Error("Erro ao atualizar perfil: ".concat(e.message));let{error:a}=await i.N.from("influencer_profiles").update({bio:q,instagram_username:V,updated_at:new Date().toISOString()}).eq("id",f);if(a)throw Error("Erro ao atualizar perfil de influenciador: ".concat(a.message));let{data:r}=await i.N.from("profiles").select("*").eq("id",f).single();null==o||o(r),s()}catch(e){console.error("Erro ao salvar configura\xe7\xf5es:",e),alert("Erro ao salvar: ".concat(e.message))}finally{j(!1)}},$=[{id:"info",label:"Perfil",icon:(0,r.jsx)(l.__w,{className:"text-green-500"}),color:"green",content:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[(0,r.jsx)(l.__w,{className:"text-green-500"})," Informa\xe7\xf5es B\xe1sicas"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsx)(l.x$1,{className:"text-gray-500"})," Nome ","restaurant"===h?"do Restaurante":"Completo"]}),(0,r.jsx)("input",{type:"text",value:_,onChange:e=>C(e.target.value),className:"w-full",placeholder:"restaurant"===h?"Nome do estabelecimento":"Seu nome completo"})]}),"restaurant"===h&&(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsx)(l.x$1,{className:"text-gray-500"})," Respons\xe1vel"]}),(0,r.jsx)("input",{type:"text",value:k,onChange:e=>S(e.target.value),className:"w-full",placeholder:"Nome do respons\xe1vel"})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,r.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,r.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),"Email"]}),(0,r.jsx)("input",{type:"email",value:E,onChange:e=>A(e.target.value),className:"w-full",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"form-group",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),"Telefone"]}),(0,r.jsx)("input",{type:"tel",value:z,onChange:e=>R(e.target.value),className:"w-full",placeholder:"(00) 00000-0000"})]}),"restaurant"===h&&(0,r.jsxs)("div",{className:"form-group md:col-span-2",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),"Localiza\xe7\xe3o"]}),(0,r.jsx)("input",{type:"text",value:P,onChange:e=>F(e.target.value),className:"w-full",placeholder:"Endere\xe7o completo"})]}),"influencer"===h&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"form-group md:col-span-2",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-gray-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),"Bio"]}),(0,r.jsx)("textarea",{value:q,onChange:e=>M(e.target.value),className:"w-full",rows:3,placeholder:"Uma breve descri\xe7\xe3o sobre voc\xea"})]}),(0,r.jsxs)("div",{className:"form-group md:col-span-2",children:[(0,r.jsxs)("label",{className:"block mb-2 text-sm font-medium flex items-center gap-2",children:[(0,r.jsx)(l.ao$,{className:"text-gray-500"})," Instagram"]}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-3 text-gray-500 bg-gray-100 border-none rounded-l-lg",children:"@"}),(0,r.jsx)("input",{type:"text",value:V,onChange:e=>I(e.target.value),className:"flex-1 min-w-0 block w-full rounded-none rounded-r-lg",placeholder:"seu_usuario"})]})]})]})]}),(0,r.jsx)("div",{className:"flex justify-end mt-8",children:(0,r.jsx)("button",{onClick:()=>{"restaurant"===h?T():U()},disabled:v,className:"px-6 py-2.5 rounded-lg bg-gray-800 text-white text-sm hover:bg-gray-700 transition-all disabled:opacity-50 font-medium flex items-center gap-2",children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Salvando..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Salvar"]})})})]})},{id:"security",label:"Seguran\xe7a",icon:(0,r.jsx)(l.JhU,{className:"text-red-500"}),color:"red",lazyLoad:!0,content:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[(0,r.jsx)(l.JhU,{className:"text-red-500"})," Configura\xe7\xf5es de Seguran\xe7a"]}),(0,r.jsx)("p",{className:"text-gray-500 mb-3 text-sm",children:"As configura\xe7\xf5es de seguran\xe7a estar\xe3o dispon\xedveis em breve."}),(0,r.jsx)("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(l.SMR,{className:"h-5 w-5 text-yellow-400"})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("p",{className:"text-sm text-yellow-700",children:"Recomendamos alterar sua senha regularmente e ativar a autentica\xe7\xe3o de dois fatores quando dispon\xedvel."})})]})})]})},..."influencer"===h?[{id:"social",label:"Redes Sociais",icon:(0,r.jsx)(l.eb3,{className:"text-purple-500"}),color:"purple",lazyLoad:!0,content:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[(0,r.jsx)(l.eb3,{className:"text-purple-500"})," Conex\xf5es com Redes Sociais"]}),(0,r.jsx)("p",{className:"text-gray-500 mb-3 text-sm",children:"Conecte suas redes sociais para participar de campanhas e monitorar seu desempenho."}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-base font-medium mb-2 flex items-center gap-2",children:[(0,r.jsx)(l.ao$,{className:"text-pink-500"})," Instagram"]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"Conecte sua conta do Instagram para participar das competi\xe7\xf5es e ter seus posts contabilizados automaticamente para ganhar pontos e pr\xeamios."}),f&&(0,r.jsx)(m.A,{userId:f})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-base font-medium mb-2 flex items-center gap-2",children:[(0,r.jsx)(l.EcP,{className:"text-green-500"})," WhatsApp"]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-3",children:"Conecte seu WhatsApp para receber notifica\xe7\xf5es importantes sobre campanhas, atualiza\xe7\xf5es de ranking e pagamentos."}),(0,r.jsx)(u.A,{})]})]})]})}]:[],{id:"whatsapp",label:"WhatsApp",icon:(0,r.jsx)(l.EcP,{className:"text-green-600"}),color:"green",lazyLoad:!0,content:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[(0,r.jsx)(l.EcP,{className:"text-green-600"})," Configura\xe7\xf5es de WhatsApp"]}),(0,r.jsx)("p",{className:"text-gray-500 mb-3 text-sm",children:"Configure suas prefer\xeancias de notifica\xe7\xe3o via WhatsApp."}),(0,r.jsx)("div",{className:"space-y-6",children:"influencer"===h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{}),(0,r.jsx)(x.A,{})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b,{}),(0,r.jsx)(y,{})]})})]})},{id:"alerts",label:"Avisos",icon:(0,r.jsx)(l.jNV,{className:"text-yellow-500"}),color:"yellow",lazyLoad:!0,content:(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-lg font-bold mb-3 flex items-center gap-2",children:[(0,r.jsx)(l.jNV,{className:"text-yellow-500"})," Avisos do Sistema"]}),(0,r.jsx)("p",{className:"text-gray-500 mb-3 text-sm",children:"Escolha quais avisos voc\xea deseja ver na p\xe1gina de configura\xe7\xf5es."}),f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c,{userId:f}),(0,r.jsx)(d,{userId:f})]}):(0,r.jsx)("div",{className:"bg-gray-100 p-4 rounded",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Carregando dados de usu\xe1rio..."})})]})}],O=t.useMemo(()=>$,[]);return(0,r.jsx)(n.A,{id:"settings-modal",isOpen:a,onClose:()=>{s()},title:"Configura\xe7\xf5es",tabs:O,defaultTabId:p,onTabChange:e=>{B(e)},size:"small",minContentHeight:"500px",className:"settings-modal-premium",overlayClassName:"settings-modal-premium-overlay"})}}}]);