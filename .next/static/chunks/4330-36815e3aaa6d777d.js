"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4330],{20211:(e,a,t)=>{t.d(a,{default:()=>o});var r=t(95155);t(12115);var s=t(35695),n=t(29911),l=t(60440);function o(e){let{children:a,title:t,backLink:o}=e,i=(0,s.useRouter)();return(0,s.usePathname)(),(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[o&&(0,r.jsx)("div",{className:"mt-2 mb-4",children:(0,r.jsx)("button",{onClick:()=>i.push(o),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,r.jsx)(n.QVr,{className:"text-gray-600"})})}),(0,r.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-f5f5f7 overflow-y-auto flex-1 flex flex-col shadow-md",style:{minHeight:"calc(100vh - 6rem)",maxHeight:"calc(100vh - 6rem)"},children:(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:t}),a]})})]})})}t(52643),t(13568)},34603:(e,a,t)=>{t.d(a,{d:()=>l});var r=t(95155),s=t(12115),n=t(39688);let l=s.forwardRef((e,a)=>{let{icon:t,iconPosition:s="left",isLoading:l=!1,fullWidth:o=!1,children:i,className:d,disabled:c,...m}=e;return(0,r.jsxs)("button",{ref:a,className:(0,n.QP)("rounded-md font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2","px-4 py-2 text-sm border border-gray-300 shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 disabled:opacity-50",o&&"w-full",l&&"opacity-70 cursor-not-allowed",d),disabled:c||l,"aria-busy":l,...m,children:[l&&(0,r.jsx)("span",{className:"mr-2 animate-spin"}),t&&"left"===s&&(0,r.jsx)("span",{className:"mr-2",children:t}),i,t&&"right"===s&&(0,r.jsx)("span",{className:"ml-2",children:t})]})});l.displayName="SecondaryActionButton"},52643:(e,a,t)=>{t.d(a,{N:()=>s,b:()=>n});var r=t(73579);let s=(0,r.createClientComponentClient)();function n(){return(0,r.createClientComponentClient)()}},60440:(e,a,t)=>{t.d(a,{A:()=>d});var r=t(95155),s=t(12115),n=t(35695),l=t(52643),o=t(81452),i=t(13568);function d(e){let{children:a}=e,t=(0,n.useRouter)(),[d,c]=(0,s.useState)(!0),[m,u]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:a}=await l.N.auth.getSession();if(a)throw a;if(!e){t.push("/login?redirect=/admin/campaigns");return}let r=e.user.email;r&&(0,o.K)(r)?u(!0):(i.oR.error("Voc\xea n\xe3o tem permiss\xe3o para acessar esta \xe1rea"),t.push("/"))}catch(e){console.error("Erro ao verificar status de administrador:",e),i.oR.error("Erro ao verificar suas permiss\xf5es"),t.push("/")}finally{c(!1)}})()},[t]),d)?(0,r.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):m?(0,r.jsx)(r.Fragment,{children:a}):null}},74436:(e,a,t)=>{t.d(a,{k5:()=>c});var r=t(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=r.createContext&&r.createContext(s),l=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e}).apply(this,arguments)}function i(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);a&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function d(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?i(Object(t),!0).forEach(function(a){var r,s,n;r=e,s=a,n=t[a],(s=function(e){var a=function(e,a){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,a||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==typeof a?a:a+""}(s))in r?Object.defineProperty(r,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function c(e){return a=>r.createElement(m,o({attr:d({},e.attr)},a),function e(a){return a&&a.map((a,t)=>r.createElement(a.tag,d({key:t},a.attr),e(a.child)))}(e.child))}function m(e){var a=a=>{var t,{attr:s,size:n,title:i}=e,c=function(e,a){if(null==e)return{};var t,r,s=function(e,a){if(null==e)return{};var t={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(a.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,a);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)t=n[r],!(a.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,l),m=n||a.size||"1em";return a.className&&(t=a.className),e.className&&(t=(t?t+" ":"")+e.className),r.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},a.attr,s,c,{className:t,style:d(d({color:e.color||a.color},a.style),e.style),height:m,width:m,xmlns:"http://www.w3.org/2000/svg"}),i&&r.createElement("title",null,i),e.children)};return void 0!==n?r.createElement(n.Consumer,null,e=>a(e)):a(s)}},81452:(e,a,t)=>{t.d(a,{I:()=>r,K:()=>s});let r=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];function s(e){return r.includes(e.toLowerCase())}},98301:(e,a,t)=>{t.d(a,{A:()=>c});var r=t(95155),s=t(12115),n=t(52643),l=t(62177),o=t(29911),i=t(13568),d=t(34603);let c=e=>{var a,t;let{onCancel:c,onSuccess:m,initialData:u,isEditing:p=!1}=e,[g,h]=(0,s.useState)([]),[f,x]=(0,s.useState)(!1),b=e=>{if(!e)return"";try{let a="string"==typeof e?e:e.toISOString();return new Date(a).toISOString().split("T")[0]}catch(a){if(console.warn("Could not parse date input for YYYY-MM-DD formatting:",e,a),"string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e))return e;return""}},{register:_,handleSubmit:v,control:j,setValue:y,formState:{errors:N}}=(0,l.mN)({defaultValues:{name:(null==u?void 0:u.name)||"",description:(null==u?void 0:u.description)||"",restaurant_id:(null==u?void 0:u.restaurant_id)||"",start_date:(null==u?void 0:u.start_date)?b(u.start_date):new Date().toISOString().split("T")[0],end_date:(null==u?void 0:u.end_date)?b(u.end_date):new Date(Date.now()+2592e6).toISOString().split("T")[0],plan_type:(null==u?void 0:u.plan_type)||"prata_mensal",campaign_goal:(null==u?void 0:u.campaign_goal)||"awareness",available_visit_dates:(null==u?void 0:u.available_visit_dates)||"",key_message:(null==u?void 0:u.key_message)||"",content_type_suggestions:(null==u?void 0:u.content_type_suggestions)?Array.isArray(u.content_type_suggestions)?u.content_type_suggestions.join(", "):u.content_type_suggestions:"Reels, Stories",tone_of_voice:(null==u?void 0:u.tone_of_voice)||"",influencer_post_deadline:(null==u?void 0:u.influencer_post_deadline)?b(u.influencer_post_deadline):new Date(Date.now()+1296e6).toISOString().split("T")[0],restaurant_selection_deadline:(null==u?void 0:u.restaurant_selection_deadline)?b(u.restaurant_selection_deadline):new Date(Date.now()+6048e5).toISOString().split("T")[0],hashtags:(null==u?void 0:null===(a=u.requirements)||void 0===a?void 0:a.hashtags)?u.requirements.hashtags.join(", "):"#gastronomia, #foodie",mentions:(null==u?void 0:null===(t=u.requirements)||void 0===t?void 0:t.mentions)?u.requirements.mentions.join(", "):"@restaurante",color:(null==u?void 0:u.color)||"bg-green-100",textColor:(null==u?void 0:u.textColor)||"text-green-800",progressColor:(null==u?void 0:u.progressColor)||"bg-green-500",status:(null==u?void 0:u.status)||"draft",briefing:(null==u?void 0:u.briefing)||"",influencer_count_target:(null==u?void 0:u.influencer_count_target)||1}});(0,s.useEffect)(()=>{(async()=>{try{let{data:e,error:a}=await n.N.from("restaurant_profiles").select("id, business_name").order("business_name");if(a)throw a;if(h(e||[]),console.log("Neg\xf3cios fetched:",e),p&&(null==u?void 0:u.restaurant_id)){let a=null==e?void 0:e.find(e=>e.id===u.restaurant_id);a?y("restaurant_id",a.id):console.warn("Current neg\xf3cio not found in the list")}}catch(e){console.error("Erro ao buscar restaurantes:",e),i.oR.error("N\xe3o foi poss\xedvel carregar a lista de restaurantes")}})()},[p,null==u?void 0:u.restaurant_id,y]),(0,s.useEffect)(()=>{if(p&&(null==u?void 0:u.restaurant_id)&&restaurants.length>0){let e=restaurants.find(e=>e.id===u.restaurant_id);e?(y("restaurant_id",e.id),console.log("Re-setting restaurant_id in separate effect: ".concat(e.id))):restaurants.find(e=>e.id===u.restaurant_id)||console.warn("Initial restaurant_id ".concat(u.restaurant_id," not found in fetched restaurants. Clearing selection."))}},[p,null==u?void 0:u.restaurant_id,restaurants,y]);let w=async e=>{try{let a,t;x(!0),console.log("Dados do formul\xe1rio:",e);let r={};if(console.log("Raw form data from react-hook-form:",e),console.log("Form Data - restaurant_id (before payload):",e.restaurant_id),console.log("Form Data - start_date (before payload):",e.start_date),!e.start_date)throw Error("Data de in\xedcio da campanha \xe9 obrigat\xf3ria");if(!e.end_date)throw Error("Data de t\xe9rmino da campanha \xe9 obrigat\xf3ria");if(!e.restaurant_id)throw Error("Sele\xe7\xe3o de restaurante \xe9 obrigat\xf3ria");r={name:e.name,description:e.description,restaurant_id:e.restaurant_id,start_date:e.start_date,end_date:e.end_date,status:e.status,plan_type:e.plan_type,campaign_goal:e.campaign_goal||"awareness",available_visit_dates:e.available_visit_dates||"",key_message:e.key_message,content_type_suggestions:e.content_type_suggestions.split(",").map(e=>e.trim()).filter(e=>e),tone_of_voice:e.tone_of_voice,influencer_post_deadline:e.influencer_post_deadline,restaurant_selection_deadline:e.restaurant_selection_deadline,requirements:{hashtags:e.hashtags.split(",").map(e=>e.trim()).filter(e=>e),mentions:e.mentions.split(",").map(e=>e.trim()).filter(e=>e)},color:e.color,text_color:e.textColor,progress_color:e.progressColor,briefing:e.briefing||"Default briefing",influencer_count_target:e.influencer_count_target},p&&(null==u?void 0:u.id)?r.updated_at=new Date().toISOString():r.created_at=new Date().toISOString(),console.log("Payload to be sent to API:",r),console.log("Payload - restaurant_id:",r.restaurant_id),console.log("Payload - start_date:",r.start_date);let{data:s,error:l}=await n.N.from("restaurant_profiles").select("id, business_name").eq("id",r.restaurant_id).single();if(l||!s)throw Error("Restaurante com ID ".concat(r.restaurant_id," n\xe3o encontrado"));if(console.log("Restaurant found:",s),p&&(null==u?void 0:u.id)){console.log("Atualizando campanha existente:",u.id);let{data:e,error:s}=await n.N.from("campaigns").update(r).eq("id",u.id).select("*");a=e,t=s}else{console.log("Criando nova campanha");let{data:e,error:s}=await n.N.from("campaigns").insert(r).select("*");a=e,t=s}if(t)throw console.error(p?"Erro ao atualizar campanha:":"Erro ao criar campanha:",t),Error(t.message||"Erro desconhecido");if(console.log("Opera\xe7\xe3o bem-sucedida:",a),a&&a.length>0){let e=a[0];console.log("Dados da campanha ap\xf3s opera\xe7\xe3o:",e),console.log("Restaurant ID saved:",e.restaurant_id),i.oR.success(p?"Campanha atualizada com sucesso!":"Campanha criada com sucesso!"),m(e.id)}else console.warn("Opera\xe7\xe3o conclu\xedda, mas nenhum dado retornado do DB."),(0,i.oR)("Opera\xe7\xe3o conclu\xedda, mas os dados n\xe3o foram retornados. Por favor, verifique."),m((null==u?void 0:u.id)||"unknown")}catch(e){console.error("Erro no onSubmit:",e),i.oR.error("Erro: ".concat(e.message||"Erro desconhecido"))}finally{x(!1)}};return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-6 border-b",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Informa\xe7\xf5es da Campanha"}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:p?"Atualize os campos da campanha":"Preencha todos os campos para criar uma nova campanha"})]}),(0,r.jsxs)("form",{onSubmit:v(w),children:[(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium border-b pb-2 mb-4",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome da Campanha *"}),(0,r.jsx)("input",{id:"name",type:"text",..._("name",{required:"Nome \xe9 obrigat\xf3rio"}),className:"w-full input-class",placeholder:"Ex: Festival de Inverno"}),N.name&&(0,r.jsx)("p",{className:"form-error",children:N.name.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"restaurant_id",className:"block text-sm font-medium text-gray-700 mb-1",children:"Neg\xf3cio *"}),(0,r.jsxs)("select",{id:"restaurant_id",..._("restaurant_id",{required:"Neg\xf3cio \xe9 obrigat\xf3rio"}),className:"w-full input-class",children:[(0,r.jsx)("option",{value:"",children:"Selecione um neg\xf3cio"}),g.map(e=>(0,r.jsx)("option",{value:e.id,children:e.business_name},e.id))]}),N.restaurant_id&&(0,r.jsx)("p",{className:"form-error",children:N.restaurant_id.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Descri\xe7\xe3o *"}),(0,r.jsx)("textarea",{id:"description",..._("description",{required:"Descri\xe7\xe3o \xe9 obrigat\xf3ria"}),rows:3,className:"w-full input-class",placeholder:"Descreva os objetivos e detalhes da campanha."}),N.description&&(0,r.jsx)("p",{className:"form-error",children:N.description.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,r.jsxs)("select",{id:"status",..._("status"),className:"w-full input-class",children:[(0,r.jsx)("option",{value:"draft",children:"Rascunho"}),(0,r.jsx)("option",{value:"active",children:"Ativa"}),(0,r.jsx)("option",{value:"completed",children:"Conclu\xedda"}),(0,r.jsx)("option",{value:"cancelled",children:"Cancelada"})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium border-b pb-2 mb-4",children:"Objetivos e Planejamento"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"campaign_goal",className:"block text-sm font-medium text-gray-700 mb-1",children:"Objetivo Principal da Campanha *"}),(0,r.jsxs)("select",{id:"campaign_goal",..._("campaign_goal",{required:"Objetivo \xe9 obrigat\xf3rio"}),className:"w-full input-class",children:[(0,r.jsx)("option",{value:"",children:"Selecione um objetivo"}),(0,r.jsx)("option",{value:"awareness",children:"Awareness (Conscientiza\xe7\xe3o da marca)"}),(0,r.jsx)("option",{value:"engagement",children:"Engagement (Engajamento com o p\xfablico)"}),(0,r.jsx)("option",{value:"conversion",children:"Conversion (Convers\xe3o de vendas)"}),(0,r.jsx)("option",{value:"content",children:"Content (Gera\xe7\xe3o de conte\xfado)"})]}),N.campaign_goal&&(0,r.jsx)("p",{className:"form-error",children:N.campaign_goal.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"plan_type",className:"block text-sm font-medium text-gray-700 mb-1",children:"Plano Contratado *"}),(0,r.jsx)("select",{id:"plan_type",..._("plan_type",{required:"Plano \xe9 obrigat\xf3rio"}),className:"w-full input-class",children:[{value:"prata_mensal",label:"Prata Mensal (4 influenciadores)"},{value:"gold_mensal",label:"Gold Mensal (6 influenciadores)"},{value:"silver_mensal",label:"Silver Mensal (8 influenciadores)"},{value:"prata_semestral",label:"Prata Semestral (4 influenciadores/m\xeas)"},{value:"gold_semestral",label:"Gold Semestral (6 influenciadores/m\xeas)"},{value:"silver_semestral",label:"Silver Semestral (8 influenciadores/m\xeas)"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),N.plan_type&&(0,r.jsx)("p",{className:"form-error",children:N.plan_type.message})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium border-b pb-2 mb-4",children:"Briefing e Datas Importantes"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"briefing",className:"block text-sm font-medium text-gray-700 mb-1",children:"Briefing *"}),(0,r.jsx)("textarea",{id:"briefing",..._("briefing",{required:"Briefing \xe9 obrigat\xf3rio"}),rows:4,className:"w-full input-class",placeholder:"Detalhes sobre a campanha, objetivos, p\xfablico-alvo, etc."}),N.briefing&&(0,r.jsx)("p",{className:"form-error",children:N.briefing.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"key_message",className:"block text-sm font-medium text-gray-700 mb-1",children:"Mensagem-Chave *"}),(0,r.jsx)("textarea",{id:"key_message",..._("key_message",{required:"Mensagem-chave \xe9 obrigat\xf3ria"}),rows:2,className:"w-full input-class",placeholder:"A principal mensagem que os influenciadores devem transmitir."}),N.key_message&&(0,r.jsx)("p",{className:"form-error",children:N.key_message.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"content_type_suggestions",className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipos de Conte\xfado Sugeridos (separados por v\xedrgula)"}),(0,r.jsx)("input",{id:"content_type_suggestions",type:"text",..._("content_type_suggestions"),className:"w-full input-class",placeholder:"Ex: Reels, Stories, Post Carrossel"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"tone_of_voice",className:"block text-sm font-medium text-gray-700 mb-1",children:"Tom de Voz"}),(0,r.jsx)("input",{id:"tone_of_voice",type:"text",..._("tone_of_voice"),className:"w-full input-class",placeholder:"Ex: Divertido e informal, Sofisticado e elegante"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"influencer_count_target",className:"block text-sm font-medium text-gray-700 mb-1",children:"N\xfamero Alvo de Influenciadores *"}),(0,r.jsx)("input",{id:"influencer_count_target",type:"number",..._("influencer_count_target",{required:"N\xfamero alvo de influenciadores \xe9 obrigat\xf3rio",min:{value:1,message:"O n\xfamero deve ser pelo menos 1"}}),className:"w-full input-class",placeholder:"Ex: 5"}),N.influencer_count_target&&(0,r.jsx)("p",{className:"form-error",children:N.influencer_count_target.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"start_date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Data de In\xedcio da Campanha *"}),(0,r.jsx)("input",{id:"start_date",type:"date",..._("start_date",{required:"Data de in\xedcio \xe9 obrigat\xf3ria"}),className:"w-full input-class"}),N.start_date&&(0,r.jsx)("p",{className:"form-error",children:N.start_date.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"end_date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Data de T\xe9rmino da Campanha *"}),(0,r.jsx)("input",{id:"end_date",type:"date",..._("end_date",{required:"Data de t\xe9rmino \xe9 obrigat\xf3ria"}),className:"w-full input-class"}),N.end_date&&(0,r.jsx)("p",{className:"form-error",children:N.end_date.message})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"restaurant_selection_deadline",className:"block text-sm font-medium text-gray-700 mb-1",children:"Prazo Sele\xe7\xe3o de Influenciadores *"}),(0,r.jsx)("input",{id:"restaurant_selection_deadline",type:"date",..._("restaurant_selection_deadline",{required:"Prazo \xe9 obrigat\xf3rio"}),className:"w-full input-class"}),N.restaurant_selection_deadline&&(0,r.jsx)("p",{className:"form-error",children:N.restaurant_selection_deadline.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"influencer_post_deadline",className:"block text-sm font-medium text-gray-700 mb-1",children:"Prazo Postagem dos Influenciadores *"}),(0,r.jsx)("input",{id:"influencer_post_deadline",type:"date",..._("influencer_post_deadline",{required:"Prazo \xe9 obrigat\xf3rio"}),className:"w-full input-class"}),N.influencer_post_deadline&&(0,r.jsx)("p",{className:"form-error",children:N.influencer_post_deadline.message})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"available_visit_dates",className:"block text-sm font-medium text-gray-700 mb-1",children:"Datas/Hor\xe1rios Dispon\xedveis para Visita (texto livre)"}),(0,r.jsx)("textarea",{id:"available_visit_dates",..._("available_visit_dates"),rows:3,className:"w-full input-class",placeholder:"Ex: Ter\xe7as e Quintas das 18h \xe0s 22h, S\xe1bados das 12h \xe0s 16h"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium border-b pb-2 mb-4",children:"Requisitos de Conte\xfado"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"hashtags",className:"block text-sm font-medium text-gray-700 mb-1",children:"Hashtags (separadas por v\xedrgula) *"}),(0,r.jsx)("input",{id:"hashtags",type:"text",..._("hashtags",{required:"Hashtags s\xe3o obrigat\xf3rias"}),className:"w-full input-class",placeholder:"Ex: #gastronomia, #foodie"}),N.hashtags&&(0,r.jsx)("p",{className:"form-error",children:N.hashtags.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"mentions",className:"block text-sm font-medium text-gray-700 mb-1",children:"Men\xe7\xf5es (separadas por v\xedrgula) *"}),(0,r.jsx)("input",{id:"mentions",type:"text",..._("mentions",{required:"Men\xe7\xf5es s\xe3o obrigat\xf3rias"}),className:"w-full input-class",placeholder:"Ex: @restaurante, @chefparceiro"}),N.mentions&&(0,r.jsx)("p",{className:"form-error",children:N.mentions.message})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium border-b pb-2 mb-4",children:"Estilo da Campanha (Visual)"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"color",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cor de Fundo"}),(0,r.jsxs)("select",{id:"color",..._("color"),className:"w-full input-class",children:[(0,r.jsx)("option",{value:"bg-green-100",children:"Verde"}),(0,r.jsx)("option",{value:"bg-blue-100",children:"Azul"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"textColor",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cor do Texto"}),(0,r.jsxs)("select",{id:"textColor",..._("textColor"),className:"w-full input-class",children:[(0,r.jsx)("option",{value:"text-green-800",children:"Verde"}),(0,r.jsx)("option",{value:"text-blue-800",children:"Azul"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"progressColor",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cor da Barra de Progresso"}),(0,r.jsxs)("select",{id:"progressColor",..._("progressColor"),className:"w-full input-class",children:[(0,r.jsx)("option",{value:"bg-green-500",children:"Verde"}),(0,r.jsx)("option",{value:"bg-blue-500",children:"Azul"})]})]})]})]})]}),(0,r.jsx)("div",{className:"px-6 py-4 bg-gray-50 flex items-center justify-end",children:(0,r.jsxs)("div",{className:"flex space-x-3",children:[c&&(0,r.jsx)(d.d,{type:"button",onClick:c,disabled:f,icon:(0,r.jsx)(o.QCr,{}),children:"Cancelar"}),(0,r.jsxs)("button",{type:"submit",className:"btn-primary",disabled:f,children:[(0,r.jsx)(o.dIn,{className:"mr-2"}),f?"Salvando...":p?"Salvar Altera\xe7\xf5es":"Criar Campanha"]})]})})]})]})}}}]);