"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3660],{11275:(e,r,t)=>{t.d(r,{X:()=>l});var n=t(12115),i=t(52712);function l(e){let[r,t]=n.useState(void 0);return(0,i.N)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let n,i;if(!Array.isArray(r)||!r.length)return;let l=r[0];if("borderBoxSize"in l){let e=l.borderBoxSize,r=Array.isArray(e)?e[0]:e;n=r.inlineSize,i=r.blockSize}else n=e.offsetWidth,i=e.offsetHeight;t({width:n,height:i})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}},22436:(e,r,t)=>{var n=t(12115),i="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},l=n.useState,a=n.useEffect,o=n.useLayoutEffect,u=n.useDebugValue;function d(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!i(e,t)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),n=l({inst:{value:t,getSnapshot:r}}),i=n[0].inst,s=n[1];return o(function(){i.value=t,i.getSnapshot=r,d(i)&&s({inst:i})},[e,t,r]),a(function(){return d(i)&&s({inst:i}),e(function(){d(i)&&s({inst:i})})},[e]),u(t),t};r.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},40646:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},45503:(e,r,t)=>{t.d(r,{Z:()=>i});var n=t(12115);function i(e){let r=n.useRef({value:e,previous:e});return n.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},49033:(e,r,t)=>{e.exports=t(22436)},51154:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54011:(e,r,t)=>{t.d(r,{H4:()=>k,_V:()=>E,bL:()=>S});var n=t(12115),i=t(46081),l=t(39033),a=t(52712),o=t(63540),u=t(49033);function d(){return()=>{}}var s=t(95155),c="Avatar",[f,v]=(0,i.A)(c),[p,y]=f(c),m=n.forwardRef((e,r)=>{let{__scopeAvatar:t,...i}=e,[l,a]=n.useState("idle");return(0,s.jsx)(p,{scope:t,imageLoadingStatus:l,onImageLoadingStatusChange:a,children:(0,s.jsx)(o.sG.span,{...i,ref:r})})});m.displayName=c;var h="AvatarImage",b=n.forwardRef((e,r)=>{let{__scopeAvatar:t,src:i,onLoadingStatusChange:c=()=>{},...f}=e,v=y(h,t),p=function(e,r){let{referrerPolicy:t,crossOrigin:i}=r,l=(0,u.useSyncExternalStore)(d,()=>!0,()=>!1),o=n.useRef(null),s=l?(o.current||(o.current=new window.Image),o.current):null,[c,f]=n.useState(()=>x(s,e));return(0,a.N)(()=>{f(x(s,e))},[s,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!s)return;let r=e("loaded"),n=e("error");return s.addEventListener("load",r),s.addEventListener("error",n),t&&(s.referrerPolicy=t),"string"==typeof i&&(s.crossOrigin=i),()=>{s.removeEventListener("load",r),s.removeEventListener("error",n)}},[s,i,t]),c}(i,f),m=(0,l.c)(e=>{c(e),v.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,s.jsx)(o.sG.img,{...f,ref:r,src:i}):null});b.displayName=h;var w="AvatarFallback",g=n.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:i,...l}=e,a=y(w,t),[u,d]=n.useState(void 0===i);return n.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>d(!0),i);return()=>window.clearTimeout(e)}},[i]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(o.sG.span,{...l,ref:r}):null});function x(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}g.displayName=w;var S=m,E=b,k=g},63540:(e,r,t)=>{t.d(r,{sG:()=>d,hO:()=>s});var n=t(12115),i=t(47650),l=t(6101),a=t(95155),o=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=function(e){let r=function(e){let r=n.forwardRef((e,r)=>{var t,i,a;let o,u;let{children:d,...s}=e,c=n.isValidElement(d)?(u=(o=null===(i=Object.getOwnPropertyDescriptor((t=d).props,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in o&&o.isReactWarning)?t.ref:(u=(o=null===(a=Object.getOwnPropertyDescriptor(t,"ref"))||void 0===a?void 0:a.get)&&"isReactWarning"in o&&o.isReactWarning)?t.props.ref:t.props.ref||t.ref:void 0,f=(0,l.s)(c,r);if(n.isValidElement(d)){let e=function(e,r){let t={...r};for(let n in r){let i=e[n],l=r[n];/^on[A-Z]/.test(n)?i&&l?t[n]=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];let n=l(...r);return i(...r),n}:i&&(t[n]=i):"style"===n?t[n]={...i,...l}:"className"===n&&(t[n]=[i,l].filter(Boolean).join(" "))}return{...e,...t}}(s,d.props);return d.type!==n.Fragment&&(e.ref=f),n.cloneElement(d,e)}return n.Children.count(d)>1?n.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),t=n.forwardRef((e,t)=>{let{children:i,...l}=e,o=n.Children.toArray(i),d=o.find(u);if(d){let e=d.props.children,i=o.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(r,{...l,ref:t,children:i})});return t.displayName="".concat(e,".Slot"),t}(`Primitive.${r}`),i=n.forwardRef((e,n)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?t:r,{...l,ref:n})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{});function s(e,r){e&&i.flushSync(()=>e.dispatchEvent(r))}},72797:(e,r,t)=>{t.d(r,{b:()=>u});var n=t(12115);t(47650);var i=t(99708),l=t(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,i.TL)(`Primitive.${r}`),a=n.forwardRef((e,n)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i?t:r,{...a,ref:n})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{}),o=n.forwardRef((e,r)=>(0,l.jsx)(a.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var u=o},74466:(e,r,t)=>{t.d(r,{F:()=>a});var n=t(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,a=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:o}=r,u=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],n=null==o?void 0:o[e];if(null===r)return null;let l=i(r)||i(n);return a[e][l]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return l(e,u,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},85339:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86225:(e,r,t)=>{t.d(r,{bL:()=>E,zi:()=>k});var n=t(12115),i=t(85185),l=t(6101),a=t(46081),o=t(5845),u=t(45503),d=t(11275);t(47650);var s=t(99708),c=t(95155),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,s.TL)(`Primitive.${r}`),i=n.forwardRef((e,n)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i?t:r,{...l,ref:n})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{}),v="Switch",[p,y]=(0,a.A)(v),[m,h]=p(v),b=n.forwardRef((e,r)=>{let{__scopeSwitch:t,name:a,checked:u,defaultChecked:d,required:s,disabled:v,value:p="on",onCheckedChange:y,form:h,...b}=e,[w,g]=n.useState(null),E=(0,l.s)(r,e=>g(e)),k=n.useRef(!1),j=!w||h||!!w.closest("form"),[A=!1,N]=(0,o.i)({prop:u,defaultProp:d,onChange:y});return(0,c.jsxs)(m,{scope:t,checked:A,disabled:v,children:[(0,c.jsx)(f.button,{type:"button",role:"switch","aria-checked":A,"aria-required":s,"data-state":S(A),"data-disabled":v?"":void 0,disabled:v,value:p,...b,ref:E,onClick:(0,i.m)(e.onClick,e=>{N(e=>!e),j&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),j&&(0,c.jsx)(x,{control:w,bubbles:!k.current,name:a,value:p,checked:A,required:s,disabled:v,form:h,style:{transform:"translateX(-100%)"}})]})});b.displayName=v;var w="SwitchThumb",g=n.forwardRef((e,r)=>{let{__scopeSwitch:t,...n}=e,i=h(w,t);return(0,c.jsx)(f.span,{"data-state":S(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:r})});g.displayName=w;var x=e=>{let{control:r,checked:t,bubbles:i=!0,...l}=e,a=n.useRef(null),o=(0,u.Z)(t),s=(0,d.X)(r);return n.useEffect(()=>{let e=a.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==t&&r){let n=new Event("click",{bubbles:i});r.call(e,t),e.dispatchEvent(n)}},[o,t,i]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...l,tabIndex:-1,ref:a,style:{...e.style,...s,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function S(e){return e?"checked":"unchecked"}var E=b,k=g}}]);