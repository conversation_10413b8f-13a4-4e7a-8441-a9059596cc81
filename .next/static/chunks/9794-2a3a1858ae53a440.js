"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9794],{79794:(e,a,t)=>{t.d(a,{A:()=>A});var s=t(95155),n=t(12115),r=t(73579),i=t(43453),c=t(19946);let o=(0,c.A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),l=(0,c.A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var d=t(4516),m=t(51154),u=t(14186),p=t(66695),h=t(30285);let g=(0,c.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function x(e){let{title:a,description:t,icon:n,actionLabel:r,onAction:i,className:c=""}=e;return(0,s.jsx)(p.Zp,{className:"overflow-hidden ".concat(c),children:(0,s.jsxs)(p.Wu,{className:"p-6 flex flex-col items-center justify-center text-center",children:[n&&(0,s.jsx)("div",{className:"mb-4 text-gray-400",children:n}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-1",children:a}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:t}),r&&i&&(0,s.jsxs)(h.$,{variant:"outline",onClick:i,className:"gap-2",children:[(0,s.jsx)(g,{className:"h-4 w-4"}),r]})]})})}var v=t(69796),N=t(82672),f=t(6711),_=t(40714),w=t(98794),y=t(57716),j=t(63008),E=t(4573);function A(e){let{userId:a,className:t,compact:c=!1}=e,g=(0,r.createClientComponentClient)(),[A,k]=(0,n.useState)(!0),[D,I]=(0,n.useState)([]),[T,b]=(0,n.useState)([]),[C,S]=(0,n.useState)([]);(0,n.useEffect)(()=>{O()},[a]);let P=async()=>{try{let D=!0;try{let{error:e}=await g.from("tasks").select("id").limit(1);if(e){console.error("Tabela tasks pode n\xe3o existir:",e),D=!1;return}}catch(e){console.error("Erro ao verificar tabela tasks:",e),D=!1;return}if(!D)return;let{data:I,error:T}=await g.from("tasks").select("id").eq("user_id",a).limit(1);if(T){console.error("Erro ao verificar tarefas existentes:",T);return}let{data:b,error:C}=await g.from("campaign_influencers").select("\n          id,\n          campaign_id,\n          campaigns(\n            id,\n            name,\n            restaurant_id,\n            restaurants(\n              id,\n              name\n            )\n          )\n        ").eq("influencer_id",a).eq("status","accepted");if(C){console.error("Erro ao buscar campanhas ativas:",C);return}if((!I||0===I.length)&&b&&b.length>0){for(let D of(console.log("Criando tarefas baseadas em campanhas ativas para o usu\xe1rio:",a),b)){var e,t,s,n,r,i,c,o,l,d,m,u,p,h,x,N,f,_,w,y,j,E,A,k;let I=null;try{let{data:e,error:a}=await g.from("campaign_content").select("id, status").eq("campaign_influencer_id",D.id).order("created_at",{ascending:!1}).limit(1);a?(console.log("Tabela campaign_content n\xe3o existe ou n\xe3o est\xe1 acess\xedvel, pulando verifica\xe7\xe3o de conte\xfado"),I=null):I=e}catch(e){console.log("Erro ao acessar tabela campaign_content, pulando verifica\xe7\xe3o de conte\xfado:",e),I=null}I&&0!==I.length?"approved"===I[0].status?await g.from("tasks").insert({user_id:a,campaign_id:D.campaign_id,title:"Publicar conte\xfado de ".concat((null===(d=D.campaigns)||void 0===d?void 0:null===(l=d[0])||void 0===l?void 0:l.name)||"Campanha Desconhecida"),description:"Publicar o conte\xfado aprovado para a campanha ".concat((null===(u=D.campaigns)||void 0===u?void 0:null===(m=u[0])||void 0===m?void 0:m.name)||"Campanha Desconhecida"),task_type:v.wP.CONTENT_PUBLICATION,status:v.e1.PENDING,priority:v.W6.HIGH,due_date:new Date(Date.now()+1728e5).toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString()}):"rejected"===I[0].status&&await g.from("tasks").insert({user_id:a,campaign_id:D.campaign_id,title:"Revisar conte\xfado de ".concat((null===(h=D.campaigns)||void 0===h?void 0:null===(p=h[0])||void 0===p?void 0:p.name)||"Campanha Desconhecida"),description:"Revisar e reenviar o conte\xfado rejeitado para a campanha ".concat((null===(N=D.campaigns)||void 0===N?void 0:null===(x=N[0])||void 0===x?void 0:x.name)||"Campanha Desconhecida"),task_type:v.wP.CONTENT_CREATION,status:v.e1.PENDING,priority:v.W6.URGENT,due_date:new Date(Date.now()+864e5).toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString()}):await g.from("tasks").insert({user_id:a,campaign_id:D.campaign_id,title:"Criar conte\xfado para ".concat((null===(t=D.campaigns)||void 0===t?void 0:null===(e=t[0])||void 0===e?void 0:e.name)||"Campanha Desconhecida"),description:"Gravar v\xeddeo para a campanha ".concat((null===(n=D.campaigns)||void 0===n?void 0:null===(s=n[0])||void 0===s?void 0:s.name)||"Campanha Desconhecida"," do restaurante ").concat((null===(o=D.campaigns)||void 0===o?void 0:null===(c=o[0])||void 0===c?void 0:null===(i=c.restaurants)||void 0===i?void 0:null===(r=i[0])||void 0===r?void 0:r.name)||"Restaurante Desconhecido"),task_type:v.wP.CONTENT_CREATION,status:v.e1.PENDING,priority:v.W6.HIGH,due_date:new Date(Date.now()+2592e5).toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString()});let T=null;try{let{data:e,error:t}=await g.from("campaign_schedule").select("id").eq("campaign_id",D.campaign_id).eq("influencer_id",a).limit(1);t?(console.log("Tabela campaign_schedule n\xe3o existe ou n\xe3o est\xe1 acess\xedvel, pulando verifica\xe7\xe3o de agendamento"),T=null):T=e}catch(e){console.log("Erro ao acessar tabela campaign_schedule, pulando verifica\xe7\xe3o de agendamento:",e),T=null}T&&0!==T.length||await g.from("tasks").insert({user_id:a,campaign_id:D.campaign_id,title:"Agendar visita para ".concat((null===(_=D.campaigns)||void 0===_?void 0:null===(f=_[0])||void 0===f?void 0:f.name)||"Campanha Desconhecida"),description:"Agendar visita ao restaurante ".concat((null===(E=D.campaigns)||void 0===E?void 0:null===(j=E[0])||void 0===j?void 0:null===(y=j.restaurants)||void 0===y?void 0:null===(w=y[0])||void 0===w?void 0:w.name)||"Restaurante Desconhecido"," para a campanha ").concat((null===(k=D.campaigns)||void 0===k?void 0:null===(A=k[0])||void 0===A?void 0:A.name)||"Campanha Desconhecida"),task_type:v.wP.SCHEDULE_VISIT,status:v.e1.PENDING,priority:v.W6.MEDIUM,due_date:new Date(Date.now()+432e6).toISOString(),created_at:new Date().toISOString(),updated_at:new Date().toISOString()})}console.log("Tarefas baseadas em campanhas criadas com sucesso")}}catch(e){console.error("Erro ao criar tarefas baseadas em campanhas:",e)}},O=async()=>{k(!0);try{a&&await P(),await Promise.all([R(),H()])}catch(e){console.error("Erro ao carregar dados:",e)}finally{k(!1)}},R=async()=>{try{let e=!0;try{let{error:a}=await g.from("tasks").select("id").limit(1);a&&(console.error("Erro ao verificar tabela tasks:",a),e=!1)}catch(a){console.error("Erro ao verificar tabela tasks:",a),e=!1}if(!e){I([]),M([],T);return}let t=g.from("tasks").select("\n          id,\n          user_id,\n          campaign_id,\n          title,\n          description,\n          task_type,\n          status,\n          priority,\n          due_date,\n          completed_at,\n          created_at,\n          updated_at\n        ");t=t.in("status",[v.e1.PENDING,v.e1.IN_PROGRESS,v.e1.OVERDUE]),a&&(t=t.eq("user_id",a));let{data:s,error:n}=await t.order("due_date",{ascending:!0});if(n){console.error("Erro na consulta de tarefas:",n),I([]),M([],T);return}console.log("Tarefas carregadas com sucesso:",(null==s?void 0:s.length)||0),I(s||[]),M(s||[],T)}catch(e){console.error("Erro ao carregar tarefas:",e),I([]),M([],T)}},H=async()=>{try{let e=!0;try{let{error:a}=await g.from("campaign_schedule").select("id").limit(1);a&&(console.error("Erro ao verificar tabela schedules:",a),e=!1)}catch(a){console.error("Erro ao verificar tabela schedules:",a),e=!1}if(!e){b([]),M(D,[]);return}let t=g.from("campaign_schedule").select("\n          id,\n          campaign_id,\n          influencer_id,\n          scheduled_date,\n          status,\n          notes,\n          created_at,\n          updated_at,\n          completed_at,\n          cancelled_at,\n          cancellation_reason\n        ");a&&(t=t.eq("influencer_id",a));let{data:s,error:n}=await t.order("scheduled_date",{ascending:!0});if(n){console.error("Erro na consulta de agendamentos:",n),b([]),M(D,[]);return}console.log("Agendamentos carregados com sucesso:",(null==s?void 0:s.length)||0),b(s||[]),M(D,s||[])}catch(e){console.error("Erro ao carregar agendamentos:",e),b([]),M(D,[])}},M=(e,a)=>{let t=(0,f.o)(Date.now(),void 0);(0,_.f)(t,30),S([...e.map(e=>{let a="Tarefa",t=(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1"});switch(e.task_type){case v.wP.CONTENT_CREATION:a="Criar conte\xfado",t=(0,s.jsx)(o,{className:"h-4 w-4 mr-1"});break;case v.wP.CONTENT_APPROVAL:a="Aprovar conte\xfado",t=(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1"});break;case v.wP.CONTENT_PUBLICATION:a="Publicar conte\xfado",t=(0,s.jsx)(l,{className:"h-4 w-4 mr-1"});break;case v.wP.SCHEDULE_VISIT:a="Visitar restaurante",t=(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1"});break;case v.wP.ADMINISTRATIVE:a="Tarefa administrativa",t=(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1"});break;case v.wP.PAYMENT:a="Pagamento",t=(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1"});break;case v.wP.OTHER:a="Outra tarefa",t=(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1"})}return{id:e.id,type:"task",title:e.title,date:e.due_date||"",restaurant:e.campaign_id?"Campanha ID: ".concat(e.campaign_id.substring(0,8)):"Restaurante N/A",actionType:a,status:e.status,originalData:e,icon:t}}),...a.filter(e=>{let a=(0,w.H)(e.scheduled_date);return(0,y.d)(a,t)&&e.status!==N.Y.CANCELLED}).map(e=>({id:e.id,type:"schedule",title:e.campaign_id?"Visita Campanha ID: ".concat(e.campaign_id.substring(0,8)):"Agendamento ID: ".concat(e.id.substring(0,8)),date:e.scheduled_date,restaurant:e.campaign_id?"Campanha ID: ".concat(e.campaign_id.substring(0,8)):"Restaurante N/A",actionType:"Visita",status:e.status,originalData:e,icon:(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1"})}))].sort((e,a)=>e.date?a.date?(0,w.H)(e.date).getTime()-(0,w.H)(a.date).getTime():-1:1).slice(0,5))},L=async e=>{try{let a=new Date().toISOString(),{error:t}=await g.from("tasks").update({status:v.e1.COMPLETED,completed_at:a,updated_at:a}).eq("id",e);if(t)throw t;O()}catch(e){console.error("Erro ao concluir tarefa:",e)}},V=e=>e?(0,j.GP)((0,w.H)(e),"dd/MM",{locale:E.F}):"Sem prazo",G=e=>{let a=(0,w.H)(e);return(0,j.GP)(a,"dd/MM '\xe0s' HH:mm",{locale:E.F})},q=e=>{if("task"!==e.type)return(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1 text-red-500"});switch(e.originalData.task_type){case v.wP.CONTENT_CREATION:return(0,s.jsx)(o,{className:"h-4 w-4 mr-1 text-blue-500"});case v.wP.CONTENT_APPROVAL:return(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1 text-green-500"});case v.wP.CONTENT_PUBLICATION:return(0,s.jsx)(l,{className:"h-4 w-4 mr-1 text-indigo-500"});case v.wP.SCHEDULE_VISIT:return(0,s.jsx)(d.A,{className:"h-4 w-4 mr-1 text-purple-500"});case v.wP.PAYMENT:return(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1 text-yellow-500"});case v.wP.ADMINISTRATIVE:return(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1 text-orange-500"});default:return(0,s.jsx)(i.A,{className:"h-4 w-4 mr-1 text-gray-500"})}};return c?(0,s.jsx)("div",{className:t,children:A?(0,s.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,s.jsx)(m.A,{className:"h-5 w-5 animate-spin text-gray-400"})}):0===C.length?(0,s.jsx)("div",{className:"text-center py-3 text-sm text-gray-500",children:"Sem a\xe7\xf5es pendentes"}):(0,s.jsx)("div",{className:"divide-y divide-gray-100",children:C.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"py-2.5 px-5 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[q(e),(0,s.jsx)("span",{className:"truncate font-medium text-gray-700",children:e.title})]}),(0,s.jsxs)("div",{className:"flex items-center mt-1 text-xs text-gray-500",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1 flex-shrink-0"}),(0,s.jsxs)("span",{className:"truncate",children:["task"===e.type?V(e.date):G(e.date),e.restaurant&&" • ".concat(e.restaurant)]})]})]}),"task"===e.type&&(0,s.jsx)(h.$,{size:"sm",variant:"ghost",className:"h-7 px-2 text-xs ml-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50",onClick:()=>L(e.id),children:"Concluir"})]},"".concat(e.type,"-").concat(e.id)))})}):(0,s.jsxs)(p.Zp,{className:"overflow-hidden ".concat(t),children:[(0,s.jsx)(p.aR,{className:"bg-[#f5f5f5] pb-2",children:(0,s.jsxs)(p.ZB,{className:"text-lg font-medium",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 inline-block mr-2 text-blue-500"}),"Pr\xf3ximas A\xe7\xf5es"]})}),(0,s.jsx)(p.Wu,{className:"p-4",children:A?(0,s.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,s.jsx)(m.A,{className:"h-6 w-6 animate-spin text-gray-500"})}):0===C.length?(0,s.jsx)(x,{title:"Sem a\xe7\xf5es pendentes",description:"Voc\xea n\xe3o tem tarefas ou agendamentos pr\xf3ximos.",icon:(0,s.jsx)(u.A,{className:"h-12 w-12"}),actionLabel:"Atualizar",onAction:O}):(0,s.jsx)("div",{className:"space-y-3",children:C.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"font-medium",children:e.title}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center mt-1 text-sm text-gray-500",children:[(0,s.jsxs)("div",{className:"flex items-center mr-3",children:[q(e),e.actionType]}),(0,s.jsxs)("div",{className:"flex items-center mr-3",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"task"===e.type?V(e.date):G(e.date)]}),e.restaurant&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 mr-1"}),e.restaurant]})]})]}),"task"===e.type&&(0,s.jsx)(h.$,{size:"sm",variant:"outline",onClick:()=>L(e.id),children:"Concluir"})]},"".concat(e.type,"-").concat(e.id)))})})]})}}}]);