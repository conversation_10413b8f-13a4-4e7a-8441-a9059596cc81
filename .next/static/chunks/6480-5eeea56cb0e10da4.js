"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6480],{20211:(e,r,t)=>{t.d(r,{default:()=>i});var o=t(95155);t(12115);var s=t(35695),n=t(29911),a=t(60440);function i(e){let{children:r,title:t,backLink:i}=e,l=(0,s.useRouter)();return(0,s.usePathname)(),(0,o.jsx)(a.A,{children:(0,o.jsxs)("div",{className:"h-full flex flex-col",children:[i&&(0,o.jsx)("div",{className:"mt-2 mb-4",children:(0,o.jsx)("button",{onClick:()=>l.push(i),className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors",children:(0,o.jsx)(n.QVr,{className:"text-gray-600"})})}),(0,o.jsx)("main",{className:"p-5 pt-4 rounded-xl bg-f5f5f7 overflow-y-auto flex-1 flex flex-col shadow-md",style:{minHeight:"calc(100vh - 6rem)",maxHeight:"calc(100vh - 6rem)"},children:(0,o.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:t}),r]})})]})})}t(52643),t(13568)},21115:(e,r,t)=>{t.d(r,{A:()=>c});var o=t(95155),s=t(12115),n=t(62177),a=t(29911),i=t(13568),l=t(34603);function c(e){let{onCancel:r,onSuccess:t,initialData:c,isEditing:d=!1}=e,[u,m]=(0,s.useState)(!1),{register:p,handleSubmit:f,formState:{errors:g}}=(0,n.mN)({defaultValues:{business_name:(null==c?void 0:c.business_name)||"",description:(null==c?void 0:c.description)||"",cuisine_type:(null==c?void 0:c.cuisine_type)||"",address:(null==c?void 0:c.address)||"",city:(null==c?void 0:c.city)||"",state:(null==c?void 0:c.state)||"",postal_code:(null==c?void 0:c.postal_code)||"",website:(null==c?void 0:c.website)||"",instagram_url:(null==c?void 0:c.instagram_url)||"",facebook_url:(null==c?void 0:c.facebook_url)||"",tiktok_url:(null==c?void 0:c.tiktok_url)||""}}),x=async e=>{try{let r;m(!0);let o={business_name:e.business_name,description:e.description,cuisine_type:e.cuisine_type,address:e.address,city:e.city,state:e.state,postal_code:e.postal_code,website:e.website,instagram_url:e.instagram_url,facebook_url:e.facebook_url,tiktok_url:e.tiktok_url,updated_at:new Date().toISOString(),sync_with_negocios:!0};if(d&&(null==c?void 0:c.id)){console.log("Atualizando negocioe existente:",c.id);try{let e={...o,id:c.id},t=window.location.origin,s=await fetch("".concat(t,"/api/admin/negocios/").concat(c.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Erro HTTP: ".concat(s.status))}r=await s.json(),i.oR.success("Negocioe atualizado com sucesso!")}catch(e){console.error("Erro ao atualizar negocioe:",e),i.oR.error("Erro ao atualizar negocioe: ".concat(e.message)),m(!1);return}}else{console.log("Criando novo negocioe");try{let e={...o,created_at:new Date().toISOString()},t=window.location.origin,s=await fetch("".concat(t,"/api/admin/negocios"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Erro HTTP: ".concat(s.status))}r=await s.json(),i.oR.success("Negocioe criado com sucesso!")}catch(e){console.error("Erro ao criar negocioe:",e),i.oR.error("Erro ao criar negocioe: ".concat(e.message)),m(!1);return}}r?Array.isArray(r)&&r.length>0?t(r[0].id):r.id?t(r.id):d&&(null==c?void 0:c.id)?t(c.id):(console.warn("Opera\xe7\xe3o conclu\xedda, mas formato de resultado inesperado:",r),window.location.href="/admin/negocios"):(console.warn("Opera\xe7\xe3o conclu\xedda, mas nenhum resultado retornado"),window.location.href="/admin/negocios")}catch(e){console.error("Erro ao salvar negocioe:",e),i.oR.error("Erro ao salvar negocioe")}finally{m(!1)}};return(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,o.jsxs)("div",{className:"p-6 border-b",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold",children:"Informa\xe7\xf5es do Negocioe"}),(0,o.jsx)("p",{className:"text-gray-600 mt-1",children:"Preencha todos os campos obrigat\xf3rios (*)"})]}),(0,o.jsx)("form",{onSubmit:f(x),children:(0,o.jsxs)("div",{className:"p-6 space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Informa\xe7\xf5es B\xe1sicas"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nome do Neg\xf3cio *"}),(0,o.jsx)("input",{type:"text",...p("business_name",{required:"Nome \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: Neg\xf3cio Exemplo"}),g.business_name&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.business_name.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Descri\xe7\xe3o"}),(0,o.jsx)("textarea",{...p("description"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Descreva o neg\xf3cio"}),g.description&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.description.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipo de Culin\xe1ria"}),(0,o.jsx)("input",{type:"text",...p("cuisine_type"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: Italiana, Japonesa, Brasileira"})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Localiza\xe7\xe3o"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Endere\xe7o"}),(0,o.jsx)("input",{type:"text",...p("address"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: Rua Exemplo, 123"})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Cidade *"}),(0,o.jsx)("input",{type:"text",...p("city",{required:"Cidade \xe9 obrigat\xf3ria"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: S\xe3o Paulo"}),g.city&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.city.message})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Estado *"}),(0,o.jsx)("input",{type:"text",...p("state",{required:"Estado \xe9 obrigat\xf3rio"}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: SP"}),g.state&&(0,o.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.state.message})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CEP"}),(0,o.jsx)("input",{type:"text",...p("postal_code"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: 01234-567"})]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium border-b pb-2",children:"Redes Sociais e Website"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website"}),(0,o.jsx)("input",{type:"url",...p("website"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: https://www.negocioeexemplo.com.br"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Instagram URL"}),(0,o.jsx)("input",{type:"url",...p("instagram_url"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: https://instagram.com/negocioeexemplo"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Facebook URL"}),(0,o.jsx)("input",{type:"url",...p("facebook_url"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: https://facebook.com/negocioeexemplo"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"TikTok URL"}),(0,o.jsx)("input",{type:"url",...p("tiktok_url"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500",placeholder:"Ex: https://tiktok.com/@negocioeexemplo"})]})]}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[(0,o.jsx)(l.d,{type:"button",onClick:r,disabled:u,icon:(0,o.jsx)(a.QCr,{}),children:"Cancelar"}),(0,o.jsxs)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",disabled:u,children:[(0,o.jsx)(a.dIn,{className:"mr-2"}),u?"Salvando...":"Salvar Neg\xf3cio"]})]})]})})]})}},34603:(e,r,t)=>{t.d(r,{d:()=>a});var o=t(95155),s=t(12115),n=t(39688);let a=s.forwardRef((e,r)=>{let{icon:t,iconPosition:s="left",isLoading:a=!1,fullWidth:i=!1,children:l,className:c,disabled:d,...u}=e;return(0,o.jsxs)("button",{ref:r,className:(0,n.QP)("rounded-md font-medium flex items-center justify-center transition-all focus:outline-none focus:ring-2 focus:ring-offset-2","px-4 py-2 text-sm border border-gray-300 shadow-sm text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 disabled:opacity-50",i&&"w-full",a&&"opacity-70 cursor-not-allowed",c),disabled:d||a,"aria-busy":a,...u,children:[a&&(0,o.jsx)("span",{className:"mr-2 animate-spin"}),t&&"left"===s&&(0,o.jsx)("span",{className:"mr-2",children:t}),l,t&&"right"===s&&(0,o.jsx)("span",{className:"ml-2",children:t})]})});a.displayName="SecondaryActionButton"},52643:(e,r,t)=>{t.d(r,{N:()=>s,b:()=>n});var o=t(73579);let s=(0,o.createClientComponentClient)();function n(){return(0,o.createClientComponentClient)()}},60440:(e,r,t)=>{t.d(r,{A:()=>c});var o=t(95155),s=t(12115),n=t(35695),a=t(52643),i=t(81452),l=t(13568);function c(e){let{children:r}=e,t=(0,n.useRouter)(),[c,d]=(0,s.useState)(!0),[u,m]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:r}=await a.N.auth.getSession();if(r)throw r;if(!e){t.push("/login?redirect=/admin/campaigns");return}let o=e.user.email;o&&(0,i.K)(o)?m(!0):(l.oR.error("Voc\xea n\xe3o tem permiss\xe3o para acessar esta \xe1rea"),t.push("/"))}catch(e){console.error("Erro ao verificar status de administrador:",e),l.oR.error("Erro ao verificar suas permiss\xf5es"),t.push("/")}finally{d(!1)}})()},[t]),c)?(0,o.jsx)("div",{className:"flex justify-center items-center h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):u?(0,o.jsx)(o.Fragment,{children:r}):null}},74436:(e,r,t)=>{t.d(r,{k5:()=>d});var o=t(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=o.createContext&&o.createContext(s),a=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e}).apply(this,arguments)}function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var o,s,n;o=e,s=r,n=t[r],(s=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,r||"default");if("object"!=typeof o)return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(s))in o?Object.defineProperty(o,s,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[s]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>o.createElement(u,i({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,t)=>o.createElement(r.tag,c({key:t},r.attr),e(r.child)))}(e.child))}function u(e){var r=r=>{var t,{attr:s,size:n,title:l}=e,d=function(e,r){if(null==e)return{};var t,o,s=function(e,r){if(null==e)return{};var t={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(r.indexOf(o)>=0)continue;t[o]=e[o]}return t}(e,r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(o=0;o<n.length;o++)t=n[o],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,a),u=n||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),o.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,s,d,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&o.createElement("title",null,l),e.children)};return void 0!==n?o.createElement(n.Consumer,null,e=>r(e)):r(s)}},81452:(e,r,t)=>{t.d(r,{I:()=>o,K:()=>s});let o=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];function s(e){return o.includes(e.toLowerCase())}}}]);