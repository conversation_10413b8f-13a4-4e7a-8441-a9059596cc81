"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7084],{5845:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(12115),o=n(39033);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,a=r.useRef(i),l=(0,o.c)(t);return r.useEffect(()=>{a.current!==i&&(l(i),a.current=i)},[i,a,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i,s=(0,o.c)(n);return[u,r.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else a(t)},[l,e,a,s])]}},6101:(e,t,n)=>{n.d(t,{s:()=>a,t:()=>i});var r=n(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function a(...e){return r.useCallback(i(...e),e)}},36217:(e,t,n)=>{n.d(t,{UC:()=>J,B8:()=>X,bL:()=>z,l9:()=>Z});var r=n(12115),o=n(85185),i=n(46081),a=n(6101),l=n(99708),u=n(95155),s=n(61285);n(47650);var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,l.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),f=n(39033),d=n(5845),m=n(94315),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[b,g,w]=function(e){let t=e+"CollectionProvider",[n,o]=(0,i.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,u.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",m=(0,l.TL)(d),p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(d,n),i=(0,a.s)(t,o.collectionRef);return(0,u.jsx)(m,{ref:i,children:r})});p.displayName=d;let v=e+"CollectionItemSlot",y="data-radix-collection-item",b=(0,l.TL)(v),g=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,l=r.useRef(null),s=(0,a.s)(t,l),f=c(v,n);return r.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,u.jsx)(b,{[y]:"",ref:s,children:o})});return g.displayName=v,[{Provider:f,Slot:p,ItemSlot:g},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(y),[h,N]=(0,i.A)(y,[w]),[x,R]=h(y),C=r.forwardRef((e,t)=>(0,u.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(E,{...e,ref:t})})}));C.displayName=y;var E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:s,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:h,preventScrollOnEntryFocus:N=!1,...R}=e,C=r.useRef(null),E=(0,a.s)(t,C),A=(0,m.jH)(s),[j=null,I]=(0,d.i)({prop:y,defaultProp:b,onChange:w}),[M,S]=r.useState(!1),D=(0,f.c)(h),O=g(n),F=r.useRef(!1),[_,P]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(p,D),()=>e.removeEventListener(p,D)},[D]),(0,u.jsx)(x,{scope:n,orientation:i,dir:A,loop:l,currentTabStopId:j,onItemFocus:r.useCallback(e=>I(e),[I]),onItemShiftTab:r.useCallback(()=>S(!0),[]),onFocusableItemAdd:r.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>P(e=>e-1),[]),children:(0,u.jsx)(c.div,{tabIndex:M||0===_?-1:0,"data-orientation":i,...R,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),N)}}F.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>S(!1))})})}),A="RovingFocusGroupItem",j=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,...f}=e,d=(0,s.B)(),m=l||d,p=R(A,n),v=p.currentTabStopId===m,y=g(n),{onFocusableItemAdd:w,onFocusableItemRemove:h}=p;return r.useEffect(()=>{if(i)return w(),()=>h()},[i,w,h]),(0,u.jsx)(b.ItemSlot,{scope:n,id:m,focusable:i,active:a,children:(0,u.jsx)(c.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>T(n))}})})})});j.displayName=A;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var M=n(52712),S=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef({}),l=r.useRef(e),u=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=D(a.current);u.current="mounted"===s?e:"none"},[s]),(0,M.N)(()=>{let t=a.current,n=l.current;if(n!==e){let r=u.current,o=D(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),l.current=e}},[e,c]),(0,M.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=D(a.current).includes(e.animationName);if(e.target===o&&r&&(c("ANIMATION_END"),!l.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(u.current=D(a.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),l=(0,a.s)(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:l}):null};function D(e){return(null==e?void 0:e.animationName)||"none"}S.displayName="Presence";var O="Tabs",[F,_]=(0,i.A)(O,[N]),P=N(),[L,k]=F(O),U=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:f="automatic",...p}=e,v=(0,m.jH)(l),[y,b]=(0,d.i)({prop:r,onChange:o,defaultProp:i});return(0,u.jsx)(L,{scope:n,baseId:(0,s.B)(),value:y,onValueChange:b,orientation:a,dir:v,activationMode:f,children:(0,u.jsx)(c.div,{dir:v,"data-orientation":a,...p,ref:t})})});U.displayName=O;var $="TabsList",K=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=k($,n),a=P(n);return(0,u.jsx)(C,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,u.jsx)(c.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});K.displayName=$;var V="TabsTrigger",B=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,l=k(V,n),s=P(n),f=H(l.baseId,r),d=q(l.baseId,r),m=r===l.value;return(0,u.jsx)(j,{asChild:!0,...s,focusable:!i,active:m,children:(0,u.jsx)(c.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":d,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:f,...a,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||i||!e||l.onValueChange(r)})})})});B.displayName=V;var W="TabsContent",G=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:a,...l}=e,s=k(W,n),f=H(s.baseId,o),d=q(s.baseId,o),m=o===s.value,p=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(S,{present:i||m,children:n=>{let{present:r}=n;return(0,u.jsx)(c.div,{"data-state":m?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:d,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&a})}})});function H(e,t){return"".concat(e,"-trigger-").concat(t)}function q(e,t){return"".concat(e,"-content-").concat(t)}G.displayName=W;var z=U,X=K,Z=B,J=G},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(12115),o=n(95155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[l]||a,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},52596:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(e))&&(r&&(r+=" "),r+=t);return r}},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},61285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(12115),i=n(52712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},94315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(12115);n(95155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},99708:(e,t,n)=>{n.d(t,{DX:()=>l,Dc:()=>s,TL:()=>a});var r=n(12115),o=n(6101),i=n(95155);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var a;let e,l;let u=(a=n,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...a}=e,l=r.Children.toArray(o),u=l.find(c);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var l=a("Slot"),u=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);