"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5593],{25593:(e,t,a)=>{a.d(t,{A:()=>c});var n=a(95155),r=a(12115),i=a(44912),o=a(29911);function c(e){let{userId:t,onConnectionChange:a}=e,[c,s]=(0,r.useState)(!1),[l,m]=(0,r.useState)(!0),[u,d]=(0,r.useState)(null);(0,r.useEffect)(()=>{async function e(){try{m(!0),d(null);let e=await (0,i.rd)(t);s(e),a&&a(e)}catch(e){console.error("Erro ao verificar conex\xe3o com Instagram:",e),d("N\xe3o foi poss\xedvel verificar a conex\xe3o")}finally{m(!1)}}t&&e()},[t,a]);let f=async()=>{try{m(!0),d(null),await (0,i.Tt)(t),s(!1),a&&a(!1)}catch(e){console.error("Erro ao desconectar Instagram:",e),d("N\xe3o foi poss\xedvel desconectar")}finally{m(!1)}};return l?(0,n.jsxs)("button",{className:"flex items-center justify-center px-4 py-2.5 bg-[#f5f5f5] text-gray-400 rounded-lg w-full social-connect",disabled:!0,children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-gray-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Verificando conex\xe3o..."]}):c?(0,n.jsxs)("button",{onClick:f,className:"flex items-center justify-center px-4 py-2.5 bg-[#f5f5f5] text-gray-700 hover:bg-[#f5f5f5] border border-gray-200 rounded-lg w-full transition-all social-connect",children:[(0,n.jsx)(o.ao$,{className:"mr-2 text-pink-500"}),"Desconectar Instagram"]}):(0,n.jsxs)("button",{onClick:()=>{try{let e=(0,i._0)(t);window.location.href=e}catch(e){console.error("Erro ao gerar URL de autentica\xe7\xe3o:",e),d("N\xe3o foi poss\xedvel iniciar a conex\xe3o")}},className:"flex items-center justify-center px-4 py-2.5 bg-[#f5f5f5] text-gray-700 hover:bg-[#f5f5f5] border border-gray-200 rounded-lg w-full transition-all social-connect",children:[(0,n.jsx)(o.ao$,{className:"mr-2 text-pink-500"}),"Conectar Instagram"]})}},44912:(e,t,a)=>{a.d(t,{Tt:()=>p,MH:()=>d,_0:()=>s,rd:()=>l,Yv:()=>f});var n=a(52643);function r(e,t){console.error("[".concat(e,"] Erro:"),t.message||t)}var i=a(49509),o=a(44134).hp;let c=(0,n.b)();function s(e){try{let t=i.env.INSTAGRAM_APP_ID,a=i.env.INSTAGRAM_REDIRECT_URI;if(!t||!a)throw Error("Instagram App ID and Redirect URI are required");let n=o.from(JSON.stringify({userId:e,timestamp:Date.now()})).toString("base64"),r=new URL("https://api.instagram.com/oauth/authorize");return r.searchParams.append("client_id",t),r.searchParams.append("redirect_uri",a),r.searchParams.append("scope","user_profile,user_media"),r.searchParams.append("response_type","code"),r.searchParams.append("state",n),r.toString()}catch(e){throw r("Instagram Auth URL Generation",e),e}}async function l(e){try{let{data:t}=await c.from("social_connections").select("id").eq("user_id",e).eq("platform","instagram").eq("is_active",!0).single();return!!t}catch(e){return r("Instagram Connection Check",e),!1}}async function m(e){try{let{data:t}=await c.from("social_connections").select("*").eq("user_id",e).eq("platform","instagram").eq("is_active",!0).single();return t}catch(e){return r("Get Instagram Connection",e),null}}async function u(e){try{let t=await m(e);if(!t)throw Error("No active Instagram connection found");return{instagramUserId:t.platform_user_id,username:t.username,accessToken:t.access_token}}catch(e){throw r("Get Instagram User Info",e),e}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;try{let{accessToken:a}=await u(e),n=await fetch("https://graph.instagram.com/me/media?fields=id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,username,children{media_url,thumbnail_url}&limit=".concat(t,"&access_token=").concat(a));if(!n.ok)throw Error("Failed to fetch media: ".concat(await n.text()));return(await n.json()).data||[]}catch(e){return r("Fetch Instagram Media",e),[]}}async function f(e){try{let t=await m(e);if(!t)throw Error("No active Instagram connection found");let{data:a}=await c.from("campaign_influencers").select("\n        id,\n        campaign_id,\n        campaigns(id, name, hashtags, mentions)\n      ").eq("influencer_id",e).eq("campaigns.status","active");if(!a||0===a.length)throw Error("No active campaigns found");let n=await fetch("https://graph.instagram.com/me/media?fields=id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,username,children{media_url,thumbnail_url}&limit=25&access_token=".concat(t.access_token));if(!n.ok)throw Error("Failed to fetch media: ".concat(await n.text()));let r=(await n.json()).data||[],i=0;for(let e of r){let t=e.caption||"";for(let n of a){let a=n.campaigns,r=a.hashtags.every(e=>t.includes("#".concat(e))||t.includes("#".concat(e.toLowerCase()))),o=a.mentions.every(e=>t.includes("@".concat(e))||t.includes("@".concat(e.toLowerCase())));if(r&&o){let{data:t}=await c.from("posts").select("id").eq("campaign_influencer_id",n.id).eq("platform_post_id",e.id).single();if(!t){let t=[];e.media_url&&t.push(e.media_url),e.children&&e.children.data&&e.children.data.forEach(e=>{e.media_url&&t.push(e.media_url)});let{data:a,error:r}=await c.from("posts").insert({campaign_influencer_id:n.id,platform:"instagram",platform_post_id:e.id,post_url:e.permalink,post_type:function(e){switch(e){case"IMAGE":case"CAROUSEL_ALBUM":default:return"feed";case"VIDEO":return"reel"}}(e.media_type),caption:e.caption,media_urls:t,likes_count:0,comments_count:0,shares_count:0,views_count:0,engagement_rate:0,is_verified:!0,created_at:new Date(e.timestamp||Date.now()).toISOString(),updated_at:new Date().toISOString()}).select("id").single();if(r){console.error("Error creating post:",r);continue}i++,await c.from("post_metrics_history").insert({post_id:a.id,likes_count:0,comments_count:0,shares_count:0,views_count:0,engagement_rate:0,snapshot_at:new Date().toISOString()})}}}}return{success:!0,syncedCount:i}}catch(e){throw r("Sync Instagram Posts",e),e}}async function p(e){try{let{error:t}=await c.from("social_connections").update({is_active:!1,updated_at:new Date().toISOString()}).eq("user_id",e).eq("platform","instagram");if(t)return r("Disconnect Instagram",t),!1;return!0}catch(e){return r("Disconnect Instagram",e),!1}}},52643:(e,t,a)=>{a.d(t,{N:()=>r,b:()=>i});var n=a(73579);let r=(0,n.createClientComponentClient)();function i(){return(0,n.createClientComponentClient)()}}}]);