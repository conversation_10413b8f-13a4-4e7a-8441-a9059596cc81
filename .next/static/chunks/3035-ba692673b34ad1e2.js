"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3035],{3727:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(12115),o=n(47650),a=n(99708),i=n(95155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),l=n(52712),c=r.forwardRef((e,t)=>{var n,a;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let v=c||d&&(null===(a=globalThis)||void 0===a?void 0:null===(n=a.document)||void 0===n?void 0:n.body);return v?o.createPortal((0,i.jsx)(u.div,{...s,ref:t}),v):null});c.displayName="Portal"},11662:(e,t,n)=>{n.d(t,{bm:()=>eg,UC:()=>ep,VY:()=>eh,hJ:()=>ev,ZL:()=>ef,bL:()=>es,hE:()=>em,l9:()=>ed});var r=n(12115),o=n(85185),a=n(6101),i=n(46081),u=n(61285),l=n(5845),c=n(94903);n(47650);var s=n(99708),d=n(95155),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,s.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),v=n(39033),p="focusScope.autoFocusOnMount",m="focusScope.autoFocusOnUnmount",h={bubbles:!1,cancelable:!0},g=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:u,...l}=e,[c,s]=r.useState(null),g=(0,v.c)(i),N=(0,v.c)(u),C=r.useRef(null),x=(0,a.s)(t,e=>s(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(o){let e=function(e){if(R.paused||!c)return;let t=e.target;c.contains(t)?C.current=t:w(C.current,{select:!0})},t=function(e){if(R.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||w(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&w(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[o,c,R.paused]),r.useEffect(()=>{if(c){E.add(R);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(p,h);c.addEventListener(p,g),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(w(r,{select:t}),document.activeElement!==n)return}(y(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&w(c))}return()=>{c.removeEventListener(p,g),setTimeout(()=>{let t=new CustomEvent(m,h);c.addEventListener(m,N),c.dispatchEvent(t),t.defaultPrevented||w(null!=e?e:document.body,{select:!0}),c.removeEventListener(m,N),E.remove(R)},0)}}},[c,g,N,R]);let S=r.useCallback(e=>{if(!n&&!o||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=y(e);return[b(t,e),b(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&w(a,{select:!0})):(e.preventDefault(),n&&w(o,{select:!0})):r===t&&e.preventDefault()}},[n,o,R.paused]);return(0,d.jsx)(f.div,{tabIndex:-1,...l,ref:x,onKeyDown:S})});function y(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function b(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function w(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}g.displayName="FocusScope";var E=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=N(e,t)).unshift(t)},remove(t){var n;null===(n=(e=N(e,t))[0])||void 0===n||n.resume()}}}();function N(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var C=n(3727),x=n(52712),R=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,a]=r.useState(),i=r.useRef({}),u=r.useRef(e),l=r.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=S(i.current);l.current="mounted"===c?e:"none"},[c]),(0,x.N)(()=>{let t=i.current,n=u.current;if(n!==e){let r=l.current,o=S(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),(0,x.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=S(i.current).includes(e.animationName);if(e.target===o&&r&&(s("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(l.current=S(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}s("ANIMATION_END")},[o,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(i.current=getComputedStyle(e)),a(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),u=(0,a.s)(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:u}):null};function S(e){return(null==e?void 0:e.animationName)||"none"}R.displayName="Presence";var O=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,s.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),L=n(92293),A=n(93795),T=n(38168),D="Dialog",[P,k]=(0,i.A)(D),[M,j]=P(D),I=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,s=r.useRef(null),f=r.useRef(null),[v=!1,p]=(0,l.i)({prop:o,defaultProp:a,onChange:i});return(0,d.jsx)(M,{scope:t,triggerRef:s,contentRef:f,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:v,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};I.displayName=D;var F="DialogTrigger",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=j(F,n),u=(0,a.s)(t,i.triggerRef);return(0,d.jsx)(O.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":eo(i.open),...r,ref:u,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});W.displayName=F;var _="DialogPortal",[B,U]=P(_,{forceMount:void 0}),K=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,i=j(_,t);return(0,d.jsx)(B,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,d.jsx)(R,{present:n||i.open,children:(0,d.jsx)(C.Z,{asChild:!0,container:a,children:e})}))})};K.displayName=_;var Z="DialogOverlay",$=r.forwardRef((e,t)=>{let n=U(Z,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=j(Z,e.__scopeDialog);return a.modal?(0,d.jsx)(R,{present:r||a.open,children:(0,d.jsx)(Y,{...o,ref:t})}):null});$.displayName=Z;var q=(0,s.TL)("DialogOverlay.RemoveScroll"),Y=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(Z,n);return(0,d.jsx)(A.A,{as:q,allowPinchZoom:!0,shards:[o.contentRef],children:(0,d.jsx)(O.div,{"data-state":eo(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),z="DialogContent",X=r.forwardRef((e,t)=>{let n=U(z,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=j(z,e.__scopeDialog);return(0,d.jsx)(R,{present:r||a.open,children:a.modal?(0,d.jsx)(H,{...o,ref:t}):(0,d.jsx)(V,{...o,ref:t})})});X.displayName=z;var H=r.forwardRef((e,t)=>{let n=j(z,e.__scopeDialog),i=r.useRef(null),u=(0,a.s)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,T.Eq)(e)},[]),(0,d.jsx)(J,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),V=r.forwardRef((e,t)=>{let n=j(z,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,d.jsx)(J,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let u=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),J=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:u,...l}=e,s=j(z,n),f=r.useRef(null),v=(0,a.s)(t,f);return(0,L.Oh)(),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:u,children:(0,d.jsx)(c.qW,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":eo(s.open),...l,ref:v,onDismiss:()=>s.onOpenChange(!1)})}),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(el,{titleId:s.titleId}),(0,d.jsx)(ec,{contentRef:f,descriptionId:s.descriptionId})]})]})}),G="DialogTitle",Q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(G,n);return(0,d.jsx)(O.h2,{id:o.titleId,...r,ref:t})});Q.displayName=G;var ee="DialogDescription",et=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=j(ee,n);return(0,d.jsx)(O.p,{id:o.descriptionId,...r,ref:t})});et.displayName=ee;var en="DialogClose",er=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=j(en,n);return(0,d.jsx)(O.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function eo(e){return e?"open":"closed"}er.displayName=en;var ea="DialogTitleWarning",[ei,eu]=(0,i.q)(ea,{contentName:z,titleName:G,docsSlug:"dialog"}),el=e=>{let{titleId:t}=e,n=eu(ea),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},ec=e=>{let{contentRef:t,descriptionId:n}=e,o=eu("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(a)},[a,t,n]),null},es=I,ed=W,ef=K,ev=$,ep=X,em=Q,eh=et,eg=er},19946:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),i=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:c="",children:s,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:n,strokeWidth:i?24*Number(a)/Number(o):a,className:u("lucide",c),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),s=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:l,...s}=n;return(0,r.createElement)(c,{ref:a,iconNode:t,className:u("lucide-".concat(o(i(e))),"lucide-".concat(e),l),...s})});return n.displayName=i(e),n}},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},u=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],d=[],f=new Set,v=new Set(c),p=function(e){!(!e||f.has(e))&&(f.add(e),p(e.parentNode))};c.forEach(p);var m=function(e){!(!e||v.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,u=(o.get(e)||0)+1,l=(s.get(e)||0)+1;o.set(e,u),s.set(e,l),d.push(e),1===u&&i&&a.set(e,!0),1===l&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),u++,function(){d.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--u||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),c(o,a,n,"aria-hidden")):function(){return null}}},51595:(e,t,n)=>{n.d(t,{U:()=>a});var r=n(12115),o=n(39033);function a(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},92293:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(12115),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:i()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,n)=>{n.d(t,{A:()=>q});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(12115)),u="right-scroll-bar-position",l="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,d=new WeakMap;function f(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,a,i=(t=null,void 0===n&&(n=f),r=[],a=!1,{read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,a);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(a=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){a=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(o)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=o({async:!0,ssr:!1},e),i}(),p=function(){},m=i.forwardRef(function(e,t){var n,r,u,l,f=i.useRef(null),m=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),h=m[0],g=m[1],y=e.forwardProps,b=e.children,w=e.className,E=e.removeScrollBar,N=e.enabled,C=e.shards,x=e.sideCar,R=e.noIsolation,S=e.inert,O=e.allowPinchZoom,L=e.as,A=e.gapMode,T=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(u=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return u.value},set current(value){var e=u.value;e!==value&&(u.value=value,u.callback(value,e))}}}})[0]).callback=r,l=u.facade,s(function(){var e=d.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(l,n)},[n]),l),P=o(o({},T),h);return i.createElement(i.Fragment,null,N&&i.createElement(x,{sideCar:v,removeScrollBar:E,shards:C,noIsolation:R,inert:S,setCallbacks:g,allowPinchZoom:!!O,lockRef:f,gapMode:A}),y?i.cloneElement(i.Children.only(b),o(o({},P),{ref:D})):i.createElement(void 0===L?"div":L,o({},P,{className:w,ref:D}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:u};var h=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,o({},n))};h.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},N=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=N(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},x=b(),R="data-scroll-locked",S=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},L=function(){i.useEffect(function(){return document.body.setAttribute(R,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var a=i.useMemo(function(){return C(o)},[o]);return i.createElement(x,{styles:S(a,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){T=!1}var P=!!T&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},M=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),j(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},j=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,v=0;do{var p=I(e,l),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&j(e,l)&&(f+=h,v+=m),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},U=0,K=[];let Z=(v.useMedium(function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(U++)[0],a=i.useState(b)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=W(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=M(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=M(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var v=r.current||o;return F(v,t,e,"h"===v?l:c,!0)},[]),c=i.useCallback(function(e){if(K.length&&K[K.length-1]===a){var n="deltaY"in e?_(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,_(t),t.target,l(t,e.lockRef.current))},[]),v=i.useCallback(function(t){s(t.type,W(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return K.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",c,P),document.addEventListener("touchmove",c,P),document.addEventListener("touchstart",d,P),function(){K=K.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,P),document.removeEventListener("touchmove",c,P),document.removeEventListener("touchstart",d,P)}},[]);var p=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?i.createElement(A,{gapMode:e.gapMode}):null)}),h);var $=i.forwardRef(function(e,t){return i.createElement(m,o({},e,{ref:t,sideCar:Z}))});$.classNames=m.classNames;let q=$},94903:(e,t,n)=>{n.d(t,{qW:()=>m});var r,o=n(12115),a=n(85185),i=n(47650),u=n(99708),l=n(95155),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,u.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),s=n(6101),d=n(39033),f=n(51595),v="dismissableLayer.update",p=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),m=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:w,onDismiss:E,...N}=e,C=o.useContext(p),[x,R]=o.useState(null),S=null!==(i=null==x?void 0:x.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,O]=o.useState({}),L=(0,s.s)(t,e=>R(e)),A=Array.from(C.layers),[T]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),D=A.indexOf(T),P=x?A.indexOf(x):-1,k=C.layersWithOutsidePointerEventsDisabled.size>0,M=P>=D,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,d.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){g("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));!M||n||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==E||E())},S),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,d.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&g("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...C.branches].some(e=>e.contains(t))||(null==b||b(e),null==w||w(e),e.defaultPrevented||null==E||E())},S);return(0,f.U)(e=>{P===C.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},S),o.useEffect(()=>{if(x)return u&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(x)),C.layers.add(x),h(),()=>{u&&1===C.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[x,S,u,C]),o.useEffect(()=>()=>{x&&(C.layers.delete(x),C.layersWithOutsidePointerEventsDisabled.delete(x),h())},[x,C]),o.useEffect(()=>{let e=()=>O({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,l.jsx)(c.div,{...N,ref:L,style:{pointerEvents:k?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});function h(){let e=new CustomEvent(v);document.dispatchEvent(e)}function g(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&a.addEventListener(e,t,{once:!0}),o)a&&i.flushSync(()=>a.dispatchEvent(u));else a.dispatchEvent(u)}m.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(p),r=o.useRef(null),a=(0,s.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(c.div,{...e,ref:a})}).displayName="DismissableLayerBranch"}}]);