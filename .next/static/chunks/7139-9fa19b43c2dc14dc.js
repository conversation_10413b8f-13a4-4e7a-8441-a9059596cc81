"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7139],{11275:(e,r,t)=>{t.d(r,{X:()=>i});var n=t(12115),l=t(52712);function i(e){let[r,t]=n.useState(void 0);return(0,l.N)(()=>{if(e){t({width:e.offsetWidth,height:e.offsetHeight});let r=new ResizeObserver(r=>{let n,l;if(!Array.isArray(r)||!r.length)return;let i=r[0];if("borderBoxSize"in i){let e=i.borderBoxSize,r=Array.isArray(e)?e[0]:e;n=r.inlineSize,l=r.blockSize}else n=e.offsetWidth,l=e.offsetHeight;t({width:n,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}t(void 0)},[e]),r}},19946:(e,r,t)=>{t.d(r,{A:()=>u});var n=t(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),o=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:s="",children:u,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:r,...c,width:l,height:l,stroke:t,strokeWidth:o?24*Number(i)/Number(l):i,className:a("lucide",s),...f},[...d.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,n.forwardRef)((t,i)=>{let{className:c,...u}=t;return(0,n.createElement)(s,{ref:i,iconNode:r,className:a("lucide-".concat(l(o(e))),"lucide-".concat(e),c),...u})});return t.displayName=o(e),t}},40646:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},45503:(e,r,t)=>{t.d(r,{Z:()=>l});var n=t(12115);function l(e){let r=n.useRef({value:e,previous:e});return n.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}},51154:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},72797:(e,r,t)=>{t.d(r,{b:()=>c});var n=t(12115);t(47650);var l=t(99708),i=t(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,l.TL)(`Primitive.${r}`),o=n.forwardRef((e,n)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l?t:r,{...o,ref:n})});return o.displayName=`Primitive.${r}`,{...e,[r]:o}},{}),a=n.forwardRef((e,r)=>(0,i.jsx)(o.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var c=a},74436:(e,r,t)=>{t.d(r,{k5:()=>u});var n=t(12115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=n.createContext&&n.createContext(l),o=["attr","size","title"];function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function s(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var n,l,i;n=e,l=r,i=t[r],(l=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(l))in n?Object.defineProperty(n,l,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[l]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(d,a({attr:s({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,s({key:t},r.attr),e(r.child)))}(e.child))}function d(e){var r=r=>{var t,{attr:l,size:i,title:c}=e,u=function(e,r){if(null==e)return{};var t,n,l=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)t=i[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(l[t]=e[t])}return l}(e,o),d=i||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",a({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,l,u,{className:t,style:s(s({color:e.color||r.color},r.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==i?n.createElement(i.Consumer,null,e=>r(e)):r(l)}},74466:(e,r,t)=>{t.d(r,{F:()=>o});var n=t(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,o=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:a}=r,c=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],n=null==a?void 0:a[e];if(null===r)return null;let i=l(r)||l(n);return o[e][i]}),s=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return i(e,c,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...l}=r;return Object.entries(l).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...s}[r]):({...a,...s})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},85339:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},86225:(e,r,t)=>{t.d(r,{bL:()=>j,zi:()=>x});var n=t(12115),l=t(85185),i=t(6101),o=t(46081),a=t(5845),c=t(45503),s=t(11275);t(47650);var u=t(99708),d=t(95155),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=(0,u.TL)(`Primitive.${r}`),l=n.forwardRef((e,n)=>{let{asChild:l,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(l?t:r,{...i,ref:n})});return l.displayName=`Primitive.${r}`,{...e,[r]:l}},{}),p="Switch",[v,y]=(0,o.A)(p),[b,m]=v(p),h=n.forwardRef((e,r)=>{let{__scopeSwitch:t,name:o,checked:c,defaultChecked:s,required:u,disabled:p,value:v="on",onCheckedChange:y,form:m,...h}=e,[w,g]=n.useState(null),j=(0,i.s)(r,e=>g(e)),x=n.useRef(!1),P=!w||m||!!w.closest("form"),[N=!1,A]=(0,a.i)({prop:c,defaultProp:s,onChange:y});return(0,d.jsxs)(b,{scope:t,checked:N,disabled:p,children:[(0,d.jsx)(f.button,{type:"button",role:"switch","aria-checked":N,"aria-required":u,"data-state":k(N),"data-disabled":p?"":void 0,disabled:p,value:v,...h,ref:j,onClick:(0,l.m)(e.onClick,e=>{A(e=>!e),P&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),P&&(0,d.jsx)(O,{control:w,bubbles:!x.current,name:o,value:v,checked:N,required:u,disabled:p,form:m,style:{transform:"translateX(-100%)"}})]})});h.displayName=p;var w="SwitchThumb",g=n.forwardRef((e,r)=>{let{__scopeSwitch:t,...n}=e,l=m(w,t);return(0,d.jsx)(f.span,{"data-state":k(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:r})});g.displayName=w;var O=e=>{let{control:r,checked:t,bubbles:l=!0,...i}=e,o=n.useRef(null),a=(0,c.Z)(t),u=(0,s.X)(r);return n.useEffect(()=>{let e=o.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(a!==t&&r){let n=new Event("click",{bubbles:l});r.call(e,t),e.dispatchEvent(n)}},[a,t,l]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...i,tabIndex:-1,ref:o,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function k(e){return e?"checked":"unchecked"}var j=h,x=g}}]);