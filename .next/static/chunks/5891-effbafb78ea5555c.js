"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5891],{15891:(t,e,n)=>{n.d(e,{m:()=>h});var r=n(7239),a=n(83039),i=n(95490),o=n(97444),u=n(61183),s=n(89447);function l(t,e){let n=+(0,s.a)(t)-+(0,s.a)(e);return n<0?-1:n>0?1:n}var f=n(25703),c=n(59875),d=n(32944);function h(t,e){return function(t,e,n){var r,h;let v;let M=(0,i.q)(),D=null!==(h=null!==(r=null==n?void 0:n.locale)&&void 0!==r?r:M.locale)&&void 0!==h?h:a.c,m=l(t,e);if(isNaN(m))throw RangeError("Invalid time value");let g=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:m}),[x,X]=(0,u.x)(null==n?void 0:n.in,...m>0?[e,t]:[t,e]),w=function(t,e,n){var r;let a=(+(0,s.a)(t)-+(0,s.a)(e))/1e3;return(r=null==void 0?void 0:(void 0).roundingMethod,t=>{let e=(r?Math[r]:Math.trunc)(t);return 0===e?0:e})(a)}(X,x),b=Math.round((w-((0,o.G)(X)-(0,o.G)(x))/1e3)/60);if(b<2){if(null==n?void 0:n.includeSeconds){if(w<5)return D.formatDistance("lessThanXSeconds",5,g);if(w<10)return D.formatDistance("lessThanXSeconds",10,g);if(w<20)return D.formatDistance("lessThanXSeconds",20,g);else if(w<40)return D.formatDistance("halfAMinute",0,g);else if(w<60)return D.formatDistance("lessThanXMinutes",1,g);else return D.formatDistance("xMinutes",1,g)}return 0===b?D.formatDistance("lessThanXMinutes",1,g):D.formatDistance("xMinutes",b,g)}if(b<45)return D.formatDistance("xMinutes",b,g);if(b<90)return D.formatDistance("aboutXHours",1,g);if(b<f.F6){let t=Math.round(b/60);return D.formatDistance("aboutXHours",t,g)}if(b<2520)return D.formatDistance("xDays",1,g);else if(b<f.Nw){let t=Math.round(b/f.F6);return D.formatDistance("xDays",t,g)}else if(b<2*f.Nw)return v=Math.round(b/f.Nw),D.formatDistance("aboutXMonths",v,g);if((v=function(t,e,n){let[r,a,i]=(0,u.x)(void 0,t,t,e),o=l(a,i),f=Math.abs((0,c.U)(a,i));if(f<1)return 0;1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-o*f);let h=l(a,i)===-o;(function(t,e){let n=(0,s.a)(t,void 0);return+function(t,e){let n=(0,s.a)(t,null==e?void 0:e.in);return n.setHours(23,59,59,999),n}(n,void 0)==+(0,d.p)(n,e)})(r)&&1===f&&1===l(r,i)&&(h=!1);let v=o*(f-+h);return 0===v?0:v}(X,x))<12){let t=Math.round(b/f.Nw);return D.formatDistance("xMonths",t,g)}{let t=v%12,e=Math.trunc(v/12);return t<3?D.formatDistance("aboutXYears",e,g):t<9?D.formatDistance("overXYears",e,g):D.formatDistance("almostXYears",e+1,g)}}(t,(0,r.w)(t,Date.now()),e)}},32944:(t,e,n)=>{n.d(e,{p:()=>a});var r=n(89447);function a(t,e){let n=(0,r.a)(t,null==e?void 0:e.in),a=n.getMonth();return n.setFullYear(n.getFullYear(),a+1,0),n.setHours(23,59,59,999),n}},59875:(t,e,n)=>{n.d(e,{U:()=>a});var r=n(61183);function a(t,e,n){let[a,i]=(0,r.x)(null==n?void 0:n.in,t,e);return 12*(a.getFullYear()-i.getFullYear())+(a.getMonth()-i.getMonth())}}}]);