"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7292],{17292:(e,t,r)=>{r.d(t,{A:()=>m});var a=r(95155),i=r(11518),n=r.n(i),o=r(12115),l=r(60760),s=r(63527),c=r(57740),d=r(92692);function m(e){let{isOpen:t,onClose:r,title:i,tabs:m,defaultTabId:u,size:p="medium",id:h,showCloseButton:f=!0,closeOnClickOutside:x=!0,closeOnEsc:g=!0,className:b="",overlayClassName:w="",onTabChange:y,minContentHeight:v="350px",headerContent:k,stickyHeader:j=!1,headerMenu:N,headerActions:z}=e,{theme:C}=(0,c.D)(),[E,S]=(0,o.useState)(()=>{var e;return u||(null===(e=m[0])||void 0===e?void 0:e.id)||""}),H=(0,o.useRef)(null),[L,P]=(0,o.useState)(!1),[T,M]=(0,o.useState)(0),[W,D]=(0,o.useState)(()=>{var e;let t={};return t[u||(null===(e=m[0])||void 0===e?void 0:e.id)||""]=!0,t}),[_,I]=(0,o.useState)({}),R={small:{width:"600px",height:"650px",maxWidth:"90vw",maxHeight:"85vh"},medium:{width:"900px",height:"800px",maxWidth:"95vw",maxHeight:"90vh"},large:{width:"1400px",height:"900px",maxWidth:"98vw",maxHeight:"95vh"},fullscreen:{width:"100vw",height:"100vh",maxWidth:"100vw",maxHeight:"100vh"}};(0,o.useEffect)(()=>{if(t){let e=new URL(window.location.href),t=e.searchParams.get("popup"),r=e.searchParams.get("tab");(t!==h||r!==E)&&(e.searchParams.set("popup",h),e.searchParams.set("tab",E),window.history.replaceState({},"",e.toString()))}},[t,E,h]),(0,o.useEffect)(()=>{if(!g)return;let e=e=>{"Escape"===e.key&&r()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[r,g]),(0,o.useMemo)(()=>m.find(e=>e.id===E)||m[0],[m,E]);let B=e=>e.color&&({blue:"blue-600",purple:"purple-600",green:"green-600",red:"red-600",yellow:"yellow-500",orange:"orange-500",pink:"pink-500",indigo:"indigo-600",gray:"gray-600"})[e.color]||"blue-600";return(0,a.jsx)(l.N,{children:t&&(0,a.jsx)(s.P.div,{className:"".concat(d.z.overlay," ").concat("fullscreen"===p?"p-0":"p-4"," ").concat(d.z.responsive.container," ").concat(w),style:{backgroundColor:"rgba(255, 255, 255, 0.9)",..."fullscreen"===p?{backdropFilter:"none"}:{}},initial:d.z.animation.overlay.initial,animate:d.z.animation.overlay.animate,exit:d.z.animation.overlay.exit,transition:d.z.animation.overlay.transition,onClick:x?r:void 0,children:(0,a.jsxs)(s.P.div,{className:"".concat(d.z.container.base," ").concat("fullscreen"!==p?"rounded-xl":""," ").concat(b," ").concat(L?"cursor-grabbing":""," ").concat(d.z.responsive.container," ").concat("fullscreen"===p?"fixed inset-0":""),style:{width:"fullscreen"===p?"100vw":R[p].width,height:"fullscreen"===p?"100vh":R[p].height,maxWidth:"fullscreen"===p?"100vw":R[p].maxWidth,maxHeight:"fullscreen"===p?"100vh":R[p].maxHeight,transform:T>0?"translateY(".concat(T,"px)"):void 0,opacity:T>0?Math.max(1-T/500,.5):1,backgroundColor:"white",boxShadow:"fullscreen"===p?"none":"0 10px 25px -5px rgba(var(--popup-shadow))",border:"fullscreen"===p?"none":"1px solid rgb(var(--popup-border))",color:"rgb(var(--text-primary))",borderRadius:"fullscreen"===p?"0":void 0},initial:"fullscreen"===p?{opacity:0}:{opacity:0,y:20,scale:.98},animate:"fullscreen"===p?{opacity:1}:{opacity:1,y:0,scale:1},exit:"fullscreen"===p?{opacity:0}:{opacity:0,y:20,scale:.98},transition:{type:"spring",damping:25,stiffness:300,mass:.5},onClick:e=>e.stopPropagation(),drag:"fullscreen"!==p&&"y",dragConstraints:{top:0,bottom:0},dragElastic:.1,onDragStart:()=>{P(!0)},onDrag:(e,t)=>{t.offset.y>0&&M(t.offset.y)},onDragEnd:(e,t)=>{P(!1),M(0),(t.offset.y>100||t.velocity.y>500)&&r()},ref:H,children:["fullscreen"!==p&&(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" w-full flex justify-center pt-2",children:(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" w-12 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600 opacity-80"})}),(0,a.jsx)("div",{style:{backgroundColor:"#f9fafb",borderColor:"#e5e7eb"},className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" "+"border-b ".concat(d.z.responsive.header," ").concat(m.length>1?d.z.header.withTabs:""," ").concat(j?d.z.header.sticky:""),children:(0,a.jsxs)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" p-3 sm:p-4",children:[(0,a.jsxs)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex items-center justify-between",children:[(0,a.jsx)("h2",{style:{color:"rgb(var(--text-primary))"},className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" text-lg font-semibold flex items-center gap-2",children:i}),f&&(0,a.jsx)("button",{onClick:r,style:{color:"rgb(var(--text-secondary))",fontSize:"28px",":hover":{color:"rgb(var(--text-primary))",backgroundColor:"rgb(var(--background-tertiary))"}},"aria-label":"Close",className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" rounded-full w-10 h-10 flex items-center justify-center transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",children:"\xd7"})]}),k&&(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" mt-4 w-full",children:k}),(N||z)&&(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" mt-4 w-full border-t border-gray-200 pt-4",children:(0,a.jsxs)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex flex-col md:flex-row md:justify-between md:items-center gap-4",children:[N&&(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex-grow",children:N}),z&&(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex-shrink-0",children:z})]})})]})}),m.length>1&&(0,a.jsx)("div",{style:{backgroundColor:"#f9fafb"},className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" "+"".concat(d.z.responsive.tabs," ").concat(j?"sticky top-[60px] z-10":""),children:(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex overflow-x-auto hide-scrollbar settings-modal-premium-tabs",children:m.map(e=>{let t=B(e);return(0,a.jsx)("button",{style:{color:E===e.id?"rgb(var(--tab-active-text))":"rgb(var(--tab-inactive-text))",borderBottom:E===e.id?"3px solid rgb(var(--tab-active-border))":"none"},onClick:()=>{e.lazyLoad&&!W[e.id]?(I(t=>({...t,[e.id]:!0})),setTimeout(()=>{D(t=>({...t,[e.id]:!0})),I(t=>{let r={...t};return delete r[e.id],r})},300)):D(t=>({...t,[e.id]:!0})),S(e.id),y&&y(e.id)},"aria-selected":E===e.id,role:"tab",className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" px-4 py-2 text-sm relative whitespace-nowrap transition-colors focus:outline-none focus:ring-0",children:(0,a.jsxs)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex items-center gap-1.5",children:[e.icon&&(0,a.jsx)("span",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" "+((E===e.id?t:"text-gray-400 dark:text-gray-500")||""),children:e.icon}),e.label]})},e.id)})})}),(0,a.jsx)("div",{style:{backgroundColor:"white",minHeight:v,..."fullscreen"===p&&j&&m.length>1?{paddingTop:"1rem"}:{}},className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" "+"p-4 sm:p-6 overflow-y-auto flex-1 ".concat(d.z.responsive.content),children:m.map(e=>{if(!(e.id===E||e.keepContentMounted||!e.lazyLoad&&W[e.id]))return null;let t=_[e.id];return(0,a.jsx)("div",{style:{minHeight:v},className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" "+"transition-opacity duration-200 ".concat(e.id===E?"opacity-100":"opacity-0 hidden"),children:t?(0,a.jsxs)("div",{style:{minHeight:v},className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" flex flex-col items-center justify-center py-12",children:[(0,a.jsxs)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" relative",children:[(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700"}),(0,a.jsx)("div",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500 dark:border-blue-400 absolute top-0 left-0"})]}),(0,a.jsx)("p",{className:n().dynamic([["639e5115862c52be",["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"]]])+" text-gray-500 dark:text-gray-400 mt-4 font-medium",children:"Carregando conte\xfado..."})]}):e.content},e.id)})}),(0,a.jsx)(n(),{id:"639e5115862c52be",dynamic:["fullscreen"===p?"":"width: 100% !important;","fullscreen"===p?"":"max-width: 100% !important;","fullscreen"===p?"":"height: auto !important;","fullscreen"===p?"":"max-height: 90vh !important;","fullscreen"===p?"":"border-radius: 12px !important;"],children:".hide-scrollbar{-ms-overflow-style:none;scrollbar-width:none}.hide-scrollbar::-webkit-scrollbar{display:none}@media(max-width:640px){.popup-container{width:100%!important;max-width:100%!important;height:auto!important;max-height:90vh!important;-webkit-border-radius:12px 12px 0 0!important;-moz-border-radius:12px 12px 0 0!important;border-radius:12px 12px 0 0!important;margin-top:auto!important}.popup-header{padding:16px!important}.popup-content{padding:16px!important}.popup-tabs{overflow-x:auto!important;-webkit-flex-wrap:nowrap!important;-ms-flex-wrap:nowrap!important;flex-wrap:nowrap!important;padding:12px 16px 0!important}.bg-[#f7f7f7] {".concat("fullscreen"===p?"":"width: 100% !important;","                  ").concat("fullscreen"===p?"":"max-width: 100% !important;","                  ").concat("fullscreen"===p?"":"height: auto !important;","                  ").concat("fullscreen"===p?"":"max-height: 90vh !important;","                  ").concat("fullscreen"===p?"":"border-radius: 12px !important;","}}")})]})})})}},57740:(e,t,r)=>{r.d(t,{D:()=>l,N:()=>o});var a=r(95155),i=r(12115);let n=(0,i.createContext)(void 0);function o(e){let{children:t,defaultTheme:r="light"}=e,[o,l]=(0,i.useState)(r);return(0,i.useEffect)(()=>{{let e=localStorage.getItem("theme");e?l(e):window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches&&l("dark");let t=window.matchMedia("(prefers-color-scheme: dark)"),r=e=>{localStorage.getItem("theme")||l(e.matches?"dark":"light")};return t.addEventListener("change",r),()=>t.removeEventListener("change",r)}},[]),(0,i.useEffect)(()=>{if("undefined"!=typeof document){let e=document.documentElement;e.classList.remove("light-theme","dark-theme"),e.classList.add("".concat(o,"-theme")),localStorage.setItem("theme",o)}},[o]),(0,a.jsx)(n.Provider,{value:{theme:o,setTheme:l,toggleTheme:()=>{l(e=>"light"===e?"dark":"light")}},children:t})}function l(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},92692:(e,t,r)=>{r.d(t,{z:()=>a});let a={overlay:"fixed inset-0 z-50 flex items-center justify-center bg-white/90 dark:bg-black/90 backdrop-blur-sm",container:{base:"bg-[#f7f7f7] rounded-xl shadow-xl flex flex-col overflow-hidden border border-gray-200 dark:border-gray-700 transition-all duration-200",small:"w-full sm:w-[400px] max-w-[95vw] sm:max-w-[400px] max-h-[90vh]",medium:"w-full sm:w-[600px] max-w-[95vw] sm:max-w-[600px] max-h-[90vh]",large:"w-full sm:w-[850px] max-w-[95vw] sm:max-w-[850px] max-h-[90vh]",fullscreen:"w-full max-w-[100vw] max-h-[100vh]"},header:{base:"bg-[#f0f0f0] dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center",withTabs:"border-b-0",withControls:"flex justify-between items-center",sticky:"sticky top-0 z-10"},title:"text-lg sm:text-xl font-semibold text-gray-800 dark:text-gray-100",closeButton:"p-1.5 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",tabsContainer:"flex space-x-1 px-4 sm:px-6 pt-2 pb-0 bg-[#f0f0f0] dark:bg-gray-800 overflow-x-auto hide-scrollbar border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10",tab:{base:"px-4 py-2 text-sm font-medium rounded-t-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",active:"bg-white dark:bg-gray-900 text-blue-600 dark:text-blue-400 border-b-2 border-blue-500",inactive:"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},content:"p-0 overflow-y-auto bg-white dark:bg-gray-900 flex-1",animation:{overlay:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2}},container:{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},transition:{type:"spring",damping:25,stiffness:300}}},card:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm border border-gray-100 dark:border-gray-700",form:{group:"mb-4",label:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",input:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",select:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",button:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"},responsive:{container:"popup-container",header:"popup-header",content:"popup-content",tabs:"popup-tabs"}}}}]);