"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3579],{26514:(e,t,r)=>{r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>O,CookieAuthStorageAdapter:()=>w,DEFAULT_COOKIE_OPTIONS:()=>v,createSupabaseClient:()=>y,isBrowser:()=>k,parseCookies:()=>A,parseSupabaseCookie:()=>C,serializeCookie:()=>x,stringifySupabaseSession:()=>g}),new TextEncoder;let o=new TextDecoder,i=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},s=e=>{let t=e;t instanceof Uint8Array&&(t=o.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return i(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}};var n=r(99724),a=Object.create,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,p=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,h=(e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of c(t))d.call(e,i)||i===r||l(e,i,{get:()=>t[i],enumerable:!(o=u(t,i))||o.enumerable});return e},f=(e,t,r)=>(r=null!=e?a(p(e)):{},h(!t&&e&&e.__esModule?r:l(r,"default",{value:e,enumerable:!0}),e)),I=((e,t)=>function(){return t||(0,e[c(e)[0]])((t={exports:{}}).exports,t),t.exports})({"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},i=(t||{}).decode||o,s=0;s<e.length;){var n=e.indexOf("=",s);if(-1===n)break;var a=e.indexOf(";",s);if(-1===a)a=e.length;else if(a<n){s=e.lastIndexOf(";",n-1)+1;continue}var l=e.slice(s,n).trim();if(void 0===r[l]){var u=e.slice(n+1,a).trim();34===u.charCodeAt(0)&&(u=u.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(u,i)}s=a+1}return r},e.serialize=function(e,o,s){var n=s||{},a=n.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(o);if(l&&!r.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");u+="; Max-Age="+Math.floor(c)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");u+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");u+="; Path="+n.path}if(n.expires){var p,d=n.expires;if(p=d,"[object Date]"!==t.call(p)&&!(p instanceof Date)||isNaN(d.valueOf()))throw TypeError("option expires is invalid");u+="; Expires="+d.toUTCString()}if(n.httpOnly&&(u+="; HttpOnly"),n.secure&&(u+="; Secure"),n.priority)switch("string"==typeof n.priority?n.priority.toLowerCase():n.priority){case"low":u+="; Priority=Low";break;case"medium":u+="; Priority=Medium";break;case"high":u+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function i(e){return encodeURIComponent(e)}}}),m=f(I()),b=f(I());function C(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,o,i]=t[0].split("."),n=s(o),a=new TextDecoder,{exp:l,sub:u,...c}=JSON.parse(a.decode(n));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:u,factors:t[4],...c}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function g(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function k(){return"undefined"!=typeof window&&void 0!==window.document}var v={path:"/",sameSite:"lax",maxAge:31536e6},S=RegExp(".{1,3180}","g"),w=class{constructor(e){this.cookieOptions={...v,...e,maxAge:v.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(C(t));let r=function(e,t=()=>null){let r=[];for(let o=0;;o++){let i=t(`${e}.${o}`);if(!i)break;r.push(i)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(C(r)):null}setItem(e,t){if(e.endsWith("-code-verifier")){this.setCookie(e,t);return}(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let o=[],i=t.match(S);return null==i||i.forEach((t,r)=>{let i=`${e}.${r}`;o.push({name:i,value:t})}),o})(e,g(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},O=class extends w{constructor(e){super(e)}getCookie(e){return k()?(0,m.parse)(document.cookie)[e]:null}setCookie(e,t){if(!k())return null;document.cookie=(0,m.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!k())return null;document.cookie=(0,m.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function y(e,t,r){var o;let i=k();return(0,n.UU)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:i,detectSessionInUrl:i,persistSession:!0,storage:r.auth.storage,...(null==(o=r.auth)?void 0:o.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var A=b.parse,x=b.serialize},53370:e=>{var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function o(e,o){var i,s,n,a,l=e.split(";").filter(r),u=(i=l.shift(),s="",n="",(a=i.split("=")).length>1?(s=a.shift(),n=a.join("=")):n=i,{name:s,value:n}),c=u.name,p=u.value;o=o?Object.assign({},t,o):t;try{p=o.decodeValues?decodeURIComponent(p):p}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+p+"'. Set options.decodeValues to false to disable this feature.",e)}var d={name:c,value:p};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),o=t.join("=");"expires"===r?d.expires=new Date(o):"max-age"===r?d.maxAge=parseInt(o,10):"secure"===r?d.secure=!0:"httponly"===r?d.httpOnly=!0:"samesite"===r?d.sameSite=o:"partitioned"===r?d.partitioned=!0:d[r]=o}),d}function i(e,i){if(i=i?Object.assign({},t,i):t,!e)return i.map?{}:[];if(e.headers){if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var s=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];s||!e.headers.cookie||i.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=s}}return(Array.isArray(e)||(e=[e]),i.map)?e.filter(r).reduce(function(e,t){var r=o(t,i);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return o(e,i)})}e.exports=i,e.exports.parse=i,e.exports.parseString=o,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,o,i,s,n=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;l();)if(","===(r=e.charAt(a))){for(o=a,a+=1,l(),i=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=i,n.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!s||a>=e.length)&&n.push(e.substring(t,e.length))}return n}},73579:(e,t,r)=>{var o,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>x,createClientComponentClient:()=>c,createMiddlewareClient:()=>g,createMiddlewareSupabaseClient:()=>J,createPagesBrowserClient:()=>p,createPagesServerClient:()=>I,createRouteHandlerClient:()=>y,createServerActionClient:()=>A,createServerComponentClient:()=>S,createServerSupabaseClient:()=>_}),e.exports=((e,t,r,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of n(t))a.call(e,r)||void 0===r||i(e,r,{get:()=>t[r],enumerable:!(o=s(t,r))||o.enumerable});return e})(i({},"__esModule",{value:!0}),l);var u=r(26514);function c({supabaseUrl:e="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:r,cookieOptions:i,isSingleton:s=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let n=()=>{var o;return(0,u.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(o=null==r?void 0:r.global)?void 0:o.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new u.BrowserCookieAuthStorageAdapter(i)}})};if(s){let e=o??n();return"undefined"==typeof window?e:(o||(o=e),o)}return n()}var p=c,d=r(26514),h=r(53370),f=class extends d.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,o;return(0,h.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,d.parseCookies)(t)[e]).find(e=>!!e)??(null==(o=this.context.req)?void 0:o.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var o;let i=(0,h.splitCookiesString)((null==(o=this.context.res.getHeader("set-cookie"))?void 0:o.toString())??"").filter(t=>!(e in(0,d.parseCookies)(t))),s=(0,d.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...i,s])}};function I(e,{supabaseUrl:t="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,d.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new f(e,i)}})}var m=r(26514),b=r(53370),C=class extends m.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;let r=(0,b.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,m.parseCookies)(t)[e]).find(e=>!!e);return r||(0,m.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let o=(0,m.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",o)}};function g(e,{supabaseUrl:t="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,m.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new C(e,i)}})}var k=r(26514),v=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function S(e,{supabaseUrl:t="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new v(e,i)}})}var w=r(26514),O=class extends w.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function y(e,{supabaseUrl:t="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:o,cookieOptions:i}={}){var s;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,w.createSupabaseClient)(t,r,{...o,global:{...null==o?void 0:o.global,headers:{...null==(s=null==o?void 0:o.global)?void 0:s.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new O(e,i)}})}var A=y;function x({supabaseUrl:e="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:r,cookieOptions:o}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),p({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:o})}function _(e,{supabaseUrl:t="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),I(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}function J(e,{supabaseUrl:t="https://pbehloddlzwandfmpzbo.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MDIwNjAsImV4cCI6MjA1OTM3ODA2MH0.Wp8Hj839iTUohsMD7rBeg1GI7VmEepB8653m11F8U38",options:o,cookieOptions:i}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),g(e,{supabaseUrl:t,supabaseKey:r,options:o,cookieOptions:i})}}}]);