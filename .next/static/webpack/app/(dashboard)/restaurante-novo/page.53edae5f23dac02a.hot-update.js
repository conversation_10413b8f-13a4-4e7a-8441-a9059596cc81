"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/restaurante-novo/page",{

/***/ "(app-pages-browser)/./src/contexts/AppBarContext.tsx":
/*!****************************************!*\
  !*** ./src/contexts/AppBarContext.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppBarProvider: () => (/* binding */ AppBarProvider),\n/* harmony export */   useAppBar: () => (/* binding */ useAppBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AppBarProvider,useAppBar auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AppBarContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AppBarProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [appBarTitle, setAppBarTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"crIAdores\"); // Default title\n    const [appBarTrailing, setAppBarTrailing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // Default trailing content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppBarContext.Provider, {\n        value: {\n            appBarTitle,\n            setAppBarTitle,\n            appBarTrailing,\n            setAppBarTrailing\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/contexts/AppBarContext.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppBarProvider, \"X83Or3gApRuoLEUObKQ1vja3u54=\");\n_c = AppBarProvider;\nconst useAppBar = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppBarContext);\n    if (context === undefined) {\n        throw new Error('useAppBar must be used within an AppBarProvider');\n    }\n    return context;\n};\n_s1(useAppBar, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AppBarProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AppBarContext.tsx\n"));

/***/ })

});