"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/restaurante-novo/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/criador/NotificationSettings.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/dashboard/criador/NotificationSettings.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NotificationSettings(param) {\n    let { whatsappOnly = false } = param;\n    _s();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        enable_notifications: true,\n        whatsapp_enabled: true,\n        whatsapp_campaign_invite: true,\n        whatsapp_campaign_status_change: true,\n        whatsapp_ranking_update: true,\n        whatsapp_payment_status: true,\n        whatsapp_post_approval: true,\n        email_enabled: true,\n        email_campaign_invite: true,\n        email_campaign_status_change: true,\n        email_ranking_update: true,\n        email_payment_status: true,\n        email_post_approval: true\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Carregar preferências do usuário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationSettings.useEffect\": ()=>{\n            async function loadPreferences() {\n                try {\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (!session) return;\n                    const response = await fetch('/api/v1/notifications/settings');\n                    if (!response.ok) {\n                        throw new Error('Erro ao carregar preferências');\n                    }\n                    const data = await response.json();\n                    // Mesclar com valores padrão\n                    setPreferences({\n                        \"NotificationSettings.useEffect.loadPreferences\": (prev)=>({\n                                ...prev,\n                                ...data\n                            })\n                    }[\"NotificationSettings.useEffect.loadPreferences\"]);\n                } catch (error) {\n                    console.error('Erro ao carregar preferências:', error);\n                } finally{\n                    setLoading(false);\n                }\n            }\n            loadPreferences();\n        }\n    }[\"NotificationSettings.useEffect\"], [\n        supabase\n    ]);\n    // Salvar preferências\n    async function savePreferences() {\n        setSaving(true);\n        try {\n            const response = await fetch('/api/v1/notifications/settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(preferences)\n            });\n            if (!response.ok) {\n                throw new Error('Erro ao salvar preferências');\n            }\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: 'Preferências salvas',\n                description: 'Suas preferências de notificação foram atualizadas com sucesso'\n            });\n        } catch (error) {\n            console.error('Erro ao salvar preferências:', error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.toast)({\n                title: 'Erro',\n                description: 'Não foi possível salvar suas preferências',\n                variant: 'destructive'\n            });\n        } finally{\n            setSaving(false);\n        }\n    }\n    // Atualizar preferência\n    function updatePreference(key, value) {\n        setPreferences((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-6 w-6 animate-spin text-gray-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            !whatsappOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"enable_notifications\",\n                                className: \"text-base font-medium\",\n                                children: \"Ativar notifica\\xe7\\xf5es\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Receba notifica\\xe7\\xf5es sobre campanhas, atualiza\\xe7\\xf5es e pagamentos\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                        id: \"enable_notifications\",\n                        checked: preferences.enable_notifications,\n                        onCheckedChange: (checked)=>updatePreference('enable_notifications', checked)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this),\n            preferences.enable_notifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    !whatsappOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Canais de notifica\\xe7\\xe3o\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"whatsapp_enabled\",\n                                                        className: \"font-medium\",\n                                                        children: \"WhatsApp\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es via WhatsApp\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"whatsapp_enabled\",\n                                                checked: preferences.whatsapp_enabled,\n                                                onCheckedChange: (checked)=>updatePreference('whatsapp_enabled', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"email_enabled\",\n                                                        className: \"font-medium\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es via email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"email_enabled\",\n                                                checked: preferences.email_enabled,\n                                                onCheckedChange: (checked)=>updatePreference('email_enabled', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 13\n                    }, this),\n                    (preferences.whatsapp_enabled || whatsappOnly) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: whatsappOnly ? '' : 'border-t pt-4 mt-4',\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Notifica\\xe7\\xf5es via WhatsApp\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"whatsapp_campaign_invite\",\n                                                        className: \"font-medium\",\n                                                        children: \"Convites de campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando for convidado para novas campanhas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"whatsapp_campaign_invite\",\n                                                checked: preferences.whatsapp_campaign_invite,\n                                                onCheckedChange: (checked)=>updatePreference('whatsapp_campaign_invite', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"whatsapp_campaign_status_change\",\n                                                        className: \"font-medium\",\n                                                        children: \"Atualiza\\xe7\\xf5es de campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando o status de uma campanha mudar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"whatsapp_campaign_status_change\",\n                                                checked: preferences.whatsapp_campaign_status_change,\n                                                onCheckedChange: (checked)=>updatePreference('whatsapp_campaign_status_change', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"whatsapp_ranking_update\",\n                                                        className: \"font-medium\",\n                                                        children: \"Atualiza\\xe7\\xf5es de ranking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando sua posi\\xe7\\xe3o no ranking mudar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"whatsapp_ranking_update\",\n                                                checked: preferences.whatsapp_ranking_update,\n                                                onCheckedChange: (checked)=>updatePreference('whatsapp_ranking_update', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"whatsapp_payment_status\",\n                                                        className: \"font-medium\",\n                                                        children: \"Pagamentos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es sobre pagamentos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"whatsapp_payment_status\",\n                                                checked: preferences.whatsapp_payment_status,\n                                                onCheckedChange: (checked)=>updatePreference('whatsapp_payment_status', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"whatsapp_post_approval\",\n                                                        className: \"font-medium\",\n                                                        children: \"Aprova\\xe7\\xe3o de posts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando seus posts forem aprovados ou rejeitados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"whatsapp_post_approval\",\n                                                checked: preferences.whatsapp_post_approval,\n                                                onCheckedChange: (checked)=>updatePreference('whatsapp_post_approval', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this),\n                    preferences.email_enabled && !whatsappOnly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Notifica\\xe7\\xf5es via Email\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"email_campaign_invite\",\n                                                        className: \"font-medium\",\n                                                        children: \"Convites de campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando for convidado para novas campanhas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"email_campaign_invite\",\n                                                checked: preferences.email_campaign_invite,\n                                                onCheckedChange: (checked)=>updatePreference('email_campaign_invite', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"email_campaign_status_change\",\n                                                        className: \"font-medium\",\n                                                        children: \"Atualiza\\xe7\\xf5es de campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando o status de uma campanha mudar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"email_campaign_status_change\",\n                                                checked: preferences.email_campaign_status_change,\n                                                onCheckedChange: (checked)=>updatePreference('email_campaign_status_change', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"email_ranking_update\",\n                                                        className: \"font-medium\",\n                                                        children: \"Atualiza\\xe7\\xf5es de ranking\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando sua posi\\xe7\\xe3o no ranking mudar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"email_ranking_update\",\n                                                checked: preferences.email_ranking_update,\n                                                onCheckedChange: (checked)=>updatePreference('email_ranking_update', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"email_payment_status\",\n                                                        className: \"font-medium\",\n                                                        children: \"Pagamentos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es sobre pagamentos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"email_payment_status\",\n                                                checked: preferences.email_payment_status,\n                                                onCheckedChange: (checked)=>updatePreference('email_payment_status', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"email_post_approval\",\n                                                        className: \"font-medium\",\n                                                        children: \"Aprova\\xe7\\xe3o de posts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Receba notifica\\xe7\\xf5es quando seus posts forem aprovados ou rejeitados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                                id: \"email_post_approval\",\n                                                checked: preferences.email_post_approval,\n                                                onCheckedChange: (checked)=>updatePreference('email_post_approval', checked)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    onClick: savePreferences,\n                    disabled: saving,\n                    children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"mr-2 h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this),\n                            \"Salvando...\"\n                        ]\n                    }, void 0, true) : 'Salvar preferências'\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                    lineNumber: 358,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/NotificationSettings.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationSettings, \"quxUtR5KUd5nl4vcjm448ZPiGKA=\");\n_c = NotificationSettings;\nvar _c;\n$RefreshReg$(_c, \"NotificationSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/criador/NotificationSettings.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/criador/WhatsAppVerification.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/dashboard/criador/WhatsAppVerification.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WhatsAppVerification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction WhatsAppVerification() {\n    _s();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const [phone, setPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationCode, setVerificationCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVerifying, setIsVerifying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isVerified, setIsVerified] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialLoading, setInitialLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carregar dados do usuário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WhatsAppVerification.useEffect\": ()=>{\n            async function loadUserData() {\n                try {\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session) {\n                        const { data: profile, error } = await supabase.from('profiles').select('phone, phone_verified').eq('id', session.user.id).single();\n                        if (error) throw error;\n                        if (profile) {\n                            setPhone(profile.phone || '');\n                            setIsVerified(profile.phone_verified || false);\n                        }\n                    }\n                } catch (error) {\n                    console.error('Erro ao carregar dados do usuário:', error);\n                } finally{\n                    setInitialLoading(false);\n                }\n            }\n            loadUserData();\n        }\n    }[\"WhatsAppVerification.useEffect\"], [\n        supabase\n    ]);\n    // Iniciar verificação\n    async function startVerification() {\n        if (!phone) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: 'Erro',\n                description: 'Por favor, informe seu número de telefone',\n                variant: 'destructive'\n            });\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/v1/whatsapp/verify', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    phone\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Erro ao iniciar verificação');\n            }\n            setIsVerifying(true);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: 'Código enviado',\n                description: 'Um código de verificação foi enviado para o seu WhatsApp'\n            });\n        } catch (error) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: 'Erro',\n                description: error.message || 'Não foi possível enviar o código',\n                variant: 'destructive'\n            });\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Confirmar código\n    async function confirmCode() {\n        if (!verificationCode) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: 'Erro',\n                description: 'Por favor, informe o código de verificação',\n                variant: 'destructive'\n            });\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/v1/whatsapp/verify', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    phone,\n                    code: verificationCode\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Código inválido');\n            }\n            setIsVerified(true);\n            setIsVerifying(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: 'Número verificado',\n                description: 'Seu número de WhatsApp foi verificado com sucesso'\n            });\n        } catch (error) {\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.toast)({\n                title: 'Erro',\n                description: error.message || 'Não foi possível verificar o código',\n                variant: 'destructive'\n            });\n        } finally{\n            setLoading(false);\n        }\n    }\n    // Alterar número\n    function changeNumber() {\n        setIsVerified(false);\n        setPhone('');\n        setVerificationCode('');\n        setIsVerifying(false);\n    }\n    if (initialLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-gray-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6 text-gray-600 text-sm\",\n                    children: \"Conecte seu WhatsApp para receber notifica\\xe7\\xf5es importantes sobre campanhas, atualiza\\xe7\\xf5es de ranking e pagamentos.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                isVerified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-5 bg-[#f5f5f5] rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full bg-[#f5f5f5] border border-gray-200 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 font-medium\",\n                                            children: \"WhatsApp Conectado\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: phone\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            className: \"mt-4 bg-[#f5f5f5] hover:bg-[#f5f5f5] text-gray-700 border border-gray-200 rounded-lg\",\n                            onClick: changeNumber,\n                            children: \"Alterar n\\xfamero\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2 text-gray-700\",\n                                    children: \"Seu n\\xfamero de WhatsApp\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"tel\",\n                                            placeholder: \"Ex: +5511999999999\",\n                                            value: phone,\n                                            onChange: (e)=>setPhone(e.target.value),\n                                            className: \"flex-1 bg-[#f5f5f5] border-none rounded-lg\",\n                                            disabled: isVerifying || loading\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: startVerification,\n                                            disabled: !phone || isVerifying || loading,\n                                            className: \"bg-gray-800 hover:bg-gray-700 text-white rounded-lg\",\n                                            children: [\n                                                loading && !isVerifying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this) : null,\n                                                loading && !isVerifying ? 'Enviando...' : 'Verificar'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: \"Informe seu n\\xfamero no formato internacional com o c\\xf3digo do pa\\xeds\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this),\n                        isVerifying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-5 bg-[#f5f5f5] rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-[#f5f5f5] border border-gray-200 flex items-center justify-center mr-3 mt-0.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-700\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-800 font-medium\",\n                                                    children: \"C\\xf3digo de verifica\\xe7\\xe3o enviado\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Enviamos um c\\xf3digo para o seu WhatsApp. Por favor, informe o c\\xf3digo abaixo:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"text\",\n                                            placeholder: \"C\\xf3digo de verifica\\xe7\\xe3o\",\n                                            value: verificationCode,\n                                            onChange: (e)=>setVerificationCode(e.target.value),\n                                            className: \"flex-1 bg-[#f5f5f5] border-none rounded-lg\",\n                                            disabled: loading\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: confirmCode,\n                                            disabled: !verificationCode || loading,\n                                            className: \"bg-gray-800 hover:bg-gray-700 text-white rounded-lg\",\n                                            children: [\n                                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 23\n                                                }, this) : null,\n                                                loading ? 'Verificando...' : 'Confirmar'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/criador/WhatsAppVerification.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(WhatsAppVerification, \"+KsNp/k4u2RXoKqIQtOO2l15YUY=\");\n_c = WhatsAppVerification;\nvar _c;\n$RefreshReg$(_c, \"WhatsAppVerification\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/criador/WhatsAppVerification.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/SettingsModal.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/SettingsModal.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FaBell,FaInfoCircle,FaInstagram,FaLock,FaShareAlt,FaShieldAlt,FaUser,FaWhatsapp!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/supabaseClient */ \"(app-pages-browser)/./src/utils/supabaseClient.ts\");\n/* harmony import */ var _components_ui_StandardPopup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/StandardPopup */ \"(app-pages-browser)/./src/components/ui/StandardPopup.tsx\");\n/* harmony import */ var _components_UserAlerts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/UserAlerts */ \"(app-pages-browser)/./src/components/UserAlerts.tsx\");\n/* harmony import */ var _components_AlertsSettings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AlertsSettings */ \"(app-pages-browser)/./src/components/AlertsSettings.tsx\");\n/* harmony import */ var _components_instagram_InstagramConnectButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/instagram/InstagramConnectButton */ \"(app-pages-browser)/./src/components/instagram/InstagramConnectButton.tsx\");\n/* harmony import */ var _components_dashboard_criador_WhatsAppVerification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/criador/WhatsAppVerification */ \"(app-pages-browser)/./src/components/dashboard/criador/WhatsAppVerification.tsx\");\n/* harmony import */ var _components_dashboard_criador_NotificationSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/criador/NotificationSettings */ \"(app-pages-browser)/./src/components/dashboard/criador/NotificationSettings.tsx\");\n/* harmony import */ var _components_dashboard_restaurant_WhatsAppVerification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/dashboard/restaurant/WhatsAppVerification */ \"(app-pages-browser)/./src/components/dashboard/restaurant/WhatsAppVerification.tsx\");\n/* harmony import */ var _components_dashboard_restaurant_NotificationSettings__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/restaurant/NotificationSettings */ \"(app-pages-browser)/./src/components/dashboard/restaurant/NotificationSettings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SettingsModal(param) {\n    let { isOpen, onClose, onSaved, userType, defaultTab = 'info' } = param;\n    _s();\n    // Estado para dados do usuário\n    const [userId, setUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para restaurante\n    const [restaurantId, setRestaurantId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [responsible, setResponsible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [phone, setPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Estados para influenciador\n    const [bio, setBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [instagramUsername, setInstagramUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Usar o defaultTab como activeTabId inicial\n    const [activeTabId, setActiveTabId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTab);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsModal.useEffect\": ()=>{\n            // Only fetch data when the modal is open\n            if (!isOpen) return;\n            const fetchData = {\n                \"SettingsModal.useEffect.fetchData\": async ()=>{\n                    const { data: { session } } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                    if (!session) return;\n                    // Salvar o ID do usuário para uso com o componente UserAlerts\n                    setUserId(session.user.id);\n                    if (userType === 'restaurant') {\n                        // Buscar dados do restaurante\n                        const { data } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"restaurants\").select(\"*\").eq(\"owner_id\", session.user.id).maybeSingle();\n                        if (data) {\n                            setRestaurantId(data.id);\n                            var _data_name;\n                            setName((_data_name = data.name) !== null && _data_name !== void 0 ? _data_name : \"\");\n                            var _data_responsible;\n                            setResponsible((_data_responsible = data.responsible) !== null && _data_responsible !== void 0 ? _data_responsible : \"\");\n                            var _data_email;\n                            setEmail((_data_email = data.email) !== null && _data_email !== void 0 ? _data_email : \"\");\n                            var _data_phone;\n                            setPhone((_data_phone = data.phone) !== null && _data_phone !== void 0 ? _data_phone : \"\");\n                            var _data_location;\n                            setLocation((_data_location = data.location) !== null && _data_location !== void 0 ? _data_location : \"\");\n                        }\n                    } else {\n                        // Buscar dados do perfil\n                        const { data: profileData } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"profiles\").select(\"*\").eq(\"id\", session.user.id).maybeSingle();\n                        if (profileData) {\n                            var _profileData_full_name;\n                            setName((_profileData_full_name = profileData.full_name) !== null && _profileData_full_name !== void 0 ? _profileData_full_name : \"\");\n                            var _profileData_email;\n                            setEmail((_profileData_email = profileData.email) !== null && _profileData_email !== void 0 ? _profileData_email : \"\");\n                            var _profileData_phone;\n                            setPhone((_profileData_phone = profileData.phone) !== null && _profileData_phone !== void 0 ? _profileData_phone : \"\");\n                        }\n                        // Buscar dados do perfil de influenciador\n                        const { data: influencerData } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"influencer_profiles\").select(\"*\").eq(\"id\", session.user.id).maybeSingle();\n                        if (influencerData) {\n                            var _influencerData_bio;\n                            setBio((_influencerData_bio = influencerData.bio) !== null && _influencerData_bio !== void 0 ? _influencerData_bio : \"\");\n                            var _influencerData_instagram_username;\n                            setInstagramUsername((_influencerData_instagram_username = influencerData.instagram_username) !== null && _influencerData_instagram_username !== void 0 ? _influencerData_instagram_username : \"\");\n                        }\n                    }\n                }\n            }[\"SettingsModal.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"SettingsModal.useEffect\"], [\n        isOpen,\n        userType\n    ]);\n    const handleSaveRestaurant = async ()=>{\n        if (!restaurantId) return;\n        setSaving(true);\n        try {\n            const { data, error } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"restaurants\").update({\n                name,\n                responsible,\n                email,\n                phone,\n                location\n            }).eq(\"id\", restaurantId).select();\n            if (!error && data && data.length > 0) {\n                onSaved === null || onSaved === void 0 ? void 0 : onSaved(data[0]);\n                onClose();\n            } else if (error) {\n                alert(\"Erro ao salvar: \" + error.message);\n            }\n        } catch (err) {\n            console.error(\"Unexpected error saving restaurant info:\", err);\n            alert(\"Erro inesperado ao salvar.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleSaveInfluencer = async ()=>{\n        try {\n            setSaving(true);\n            if (!userId) {\n                console.error('Usuário não encontrado');\n                return;\n            }\n            // Atualizar perfil no banco de dados\n            const { error: profileError } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('profiles').update({\n                full_name: name,\n                email: email,\n                phone: phone,\n                updated_at: new Date().toISOString()\n            }).eq('id', userId);\n            if (profileError) {\n                throw new Error(\"Erro ao atualizar perfil: \".concat(profileError.message));\n            }\n            // Atualizar perfil de influenciador\n            const { error: influencerError } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('influencer_profiles').update({\n                bio: bio,\n                instagram_username: instagramUsername,\n                updated_at: new Date().toISOString()\n            }).eq('id', userId);\n            if (influencerError) {\n                throw new Error(\"Erro ao atualizar perfil de influenciador: \".concat(influencerError.message));\n            }\n            // Buscar dados atualizados\n            const { data: updatedProfile } = await _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.from('profiles').select('*').eq('id', userId).single();\n            onSaved === null || onSaved === void 0 ? void 0 : onSaved(updatedProfile);\n            onClose();\n        } catch (err) {\n            console.error('Erro ao salvar configurações:', err);\n            alert(\"Erro ao salvar: \".concat(err.message));\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleSave = ()=>{\n        if (userType === 'restaurant') {\n            handleSaveRestaurant();\n        } else {\n            handleSaveInfluencer();\n        }\n    };\n    // Definir as abas do popup\n    const tabs = [\n        {\n            id: 'info',\n            label: 'Perfil',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaInfoCircle, {\n                className: \"text-green-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 207,\n                columnNumber: 13\n            }, this),\n            color: 'green',\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaInfoCircle, {\n                                className: \"text-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            \" Informa\\xe7\\xf5es B\\xe1sicas\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaUser, {\n                                                className: \"text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Nome \",\n                                            userType === 'restaurant' ? 'do Restaurante' : 'Completo'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: name,\n                                        onChange: (e)=>setName(e.target.value),\n                                        className: \"w-full\",\n                                        placeholder: userType === 'restaurant' ? \"Nome do estabelecimento\" : \"Seu nome completo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            userType === 'restaurant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaUser, {\n                                                className: \"text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Respons\\xe1vel\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: responsible,\n                                        onChange: (e)=>setResponsible(e.target.value),\n                                        className: \"w-full\",\n                                        placeholder: \"Nome do respons\\xe1vel\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 text-gray-500\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Email\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        className: \"w-full\",\n                                        placeholder: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 text-gray-500\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Telefone\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        value: phone,\n                                        onChange: (e)=>setPhone(e.target.value),\n                                        className: \"w-full\",\n                                        placeholder: \"(00) 00000-0000\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            userType === 'restaurant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group md:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 text-gray-500\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Localiza\\xe7\\xe3o\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: location,\n                                        onChange: (e)=>setLocation(e.target.value),\n                                        className: \"w-full\",\n                                        placeholder: \"Endere\\xe7o completo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this),\n                            userType === 'influencer' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-group md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-4 w-4 text-gray-500\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Bio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: bio,\n                                                onChange: (e)=>setBio(e.target.value),\n                                                className: \"w-full\",\n                                                rows: 3,\n                                                placeholder: \"Uma breve descri\\xe7\\xe3o sobre voc\\xea\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-group md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block mb-2 text-sm font-medium flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaInstagram, {\n                                                        className: \"text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \" Instagram\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 text-gray-500 bg-gray-100 border-none rounded-l-lg\",\n                                                        children: \"@\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: instagramUsername,\n                                                        onChange: (e)=>setInstagramUsername(e.target.value),\n                                                        className: \"flex-1 min-w-0 block w-full rounded-none rounded-r-lg\",\n                                                        placeholder: \"seu_usuario\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSave,\n                            disabled: saving,\n                            className: \"px-6 py-2.5 rounded-lg bg-gray-800 text-white text-sm hover:bg-gray-700 transition-all disabled:opacity-50 font-medium flex items-center gap-2\",\n                            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Salvando...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Salvar\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            id: 'security',\n            label: 'Segurança',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaLock, {\n                className: \"text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 364,\n                columnNumber: 13\n            }, this),\n            color: 'red',\n            lazyLoad: true,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaLock, {\n                                className: \"text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            \" Configura\\xe7\\xf5es de Seguran\\xe7a\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mb-3 text-sm\",\n                        children: \"As configura\\xe7\\xf5es de seguran\\xe7a estar\\xe3o dispon\\xedveis em breve.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaShieldAlt, {\n                                        className: \"h-5 w-5 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-yellow-700\",\n                                        children: \"Recomendamos alterar sua senha regularmente e ativar a autentica\\xe7\\xe3o de dois fatores quando dispon\\xedvel.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, this)\n        },\n        // Social tab - Only for influencers\n        ...userType === 'influencer' ? [\n            {\n                id: 'social',\n                label: 'Redes Sociais',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaShareAlt, {\n                    className: \"text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 13\n                }, this),\n                color: 'purple',\n                lazyLoad: true,\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-bold mb-3 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaShareAlt, {\n                                    className: \"text-purple-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                \" Conex\\xf5es com Redes Sociais\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mb-3 text-sm\",\n                            children: \"Conecte suas redes sociais para participar de campanhas e monitorar seu desempenho.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-base font-medium mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaInstagram, {\n                                                    className: \"text-pink-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Instagram\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mb-3\",\n                                            children: \"Conecte sua conta do Instagram para participar das competi\\xe7\\xf5es e ter seus posts contabilizados automaticamente para ganhar pontos e pr\\xeamios.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 15\n                                        }, this),\n                                        userId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_instagram_InstagramConnectButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            userId: userId\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 26\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-base font-medium mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaWhatsapp, {\n                                                    className: \"text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" WhatsApp\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 mb-3\",\n                                            children: \"Conecte seu WhatsApp para receber notifica\\xe7\\xf5es importantes sobre campanhas, atualiza\\xe7\\xf5es de ranking e pagamentos.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_criador_WhatsAppVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }\n        ] : [],\n        // WhatsApp tab\n        {\n            id: 'whatsapp',\n            label: 'WhatsApp',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaWhatsapp, {\n                className: \"text-green-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 432,\n                columnNumber: 13\n            }, this),\n            color: 'green',\n            lazyLoad: true,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaWhatsapp, {\n                                className: \"text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            \" Configura\\xe7\\xf5es de WhatsApp\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mb-3 text-sm\",\n                        children: \"Configure suas prefer\\xeancias de notifica\\xe7\\xe3o via WhatsApp.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: userType === 'influencer' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_criador_WhatsAppVerification__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_criador_NotificationSettings__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_restaurant_WhatsAppVerification__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_restaurant_NotificationSettings__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            id: 'alerts',\n            label: 'Avisos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaBell, {\n                className: \"text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 461,\n                columnNumber: 13\n            }, this),\n            color: 'yellow',\n            lazyLoad: true,\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold mb-3 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_FaInfoCircle_FaInstagram_FaLock_FaShareAlt_FaShieldAlt_FaUser_FaWhatsapp_react_icons_fa__WEBPACK_IMPORTED_MODULE_11__.FaBell, {\n                                className: \"text-yellow-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this),\n                            \" Avisos do Sistema\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mb-3 text-sm\",\n                        children: \"Escolha quais avisos voc\\xea deseja ver na p\\xe1gina de configura\\xe7\\xf5es.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 11\n                    }, this),\n                    userId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserAlerts__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AlertsSettings__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                userId: userId\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 p-4 rounded\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"Carregando dados de usu\\xe1rio...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n                lineNumber: 465,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    // Função para lidar com o fechamento do popup\n    const handleClose = ()=>{\n        onClose();\n    };\n    // Atualizar o activeTabId quando a aba mudar\n    const handleTabChange = (tabId)=>{\n        setActiveTabId(tabId);\n    };\n    // Memoize tabs to prevent unnecessary re-renders\n    const memoizedTabs = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"SettingsModal.useMemo[memoizedTabs]\": ()=>tabs\n    }[\"SettingsModal.useMemo[memoizedTabs]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StandardPopup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        id: \"settings-modal\",\n        isOpen: isOpen,\n        onClose: handleClose,\n        title: \"Configura\\xe7\\xf5es\",\n        tabs: memoizedTabs,\n        defaultTabId: defaultTab,\n        onTabChange: handleTabChange,\n        size: \"small\",\n        minContentHeight: \"500px\",\n        className: \"settings-modal-premium\",\n        overlayClassName: \"settings-modal-premium-overlay\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/ui/SettingsModal.tsx\",\n        lineNumber: 500,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsModal, \"GuBNS2bvwmf/MXytJ6YWqOpJPEg=\");\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SettingsModal.tsx\n"));

/***/ })

});