"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TasksAndScheduleCard.tsx":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/TasksAndScheduleCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TasksAndScheduleCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_EmptyState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/EmptyState */ \"(app-pages-browser)/./src/components/ui/EmptyState.tsx\");\n/* harmony import */ var _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/tasks */ \"(app-pages-browser)/./src/lib/services/tasks.ts\");\n/* harmony import */ var _lib_services_scheduling__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/scheduling */ \"(app-pages-browser)/./src/lib/services/scheduling.ts\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/startOfToday.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TasksAndScheduleCard(param) {\n    let { userId, className, compact = false } = param;\n    _s();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [actionItems, setActionItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksAndScheduleCard.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"TasksAndScheduleCard.useEffect\"], [\n        userId\n    ]);\n    // Função para verificar e criar tarefas com base em campanhas ativas\n    const checkAndCreateTasksFromCampaigns = async ()=>{\n        try {\n            // Verificar se a tabela 'tasks' existe\n            let tasksTableExists = true;\n            try {\n                const { error: tableCheckError } = await supabase.from('tasks').select('id').limit(1);\n                if (tableCheckError) {\n                    console.error('Tabela tasks pode não existir:', tableCheckError);\n                    tasksTableExists = false;\n                    return;\n                }\n            } catch (error) {\n                console.error('Erro ao verificar tabela tasks:', error);\n                tasksTableExists = false;\n                return;\n            }\n            if (!tasksTableExists) {\n                return;\n            }\n            // Verificar se já existem tarefas para o usuário\n            const { data: existingTasks, error: checkError } = await supabase.from('tasks').select('id').eq('user_id', userId).limit(1);\n            if (checkError) {\n                console.error('Erro ao verificar tarefas existentes:', checkError);\n                return;\n            }\n            // Buscar campanhas ativas do usuário\n            const { data: activeCampaigns, error: campaignsError } = await supabase.from('campaign_influencers').select(\"\\n          id,\\n          campaign_id,\\n          campaigns(\\n            id,\\n            name,\\n            restaurant_id,\\n            restaurants(\\n              id,\\n              name\\n            )\\n          )\\n        \").eq('influencer_id', userId).eq('status', 'accepted');\n            if (campaignsError) {\n                console.error('Erro ao buscar campanhas ativas:', campaignsError);\n                return;\n            }\n            // Se não houver tarefas mas existirem campanhas ativas, criar tarefas baseadas nas campanhas\n            if ((!existingTasks || existingTasks.length === 0) && activeCampaigns && activeCampaigns.length > 0) {\n                console.log('Criando tarefas baseadas em campanhas ativas para o usuário:', userId);\n                for (const campaign of activeCampaigns){\n                    // Verificar se já existe conteúdo para esta campanha\n                    let existingContent = null;\n                    try {\n                        const { data, error: contentError } = await supabase.from('campaign_content').select('id, status').eq('campaign_influencer_id', campaign.id).order('created_at', {\n                            ascending: false\n                        }).limit(1);\n                        if (contentError) {\n                            // Se a tabela não existir, apenas continue sem criar tarefas baseadas em conteúdo\n                            console.log('Tabela campaign_content não existe ou não está acessível, pulando verificação de conteúdo');\n                            existingContent = null;\n                        } else {\n                            existingContent = data;\n                        }\n                    } catch (error) {\n                        console.log('Erro ao acessar tabela campaign_content, pulando verificação de conteúdo:', error);\n                        existingContent = null;\n                    }\n                    // Criar tarefa com base no status do conteúdo\n                    if (!existingContent || existingContent.length === 0) {\n                        var _campaign_campaigns_, _campaign_campaigns, _campaign_campaigns_1, _campaign_campaigns1, _campaign_campaigns__restaurants_, _campaign_campaigns__restaurants, _campaign_campaigns_2, _campaign_campaigns2;\n                        // Não há conteúdo, criar tarefa para criar conteúdo\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Criar conte\\xfado para \".concat(((_campaign_campaigns = campaign.campaigns) === null || _campaign_campaigns === void 0 ? void 0 : (_campaign_campaigns_ = _campaign_campaigns[0]) === null || _campaign_campaigns_ === void 0 ? void 0 : _campaign_campaigns_.name) || 'Campanha Desconhecida'),\n                            description: \"Gravar v\\xeddeo para a campanha \".concat(((_campaign_campaigns1 = campaign.campaigns) === null || _campaign_campaigns1 === void 0 ? void 0 : (_campaign_campaigns_1 = _campaign_campaigns1[0]) === null || _campaign_campaigns_1 === void 0 ? void 0 : _campaign_campaigns_1.name) || 'Campanha Desconhecida', \" do restaurante \").concat(((_campaign_campaigns2 = campaign.campaigns) === null || _campaign_campaigns2 === void 0 ? void 0 : (_campaign_campaigns_2 = _campaign_campaigns2[0]) === null || _campaign_campaigns_2 === void 0 ? void 0 : (_campaign_campaigns__restaurants = _campaign_campaigns_2.restaurants) === null || _campaign_campaigns__restaurants === void 0 ? void 0 : (_campaign_campaigns__restaurants_ = _campaign_campaigns__restaurants[0]) === null || _campaign_campaigns__restaurants_ === void 0 ? void 0 : _campaign_campaigns__restaurants_.name) || 'Restaurante Desconhecido'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                            due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    } else if (existingContent[0].status === 'approved') {\n                        var _campaign_campaigns_3, _campaign_campaigns3, _campaign_campaigns_4, _campaign_campaigns4;\n                        // Conteúdo aprovado, criar tarefa para publicar\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Publicar conte\\xfado de \".concat(((_campaign_campaigns3 = campaign.campaigns) === null || _campaign_campaigns3 === void 0 ? void 0 : (_campaign_campaigns_3 = _campaign_campaigns3[0]) === null || _campaign_campaigns_3 === void 0 ? void 0 : _campaign_campaigns_3.name) || 'Campanha Desconhecida'),\n                            description: \"Publicar o conte\\xfado aprovado para a campanha \".concat(((_campaign_campaigns4 = campaign.campaigns) === null || _campaign_campaigns4 === void 0 ? void 0 : (_campaign_campaigns_4 = _campaign_campaigns4[0]) === null || _campaign_campaigns_4 === void 0 ? void 0 : _campaign_campaigns_4.name) || 'Campanha Desconhecida'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_PUBLICATION,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                            due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    } else if (existingContent[0].status === 'rejected') {\n                        var _campaign_campaigns_5, _campaign_campaigns5, _campaign_campaigns_6, _campaign_campaigns6;\n                        // Conteúdo rejeitado, criar tarefa para revisar\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Revisar conte\\xfado de \".concat(((_campaign_campaigns5 = campaign.campaigns) === null || _campaign_campaigns5 === void 0 ? void 0 : (_campaign_campaigns_5 = _campaign_campaigns5[0]) === null || _campaign_campaigns_5 === void 0 ? void 0 : _campaign_campaigns_5.name) || 'Campanha Desconhecida'),\n                            description: \"Revisar e reenviar o conte\\xfado rejeitado para a campanha \".concat(((_campaign_campaigns6 = campaign.campaigns) === null || _campaign_campaigns6 === void 0 ? void 0 : (_campaign_campaigns_6 = _campaign_campaigns6[0]) === null || _campaign_campaigns_6 === void 0 ? void 0 : _campaign_campaigns_6.name) || 'Campanha Desconhecida'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.URGENT,\n                            due_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    }\n                    // Verificar se há agendamento para esta campanha\n                    let existingSchedule = null;\n                    try {\n                        const { data, error: scheduleError } = await supabase.from('campaign_schedule').select('id').eq('campaign_id', campaign.campaign_id).eq('influencer_id', userId).limit(1);\n                        if (scheduleError) {\n                            // Se a tabela não existir, apenas continue sem criar tarefas baseadas em agendamento\n                            console.log('Tabela campaign_schedule não existe ou não está acessível, pulando verificação de agendamento');\n                            existingSchedule = null;\n                        } else {\n                            existingSchedule = data;\n                        }\n                    } catch (error) {\n                        console.log('Erro ao acessar tabela campaign_schedule, pulando verificação de agendamento:', error);\n                        existingSchedule = null;\n                    }\n                    // Se não houver agendamento, criar tarefa para agendar visita\n                    if (!existingSchedule || existingSchedule.length === 0) {\n                        var _campaign_campaigns_7, _campaign_campaigns7, _campaign_campaigns__restaurants_1, _campaign_campaigns__restaurants1, _campaign_campaigns_8, _campaign_campaigns8, _campaign_campaigns_9, _campaign_campaigns9;\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Agendar visita para \".concat(((_campaign_campaigns7 = campaign.campaigns) === null || _campaign_campaigns7 === void 0 ? void 0 : (_campaign_campaigns_7 = _campaign_campaigns7[0]) === null || _campaign_campaigns_7 === void 0 ? void 0 : _campaign_campaigns_7.name) || 'Campanha Desconhecida'),\n                            description: \"Agendar visita ao restaurante \".concat(((_campaign_campaigns8 = campaign.campaigns) === null || _campaign_campaigns8 === void 0 ? void 0 : (_campaign_campaigns_8 = _campaign_campaigns8[0]) === null || _campaign_campaigns_8 === void 0 ? void 0 : (_campaign_campaigns__restaurants1 = _campaign_campaigns_8.restaurants) === null || _campaign_campaigns__restaurants1 === void 0 ? void 0 : (_campaign_campaigns__restaurants_1 = _campaign_campaigns__restaurants1[0]) === null || _campaign_campaigns__restaurants_1 === void 0 ? void 0 : _campaign_campaigns__restaurants_1.name) || 'Restaurante Desconhecido', \" para a campanha \").concat(((_campaign_campaigns9 = campaign.campaigns) === null || _campaign_campaigns9 === void 0 ? void 0 : (_campaign_campaigns_9 = _campaign_campaigns9[0]) === null || _campaign_campaigns_9 === void 0 ? void 0 : _campaign_campaigns_9.name) || 'Campanha Desconhecida'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.SCHEDULE_VISIT,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.MEDIUM,\n                            due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    }\n                }\n                console.log('Tarefas baseadas em campanhas criadas com sucesso');\n            }\n        } catch (error) {\n            console.error('Erro ao criar tarefas baseadas em campanhas:', error);\n        }\n    };\n    const fetchData = async ()=>{\n        setLoading(true);\n        try {\n            // Se houver um userId, verificar se há dados baseados em campanhas\n            if (userId) {\n                await checkAndCreateTasksFromCampaigns();\n            }\n            await Promise.all([\n                fetchTasks(),\n                fetchSchedules()\n            ]);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchTasks = async ()=>{\n        try {\n            // Verificar se a tabela 'tasks' existe\n            let tableExists = true;\n            try {\n                const { error: tableCheckError } = await supabase.from('tasks').select('id').limit(1);\n                if (tableCheckError) {\n                    console.error('Erro ao verificar tabela tasks:', tableCheckError);\n                    tableExists = false;\n                }\n            } catch (error) {\n                console.error('Erro ao verificar tabela tasks:', error);\n                tableExists = false;\n            }\n            if (!tableExists) {\n                // Se a tabela não existir, usar um array vazio\n                setTasks([]);\n                updateActionItems([], schedules);\n                return;\n            }\n            // Usar o cliente Supabase diretamente\n            let query = supabase.from('tasks').select(\"\\n          id,\\n          user_id,\\n          campaign_id,\\n          title,\\n          description,\\n          task_type,\\n          status,\\n          priority,\\n          due_date,\\n          completed_at,\\n          created_at,\\n          updated_at\\n        \") // Select specific fields from tasks table only\n            ;\n            // Adicionar filtro de status\n            query = query.in('status', [\n                _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.IN_PROGRESS,\n                _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.OVERDUE\n            ]);\n            // Adicionar filtro de usuário se necessário\n            if (userId) {\n                query = query.eq('user_id', userId);\n            }\n            // Executar a consulta\n            const { data, error } = await query.order('due_date', {\n                ascending: true\n            });\n            if (error) {\n                console.error('Erro na consulta de tarefas:', error);\n                setTasks([]);\n                updateActionItems([], schedules);\n                return;\n            }\n            console.log('Tarefas carregadas com sucesso:', (data === null || data === void 0 ? void 0 : data.length) || 0);\n            setTasks(data || []);\n            // Atualizar a lista combinada\n            updateActionItems(data || [], schedules);\n        } catch (error) {\n            console.error('Erro ao carregar tarefas:', error);\n            // Em caso de erro, usar um array vazio\n            setTasks([]);\n            updateActionItems([], schedules);\n        }\n    };\n    const fetchSchedules = async ()=>{\n        try {\n            // Verificar se a tabela 'schedules' existe\n            let tableExists = true;\n            try {\n                const { error: tableCheckError } = await supabase.from('campaign_schedule').select('id').limit(1);\n                if (tableCheckError) {\n                    console.error('Erro ao verificar tabela schedules:', tableCheckError);\n                    tableExists = false;\n                }\n            } catch (error) {\n                console.error('Erro ao verificar tabela schedules:', error);\n                tableExists = false;\n            }\n            if (!tableExists) {\n                // Se a tabela não existir, usar um array vazio\n                setSchedules([]);\n                updateActionItems(tasks, []);\n                return;\n            }\n            // Usar o cliente Supabase diretamente\n            let query = supabase.from('campaign_schedule').select(\"\\n          id,\\n          campaign_id,\\n          influencer_id,\\n          scheduled_date,\\n          status,\\n          notes,\\n          created_at,\\n          updated_at,\\n          completed_at,\\n          cancelled_at,\\n          cancellation_reason\\n        \") // Select specific fields from campaign_schedule table only\n            ;\n            // Adicionar filtro de usuário se necessário\n            if (userId) {\n                query = query.eq('influencer_id', userId);\n            }\n            // Executar a consulta\n            const { data, error } = await query.order('scheduled_date', {\n                ascending: true\n            });\n            if (error) {\n                console.error('Erro na consulta de agendamentos:', error);\n                setSchedules([]);\n                updateActionItems(tasks, []);\n                return;\n            }\n            console.log('Agendamentos carregados com sucesso:', (data === null || data === void 0 ? void 0 : data.length) || 0);\n            setSchedules(data || []);\n            // Atualizar a lista combinada\n            updateActionItems(tasks, data || []);\n        } catch (error) {\n            console.error('Erro ao carregar agendamentos:', error);\n            // Em caso de erro, usar um array vazio\n            setSchedules([]);\n            updateActionItems(tasks, []);\n        }\n    };\n    // Combinar tarefas e agendamentos em uma única lista\n    const updateActionItems = (tasksList, schedulesList)=>{\n        const today = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_8__.startOfToday)();\n        const nextMonth = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_9__.addDays)(today, 30);\n        // Converter tarefas para o formato de exibição\n        const taskItems = tasksList.map((task)=>{\n            // Determinar o tipo de ação com base no tipo de tarefa\n            let actionType = 'Tarefa';\n            let icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 439,\n                columnNumber: 18\n            }, this);\n            switch(task.task_type){\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION:\n                    actionType = 'Criar conteúdo';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_APPROVAL:\n                    actionType = 'Aprovar conteúdo';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_PUBLICATION:\n                    actionType = 'Publicar conteúdo';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.SCHEDULE_VISIT:\n                    actionType = 'Visitar restaurante';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.ADMINISTRATIVE:\n                    actionType = 'Tarefa administrativa';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.PAYMENT:\n                    actionType = 'Pagamento';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.OTHER:\n                    actionType = 'Outra tarefa';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 18\n                    }, this);\n                    break;\n            }\n            return {\n                id: task.id,\n                type: 'task',\n                title: task.title,\n                date: task.due_date || '',\n                restaurant: task.campaign_id ? \"Campanha ID: \".concat(task.campaign_id.substring(0, 8)) : 'Restaurante N/A',\n                actionType,\n                status: task.status,\n                originalData: task,\n                icon\n            };\n        });\n        // Converter agendamentos para o formato de exibição\n        const scheduleItems = schedulesList.filter((schedule)=>{\n            const scheduleDate = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(schedule.scheduled_date);\n            return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_15__.isAfter)(scheduleDate, today) && schedule.status !== _lib_services_scheduling__WEBPACK_IMPORTED_MODULE_7__.ScheduleStatus.CANCELLED;\n        }).map((schedule)=>({\n                id: schedule.id,\n                type: 'schedule',\n                title: schedule.campaign_id ? \"Visita Campanha ID: \".concat(schedule.campaign_id.substring(0, 8)) : \"Agendamento ID: \".concat(schedule.id.substring(0, 8)),\n                date: schedule.scheduled_date,\n                restaurant: schedule.campaign_id ? \"Campanha ID: \".concat(schedule.campaign_id.substring(0, 8)) : 'Restaurante N/A',\n                actionType: 'Visita',\n                status: schedule.status,\n                originalData: schedule,\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 mr-1\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 501,\n                    columnNumber: 15\n                }, this)\n            }));\n        // Combinar e ordenar por data\n        const combined = [\n            ...taskItems,\n            ...scheduleItems\n        ].sort((a, b)=>{\n            if (!a.date) return 1;\n            if (!b.date) return -1;\n            return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(a.date).getTime() - (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(b.date).getTime();\n        }).slice(0, 5) // Mostrar apenas os 5 próximos itens\n        ;\n        setActionItems(combined);\n    };\n    const handleTaskComplete = async (taskId)=>{\n        try {\n            // Usar o cliente Supabase diretamente\n            const now = new Date().toISOString();\n            const { error } = await supabase.from('tasks').update({\n                status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED,\n                completed_at: now,\n                updated_at: now\n            }).eq('id', taskId);\n            if (error) {\n                throw error;\n            }\n            // Atualizar lista de tarefas\n            fetchData();\n        } catch (error) {\n            console.error('Erro ao concluir tarefa:', error);\n        }\n    };\n    // Formatar data\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'Sem prazo';\n        return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)((0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(dateString), \"dd/MM\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR\n        });\n    };\n    // Formatar data e hora\n    const formatDateTime = (dateString)=>{\n        const date = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(dateString);\n        return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(date, \"dd/MM 'às' HH:mm\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR\n        });\n    };\n    // Renderizar ícone com base no tipo de ação\n    const renderActionIcon = (item)=>{\n        if (item.type === 'task') {\n            switch(item.originalData.task_type){\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_APPROVAL:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_PUBLICATION:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-indigo-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 562,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.SCHEDULE_VISIT:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-purple-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.PAYMENT:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.ADMINISTRATIVE:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 18\n                    }, this);\n                default:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 18\n                    }, this);\n            }\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4 mr-1 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 573,\n                columnNumber: 14\n            }, this);\n        }\n    };\n    // Se for modo compacto, renderizar apenas a lista de itens sem o card completo\n    if (compact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-5 w-5 animate-spin text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 582,\n                columnNumber: 11\n            }, this) : actionItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-3 text-sm text-gray-500\",\n                children: \"Sem a\\xe7\\xf5es pendentes\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 586,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-gray-100\",\n                children: actionItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2.5 px-5 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm\",\n                                        children: [\n                                            renderActionIcon(item),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-medium text-gray-700\",\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1 text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate\",\n                                                children: [\n                                                    item.type === 'task' ? formatDate(item.date) : formatDateTime(item.date),\n                                                    item.restaurant && \" • \".concat(item.restaurant)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 17\n                            }, this),\n                            item.type === 'task' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                className: \"h-7 px-2 text-xs ml-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                onClick: ()=>handleTaskComplete(item.id),\n                                children: \"Concluir\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, \"\".concat(item.type, \"-\").concat(item.id), true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 15\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 590,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n            lineNumber: 580,\n            columnNumber: 7\n        }, this);\n    }\n    // Versão padrão com card completo\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"bg-[#f5f5f5] pb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-lg font-medium\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-5 w-5 inline-block mr-2 text-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                            lineNumber: 632,\n                            columnNumber: 11\n                        }, this),\n                        \"Pr\\xf3ximas A\\xe7\\xf5es\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 630,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-4\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 11\n                }, this) : actionItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EmptyState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    title: \"Sem a\\xe7\\xf5es pendentes\",\n                    description: \"Voc\\xea n\\xe3o tem tarefas ou agendamentos pr\\xf3ximos.\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-12 w-12\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 19\n                    }, void 0),\n                    actionLabel: \"Atualizar\",\n                    onAction: fetchData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 643,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: actionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row sm:items-center mt-1 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mr-3\",\n                                                    children: [\n                                                        renderActionIcon(item),\n                                                        item.actionType\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                            lineNumber: 665,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        item.type === 'task' ? formatDate(item.date) : formatDateTime(item.date)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                    lineNumber: 664,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.restaurant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.restaurant\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 17\n                                }, this),\n                                item.type === 'task' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    onClick: ()=>handleTaskComplete(item.id),\n                                    children: \"Concluir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, \"\".concat(item.type, \"-\").concat(item.id), true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n        lineNumber: 629,\n        columnNumber: 5\n    }, this);\n}\n_s(TasksAndScheduleCard, \"JVPue+tWxQn/VGvfk7eirdLBxvM=\");\n_c = TasksAndScheduleCard;\nvar _c;\n$RefreshReg$(_c, \"TasksAndScheduleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TasksAndScheduleCard.tsx\n"));

/***/ })

});