"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TasksAndScheduleCard.tsx":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/TasksAndScheduleCard.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TasksAndScheduleCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Camera,CheckCircle2,Clock,Loader2,MapPin,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_EmptyState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/EmptyState */ \"(app-pages-browser)/./src/components/ui/EmptyState.tsx\");\n/* harmony import */ var _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/tasks */ \"(app-pages-browser)/./src/lib/services/tasks.ts\");\n/* harmony import */ var _lib_services_scheduling__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/scheduling */ \"(app-pages-browser)/./src/lib/services/scheduling.ts\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/startOfToday.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addDays.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isAfter,parseISO,startOfToday!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TasksAndScheduleCard(param) {\n    let { userId, className, compact = false } = param;\n    _s();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tasks, setTasks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedules, setSchedules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [actionItems, setActionItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TasksAndScheduleCard.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"TasksAndScheduleCard.useEffect\"], [\n        userId\n    ]);\n    // Função para verificar e criar tarefas com base em campanhas ativas\n    const checkAndCreateTasksFromCampaigns = async ()=>{\n        try {\n            // Verificar se a tabela 'tasks' existe\n            let tasksTableExists = true;\n            try {\n                const { error: tableCheckError } = await supabase.from('tasks').select('id').limit(1);\n                if (tableCheckError) {\n                    console.error('Tabela tasks pode não existir:', tableCheckError);\n                    tasksTableExists = false;\n                    return;\n                }\n            } catch (error) {\n                console.error('Erro ao verificar tabela tasks:', error);\n                tasksTableExists = false;\n                return;\n            }\n            if (!tasksTableExists) {\n                return;\n            }\n            // Verificar se já existem tarefas para o usuário\n            const { data: existingTasks, error: checkError } = await supabase.from('tasks').select('id').eq('user_id', userId).limit(1);\n            if (checkError) {\n                console.error('Erro ao verificar tarefas existentes:', checkError);\n                return;\n            }\n            // Buscar campanhas ativas do usuário\n            const { data: activeCampaigns, error: campaignsError } = await supabase.from('campaign_influencers').select(\"\\n          id,\\n          campaign_id,\\n          campaigns(\\n            id,\\n            name,\\n            restaurant_id,\\n            restaurants(\\n              id,\\n              name\\n            )\\n          )\\n        \").eq('influencer_id', userId).eq('status', 'accepted');\n            if (campaignsError) {\n                console.error('Erro ao buscar campanhas ativas:', campaignsError);\n                return;\n            }\n            // Se não houver tarefas mas existirem campanhas ativas, criar tarefas baseadas nas campanhas\n            if ((!existingTasks || existingTasks.length === 0) && activeCampaigns && activeCampaigns.length > 0) {\n                console.log('Criando tarefas baseadas em campanhas ativas para o usuário:', userId);\n                for (const campaign of activeCampaigns){\n                    // Verificar se já existe conteúdo para esta campanha\n                    let existingContent = null;\n                    try {\n                        const { data, error: contentError } = await supabase.from('campaign_content').select('id, status').eq('campaign_influencer_id', campaign.id).order('created_at', {\n                            ascending: false\n                        }).limit(1);\n                        if (contentError) {\n                            // Se a tabela não existir, apenas continue sem criar tarefas baseadas em conteúdo\n                            console.log('Tabela campaign_content não existe ou não está acessível, pulando verificação de conteúdo');\n                            existingContent = null;\n                        } else {\n                            existingContent = data;\n                        }\n                    } catch (error) {\n                        console.log('Erro ao acessar tabela campaign_content, pulando verificação de conteúdo:', error);\n                        existingContent = null;\n                    }\n                    // Criar tarefa com base no status do conteúdo\n                    if (!existingContent || existingContent.length === 0) {\n                        var _campaign_campaigns_, _campaign_campaigns, _campaign_campaigns_1, _campaign_campaigns1, _campaign_campaigns__restaurants_, _campaign_campaigns__restaurants, _campaign_campaigns_2, _campaign_campaigns2;\n                        // Não há conteúdo, criar tarefa para criar conteúdo\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Criar conte\\xfado para \".concat(((_campaign_campaigns = campaign.campaigns) === null || _campaign_campaigns === void 0 ? void 0 : (_campaign_campaigns_ = _campaign_campaigns[0]) === null || _campaign_campaigns_ === void 0 ? void 0 : _campaign_campaigns_.name) || 'Campanha Desconhecida'),\n                            description: \"Gravar v\\xeddeo para a campanha \".concat(((_campaign_campaigns1 = campaign.campaigns) === null || _campaign_campaigns1 === void 0 ? void 0 : (_campaign_campaigns_1 = _campaign_campaigns1[0]) === null || _campaign_campaigns_1 === void 0 ? void 0 : _campaign_campaigns_1.name) || 'Campanha Desconhecida', \" do restaurante \").concat(((_campaign_campaigns2 = campaign.campaigns) === null || _campaign_campaigns2 === void 0 ? void 0 : (_campaign_campaigns_2 = _campaign_campaigns2[0]) === null || _campaign_campaigns_2 === void 0 ? void 0 : (_campaign_campaigns__restaurants = _campaign_campaigns_2.restaurants) === null || _campaign_campaigns__restaurants === void 0 ? void 0 : (_campaign_campaigns__restaurants_ = _campaign_campaigns__restaurants[0]) === null || _campaign_campaigns__restaurants_ === void 0 ? void 0 : _campaign_campaigns__restaurants_.name) || 'Restaurante Desconhecido'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                            due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    } else if (existingContent[0].status === 'approved') {\n                        var _campaign_campaigns_3, _campaign_campaigns3, _campaign_campaigns_4, _campaign_campaigns4;\n                        // Conteúdo aprovado, criar tarefa para publicar\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Publicar conte\\xfado de \".concat(((_campaign_campaigns3 = campaign.campaigns) === null || _campaign_campaigns3 === void 0 ? void 0 : (_campaign_campaigns_3 = _campaign_campaigns3[0]) === null || _campaign_campaigns_3 === void 0 ? void 0 : _campaign_campaigns_3.name) || 'Campanha Desconhecida'),\n                            description: \"Publicar o conte\\xfado aprovado para a campanha \".concat(((_campaign_campaigns4 = campaign.campaigns) === null || _campaign_campaigns4 === void 0 ? void 0 : (_campaign_campaigns_4 = _campaign_campaigns4[0]) === null || _campaign_campaigns_4 === void 0 ? void 0 : _campaign_campaigns_4.name) || 'Campanha Desconhecida'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_PUBLICATION,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.HIGH,\n                            due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    } else if (existingContent[0].status === 'rejected') {\n                        var _campaign_campaigns_5, _campaign_campaigns5, _campaign_campaigns_6, _campaign_campaigns6;\n                        // Conteúdo rejeitado, criar tarefa para revisar\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Revisar conte\\xfado de \".concat(((_campaign_campaigns5 = campaign.campaigns) === null || _campaign_campaigns5 === void 0 ? void 0 : (_campaign_campaigns_5 = _campaign_campaigns5[0]) === null || _campaign_campaigns_5 === void 0 ? void 0 : _campaign_campaigns_5.name) || 'Campanha Desconhecida'),\n                            description: \"Revisar e reenviar o conte\\xfado rejeitado para a campanha \".concat(((_campaign_campaigns6 = campaign.campaigns) === null || _campaign_campaigns6 === void 0 ? void 0 : (_campaign_campaigns_6 = _campaign_campaigns6[0]) === null || _campaign_campaigns_6 === void 0 ? void 0 : _campaign_campaigns_6.name) || 'Campanha Desconhecida'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.URGENT,\n                            due_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    }\n                    // Verificar se há agendamento para esta campanha\n                    const { data: existingSchedule, error: scheduleError } = await supabase.from('campaign_schedule').select('id').eq('campaign_id', campaign.campaign_id).eq('influencer_id', userId).limit(1);\n                    if (scheduleError) {\n                        console.error('Erro ao verificar agendamento existente:', scheduleError);\n                        continue;\n                    }\n                    // Se não houver agendamento, criar tarefa para agendar visita\n                    if (!existingSchedule || existingSchedule.length === 0) {\n                        var _campaign_campaigns_7, _campaign_campaigns7, _campaign_campaigns__restaurants_1, _campaign_campaigns__restaurants1, _campaign_campaigns_8, _campaign_campaigns8, _campaign_campaigns_9, _campaign_campaigns9;\n                        await supabase.from('tasks').insert({\n                            user_id: userId,\n                            campaign_id: campaign.campaign_id,\n                            title: \"Agendar visita para \".concat(((_campaign_campaigns7 = campaign.campaigns) === null || _campaign_campaigns7 === void 0 ? void 0 : (_campaign_campaigns_7 = _campaign_campaigns7[0]) === null || _campaign_campaigns_7 === void 0 ? void 0 : _campaign_campaigns_7.name) || 'Campanha Desconhecida'),\n                            description: \"Agendar visita ao restaurante \".concat(((_campaign_campaigns8 = campaign.campaigns) === null || _campaign_campaigns8 === void 0 ? void 0 : (_campaign_campaigns_8 = _campaign_campaigns8[0]) === null || _campaign_campaigns_8 === void 0 ? void 0 : (_campaign_campaigns__restaurants1 = _campaign_campaigns_8.restaurants) === null || _campaign_campaigns__restaurants1 === void 0 ? void 0 : (_campaign_campaigns__restaurants_1 = _campaign_campaigns__restaurants1[0]) === null || _campaign_campaigns__restaurants_1 === void 0 ? void 0 : _campaign_campaigns__restaurants_1.name) || 'Restaurante Desconhecido', \" para a campanha \").concat(((_campaign_campaigns9 = campaign.campaigns) === null || _campaign_campaigns9 === void 0 ? void 0 : (_campaign_campaigns_9 = _campaign_campaigns9[0]) === null || _campaign_campaigns_9 === void 0 ? void 0 : _campaign_campaigns_9.name) || 'Campanha Desconhecida'),\n                            task_type: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.SCHEDULE_VISIT,\n                            status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                            priority: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskPriority.MEDIUM,\n                            due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),\n                            created_at: new Date().toISOString(),\n                            updated_at: new Date().toISOString()\n                        });\n                    }\n                }\n                console.log('Tarefas baseadas em campanhas criadas com sucesso');\n            }\n        } catch (error) {\n            console.error('Erro ao criar tarefas baseadas em campanhas:', error);\n        }\n    };\n    const fetchData = async ()=>{\n        setLoading(true);\n        try {\n            // Se houver um userId, verificar se há dados baseados em campanhas\n            if (userId) {\n                await checkAndCreateTasksFromCampaigns();\n            }\n            await Promise.all([\n                fetchTasks(),\n                fetchSchedules()\n            ]);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchTasks = async ()=>{\n        try {\n            // Verificar se a tabela 'tasks' existe\n            let tableExists = true;\n            try {\n                const { error: tableCheckError } = await supabase.from('tasks').select('id').limit(1);\n                if (tableCheckError) {\n                    console.error('Erro ao verificar tabela tasks:', tableCheckError);\n                    tableExists = false;\n                }\n            } catch (error) {\n                console.error('Erro ao verificar tabela tasks:', error);\n                tableExists = false;\n            }\n            if (!tableExists) {\n                // Se a tabela não existir, usar um array vazio\n                setTasks([]);\n                updateActionItems([], schedules);\n                return;\n            }\n            // Usar o cliente Supabase diretamente\n            let query = supabase.from('tasks').select(\"\\n          id,\\n          user_id,\\n          campaign_id,\\n          title,\\n          description,\\n          task_type,\\n          status,\\n          priority,\\n          due_date,\\n          completed_at,\\n          created_at,\\n          updated_at\\n        \") // Select specific fields from tasks table only\n            ;\n            // Adicionar filtro de status\n            query = query.in('status', [\n                _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.PENDING,\n                _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.IN_PROGRESS,\n                _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.OVERDUE\n            ]);\n            // Adicionar filtro de usuário se necessário\n            if (userId) {\n                query = query.eq('user_id', userId);\n            }\n            // Executar a consulta\n            const { data, error } = await query.order('due_date', {\n                ascending: true\n            });\n            if (error) {\n                console.error('Erro na consulta de tarefas:', error);\n                setTasks([]);\n                updateActionItems([], schedules);\n                return;\n            }\n            console.log('Tarefas carregadas com sucesso:', (data === null || data === void 0 ? void 0 : data.length) || 0);\n            setTasks(data || []);\n            // Atualizar a lista combinada\n            updateActionItems(data || [], schedules);\n        } catch (error) {\n            console.error('Erro ao carregar tarefas:', error);\n            // Em caso de erro, usar um array vazio\n            setTasks([]);\n            updateActionItems([], schedules);\n        }\n    };\n    const fetchSchedules = async ()=>{\n        try {\n            // Verificar se a tabela 'schedules' existe\n            let tableExists = true;\n            try {\n                const { error: tableCheckError } = await supabase.from('campaign_schedule').select('id').limit(1);\n                if (tableCheckError) {\n                    console.error('Erro ao verificar tabela schedules:', tableCheckError);\n                    tableExists = false;\n                }\n            } catch (error) {\n                console.error('Erro ao verificar tabela schedules:', error);\n                tableExists = false;\n            }\n            if (!tableExists) {\n                // Se a tabela não existir, usar um array vazio\n                setSchedules([]);\n                updateActionItems(tasks, []);\n                return;\n            }\n            // Usar o cliente Supabase diretamente\n            let query = supabase.from('campaign_schedule').select(\"\\n          id,\\n          campaign_id,\\n          influencer_id,\\n          scheduled_date,\\n          status,\\n          notes,\\n          created_at,\\n          updated_at,\\n          completed_at,\\n          cancelled_at,\\n          cancellation_reason\\n        \") // Select specific fields from campaign_schedule table only\n            ;\n            // Adicionar filtro de usuário se necessário\n            if (userId) {\n                query = query.eq('influencer_id', userId);\n            }\n            // Executar a consulta\n            const { data, error } = await query.order('scheduled_date', {\n                ascending: true\n            });\n            if (error) {\n                console.error('Erro na consulta de agendamentos:', error);\n                setSchedules([]);\n                updateActionItems(tasks, []);\n                return;\n            }\n            console.log('Agendamentos carregados com sucesso:', (data === null || data === void 0 ? void 0 : data.length) || 0);\n            setSchedules(data || []);\n            // Atualizar a lista combinada\n            updateActionItems(tasks, data || []);\n        } catch (error) {\n            console.error('Erro ao carregar agendamentos:', error);\n            // Em caso de erro, usar um array vazio\n            setSchedules([]);\n            updateActionItems(tasks, []);\n        }\n    };\n    // Combinar tarefas e agendamentos em uma única lista\n    const updateActionItems = (tasksList, schedulesList)=>{\n        const today = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_8__.startOfToday)();\n        const nextMonth = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_9__.addDays)(today, 30);\n        // Converter tarefas para o formato de exibição\n        const taskItems = tasksList.map((task)=>{\n            // Determinar o tipo de ação com base no tipo de tarefa\n            let actionType = 'Tarefa';\n            let icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 430,\n                columnNumber: 18\n            }, this);\n            switch(task.task_type){\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION:\n                    actionType = 'Criar conteúdo';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_APPROVAL:\n                    actionType = 'Aprovar conteúdo';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_PUBLICATION:\n                    actionType = 'Publicar conteúdo';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.SCHEDULE_VISIT:\n                    actionType = 'Visitar restaurante';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.ADMINISTRATIVE:\n                    actionType = 'Tarefa administrativa';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.PAYMENT:\n                    actionType = 'Pagamento';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 18\n                    }, this);\n                    break;\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.OTHER:\n                    actionType = 'Outra tarefa';\n                    icon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 18\n                    }, this);\n                    break;\n            }\n            return {\n                id: task.id,\n                type: 'task',\n                title: task.title,\n                date: task.due_date || '',\n                restaurant: task.campaign_id ? \"Campanha ID: \".concat(task.campaign_id.substring(0, 8)) : 'Restaurante N/A',\n                actionType,\n                status: task.status,\n                originalData: task,\n                icon\n            };\n        });\n        // Converter agendamentos para o formato de exibição\n        const scheduleItems = schedulesList.filter((schedule)=>{\n            const scheduleDate = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(schedule.scheduled_date);\n            return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_15__.isAfter)(scheduleDate, today) && schedule.status !== _lib_services_scheduling__WEBPACK_IMPORTED_MODULE_7__.ScheduleStatus.CANCELLED;\n        }).map((schedule)=>({\n                id: schedule.id,\n                type: 'schedule',\n                title: schedule.campaign_id ? \"Visita Campanha ID: \".concat(schedule.campaign_id.substring(0, 8)) : \"Agendamento ID: \".concat(schedule.id.substring(0, 8)),\n                date: schedule.scheduled_date,\n                restaurant: schedule.campaign_id ? \"Campanha ID: \".concat(schedule.campaign_id.substring(0, 8)) : 'Restaurante N/A',\n                actionType: 'Visita',\n                status: schedule.status,\n                originalData: schedule,\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 mr-1\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 15\n                }, this)\n            }));\n        // Combinar e ordenar por data\n        const combined = [\n            ...taskItems,\n            ...scheduleItems\n        ].sort((a, b)=>{\n            if (!a.date) return 1;\n            if (!b.date) return -1;\n            return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(a.date).getTime() - (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(b.date).getTime();\n        }).slice(0, 5) // Mostrar apenas os 5 próximos itens\n        ;\n        setActionItems(combined);\n    };\n    const handleTaskComplete = async (taskId)=>{\n        try {\n            // Usar o cliente Supabase diretamente\n            const now = new Date().toISOString();\n            const { error } = await supabase.from('tasks').update({\n                status: _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskStatus.COMPLETED,\n                completed_at: now,\n                updated_at: now\n            }).eq('id', taskId);\n            if (error) {\n                throw error;\n            }\n            // Atualizar lista de tarefas\n            fetchData();\n        } catch (error) {\n            console.error('Erro ao concluir tarefa:', error);\n        }\n    };\n    // Formatar data\n    const formatDate = (dateString)=>{\n        if (!dateString) return 'Sem prazo';\n        return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)((0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(dateString), \"dd/MM\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR\n        });\n    };\n    // Formatar data e hora\n    const formatDateTime = (dateString)=>{\n        const date = (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_14__.parseISO)(dateString);\n        return (0,_barrel_optimize_names_addDays_format_isAfter_parseISO_startOfToday_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(date, \"dd/MM 'às' HH:mm\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR\n        });\n    };\n    // Renderizar ícone com base no tipo de ação\n    const renderActionIcon = (item)=>{\n        if (item.type === 'task') {\n            switch(item.originalData.task_type){\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_CREATION:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-blue-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_APPROVAL:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.CONTENT_PUBLICATION:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-indigo-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.SCHEDULE_VISIT:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-purple-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.PAYMENT:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 18\n                    }, this);\n                case _lib_services_tasks__WEBPACK_IMPORTED_MODULE_6__.TaskType.ADMINISTRATIVE:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-orange-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 18\n                    }, this);\n                default:\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4 mr-1 text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 18\n                    }, this);\n            }\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4 mr-1 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 564,\n                columnNumber: 14\n            }, this);\n        }\n    };\n    // Se for modo compacto, renderizar apenas a lista de itens sem o card completo\n    if (compact) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    className: \"h-5 w-5 animate-spin text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 574,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 573,\n                columnNumber: 11\n            }, this) : actionItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-3 text-sm text-gray-500\",\n                children: \"Sem a\\xe7\\xf5es pendentes\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 577,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"divide-y divide-gray-100\",\n                children: actionItems.slice(0, 3).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2.5 px-5 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm\",\n                                        children: [\n                                            renderActionIcon(item),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-medium text-gray-700\",\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-1 text-xs text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate\",\n                                                children: [\n                                                    item.type === 'task' ? formatDate(item.date) : formatDateTime(item.date),\n                                                    item.restaurant && \" • \".concat(item.restaurant)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 17\n                            }, this),\n                            item.type === 'task' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                className: \"h-7 px-2 text-xs ml-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50\",\n                                onClick: ()=>handleTaskComplete(item.id),\n                                children: \"Concluir\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, \"\".concat(item.type, \"-\").concat(item.id), true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 15\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 581,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n            lineNumber: 571,\n            columnNumber: 7\n        }, this);\n    }\n    // Versão padrão com card completo\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                className: \"bg-[#f5f5f5] pb-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-lg font-medium\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-5 w-5 inline-block mr-2 text-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, this),\n                        \"Pr\\xf3ximas A\\xe7\\xf5es\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-4\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 630,\n                    columnNumber: 11\n                }, this) : actionItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EmptyState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    title: \"Sem a\\xe7\\xf5es pendentes\",\n                    description: \"Voc\\xea n\\xe3o tem tarefas ou agendamentos pr\\xf3ximos.\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-12 w-12\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 19\n                    }, void 0),\n                    actionLabel: \"Atualizar\",\n                    onAction: fetchData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 634,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: actionItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-3 border rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row sm:items-center mt-1 text-sm text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mr-3\",\n                                                    children: [\n                                                        renderActionIcon(item),\n                                                        item.actionType\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        item.type === 'task' ? formatDate(item.date) : formatDateTime(item.date)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.restaurant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Camera_CheckCircle2_Clock_Loader2_MapPin_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.restaurant\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 17\n                                }, this),\n                                item.type === 'task' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    size: \"sm\",\n                                    variant: \"outline\",\n                                    onClick: ()=>handleTaskComplete(item.id),\n                                    children: \"Concluir\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, \"\".concat(item.type, \"-\").concat(item.id), true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                    lineNumber: 642,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n                lineNumber: 628,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/dashboard/TasksAndScheduleCard.tsx\",\n        lineNumber: 620,\n        columnNumber: 5\n    }, this);\n}\n_s(TasksAndScheduleCard, \"JVPue+tWxQn/VGvfk7eirdLBxvM=\");\n_c = TasksAndScheduleCard;\nvar _c;\n$RefreshReg$(_c, \"TasksAndScheduleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TasksAndScheduleCard.tsx\n"));

/***/ })

});