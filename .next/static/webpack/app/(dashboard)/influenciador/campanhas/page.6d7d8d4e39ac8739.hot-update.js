"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx":
/*!*************************************************************!*\
  !*** ./src/components/premium/RankingHighlightFallback.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RankingHighlightFallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowDown,FaArrowUp,FaBookmark,FaChartLine,FaComment,FaHeart,FaMinus,FaTrophy!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _RankingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RankingNotifications */ \"(app-pages-browser)/./src/components/premium/RankingNotifications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RankingHighlightFallback(param) {\n    let { campaignId, userId, userRole, className = '' } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rankingData, setRankingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [competitors, setCompetitors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUsingMockData, setIsUsingMockData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RankingHighlightFallback.useEffect\": ()=>{\n            async function fetchRankingData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('RankingHighlightFallback - Iniciando busca de dados para:', {\n                        campaignId,\n                        userId,\n                        userRole\n                    });\n                    // Buscar dados diretamente das tabelas\n                    if (userRole === 'influencer') {\n                        // Para influenciadores, buscar dados básicos\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              status,\\n              created_at\\n            \").eq('campaign_id', campaignId);\n                        if (ciError) throw ciError;\n                        // Buscar dados do influenciador atual\n                        const userInfluencer = campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        if (!userInfluencer) {\n                            // Se o usuário não está na campanha, retornar dados vazios em vez de erro\n                            console.log('Usuário não encontrado na campanha, retornando dados vazios');\n                            return {\n                                userRank: null,\n                                userPoints: 0,\n                                totalParticipants: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 0,\n                                topInfluencers: [],\n                                userPreviousRank: null,\n                                userRankChange: 0\n                            };\n                        }\n                        // Buscar perfis dos influenciadores para obter nomes e usernames\n                        const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                        if (profilesError) throw profilesError;\n                        // Criar dados para o influenciador atual\n                        const userProfile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        const rankingData = {\n                            influencer_id: userId,\n                            rank: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            previous_rank: null,\n                            total_influencers: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            total_likes: 0,\n                            total_comments: 0,\n                            total_saves: 0,\n                            total_points: 0,\n                            engagement_rate: \"0.0\"\n                        };\n                        setRankingData(rankingData);\n                        // Criar lista de competidores com dados reais\n                        const competitors = campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                var _profile_profile_data;\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || 'username';\n                                return {\n                                    influencer_id: ci.influencer_id,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                    username: username,\n                                    rank: index + 1,\n                                    total_likes: 0,\n                                    total_comments: 0,\n                                    total_saves: 0,\n                                    total_points: 0,\n                                    engagement_rate: 0.0\n                                };\n                            }\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                        // Ordenar por ranking\n                        competitors.sort({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>a.rank - b.rank\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        // Limitar a 5 competidores\n                        const topCompetitors = competitors.slice(0, 5);\n                        // Adicionar o influenciador atual se não estiver nos top 5\n                        const userIncluded = topCompetitors.some({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\": (c)=>c.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\"]);\n                        if (!userIncluded) {\n                            var _userProfile_profile_data;\n                            topCompetitors.push({\n                                influencer_id: userId,\n                                influencer_name: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.full_name) || 'Você',\n                                username: (userProfile === null || userProfile === void 0 ? void 0 : (_userProfile_profile_data = userProfile.profile_data) === null || _userProfile_profile_data === void 0 ? void 0 : _userProfile_profile_data.instagram_username) || 'seu_username',\n                                rank: rankingData.rank,\n                                total_likes: rankingData.total_likes,\n                                total_comments: rankingData.total_comments,\n                                total_saves: rankingData.total_saves,\n                                total_points: rankingData.total_points,\n                                engagement_rate: parseFloat(rankingData.engagement_rate)\n                            });\n                        }\n                        setCompetitors(topCompetitors);\n                    } else {\n                        // Para restaurantes, primeiro buscar todos os influenciadores aceitos na campanha\n                        console.log('Buscando influenciadores aceitos para a campanha:', campaignId);\n                        const { data: acceptedInfluencers, error: aiError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              status\\n            \").eq('campaign_id', campaignId).eq('status', 'accepted');\n                        if (aiError) {\n                            console.error('Erro ao buscar influenciadores aceitos:', aiError);\n                            throw aiError;\n                        }\n                        console.log('Influenciadores aceitos encontrados:', acceptedInfluencers);\n                        // Se não houver influenciadores aceitos, mostrar mensagem apropriada\n                        if (!acceptedInfluencers || acceptedInfluencers.length === 0) {\n                            console.log('Nenhum influenciador aceito encontrado para esta campanha');\n                            // Vamos tentar buscar novamente sem filtrar por status para debug\n                            const { data: allInfluencers, error: allError } = await supabase.from('campaign_influencers').select(\"\\n                id,\\n                influencer_id,\\n                status\\n              \").eq('campaign_id', campaignId);\n                            console.log('Todos os influenciadores da campanha (debug):', allInfluencers);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Se existem influenciadores mas nenhum com status 'accepted', vamos usar todos\n                                console.log('Usando todos os influenciadores disponíveis como fallback');\n                                // Continuar com todos os influenciadores disponíveis\n                                setError(null);\n                            } else {\n                                setError('Nenhum influenciador aceito encontrado para esta campanha');\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        // Buscar dados de métricas para os influenciadores aceitos\n                        console.log('Buscando métricas para os influenciadores aceitos');\n                        let influencerIds = acceptedInfluencers ? acceptedInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]) : [];\n                        // Se não temos influenciadores aceitos, buscar todos os influenciadores da campanha\n                        if (influencerIds.length === 0) {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                influencerIds = allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                console.log('Usando todos os influenciadores disponíveis:', influencerIds);\n                            }\n                        }\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              status,\\n              created_at\\n            \").eq('campaign_id', campaignId).in('influencer_id', influencerIds).order('created_at', {\n                            ascending: true\n                        });\n                        if (ciError) {\n                            console.error('Erro ao buscar métricas dos influenciadores:', ciError);\n                            throw ciError;\n                        }\n                        console.log('Dados de métricas dos influenciadores:', campaignInfluencers);\n                        if (!campaignInfluencers || campaignInfluencers.length === 0) {\n                            // Se não houver dados reais, criar dados de exemplo\n                            console.warn('Nenhum dado real de métricas encontrado para os influenciadores. Usando dados de exemplo.');\n                            setIsUsingMockData(true);\n                            // Buscar perfis dos influenciadores para usar nomes reais\n                            // Usar todos os influenciadores disponíveis, não apenas os aceitos\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            const influencerIds = (allInfluencers === null || allInfluencers === void 0 ? void 0 : allInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"])) || [];\n                            console.log('Usando todos os influenciadores disponíveis para dados de exemplo:', influencerIds);\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', influencerIds);\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis dos influenciadores:', profilesError);\n                            // Continuar mesmo com erro\n                            }\n                            console.log('Perfis dos influenciadores para dados de exemplo:', profiles);\n                            // Criar dados de exemplo com nomes reais, se disponíveis\n                            const mockTopInfluencers = [];\n                            // Usar os IDs de influenciadores já obtidos anteriormente\n                            const maxInfluencers = Math.min(5, influencerIds.length || 1);\n                            for(let i = 0; i < maxInfluencers; i++){\n                                var _profile_profile_data;\n                                const influencerId = influencerIds[i] || 'mock-id';\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                mockTopInfluencers.push({\n                                    influencer_id: influencerId,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                    username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || \"influencer\".concat(i + 1),\n                                    rank: i + 1,\n                                    total_likes: Math.floor(Math.random() * 1000) + 100,\n                                    total_comments: Math.floor(Math.random() * 200) + 20,\n                                    total_saves: Math.floor(Math.random() * 100) + 10,\n                                    total_points: Math.floor(Math.random() * 2000) + 200,\n                                    engagement_rate: Math.random() * 5 + 1,\n                                    is_mock_data: true\n                                });\n                            }\n                            setCompetitors(mockTopInfluencers);\n                        } else {\n                            // Buscar perfis dos influenciadores para obter nomes e usernames\n                            console.log('Buscando perfis para os influenciadores:', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis:', profilesError);\n                                throw profilesError;\n                            }\n                            console.log('Perfis encontrados:', profiles);\n                            // Buscar posts dos influenciadores para obter métricas mais recentes\n                            console.log('Buscando posts dos influenciadores');\n                            const { data: posts, error: postsError } = await supabase.from('posts').select(\"\\n                id,\\n                campaign_influencer_id,\\n                likes_count,\\n                comments_count,\\n                saves_count,\\n                engagement_rate\\n              \").in('campaign_influencer_id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (postsError) {\n                                console.error('Erro ao buscar posts:', postsError);\n                            // Não lançar erro, apenas registrar - usaremos os dados do campaign_influencers\n                            }\n                            console.log('Posts encontrados:', posts);\n                            // Criar lista de competidores com dados reais\n                            const competitors = campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                    var _profile_profile_data;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                    console.log(\"Perfil para influenciador \".concat(ci.influencer_id, \":\"), profile);\n                                    // Buscar posts deste influenciador para esta campanha\n                                    const influencerPosts = (posts === null || posts === void 0 ? void 0 : posts.filter({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.campaign_influencer_id === ci.id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"])) || [];\n                                    console.log(\"Posts para influenciador \".concat(ci.influencer_id, \":\"), influencerPosts);\n                                    // Calcular totais de métricas dos posts\n                                    const totalLikesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\": (sum, post)=>sum + (post.likes_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\"], 0);\n                                    const totalCommentsFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\": (sum, post)=>sum + (post.comments_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\"], 0);\n                                    const totalSavesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\": (sum, post)=>sum + (post.saves_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\"], 0);\n                                    // Usar dados dos posts se disponíveis, caso contrário usar dados do campaign_influencers\n                                    const totalLikes = totalLikesFromPosts > 0 ? totalLikesFromPosts : ci.previous_total_likes || 0;\n                                    const totalComments = totalCommentsFromPosts > 0 ? totalCommentsFromPosts : ci.previous_total_comments || 0;\n                                    const totalSaves = totalSavesFromPosts > 0 ? totalSavesFromPosts : ci.previous_total_saves || 0;\n                                    // Calcular pontos com base nas métricas (fórmula simplificada)\n                                    const calculatedPoints = totalLikes + totalComments * 2 + totalSaves * 3;\n                                    // Usar índice como ranking temporário\n                                    const rank = index + 1;\n                                    // Obter username do perfil ou gerar um padrão\n                                    const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || ((profile === null || profile === void 0 ? void 0 : profile.profile_data) && 'instagram_username' in profile.profile_data ? profile.profile_data.instagram_username : \"influencer\".concat(index + 1));\n                                    return {\n                                        influencer_id: ci.influencer_id,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                        username: username,\n                                        rank: rank,\n                                        total_likes: totalLikes,\n                                        total_comments: totalComments,\n                                        total_saves: totalSaves,\n                                        total_points: calculatedPoints || 0,\n                                        engagement_rate: parseFloat('0'),\n                                        has_real_data: influencerPosts.length > 0\n                                    };\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                            console.log('Competidores criados com dados reais:', competitors);\n                            // Ordenar por pontos (decrescente) e depois por ranking (crescente)\n                            competitors.sort({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>{\n                                    if (b.total_points !== a.total_points) {\n                                        return b.total_points - a.total_points;\n                                    }\n                                    return a.rank - b.rank;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            // Atualizar ranks com base na ordenação\n                            competitors.forEach({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (comp, idx)=>{\n                                    comp.rank = idx + 1;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            console.log('Competidores ordenados:', competitors);\n                            // Limitar a 5 competidores\n                            setCompetitors(competitors.slice(0, 5));\n                        }\n                    }\n                } catch (err) {\n                    console.error('Erro ao buscar dados de ranking:', err);\n                    setError('Não foi possível carregar os dados de ranking');\n                    // Em caso de erro, criar dados de exemplo\n                    if (userRole === 'restaurant') {\n                        console.warn('Erro ao buscar dados reais. Usando dados de exemplo como fallback.');\n                        setIsUsingMockData(true);\n                        // Tentar buscar todos os influenciadores para usar nomes reais\n                        try {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select(\"id, influencer_id, status\").eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Buscar perfis de todos os influenciadores\n                                const { data: profiles } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                                // Criar dados de exemplo com nomes reais\n                                const mockTopInfluencers = [];\n                                for(let i = 0; i < Math.min(5, allInfluencers.length); i++){\n                                    var _profile_profile_data1;\n                                    const influencerId = allInfluencers[i].influencer_id;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                    mockTopInfluencers.push({\n                                        influencer_id: influencerId,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                        username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data1 = profile.profile_data) === null || _profile_profile_data1 === void 0 ? void 0 : _profile_profile_data1.instagram_username) || \"influencer\".concat(i + 1),\n                                        rank: i + 1,\n                                        total_likes: Math.floor(Math.random() * 1000) + 100,\n                                        total_comments: Math.floor(Math.random() * 200) + 20,\n                                        total_saves: Math.floor(Math.random() * 100) + 10,\n                                        total_points: Math.floor(Math.random() * 2000) + 200,\n                                        engagement_rate: Math.random() * 5 + 1,\n                                        is_mock_data: true\n                                    });\n                                }\n                                setCompetitors(mockTopInfluencers);\n                                return;\n                            }\n                        } catch (fallbackErr) {\n                            console.error('Erro ao tentar buscar dados para fallback:', fallbackErr);\n                        }\n                        // Se não conseguir dados reais, usar dados completamente fictícios\n                        const mockTopInfluencers = [];\n                        for(let i = 0; i < 5; i++){\n                            mockTopInfluencers.push({\n                                influencer_id: \"mock-id-\".concat(i),\n                                influencer_name: \"Influencer \".concat(i + 1),\n                                username: \"influencer\".concat(i + 1),\n                                rank: i + 1,\n                                total_likes: Math.floor(Math.random() * 1000) + 100,\n                                total_comments: Math.floor(Math.random() * 200) + 20,\n                                total_saves: Math.floor(Math.random() * 100) + 10,\n                                total_points: Math.floor(Math.random() * 2000) + 200,\n                                engagement_rate: Math.random() * 5 + 1,\n                                is_mock_data: true\n                            });\n                        }\n                        setCompetitors(mockTopInfluencers);\n                    }\n                } finally{\n                    setLoading(false);\n                }\n            }\n            if (campaignId && userId) {\n                fetchRankingData();\n            }\n        }\n    }[\"RankingHighlightFallback.useEffect\"], [\n        campaignId,\n        userId,\n        userRole,\n        supabase\n    ]);\n    // Renderização para estado de carregamento\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-24 bg-gray-200 rounded-lg mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gray-200 rounded-full mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 454,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para estado de erro\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg \".concat(className),\n            children: [\n                error,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium\",\n                        children: \"Tentar novamente\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 475,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para influenciadores\n    if (userRole === 'influencer' && rankingData) {\n        const { rank, total_influencers, previous_rank, engagement_rate, total_points, total_saves } = rankingData;\n        // Calcular mudança de posição\n        const rankChange = previous_rank ? previous_rank - rank : 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                        className: \"mr-2 text-yellow-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Seu Ranking na Campanha\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm bg-white/20 px-2 py-1 rounded\",\n                                children: [\n                                    total_influencers,\n                                    \" participantes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-blue-50 border-b border-blue-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl \".concat(rank === 1 ? 'bg-yellow-500' : rank === 2 ? 'bg-gray-400' : rank === 3 ? 'bg-amber-700' : 'bg-blue-600'),\n                                        children: [\n                                            rank,\n                                            \"\\xba\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Sua posi\\xe7\\xe3o atual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-900\",\n                                                children: rank === 1 ? '🏆 Primeiro Lugar!' : rank === 2 ? '🥈 Segundo Lugar!' : rank === 3 ? '🥉 Terceiro Lugar!' : \"\".concat(rank, \"\\xba Lugar\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mt-1\",\n                                                children: rankChange > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Subiu \",\n                                                        rankChange,\n                                                        \" \",\n                                                        rankChange === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this) : rankChange < 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Desceu \",\n                                                        Math.abs(rankChange),\n                                                        \" \",\n                                                        Math.abs(rankChange) === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Manteve a posi\\xe7\\xe3o\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"Taxa de Engajamento\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-900\",\n                                        children: [\n                                            engagement_rate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end mt-1 space-x-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                                        className: \"mr-1 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_points,\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de pontos acumulados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_saves\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de salvamentos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                            children: \"Seus Competidores Diretos\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: competitors.map((competitor)=>{\n                                const isCurrentUser = competitor.influencer_id === userId;\n                                const isAhead = competitor.rank < rankingData.rank;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(isCurrentUser ? 'bg-blue-50 border border-blue-200' : isAhead ? 'bg-red-50' : 'bg-green-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold \".concat(competitor.rank === 1 ? 'bg-yellow-500' : competitor.rank === 2 ? 'bg-gray-400' : competitor.rank === 3 ? 'bg-amber-700' : 'bg-gray-600'),\n                                                    children: competitor.rank\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium \".concat(isCurrentUser ? 'text-blue-700' : 'text-gray-800'),\n                                                            children: [\n                                                                competitor.influencer_name,\n                                                                \" \",\n                                                                isCurrentUser && '(Você)'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"@\",\n                                                                competitor.username\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-800\",\n                                                    children: [\n                                                        competitor.engagement_rate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-red-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_likes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-blue-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_comments\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_saves\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, competitor.influencer_id, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-yellow-800 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dicas para Melhorar seu Ranking\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mt-2 text-xs text-yellow-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Responda aos coment\\xe1rios para aumentar o engajamento geral.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Crie conte\\xfado que incentive o compartilhamento e salvamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500\",\n                    children: [\n                        \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                        new Date().toLocaleDateString('pt-BR', {\n                            day: '2-digit',\n                            month: '2-digit',\n                            year: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 666,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para restaurantes - Estilo Champions League\n    if (userRole === 'restaurant' && competitors.length > 0) {\n        console.log('Renderizando para restaurante com competidores:', competitors);\n        // Calcular pontuação total para cada influenciador\n        const totalPointsMax = Math.max(...competitors.map((inf)=>inf.total_points));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-900 to-indigo-900 p-5 relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-[url('https://www.uefa.com/contentassets/c9b1b12d4c074c3ca3f03a7fdb018a2f/ucl-2021-24-starball-on-pitch-min.jpg')] bg-cover bg-center\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold flex items-center text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                className: \"mr-3 text-yellow-300 text-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block\",\n                                                        children: \"Champions da Campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-normal text-blue-200\",\n                                                        children: \"Ranking de Influenciadores\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: competitors.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200\",\n                                                    children: \"influenciadores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 73\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RankingNotifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            campaignId: campaignId,\n                                            userId: userId,\n                                            userRole: userRole\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 9\n                }, this),\n                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-100 text-yellow-800 p-3 text-sm border-b border-yellow-200 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2 text-yellow-600\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Exibindo dados de exemplo. Os influenciadores s\\xe3o reais, mas as m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 713,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-hidden rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gray-100 to-blue-50 p-3 grid grid-cols-12 text-xs font-semibold border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 text-center text-gray-600\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-3 text-gray-600\",\n                                            children: \"Influenciador\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-red-500\",\n                                            children: \"Curtidas\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-blue-500\",\n                                            children: \"Coment\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-green-500\",\n                                            children: \"Salvamentos\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-700 text-white py-1.5 px-3 rounded-lg shadow-sm inline-block font-bold\",\n                                                children: \"PONTOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y divide-gray-100\",\n                                    children: competitors.map((influencer, index)=>{\n                                        // Determinar cores e estilos com base na posição\n                                        const isTop = index < 3;\n                                        const rankStyles = [\n                                            'bg-gradient-to-r from-yellow-500 to-yellow-400 text-white',\n                                            'bg-gradient-to-r from-gray-400 to-gray-300 text-white',\n                                            'bg-gradient-to-r from-amber-700 to-amber-600 text-white',\n                                            'bg-blue-50 text-blue-800' // Demais posições\n                                        ];\n                                        // Calcular porcentagem para a barra de progresso\n                                        const progressPercent = Math.round(influencer.total_points / totalPointsMax * 100);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative p-3 grid grid-cols-12 items-center text-sm \".concat(index < 3 ? 'bg-blue-50/50' : 'hover:bg-gray-50', \" \").concat(index === 0 ? 'border-l-4 border-yellow-400' : index === 1 ? 'border-l-4 border-gray-400' : index === 2 ? 'border-l-4 border-amber-700' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-0 top-0 h-full z-0 \".concat(index === 0 ? 'bg-yellow-50' : index === 1 ? 'bg-gray-50' : index === 2 ? 'bg-amber-50' : 'bg-blue-50'),\n                                                    style: {\n                                                        width: \"\".concat(progressPercent, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-1 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full flex items-center justify-center font-bold \".concat(rankStyles[index < 3 ? index : 3]),\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: influencer.influencer_name || \"Influencer \".concat(index + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"@\",\n                                                                    influencer.username || \"influencer\".concat(index + 1)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-red-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_likes / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-blue-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_comments\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_comments / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-green-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 808,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_saves\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_saves / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(index < 3 ? 'bg-gradient-to-r from-blue-600 to-blue-700' : 'bg-blue-600', \" rounded-lg py-2 px-4 inline-block shadow-md border \").concat(index === 0 ? 'border-yellow-300' : index === 1 ? 'border-gray-300' : index === 2 ? 'border-amber-300' : 'border-blue-500'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-white text-xl\",\n                                                                children: influencer.total_points\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-100 flex items-center justify-center mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChartLine, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    influencer.engagement_rate.toFixed(1),\n                                                                    \"% taxa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FaUser, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Participante\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, influencer.influencer_id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-5 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-5 w-5 mr-2 text-blue-300\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"An\\xe1lise de Desempenho da Campanha\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Engajamento Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        competitors.reduce((sum, inf)=>sum + inf.total_points, 0),\n                                                        \" pts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Salvamentos\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: competitors.reduce((sum, inf)=>sum + inf.total_saves, 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Taxa M\\xe9dia\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        (competitors.reduce((sum, inf)=>sum + inf.engagement_rate, 0) / competitors.length).toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-100 mb-4\",\n                                    children: [\n                                        \"Os salvamentos representam \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-white\",\n                                            children: [\n                                                Math.round(competitors.reduce((sum, inf)=>sum + inf.total_saves, 0) / competitors.reduce((sum, inf)=>sum + inf.total_likes + inf.total_comments + inf.total_saves, 0) * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 42\n                                        }, this),\n                                        \" do engajamento total desta campanha, demonstrando alto interesse do p\\xfablico em guardar o conte\\xfado para refer\\xeancia futura.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-3 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"text-sm font-semibold mb-2 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1 text-blue-300\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 875,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"F\\xf3rmula de C\\xe1lculo de Pontos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                            className: \"text-red-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 881,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Curtidas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"1 ponto cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                            className: \"text-blue-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Coment\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"2 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                            className: \"text-green-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Salvamentos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"3 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                            className: \"text-yellow-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"Soma ponderada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 842,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                                new Date().toLocaleDateString('pt-BR', {\n                                    day: '2-digit',\n                                    month: '2-digit',\n                                    year: 'numeric',\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 907,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                    className: \"text-yellow-500 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Champions League Ranking\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 906,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 686,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 \".concat(className),\n        children: isUsingMockData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 text-yellow-800 p-3 text-sm rounded-lg mb-4 inline-flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-5 w-5 mr-2 text-yellow-600\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 931,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Exibindo dados de exemplo. As m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 934,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                lineNumber: 930,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 929,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: \"Nenhum dado de ranking dispon\\xedvel para esta campanha.\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 938,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n        lineNumber: 927,\n        columnNumber: 5\n    }, this);\n}\n_s(RankingHighlightFallback, \"WGLfjIgcazs9KbS3S754fmFbURI=\");\n_c = RankingHighlightFallback;\nvar _c;\n$RefreshReg$(_c, \"RankingHighlightFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx\n"));

/***/ })

});