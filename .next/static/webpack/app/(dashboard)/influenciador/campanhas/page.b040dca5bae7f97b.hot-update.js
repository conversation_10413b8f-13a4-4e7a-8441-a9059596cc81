"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/services/campaignService.ts":
/*!*****************************************!*\
  !*** ./src/services/campaignService.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   campaignService: () => (/* binding */ campaignService)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Real campaign service that queries the Supabase database\nconst campaignService = {\n    // Fetch campaigns for an influencer\n    async getInfluencerCampaigns (influencerId) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            // Query campaign_participants table to get campaigns the influencer is participating in\n            const { data: participantsData, error: participantsError } = await supabase.from('campaign_participants').select(\"\\n          id, \\n          status, \\n          campaigns(\\n            id, \\n            name, \\n            description, \\n            start_date, \\n            end_date, \\n            status, \\n            restaurant_id, \\n            restaurant_profiles(id, business_name)\\n          )\\n        \").eq('profile_id', influencerId);\n            if (participantsError) {\n                console.error('Error fetching campaign participants:', participantsError);\n                return [];\n            }\n            if (!participantsData || participantsData.length === 0) {\n                return [];\n            }\n            // Transform the data to match the expected format\n            const campaignInfluencers = await Promise.all(participantsData.map(async (participant)=>{\n                // Get campaign details\n                const campaignData = participant.campaigns;\n                // Handle case where campaign data might be an array\n                const campaign = Array.isArray(campaignData) ? campaignData[0] : campaignData;\n                if (!campaign) {\n                    return null;\n                }\n                // Get restaurant details\n                const restaurantData = campaign.restaurant_profiles;\n                const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n                // Get total points for this campaign participation\n                const { data: pointsData, error: pointsError } = await supabase.from('points_history').select('points').eq('campaign_participant_id', participant.id);\n                let totalPoints = 0;\n                if (!pointsError && pointsData) {\n                    totalPoints = pointsData.reduce((sum, item)=>sum + (item.points || 0), 0);\n                }\n                // Get video status\n                let videoStatus;\n                const { data: videoData, error: videoError } = await supabase.from('campaign_videos').select('status').eq('campaign_participant_id', participant.id).order('created_at', {\n                    ascending: false\n                }).limit(1).single();\n                if (!videoError && videoData) {\n                    videoStatus = videoData.status;\n                }\n                // Get rank\n                let rank;\n                // This would need to be implemented based on your ranking logic\n                // For example, you might have a separate table for rankings or calculate it on the fly\n                return {\n                    id: participant.id,\n                    campaign_id: campaign.id,\n                    influencer_id: influencerId,\n                    status: participant.status,\n                    total_points: totalPoints,\n                    video_status: videoStatus,\n                    rank,\n                    campaigns: {\n                        id: campaign.id,\n                        name: campaign.name,\n                        description: campaign.description,\n                        start_date: campaign.start_date,\n                        end_date: campaign.end_date,\n                        status: campaign.status,\n                        restaurant_id: campaign.restaurant_id,\n                        restaurants: restaurant ? {\n                            id: restaurant.id,\n                            name: restaurant.business_name\n                        } : null\n                    }\n                };\n            }));\n            // Filter out any null values\n            return campaignInfluencers.filter(Boolean);\n        } catch (error) {\n            console.error('Error in getInfluencerCampaigns:', error);\n            return [];\n        }\n    },\n    // Fetch available campaigns\n    async getAvailableCampaigns (filters) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            console.log('Fetching available campaigns with filters:', filters);\n            let query = supabase.from('campaigns').select(\"\\n          id,\\n          name,\\n          description,\\n          start_date,\\n          end_date,\\n          status,\\n          restaurant_id,\\n          restaurant_profiles(id, business_name, city, state),\\n          requirements\\n        \");\n            // Remove the status filter to see all campaigns\n            // .eq('status', 'active');\n            // Apply filters if provided\n            if (filters === null || filters === void 0 ? void 0 : filters.city) {\n                query = query.eq('city', filters.city);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.state) {\n                query = query.eq('state', filters.state);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching available campaigns:', error);\n                return [];\n            }\n            console.log('Raw campaigns data from database:', data);\n            if (!data || data.length === 0) {\n                console.log('No campaigns found with the current filters');\n                return [];\n            }\n            // Transform the data to match the expected format\n            const campaigns = data.map((campaign)=>{\n                const restaurantData = campaign.restaurant_profiles;\n                const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n                // Extract hashtags and mentions from requirements\n                const requirements = campaign.requirements || {};\n                const hashtags = requirements.hashtags || [];\n                const mentions = requirements.mentions || [];\n                return {\n                    id: campaign.id,\n                    name: campaign.name,\n                    description: campaign.description,\n                    start_date: campaign.start_date,\n                    end_date: campaign.end_date,\n                    status: campaign.status,\n                    restaurant_id: campaign.restaurant_id,\n                    restaurants: restaurant ? {\n                        id: restaurant.id,\n                        name: restaurant.business_name,\n                        city: restaurant.city,\n                        state: restaurant.state\n                    } : null,\n                    hashtags,\n                    mentions,\n                    city: restaurant === null || restaurant === void 0 ? void 0 : restaurant.city,\n                    state: restaurant === null || restaurant === void 0 ? void 0 : restaurant.state\n                };\n            });\n            console.log('Transformed campaigns:', campaigns);\n            return campaigns;\n        } catch (error) {\n            console.error('Error in getAvailableCampaigns:', error);\n            return [];\n        }\n    },\n    // Apply for a campaign\n    async applyForCampaign (influencerId, campaignId) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            console.log(\"Attempting to apply for campaign: \".concat(campaignId, \" by influencer: \").concat(influencerId));\n            // Get the authenticated user's ID\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                return {\n                    success: false,\n                    message: 'Usuário não autenticado. Por favor, faça login e tente novamente.'\n                };\n            }\n            console.log(\"InfluencerId: \".concat(influencerId));\n            console.log(\"Auth.uid(): \".concat(user.id));\n            // Ensure the influencerId matches the authenticated user's ID\n            if (influencerId !== user.id) {\n                return {\n                    success: false,\n                    message: 'Erro de autenticação. Por favor, faça login novamente e tente mais uma vez.'\n                };\n            }\n            // Check if the influencer is already participating in this campaign\n            const { data: existingParticipation, error: checkError } = await supabase.from('campaign_influencers').select('id, status').eq('influencer_id', user.id).eq('campaign_id', campaignId).single();\n            if (checkError && checkError.code !== 'PGRST116') {\n                console.error('Error checking existing participation:', checkError);\n                return {\n                    success: false,\n                    message: 'Erro ao verificar participação existente. Tente novamente.'\n                };\n            }\n            if (existingParticipation) {\n                return {\n                    success: false,\n                    message: 'Você já se candidatou a esta campanha.'\n                };\n            }\n            // Get the campaign details first to ensure it exists\n            const { data: campaignData, error: campaignError } = await supabase.from('campaigns').select(\"\\n          id,\\n          name,\\n          description,\\n          start_date,\\n          end_date,\\n          status,\\n          restaurant_id,\\n          restaurant_profiles(id, business_name)\\n        \").eq('id', campaignId).single();\n            if (campaignError) {\n                console.error('Error fetching campaign details:', campaignError);\n                return {\n                    success: false,\n                    message: 'Erro ao buscar detalhes da campanha. Tente novamente.'\n                };\n            }\n            // Create a new participation record\n            const { data: newParticipation, error: insertError } = await supabase.from('campaign_influencers').insert({\n                influencer_id: user.id,\n                campaign_id: campaignId,\n                status: 'invited'\n            }).select('id').single();\n            if (insertError) {\n                console.error('Error creating participation:', insertError);\n                // Verificar se é um erro de foreign key\n                if (insertError.code === '23503') {\n                    return {\n                        success: false,\n                        message: 'Perfil de influenciador não encontrado. Por favor, complete seu perfil antes de se candidatar a campanhas.'\n                    };\n                }\n                return {\n                    success: false,\n                    message: \"Erro ao se candidatar para a campanha: \".concat(insertError.message, \". Tente novamente.\")\n                };\n            }\n            console.log('Successfully applied to campaign:', newParticipation);\n            const restaurantData = campaignData.restaurant_profiles;\n            const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n            // Create a campaign influencer object to return\n            const campaignInfluencer = {\n                id: newParticipation.id,\n                campaign_id: campaignId,\n                influencer_id: user.id,\n                status: 'invited',\n                total_points: 0,\n                campaigns: {\n                    id: campaignData.id,\n                    name: campaignData.name,\n                    description: campaignData.description,\n                    start_date: campaignData.start_date,\n                    end_date: campaignData.end_date,\n                    status: campaignData.status,\n                    restaurant_id: campaignData.restaurant_id,\n                    restaurants: restaurant ? {\n                        id: restaurant.id,\n                        name: restaurant.business_name\n                    } : null\n                }\n            };\n            return {\n                success: true,\n                message: 'Candidatura enviada com sucesso! Aguarde a aprovação do restaurante.',\n                campaignInfluencer\n            };\n        } catch (error) {\n            console.error('Error in applyForCampaign:', error);\n            return {\n                success: false,\n                message: 'Erro ao se candidatar para a campanha. Tente novamente.'\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/campaignService.ts\n"));

/***/ })

});