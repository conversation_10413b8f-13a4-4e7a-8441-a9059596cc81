"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/services/campaignService.ts":
/*!*****************************************!*\
  !*** ./src/services/campaignService.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   campaignService: () => (/* binding */ campaignService)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Real campaign service that queries the Supabase database\nconst campaignService = {\n    // Fetch campaigns for an influencer\n    async getInfluencerCampaigns (influencerId) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            // Query campaign_participants table to get campaigns the influencer is participating in\n            const { data: participantsData, error: participantsError } = await supabase.from('campaign_participants').select(\"\\n          id, \\n          status, \\n          campaigns(\\n            id, \\n            name, \\n            description, \\n            start_date, \\n            end_date, \\n            status, \\n            restaurant_id, \\n            restaurant_profiles(id, business_name)\\n          )\\n        \").eq('profile_id', influencerId);\n            if (participantsError) {\n                console.error('Error fetching campaign participants:', participantsError);\n                return [];\n            }\n            if (!participantsData || participantsData.length === 0) {\n                return [];\n            }\n            // Transform the data to match the expected format\n            const campaignInfluencers = await Promise.all(participantsData.map(async (participant)=>{\n                // Get campaign details\n                const campaignData = participant.campaigns;\n                // Handle case where campaign data might be an array\n                const campaign = Array.isArray(campaignData) ? campaignData[0] : campaignData;\n                if (!campaign) {\n                    return null;\n                }\n                // Get restaurant details\n                const restaurantData = campaign.restaurant_profiles;\n                const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n                // Get total points for this campaign participation\n                const { data: pointsData, error: pointsError } = await supabase.from('points_history').select('points').eq('campaign_participant_id', participant.id);\n                let totalPoints = 0;\n                if (!pointsError && pointsData) {\n                    totalPoints = pointsData.reduce((sum, item)=>sum + (item.points || 0), 0);\n                }\n                // Get video status\n                let videoStatus;\n                const { data: videoData, error: videoError } = await supabase.from('campaign_videos').select('status').eq('campaign_participant_id', participant.id).order('created_at', {\n                    ascending: false\n                }).limit(1).single();\n                if (!videoError && videoData) {\n                    videoStatus = videoData.status;\n                }\n                // Get rank\n                let rank;\n                // This would need to be implemented based on your ranking logic\n                // For example, you might have a separate table for rankings or calculate it on the fly\n                return {\n                    id: participant.id,\n                    campaign_id: campaign.id,\n                    influencer_id: influencerId,\n                    status: participant.status,\n                    total_points: totalPoints,\n                    video_status: videoStatus,\n                    rank,\n                    campaigns: {\n                        id: campaign.id,\n                        name: campaign.name,\n                        description: campaign.description,\n                        start_date: campaign.start_date,\n                        end_date: campaign.end_date,\n                        status: campaign.status,\n                        restaurant_id: campaign.restaurant_id,\n                        restaurants: restaurant ? {\n                            id: restaurant.id,\n                            name: restaurant.business_name\n                        } : null\n                    }\n                };\n            }));\n            // Filter out any null values\n            return campaignInfluencers.filter(Boolean);\n        } catch (error) {\n            console.error('Error in getInfluencerCampaigns:', error);\n            return [];\n        }\n    },\n    // Fetch available campaigns\n    async getAvailableCampaigns (filters) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            console.log('Fetching available campaigns with filters:', filters);\n            let query = supabase.from('campaigns').select(\"\\n          id,\\n          name,\\n          description,\\n          start_date,\\n          end_date,\\n          status,\\n          restaurant_id,\\n          restaurant_profiles(id, business_name, city, state),\\n          requirements\\n        \");\n            // Remove the status filter to see all campaigns\n            // .eq('status', 'active');\n            // Apply filters if provided\n            if (filters === null || filters === void 0 ? void 0 : filters.city) {\n                query = query.eq('city', filters.city);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.state) {\n                query = query.eq('state', filters.state);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching available campaigns:', error);\n                return [];\n            }\n            console.log('Raw campaigns data from database:', data);\n            if (!data || data.length === 0) {\n                console.log('No campaigns found with the current filters');\n                return [];\n            }\n            // Transform the data to match the expected format\n            const campaigns = data.map((campaign)=>{\n                const restaurantData = campaign.restaurant_profiles;\n                const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n                // Extract hashtags and mentions from requirements\n                const requirements = campaign.requirements || {};\n                const hashtags = requirements.hashtags || [];\n                const mentions = requirements.mentions || [];\n                return {\n                    id: campaign.id,\n                    name: campaign.name,\n                    description: campaign.description,\n                    start_date: campaign.start_date,\n                    end_date: campaign.end_date,\n                    status: campaign.status,\n                    restaurant_id: campaign.restaurant_id,\n                    restaurants: restaurant ? {\n                        id: restaurant.id,\n                        name: restaurant.business_name,\n                        city: restaurant.city,\n                        state: restaurant.state\n                    } : null,\n                    hashtags,\n                    mentions,\n                    city: restaurant === null || restaurant === void 0 ? void 0 : restaurant.city,\n                    state: restaurant === null || restaurant === void 0 ? void 0 : restaurant.state\n                };\n            });\n            console.log('Transformed campaigns:', campaigns);\n            return campaigns;\n        } catch (error) {\n            console.error('Error in getAvailableCampaigns:', error);\n            return [];\n        }\n    },\n    // Apply for a campaign\n    async applyForCampaign (influencerId, campaignId) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            console.log(\"Attempting to apply for campaign: \".concat(campaignId, \" by influencer: \").concat(influencerId));\n            // Get the authenticated user's ID\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                return {\n                    success: false,\n                    message: 'Usuário não autenticado. Por favor, faça login e tente novamente.'\n                };\n            }\n            console.log(\"InfluencerId: \".concat(influencerId));\n            console.log(\"Auth.uid(): \".concat(user.id));\n            // Ensure the influencerId matches the authenticated user's ID\n            if (influencerId !== user.id) {\n                return {\n                    success: false,\n                    message: 'Erro de autenticação. Por favor, faça login novamente e tente mais uma vez.'\n                };\n            }\n            // Check if the influencer is already participating in this campaign\n            const { data: existingParticipation, error: checkError } = await supabase.from('campaign_influencers').select('id, status').eq('influencer_id', user.id).eq('campaign_id', campaignId).single();\n            if (checkError && checkError.code !== 'PGRST116') {\n                console.error('Error checking existing participation:', checkError);\n                return {\n                    success: false,\n                    message: 'Erro ao verificar participação existente. Tente novamente.'\n                };\n            }\n            if (existingParticipation) {\n                return {\n                    success: false,\n                    message: 'Você já se candidatou a esta campanha.'\n                };\n            }\n            // Get the campaign details first to ensure it exists\n            const { data: campaignData, error: campaignError } = await supabase.from('campaigns').select(\"\\n          id,\\n          name,\\n          description,\\n          start_date,\\n          end_date,\\n          status,\\n          restaurant_id,\\n          restaurant_profiles(id, business_name)\\n        \").eq('id', campaignId).single();\n            if (campaignError) {\n                console.error('Error fetching campaign details:', campaignError);\n                return {\n                    success: false,\n                    message: 'Erro ao buscar detalhes da campanha. Tente novamente.'\n                };\n            }\n            // Create a new participation record\n            const { data: newParticipation, error: insertError } = await supabase.from('campaign_influencers').insert({\n                influencer_id: user.id,\n                campaign_id: campaignId,\n                status: 'invited'\n            }).select('id').single();\n            if (insertError) {\n                console.error('Error creating participation:', insertError);\n                return {\n                    success: false,\n                    message: \"Erro ao se candidatar para a campanha: \".concat(insertError.message, \". Tente novamente.\")\n                };\n            }\n            console.log('Successfully applied to campaign:', newParticipation);\n            const restaurantData = campaignData.restaurant_profiles;\n            const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n            // Create a campaign influencer object to return\n            const campaignInfluencer = {\n                id: newParticipation.id,\n                campaign_id: campaignId,\n                influencer_id: user.id,\n                status: 'pending',\n                total_points: 0,\n                campaigns: {\n                    id: campaignData.id,\n                    name: campaignData.name,\n                    description: campaignData.description,\n                    start_date: campaignData.start_date,\n                    end_date: campaignData.end_date,\n                    status: campaignData.status,\n                    restaurant_id: campaignData.restaurant_id,\n                    restaurants: restaurant ? {\n                        id: restaurant.id,\n                        name: restaurant.business_name\n                    } : null\n                }\n            };\n            return {\n                success: true,\n                message: 'Candidatura enviada com sucesso! Aguarde a aprovação do restaurante.',\n                campaignInfluencer\n            };\n        } catch (error) {\n            console.error('Error in applyForCampaign:', error);\n            return {\n                success: false,\n                message: 'Erro ao se candidatar para a campanha. Tente novamente.'\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/campaignService.ts\n"));

/***/ })

});