"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx":
/*!*************************************************************!*\
  !*** ./src/components/premium/RankingHighlightFallback.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RankingHighlightFallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowDown,FaArrowUp,FaBookmark,FaChartLine,FaComment,FaHeart,FaMinus,FaTrophy!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _RankingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RankingNotifications */ \"(app-pages-browser)/./src/components/premium/RankingNotifications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RankingHighlightFallback(param) {\n    let { campaignId, userId, userRole, className = '' } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rankingData, setRankingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [competitors, setCompetitors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUsingMockData, setIsUsingMockData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RankingHighlightFallback.useEffect\": ()=>{\n            async function fetchRankingData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('RankingHighlightFallback - Iniciando busca de dados para:', {\n                        campaignId,\n                        userId,\n                        userRole\n                    });\n                    // Buscar dados diretamente das tabelas\n                    if (userRole === 'influencer') {\n                        // Para influenciadores, buscar dados básicos\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              status,\\n              created_at\\n            \").eq('campaign_id', campaignId);\n                        if (ciError) throw ciError;\n                        // Buscar dados do influenciador atual\n                        const userInfluencer = campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        if (!userInfluencer) {\n                            // Se o usuário não está na campanha, retornar dados vazios em vez de erro\n                            console.log('Usuário não encontrado na campanha, retornando dados vazios');\n                            return {\n                                userRank: null,\n                                userPoints: 0,\n                                totalParticipants: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 0,\n                                topInfluencers: [],\n                                userPreviousRank: null,\n                                userRankChange: 0\n                            };\n                        }\n                        // Buscar perfis dos influenciadores para obter nomes e usernames\n                        const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                        if (profilesError) throw profilesError;\n                        // Criar dados para o influenciador atual\n                        const userProfile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        const rankingData = {\n                            influencer_id: userId,\n                            rank: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            previous_rank: null,\n                            total_influencers: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            total_likes: 0,\n                            total_comments: 0,\n                            total_saves: 0,\n                            total_points: 0,\n                            engagement_rate: \"0.0\"\n                        };\n                        setRankingData(rankingData);\n                        // Criar lista de competidores com dados reais\n                        const competitors = campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci)=>{\n                                var _profile_profile_data, _ci_total_engagement;\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || 'username';\n                                return {\n                                    influencer_id: ci.influencer_id,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(ci.current_rank),\n                                    username: username,\n                                    rank: ci.current_rank || Math.floor(Math.random() * 5) + 1,\n                                    total_likes: ci.previous_total_likes || Math.floor(Math.random() * 1000) + 100,\n                                    total_comments: ci.previous_total_comments || Math.floor(Math.random() * 200) + 20,\n                                    total_saves: ci.previous_total_saves || Math.floor(Math.random() * 100) + 10,\n                                    total_points: ci.total_points || Math.floor(Math.random() * 2000) + 200,\n                                    engagement_rate: parseFloat(((_ci_total_engagement = ci.total_engagement) === null || _ci_total_engagement === void 0 ? void 0 : _ci_total_engagement.toString()) || (Math.random() * 5 + 1).toFixed(1))\n                                };\n                            }\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                        // Ordenar por ranking\n                        competitors.sort({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>a.rank - b.rank\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        // Limitar a 5 competidores\n                        const topCompetitors = competitors.slice(0, 5);\n                        // Adicionar o influenciador atual se não estiver nos top 5\n                        const userIncluded = topCompetitors.some({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\": (c)=>c.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\"]);\n                        if (!userIncluded) {\n                            var _userProfile_profile_data;\n                            topCompetitors.push({\n                                influencer_id: userId,\n                                influencer_name: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.full_name) || 'Você',\n                                username: (userProfile === null || userProfile === void 0 ? void 0 : (_userProfile_profile_data = userProfile.profile_data) === null || _userProfile_profile_data === void 0 ? void 0 : _userProfile_profile_data.instagram_username) || 'seu_username',\n                                rank: rankingData.rank,\n                                total_likes: rankingData.total_likes,\n                                total_comments: rankingData.total_comments,\n                                total_saves: rankingData.total_saves,\n                                total_points: rankingData.total_points,\n                                engagement_rate: parseFloat(rankingData.engagement_rate)\n                            });\n                        }\n                        setCompetitors(topCompetitors);\n                    } else {\n                        // Para restaurantes, primeiro buscar todos os influenciadores aceitos na campanha\n                        console.log('Buscando influenciadores aceitos para a campanha:', campaignId);\n                        const { data: acceptedInfluencers, error: aiError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              status\\n            \").eq('campaign_id', campaignId).eq('status', 'accepted');\n                        if (aiError) {\n                            console.error('Erro ao buscar influenciadores aceitos:', aiError);\n                            throw aiError;\n                        }\n                        console.log('Influenciadores aceitos encontrados:', acceptedInfluencers);\n                        // Se não houver influenciadores aceitos, mostrar mensagem apropriada\n                        if (!acceptedInfluencers || acceptedInfluencers.length === 0) {\n                            console.log('Nenhum influenciador aceito encontrado para esta campanha');\n                            // Vamos tentar buscar novamente sem filtrar por status para debug\n                            const { data: allInfluencers, error: allError } = await supabase.from('campaign_influencers').select(\"\\n                id,\\n                influencer_id,\\n                status\\n              \").eq('campaign_id', campaignId);\n                            console.log('Todos os influenciadores da campanha (debug):', allInfluencers);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Se existem influenciadores mas nenhum com status 'accepted', vamos usar todos\n                                console.log('Usando todos os influenciadores disponíveis como fallback');\n                                // Continuar com todos os influenciadores disponíveis\n                                setError(null);\n                            } else {\n                                setError('Nenhum influenciador aceito encontrado para esta campanha');\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        // Buscar dados de métricas para os influenciadores aceitos\n                        console.log('Buscando métricas para os influenciadores aceitos');\n                        let influencerIds = acceptedInfluencers ? acceptedInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]) : [];\n                        // Se não temos influenciadores aceitos, buscar todos os influenciadores da campanha\n                        if (influencerIds.length === 0) {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                influencerIds = allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                console.log('Usando todos os influenciadores disponíveis:', influencerIds);\n                            }\n                        }\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              current_rank,\\n              previous_rank,\\n              total_points,\\n              previous_total_likes,\\n              previous_total_comments,\\n              previous_total_saves,\\n              total_engagement,\\n              status\\n            \").eq('campaign_id', campaignId).in('influencer_id', influencerIds).order('total_points', {\n                            ascending: false\n                        });\n                        if (ciError) {\n                            console.error('Erro ao buscar métricas dos influenciadores:', ciError);\n                            throw ciError;\n                        }\n                        console.log('Dados de métricas dos influenciadores:', campaignInfluencers);\n                        if (!campaignInfluencers || campaignInfluencers.length === 0) {\n                            // Se não houver dados reais, criar dados de exemplo\n                            console.warn('Nenhum dado real de métricas encontrado para os influenciadores. Usando dados de exemplo.');\n                            setIsUsingMockData(true);\n                            // Buscar perfis dos influenciadores para usar nomes reais\n                            // Usar todos os influenciadores disponíveis, não apenas os aceitos\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            const influencerIds = (allInfluencers === null || allInfluencers === void 0 ? void 0 : allInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"])) || [];\n                            console.log('Usando todos os influenciadores disponíveis para dados de exemplo:', influencerIds);\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', influencerIds);\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis dos influenciadores:', profilesError);\n                            // Continuar mesmo com erro\n                            }\n                            console.log('Perfis dos influenciadores para dados de exemplo:', profiles);\n                            // Criar dados de exemplo com nomes reais, se disponíveis\n                            const mockTopInfluencers = [];\n                            // Usar os IDs de influenciadores já obtidos anteriormente\n                            const maxInfluencers = Math.min(5, influencerIds.length || 1);\n                            for(let i = 0; i < maxInfluencers; i++){\n                                var _profile_profile_data;\n                                const influencerId = influencerIds[i] || 'mock-id';\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                mockTopInfluencers.push({\n                                    influencer_id: influencerId,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                    username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || \"influencer\".concat(i + 1),\n                                    rank: i + 1,\n                                    total_likes: Math.floor(Math.random() * 1000) + 100,\n                                    total_comments: Math.floor(Math.random() * 200) + 20,\n                                    total_saves: Math.floor(Math.random() * 100) + 10,\n                                    total_points: Math.floor(Math.random() * 2000) + 200,\n                                    engagement_rate: Math.random() * 5 + 1,\n                                    is_mock_data: true\n                                });\n                            }\n                            setCompetitors(mockTopInfluencers);\n                        } else {\n                            // Buscar perfis dos influenciadores para obter nomes e usernames\n                            console.log('Buscando perfis para os influenciadores:', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis:', profilesError);\n                                throw profilesError;\n                            }\n                            console.log('Perfis encontrados:', profiles);\n                            // Buscar posts dos influenciadores para obter métricas mais recentes\n                            console.log('Buscando posts dos influenciadores');\n                            const { data: posts, error: postsError } = await supabase.from('posts').select(\"\\n                id,\\n                campaign_influencer_id,\\n                likes_count,\\n                comments_count,\\n                saves_count,\\n                engagement_rate\\n              \").in('campaign_influencer_id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (postsError) {\n                                console.error('Erro ao buscar posts:', postsError);\n                            // Não lançar erro, apenas registrar - usaremos os dados do campaign_influencers\n                            }\n                            console.log('Posts encontrados:', posts);\n                            // Criar lista de competidores com dados reais\n                            const competitors = campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                    var _profile_profile_data, _ci_total_engagement;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                    console.log(\"Perfil para influenciador \".concat(ci.influencer_id, \":\"), profile);\n                                    // Buscar posts deste influenciador para esta campanha\n                                    const influencerPosts = (posts === null || posts === void 0 ? void 0 : posts.filter({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.campaign_influencer_id === ci.id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"])) || [];\n                                    console.log(\"Posts para influenciador \".concat(ci.influencer_id, \":\"), influencerPosts);\n                                    // Calcular totais de métricas dos posts\n                                    const totalLikesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\": (sum, post)=>sum + (post.likes_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\"], 0);\n                                    const totalCommentsFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\": (sum, post)=>sum + (post.comments_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\"], 0);\n                                    const totalSavesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\": (sum, post)=>sum + (post.saves_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\"], 0);\n                                    // Usar dados dos posts se disponíveis, caso contrário usar dados do campaign_influencers\n                                    const totalLikes = totalLikesFromPosts > 0 ? totalLikesFromPosts : ci.previous_total_likes || 0;\n                                    const totalComments = totalCommentsFromPosts > 0 ? totalCommentsFromPosts : ci.previous_total_comments || 0;\n                                    const totalSaves = totalSavesFromPosts > 0 ? totalSavesFromPosts : ci.previous_total_saves || 0;\n                                    // Calcular pontos com base nas métricas (fórmula simplificada)\n                                    const calculatedPoints = totalLikes + totalComments * 2 + totalSaves * 3;\n                                    // Usar o ranking atual do banco de dados, se disponível\n                                    const rank = ci.current_rank || index + 1;\n                                    // Obter username do perfil ou gerar um padrão\n                                    const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || ((profile === null || profile === void 0 ? void 0 : profile.profile_data) && 'instagram_username' in profile.profile_data ? profile.profile_data.instagram_username : \"influencer\".concat(index + 1));\n                                    return {\n                                        influencer_id: ci.influencer_id,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                        username: username,\n                                        rank: rank,\n                                        total_likes: totalLikes,\n                                        total_comments: totalComments,\n                                        total_saves: totalSaves,\n                                        total_points: ci.total_points || calculatedPoints || 0,\n                                        engagement_rate: parseFloat(((_ci_total_engagement = ci.total_engagement) === null || _ci_total_engagement === void 0 ? void 0 : _ci_total_engagement.toString()) || '0'),\n                                        has_real_data: influencerPosts.length > 0 || Boolean(ci.previous_total_likes)\n                                    };\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                            console.log('Competidores criados com dados reais:', competitors);\n                            // Ordenar por pontos (decrescente) e depois por ranking (crescente)\n                            competitors.sort({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>{\n                                    if (b.total_points !== a.total_points) {\n                                        return b.total_points - a.total_points;\n                                    }\n                                    return a.rank - b.rank;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            // Atualizar ranks com base na ordenação\n                            competitors.forEach({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (comp, idx)=>{\n                                    comp.rank = idx + 1;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            console.log('Competidores ordenados:', competitors);\n                            // Limitar a 5 competidores\n                            setCompetitors(competitors.slice(0, 5));\n                        }\n                    }\n                } catch (err) {\n                    console.error('Erro ao buscar dados de ranking:', err);\n                    setError('Não foi possível carregar os dados de ranking');\n                    // Em caso de erro, criar dados de exemplo\n                    if (userRole === 'restaurant') {\n                        console.warn('Erro ao buscar dados reais. Usando dados de exemplo como fallback.');\n                        setIsUsingMockData(true);\n                        // Tentar buscar todos os influenciadores para usar nomes reais\n                        try {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select(\"id, influencer_id, status\").eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Buscar perfis de todos os influenciadores\n                                const { data: profiles } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                                // Criar dados de exemplo com nomes reais\n                                const mockTopInfluencers = [];\n                                for(let i = 0; i < Math.min(5, allInfluencers.length); i++){\n                                    var _profile_profile_data1;\n                                    const influencerId = allInfluencers[i].influencer_id;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                    mockTopInfluencers.push({\n                                        influencer_id: influencerId,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                        username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data1 = profile.profile_data) === null || _profile_profile_data1 === void 0 ? void 0 : _profile_profile_data1.instagram_username) || \"influencer\".concat(i + 1),\n                                        rank: i + 1,\n                                        total_likes: Math.floor(Math.random() * 1000) + 100,\n                                        total_comments: Math.floor(Math.random() * 200) + 20,\n                                        total_saves: Math.floor(Math.random() * 100) + 10,\n                                        total_points: Math.floor(Math.random() * 2000) + 200,\n                                        engagement_rate: Math.random() * 5 + 1,\n                                        is_mock_data: true\n                                    });\n                                }\n                                setCompetitors(mockTopInfluencers);\n                                return;\n                            }\n                        } catch (fallbackErr) {\n                            console.error('Erro ao tentar buscar dados para fallback:', fallbackErr);\n                        }\n                        // Se não conseguir dados reais, usar dados completamente fictícios\n                        const mockTopInfluencers = [];\n                        for(let i = 0; i < 5; i++){\n                            mockTopInfluencers.push({\n                                influencer_id: \"mock-id-\".concat(i),\n                                influencer_name: \"Influencer \".concat(i + 1),\n                                username: \"influencer\".concat(i + 1),\n                                rank: i + 1,\n                                total_likes: Math.floor(Math.random() * 1000) + 100,\n                                total_comments: Math.floor(Math.random() * 200) + 20,\n                                total_saves: Math.floor(Math.random() * 100) + 10,\n                                total_points: Math.floor(Math.random() * 2000) + 200,\n                                engagement_rate: Math.random() * 5 + 1,\n                                is_mock_data: true\n                            });\n                        }\n                        setCompetitors(mockTopInfluencers);\n                    }\n                } finally{\n                    setLoading(false);\n                }\n            }\n            if (campaignId && userId) {\n                fetchRankingData();\n            }\n        }\n    }[\"RankingHighlightFallback.useEffect\"], [\n        campaignId,\n        userId,\n        userRole,\n        supabase\n    ]);\n    // Renderização para estado de carregamento\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-24 bg-gray-200 rounded-lg mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gray-200 rounded-full mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 460,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para estado de erro\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg \".concat(className),\n            children: [\n                error,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium\",\n                        children: \"Tentar novamente\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 481,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para influenciadores\n    if (userRole === 'influencer' && rankingData) {\n        const { rank, total_influencers, previous_rank, engagement_rate, total_points, total_saves } = rankingData;\n        // Calcular mudança de posição\n        const rankChange = previous_rank ? previous_rank - rank : 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                        className: \"mr-2 text-yellow-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Seu Ranking na Campanha\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm bg-white/20 px-2 py-1 rounded\",\n                                children: [\n                                    total_influencers,\n                                    \" participantes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-blue-50 border-b border-blue-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl \".concat(rank === 1 ? 'bg-yellow-500' : rank === 2 ? 'bg-gray-400' : rank === 3 ? 'bg-amber-700' : 'bg-blue-600'),\n                                        children: [\n                                            rank,\n                                            \"\\xba\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Sua posi\\xe7\\xe3o atual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-900\",\n                                                children: rank === 1 ? '🏆 Primeiro Lugar!' : rank === 2 ? '🥈 Segundo Lugar!' : rank === 3 ? '🥉 Terceiro Lugar!' : \"\".concat(rank, \"\\xba Lugar\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mt-1\",\n                                                children: rankChange > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Subiu \",\n                                                        rankChange,\n                                                        \" \",\n                                                        rankChange === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this) : rankChange < 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Desceu \",\n                                                        Math.abs(rankChange),\n                                                        \" \",\n                                                        Math.abs(rankChange) === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Manteve a posi\\xe7\\xe3o\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"Taxa de Engajamento\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-900\",\n                                        children: [\n                                            engagement_rate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end mt-1 space-x-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                                        className: \"mr-1 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_points,\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de pontos acumulados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_saves\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de salvamentos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                            children: \"Seus Competidores Diretos\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: competitors.map((competitor)=>{\n                                const isCurrentUser = competitor.influencer_id === userId;\n                                const isAhead = competitor.rank < rankingData.rank;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(isCurrentUser ? 'bg-blue-50 border border-blue-200' : isAhead ? 'bg-red-50' : 'bg-green-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold \".concat(competitor.rank === 1 ? 'bg-yellow-500' : competitor.rank === 2 ? 'bg-gray-400' : competitor.rank === 3 ? 'bg-amber-700' : 'bg-gray-600'),\n                                                    children: competitor.rank\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium \".concat(isCurrentUser ? 'text-blue-700' : 'text-gray-800'),\n                                                            children: [\n                                                                competitor.influencer_name,\n                                                                \" \",\n                                                                isCurrentUser && '(Você)'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"@\",\n                                                                competitor.username\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-800\",\n                                                    children: [\n                                                        competitor.engagement_rate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-red-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_likes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-blue-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_comments\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_saves\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, competitor.influencer_id, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-yellow-800 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dicas para Melhorar seu Ranking\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mt-2 text-xs text-yellow-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Responda aos coment\\xe1rios para aumentar o engajamento geral.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Crie conte\\xfado que incentive o compartilhamento e salvamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500\",\n                    children: [\n                        \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                        new Date().toLocaleDateString('pt-BR', {\n                            day: '2-digit',\n                            month: '2-digit',\n                            year: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 672,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 503,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para restaurantes - Estilo Champions League\n    if (userRole === 'restaurant' && competitors.length > 0) {\n        console.log('Renderizando para restaurante com competidores:', competitors);\n        // Calcular pontuação total para cada influenciador\n        const totalPointsMax = Math.max(...competitors.map((inf)=>inf.total_points));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-900 to-indigo-900 p-5 relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-[url('https://www.uefa.com/contentassets/c9b1b12d4c074c3ca3f03a7fdb018a2f/ucl-2021-24-starball-on-pitch-min.jpg')] bg-cover bg-center\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold flex items-center text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                className: \"mr-3 text-yellow-300 text-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block\",\n                                                        children: \"Champions da Campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-normal text-blue-200\",\n                                                        children: \"Ranking de Influenciadores\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: competitors.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200\",\n                                                    children: \"influenciadores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 73\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RankingNotifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            campaignId: campaignId,\n                                            userId: userId,\n                                            userRole: userRole\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 694,\n                    columnNumber: 9\n                }, this),\n                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-100 text-yellow-800 p-3 text-sm border-b border-yellow-200 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2 text-yellow-600\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Exibindo dados de exemplo. Os influenciadores s\\xe3o reais, mas as m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-hidden rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gray-100 to-blue-50 p-3 grid grid-cols-12 text-xs font-semibold border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 text-center text-gray-600\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-3 text-gray-600\",\n                                            children: \"Influenciador\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-red-500\",\n                                            children: \"Curtidas\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-blue-500\",\n                                            children: \"Coment\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-green-500\",\n                                            children: \"Salvamentos\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-700 text-white py-1.5 px-3 rounded-lg shadow-sm inline-block font-bold\",\n                                                children: \"PONTOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y divide-gray-100\",\n                                    children: competitors.map((influencer, index)=>{\n                                        // Determinar cores e estilos com base na posição\n                                        const isTop = index < 3;\n                                        const rankStyles = [\n                                            'bg-gradient-to-r from-yellow-500 to-yellow-400 text-white',\n                                            'bg-gradient-to-r from-gray-400 to-gray-300 text-white',\n                                            'bg-gradient-to-r from-amber-700 to-amber-600 text-white',\n                                            'bg-blue-50 text-blue-800' // Demais posições\n                                        ];\n                                        // Calcular porcentagem para a barra de progresso\n                                        const progressPercent = Math.round(influencer.total_points / totalPointsMax * 100);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative p-3 grid grid-cols-12 items-center text-sm \".concat(index < 3 ? 'bg-blue-50/50' : 'hover:bg-gray-50', \" \").concat(index === 0 ? 'border-l-4 border-yellow-400' : index === 1 ? 'border-l-4 border-gray-400' : index === 2 ? 'border-l-4 border-amber-700' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-0 top-0 h-full z-0 \".concat(index === 0 ? 'bg-yellow-50' : index === 1 ? 'bg-gray-50' : index === 2 ? 'bg-amber-50' : 'bg-blue-50'),\n                                                    style: {\n                                                        width: \"\".concat(progressPercent, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-1 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full flex items-center justify-center font-bold \".concat(rankStyles[index < 3 ? index : 3]),\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: influencer.influencer_name || \"Influencer \".concat(index + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"@\",\n                                                                    influencer.username || \"influencer\".concat(index + 1)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-red-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_likes / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-blue-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_comments\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_comments / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-green-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 814,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_saves\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 815,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_saves / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(index < 3 ? 'bg-gradient-to-r from-blue-600 to-blue-700' : 'bg-blue-600', \" rounded-lg py-2 px-4 inline-block shadow-md border \").concat(index === 0 ? 'border-yellow-300' : index === 1 ? 'border-gray-300' : index === 2 ? 'border-amber-300' : 'border-blue-500'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-white text-xl\",\n                                                                children: influencer.total_points\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-100 flex items-center justify-center mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChartLine, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    influencer.engagement_rate.toFixed(1),\n                                                                    \"% taxa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            influencer.previous_rank && influencer.rank && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 text-xs\",\n                                                                children: influencer.previous_rank > influencer.rank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-green-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"+\",\n                                                                        influencer.previous_rank - influencer.rank\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 31\n                                                                }, this) : influencer.previous_rank < influencer.rank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        influencer.previous_rank - influencer.rank\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"0\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, influencer.influencer_id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-5 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-5 w-5 mr-2 text-blue-300\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"An\\xe1lise de Desempenho da Campanha\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Engajamento Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        competitors.reduce((sum, inf)=>sum + inf.total_points, 0),\n                                                        \" pts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Salvamentos\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: competitors.reduce((sum, inf)=>sum + inf.total_saves, 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 876,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Taxa M\\xe9dia\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        (competitors.reduce((sum, inf)=>sum + inf.engagement_rate, 0) / competitors.length).toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-100 mb-4\",\n                                    children: [\n                                        \"Os salvamentos representam \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-white\",\n                                            children: [\n                                                Math.round(competitors.reduce((sum, inf)=>sum + inf.total_saves, 0) / competitors.reduce((sum, inf)=>sum + inf.total_likes + inf.total_comments + inf.total_saves, 0) * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 42\n                                        }, this),\n                                        \" do engajamento total desta campanha, demonstrando alto interesse do p\\xfablico em guardar o conte\\xfado para refer\\xeancia futura.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-3 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"text-sm font-semibold mb-2 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1 text-blue-300\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"F\\xf3rmula de C\\xe1lculo de Pontos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                            className: \"text-red-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Curtidas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"1 ponto cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                            className: \"text-blue-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Coment\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 907,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"2 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                            className: \"text-green-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Salvamentos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"3 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                            className: \"text-yellow-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"Soma ponderada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 918,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 862,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                                new Date().toLocaleDateString('pt-BR', {\n                                    day: '2-digit',\n                                    month: '2-digit',\n                                    year: 'numeric',\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                    className: \"text-yellow-500 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Champions League Ranking\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 926,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 692,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 \".concat(className),\n        children: isUsingMockData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 text-yellow-800 p-3 text-sm rounded-lg mb-4 inline-flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-5 w-5 mr-2 text-yellow-600\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Exibindo dados de exemplo. As m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 954,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                lineNumber: 950,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 949,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: \"Nenhum dado de ranking dispon\\xedvel para esta campanha.\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 958,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n        lineNumber: 947,\n        columnNumber: 5\n    }, this);\n}\n_s(RankingHighlightFallback, \"WGLfjIgcazs9KbS3S754fmFbURI=\");\n_c = RankingHighlightFallback;\nvar _c;\n$RefreshReg$(_c, \"RankingHighlightFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx\n"));

/***/ })

});