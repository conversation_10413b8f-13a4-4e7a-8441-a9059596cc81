"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx":
/*!*************************************************************!*\
  !*** ./src/components/premium/RankingHighlightFallback.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RankingHighlightFallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowDown,FaArrowUp,FaBookmark,FaChartLine,FaComment,FaHeart,FaMinus,FaTrophy!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _RankingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RankingNotifications */ \"(app-pages-browser)/./src/components/premium/RankingNotifications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RankingHighlightFallback(param) {\n    let { campaignId, userId, userRole, className = '' } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rankingData, setRankingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [competitors, setCompetitors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUsingMockData, setIsUsingMockData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RankingHighlightFallback.useEffect\": ()=>{\n            async function fetchRankingData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('RankingHighlightFallback - Iniciando busca de dados para:', {\n                        campaignId,\n                        userId,\n                        userRole\n                    });\n                    // Buscar dados diretamente das tabelas\n                    if (userRole === 'influencer') {\n                        // Para influenciadores, buscar dados básicos\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              status,\\n              created_at\\n            \").eq('campaign_id', campaignId);\n                        if (ciError) throw ciError;\n                        // Buscar dados do influenciador atual\n                        const userInfluencer = campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        if (!userInfluencer) {\n                            // Se o usuário não está na campanha, retornar dados vazios em vez de erro\n                            console.log('Usuário não encontrado na campanha, retornando dados vazios');\n                            return {\n                                userRank: null,\n                                userPoints: 0,\n                                totalParticipants: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 0,\n                                topInfluencers: [],\n                                userPreviousRank: null,\n                                userRankChange: 0\n                            };\n                        }\n                        // Buscar perfis dos influenciadores para obter nomes e usernames\n                        const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                        if (profilesError) throw profilesError;\n                        // Criar dados para o influenciador atual\n                        const userProfile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        const rankingData = {\n                            influencer_id: userId,\n                            rank: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            previous_rank: null,\n                            total_influencers: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            total_likes: 0,\n                            total_comments: 0,\n                            total_saves: 0,\n                            total_points: 0,\n                            engagement_rate: \"0.0\"\n                        };\n                        setRankingData(rankingData);\n                        // Criar lista de competidores com dados reais\n                        const competitors = campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                var _profile_profile_data;\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || 'username';\n                                return {\n                                    influencer_id: ci.influencer_id,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                    username: username,\n                                    rank: index + 1,\n                                    total_likes: 0,\n                                    total_comments: 0,\n                                    total_saves: 0,\n                                    total_points: 0,\n                                    engagement_rate: 0.0\n                                };\n                            }\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                        // Ordenar por ranking\n                        competitors.sort({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>a.rank - b.rank\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        // Limitar a 5 competidores\n                        const topCompetitors = competitors.slice(0, 5);\n                        // Adicionar o influenciador atual se não estiver nos top 5\n                        const userIncluded = topCompetitors.some({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\": (c)=>c.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\"]);\n                        if (!userIncluded) {\n                            var _userProfile_profile_data;\n                            topCompetitors.push({\n                                influencer_id: userId,\n                                influencer_name: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.full_name) || 'Você',\n                                username: (userProfile === null || userProfile === void 0 ? void 0 : (_userProfile_profile_data = userProfile.profile_data) === null || _userProfile_profile_data === void 0 ? void 0 : _userProfile_profile_data.instagram_username) || 'seu_username',\n                                rank: rankingData.rank,\n                                total_likes: rankingData.total_likes,\n                                total_comments: rankingData.total_comments,\n                                total_saves: rankingData.total_saves,\n                                total_points: rankingData.total_points,\n                                engagement_rate: parseFloat(rankingData.engagement_rate)\n                            });\n                        }\n                        setCompetitors(topCompetitors);\n                    } else {\n                        // Para restaurantes, primeiro buscar todos os influenciadores aceitos na campanha\n                        console.log('Buscando influenciadores aceitos para a campanha:', campaignId);\n                        const { data: acceptedInfluencers, error: aiError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              status\\n            \").eq('campaign_id', campaignId).eq('status', 'accepted');\n                        if (aiError) {\n                            console.error('Erro ao buscar influenciadores aceitos:', aiError);\n                            throw aiError;\n                        }\n                        console.log('Influenciadores aceitos encontrados:', acceptedInfluencers);\n                        // Se não houver influenciadores aceitos, mostrar mensagem apropriada\n                        if (!acceptedInfluencers || acceptedInfluencers.length === 0) {\n                            console.log('Nenhum influenciador aceito encontrado para esta campanha');\n                            // Vamos tentar buscar novamente sem filtrar por status para debug\n                            const { data: allInfluencers, error: allError } = await supabase.from('campaign_influencers').select(\"\\n                id,\\n                influencer_id,\\n                status\\n              \").eq('campaign_id', campaignId);\n                            console.log('Todos os influenciadores da campanha (debug):', allInfluencers);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Se existem influenciadores mas nenhum com status 'accepted', vamos usar todos\n                                console.log('Usando todos os influenciadores disponíveis como fallback');\n                                // Continuar com todos os influenciadores disponíveis\n                                setError(null);\n                            } else {\n                                setError('Nenhum influenciador aceito encontrado para esta campanha');\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        // Buscar dados de métricas para os influenciadores aceitos\n                        console.log('Buscando métricas para os influenciadores aceitos');\n                        let influencerIds = acceptedInfluencers ? acceptedInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]) : [];\n                        // Se não temos influenciadores aceitos, buscar todos os influenciadores da campanha\n                        if (influencerIds.length === 0) {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                influencerIds = allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                console.log('Usando todos os influenciadores disponíveis:', influencerIds);\n                            }\n                        }\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              status,\\n              created_at\\n            \").eq('campaign_id', campaignId).in('influencer_id', influencerIds).order('created_at', {\n                            ascending: true\n                        });\n                        if (ciError) {\n                            console.error('Erro ao buscar métricas dos influenciadores:', ciError);\n                            throw ciError;\n                        }\n                        console.log('Dados de métricas dos influenciadores:', campaignInfluencers);\n                        if (!campaignInfluencers || campaignInfluencers.length === 0) {\n                            // Se não houver dados reais, criar dados de exemplo\n                            console.warn('Nenhum dado real de métricas encontrado para os influenciadores. Usando dados de exemplo.');\n                            setIsUsingMockData(true);\n                            // Buscar perfis dos influenciadores para usar nomes reais\n                            // Usar todos os influenciadores disponíveis, não apenas os aceitos\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            const influencerIds = (allInfluencers === null || allInfluencers === void 0 ? void 0 : allInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"])) || [];\n                            console.log('Usando todos os influenciadores disponíveis para dados de exemplo:', influencerIds);\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', influencerIds);\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis dos influenciadores:', profilesError);\n                            // Continuar mesmo com erro\n                            }\n                            console.log('Perfis dos influenciadores para dados de exemplo:', profiles);\n                            // Criar dados de exemplo com nomes reais, se disponíveis\n                            const mockTopInfluencers = [];\n                            // Usar os IDs de influenciadores já obtidos anteriormente\n                            const maxInfluencers = Math.min(5, influencerIds.length || 1);\n                            for(let i = 0; i < maxInfluencers; i++){\n                                var _profile_profile_data;\n                                const influencerId = influencerIds[i] || 'mock-id';\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                mockTopInfluencers.push({\n                                    influencer_id: influencerId,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                    username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || \"influencer\".concat(i + 1),\n                                    rank: i + 1,\n                                    total_likes: Math.floor(Math.random() * 1000) + 100,\n                                    total_comments: Math.floor(Math.random() * 200) + 20,\n                                    total_saves: Math.floor(Math.random() * 100) + 10,\n                                    total_points: Math.floor(Math.random() * 2000) + 200,\n                                    engagement_rate: Math.random() * 5 + 1,\n                                    is_mock_data: true\n                                });\n                            }\n                            setCompetitors(mockTopInfluencers);\n                        } else {\n                            // Buscar perfis dos influenciadores para obter nomes e usernames\n                            console.log('Buscando perfis para os influenciadores:', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis:', profilesError);\n                                throw profilesError;\n                            }\n                            console.log('Perfis encontrados:', profiles);\n                            // Buscar posts dos influenciadores para obter métricas mais recentes\n                            console.log('Buscando posts dos influenciadores');\n                            const { data: posts, error: postsError } = await supabase.from('posts').select(\"\\n                id,\\n                campaign_influencer_id,\\n                likes_count,\\n                comments_count,\\n                saves_count,\\n                engagement_rate\\n              \").in('campaign_influencer_id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (postsError) {\n                                console.error('Erro ao buscar posts:', postsError);\n                            // Não lançar erro, apenas registrar - usaremos os dados do campaign_influencers\n                            }\n                            console.log('Posts encontrados:', posts);\n                            // Criar lista de competidores com dados reais\n                            const competitors = campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                    var _profile_profile_data, _ci_total_engagement;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                    console.log(\"Perfil para influenciador \".concat(ci.influencer_id, \":\"), profile);\n                                    // Buscar posts deste influenciador para esta campanha\n                                    const influencerPosts = (posts === null || posts === void 0 ? void 0 : posts.filter({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.campaign_influencer_id === ci.id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"])) || [];\n                                    console.log(\"Posts para influenciador \".concat(ci.influencer_id, \":\"), influencerPosts);\n                                    // Calcular totais de métricas dos posts\n                                    const totalLikesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\": (sum, post)=>sum + (post.likes_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\"], 0);\n                                    const totalCommentsFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\": (sum, post)=>sum + (post.comments_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\"], 0);\n                                    const totalSavesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\": (sum, post)=>sum + (post.saves_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\"], 0);\n                                    // Usar dados dos posts se disponíveis, caso contrário usar dados do campaign_influencers\n                                    const totalLikes = totalLikesFromPosts > 0 ? totalLikesFromPosts : ci.previous_total_likes || 0;\n                                    const totalComments = totalCommentsFromPosts > 0 ? totalCommentsFromPosts : ci.previous_total_comments || 0;\n                                    const totalSaves = totalSavesFromPosts > 0 ? totalSavesFromPosts : ci.previous_total_saves || 0;\n                                    // Calcular pontos com base nas métricas (fórmula simplificada)\n                                    const calculatedPoints = totalLikes + totalComments * 2 + totalSaves * 3;\n                                    // Usar o ranking atual do banco de dados, se disponível\n                                    const rank = ci.current_rank || index + 1;\n                                    // Obter username do perfil ou gerar um padrão\n                                    const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || ((profile === null || profile === void 0 ? void 0 : profile.profile_data) && 'instagram_username' in profile.profile_data ? profile.profile_data.instagram_username : \"influencer\".concat(index + 1));\n                                    return {\n                                        influencer_id: ci.influencer_id,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                        username: username,\n                                        rank: rank,\n                                        total_likes: totalLikes,\n                                        total_comments: totalComments,\n                                        total_saves: totalSaves,\n                                        total_points: ci.total_points || calculatedPoints || 0,\n                                        engagement_rate: parseFloat(((_ci_total_engagement = ci.total_engagement) === null || _ci_total_engagement === void 0 ? void 0 : _ci_total_engagement.toString()) || '0'),\n                                        has_real_data: influencerPosts.length > 0 || Boolean(ci.previous_total_likes)\n                                    };\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                            console.log('Competidores criados com dados reais:', competitors);\n                            // Ordenar por pontos (decrescente) e depois por ranking (crescente)\n                            competitors.sort({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>{\n                                    if (b.total_points !== a.total_points) {\n                                        return b.total_points - a.total_points;\n                                    }\n                                    return a.rank - b.rank;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            // Atualizar ranks com base na ordenação\n                            competitors.forEach({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (comp, idx)=>{\n                                    comp.rank = idx + 1;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            console.log('Competidores ordenados:', competitors);\n                            // Limitar a 5 competidores\n                            setCompetitors(competitors.slice(0, 5));\n                        }\n                    }\n                } catch (err) {\n                    console.error('Erro ao buscar dados de ranking:', err);\n                    setError('Não foi possível carregar os dados de ranking');\n                    // Em caso de erro, criar dados de exemplo\n                    if (userRole === 'restaurant') {\n                        console.warn('Erro ao buscar dados reais. Usando dados de exemplo como fallback.');\n                        setIsUsingMockData(true);\n                        // Tentar buscar todos os influenciadores para usar nomes reais\n                        try {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select(\"id, influencer_id, status\").eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Buscar perfis de todos os influenciadores\n                                const { data: profiles } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                                // Criar dados de exemplo com nomes reais\n                                const mockTopInfluencers = [];\n                                for(let i = 0; i < Math.min(5, allInfluencers.length); i++){\n                                    var _profile_profile_data1;\n                                    const influencerId = allInfluencers[i].influencer_id;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                    mockTopInfluencers.push({\n                                        influencer_id: influencerId,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                        username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data1 = profile.profile_data) === null || _profile_profile_data1 === void 0 ? void 0 : _profile_profile_data1.instagram_username) || \"influencer\".concat(i + 1),\n                                        rank: i + 1,\n                                        total_likes: Math.floor(Math.random() * 1000) + 100,\n                                        total_comments: Math.floor(Math.random() * 200) + 20,\n                                        total_saves: Math.floor(Math.random() * 100) + 10,\n                                        total_points: Math.floor(Math.random() * 2000) + 200,\n                                        engagement_rate: Math.random() * 5 + 1,\n                                        is_mock_data: true\n                                    });\n                                }\n                                setCompetitors(mockTopInfluencers);\n                                return;\n                            }\n                        } catch (fallbackErr) {\n                            console.error('Erro ao tentar buscar dados para fallback:', fallbackErr);\n                        }\n                        // Se não conseguir dados reais, usar dados completamente fictícios\n                        const mockTopInfluencers = [];\n                        for(let i = 0; i < 5; i++){\n                            mockTopInfluencers.push({\n                                influencer_id: \"mock-id-\".concat(i),\n                                influencer_name: \"Influencer \".concat(i + 1),\n                                username: \"influencer\".concat(i + 1),\n                                rank: i + 1,\n                                total_likes: Math.floor(Math.random() * 1000) + 100,\n                                total_comments: Math.floor(Math.random() * 200) + 20,\n                                total_saves: Math.floor(Math.random() * 100) + 10,\n                                total_points: Math.floor(Math.random() * 2000) + 200,\n                                engagement_rate: Math.random() * 5 + 1,\n                                is_mock_data: true\n                            });\n                        }\n                        setCompetitors(mockTopInfluencers);\n                    }\n                } finally{\n                    setLoading(false);\n                }\n            }\n            if (campaignId && userId) {\n                fetchRankingData();\n            }\n        }\n    }[\"RankingHighlightFallback.useEffect\"], [\n        campaignId,\n        userId,\n        userRole,\n        supabase\n    ]);\n    // Renderização para estado de carregamento\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-24 bg-gray-200 rounded-lg mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gray-200 rounded-full mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 454,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para estado de erro\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg \".concat(className),\n            children: [\n                error,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium\",\n                        children: \"Tentar novamente\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 475,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para influenciadores\n    if (userRole === 'influencer' && rankingData) {\n        const { rank, total_influencers, previous_rank, engagement_rate, total_points, total_saves } = rankingData;\n        // Calcular mudança de posição\n        const rankChange = previous_rank ? previous_rank - rank : 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                        className: \"mr-2 text-yellow-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Seu Ranking na Campanha\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm bg-white/20 px-2 py-1 rounded\",\n                                children: [\n                                    total_influencers,\n                                    \" participantes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 500,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-blue-50 border-b border-blue-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl \".concat(rank === 1 ? 'bg-yellow-500' : rank === 2 ? 'bg-gray-400' : rank === 3 ? 'bg-amber-700' : 'bg-blue-600'),\n                                        children: [\n                                            rank,\n                                            \"\\xba\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Sua posi\\xe7\\xe3o atual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-900\",\n                                                children: rank === 1 ? '🏆 Primeiro Lugar!' : rank === 2 ? '🥈 Segundo Lugar!' : rank === 3 ? '🥉 Terceiro Lugar!' : \"\".concat(rank, \"\\xba Lugar\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mt-1\",\n                                                children: rankChange > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Subiu \",\n                                                        rankChange,\n                                                        \" \",\n                                                        rankChange === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this) : rankChange < 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Desceu \",\n                                                        Math.abs(rankChange),\n                                                        \" \",\n                                                        Math.abs(rankChange) === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Manteve a posi\\xe7\\xe3o\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"Taxa de Engajamento\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-900\",\n                                        children: [\n                                            engagement_rate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end mt-1 space-x-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                                        className: \"mr-1 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_points,\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de pontos acumulados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_saves\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de salvamentos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 513,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 512,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                            children: \"Seus Competidores Diretos\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: competitors.map((competitor)=>{\n                                const isCurrentUser = competitor.influencer_id === userId;\n                                const isAhead = competitor.rank < rankingData.rank;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(isCurrentUser ? 'bg-blue-50 border border-blue-200' : isAhead ? 'bg-red-50' : 'bg-green-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold \".concat(competitor.rank === 1 ? 'bg-yellow-500' : competitor.rank === 2 ? 'bg-gray-400' : competitor.rank === 3 ? 'bg-amber-700' : 'bg-gray-600'),\n                                                    children: competitor.rank\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium \".concat(isCurrentUser ? 'text-blue-700' : 'text-gray-800'),\n                                                            children: [\n                                                                competitor.influencer_name,\n                                                                \" \",\n                                                                isCurrentUser && '(Você)'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"@\",\n                                                                competitor.username\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-800\",\n                                                    children: [\n                                                        competitor.engagement_rate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-red-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_likes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-blue-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_comments\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_saves\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 617,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, competitor.influencer_id, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-yellow-800 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dicas para Melhorar seu Ranking\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mt-2 text-xs text-yellow-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Responda aos coment\\xe1rios para aumentar o engajamento geral.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Crie conte\\xfado que incentive o compartilhamento e salvamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500\",\n                    children: [\n                        \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                        new Date().toLocaleDateString('pt-BR', {\n                            day: '2-digit',\n                            month: '2-digit',\n                            year: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 666,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para restaurantes - Estilo Champions League\n    if (userRole === 'restaurant' && competitors.length > 0) {\n        console.log('Renderizando para restaurante com competidores:', competitors);\n        // Calcular pontuação total para cada influenciador\n        const totalPointsMax = Math.max(...competitors.map((inf)=>inf.total_points));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-900 to-indigo-900 p-5 relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-[url('https://www.uefa.com/contentassets/c9b1b12d4c074c3ca3f03a7fdb018a2f/ucl-2021-24-starball-on-pitch-min.jpg')] bg-cover bg-center\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold flex items-center text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                className: \"mr-3 text-yellow-300 text-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block\",\n                                                        children: \"Champions da Campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-normal text-blue-200\",\n                                                        children: \"Ranking de Influenciadores\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: competitors.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200\",\n                                                    children: \"influenciadores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 73\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RankingNotifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            campaignId: campaignId,\n                                            userId: userId,\n                                            userRole: userRole\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 692,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 9\n                }, this),\n                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-100 text-yellow-800 p-3 text-sm border-b border-yellow-200 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2 text-yellow-600\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Exibindo dados de exemplo. Os influenciadores s\\xe3o reais, mas as m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 713,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-hidden rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gray-100 to-blue-50 p-3 grid grid-cols-12 text-xs font-semibold border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 text-center text-gray-600\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-3 text-gray-600\",\n                                            children: \"Influenciador\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-red-500\",\n                                            children: \"Curtidas\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-blue-500\",\n                                            children: \"Coment\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-green-500\",\n                                            children: \"Salvamentos\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-700 text-white py-1.5 px-3 rounded-lg shadow-sm inline-block font-bold\",\n                                                children: \"PONTOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y divide-gray-100\",\n                                    children: competitors.map((influencer, index)=>{\n                                        // Determinar cores e estilos com base na posição\n                                        const isTop = index < 3;\n                                        const rankStyles = [\n                                            'bg-gradient-to-r from-yellow-500 to-yellow-400 text-white',\n                                            'bg-gradient-to-r from-gray-400 to-gray-300 text-white',\n                                            'bg-gradient-to-r from-amber-700 to-amber-600 text-white',\n                                            'bg-blue-50 text-blue-800' // Demais posições\n                                        ];\n                                        // Calcular porcentagem para a barra de progresso\n                                        const progressPercent = Math.round(influencer.total_points / totalPointsMax * 100);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative p-3 grid grid-cols-12 items-center text-sm \".concat(index < 3 ? 'bg-blue-50/50' : 'hover:bg-gray-50', \" \").concat(index === 0 ? 'border-l-4 border-yellow-400' : index === 1 ? 'border-l-4 border-gray-400' : index === 2 ? 'border-l-4 border-amber-700' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-0 top-0 h-full z-0 \".concat(index === 0 ? 'bg-yellow-50' : index === 1 ? 'bg-gray-50' : index === 2 ? 'bg-amber-50' : 'bg-blue-50'),\n                                                    style: {\n                                                        width: \"\".concat(progressPercent, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-1 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full flex items-center justify-center font-bold \".concat(rankStyles[index < 3 ? index : 3]),\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: influencer.influencer_name || \"Influencer \".concat(index + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"@\",\n                                                                    influencer.username || \"influencer\".concat(index + 1)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-red-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 783,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_likes / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-blue-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_comments\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_comments / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 793,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-green-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 808,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_saves\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_saves / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(index < 3 ? 'bg-gradient-to-r from-blue-600 to-blue-700' : 'bg-blue-600', \" rounded-lg py-2 px-4 inline-block shadow-md border \").concat(index === 0 ? 'border-yellow-300' : index === 1 ? 'border-gray-300' : index === 2 ? 'border-amber-300' : 'border-blue-500'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-white text-xl\",\n                                                                children: influencer.total_points\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 820,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-100 flex items-center justify-center mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChartLine, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 822,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    influencer.engagement_rate.toFixed(1),\n                                                                    \"% taxa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            influencer.previous_rank && influencer.rank && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 text-xs\",\n                                                                children: influencer.previous_rank > influencer.rank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-green-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"+\",\n                                                                        influencer.previous_rank - influencer.rank\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 31\n                                                                }, this) : influencer.previous_rank < influencer.rank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        influencer.previous_rank - influencer.rank\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 841,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"0\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, influencer.influencer_id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-5 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-5 w-5 mr-2 text-blue-300\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"An\\xe1lise de Desempenho da Campanha\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 857,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Engajamento Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        competitors.reduce((sum, inf)=>sum + inf.total_points, 0),\n                                                        \" pts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Salvamentos\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: competitors.reduce((sum, inf)=>sum + inf.total_saves, 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Taxa M\\xe9dia\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        (competitors.reduce((sum, inf)=>sum + inf.engagement_rate, 0) / competitors.length).toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-100 mb-4\",\n                                    children: [\n                                        \"Os salvamentos representam \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-white\",\n                                            children: [\n                                                Math.round(competitors.reduce((sum, inf)=>sum + inf.total_saves, 0) / competitors.reduce((sum, inf)=>sum + inf.total_likes + inf.total_comments + inf.total_saves, 0) * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 42\n                                        }, this),\n                                        \" do engajamento total desta campanha, demonstrando alto interesse do p\\xfablico em guardar o conte\\xfado para refer\\xeancia futura.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-3 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"text-sm font-semibold mb-2 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1 text-blue-300\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"F\\xf3rmula de C\\xe1lculo de Pontos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                            className: \"text-red-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Curtidas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"1 ponto cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                            className: \"text-blue-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Coment\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"2 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 899,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                            className: \"text-green-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Salvamentos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"3 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 907,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                            className: \"text-yellow-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"Soma ponderada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 856,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 722,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                                new Date().toLocaleDateString('pt-BR', {\n                                    day: '2-digit',\n                                    month: '2-digit',\n                                    year: 'numeric',\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                    className: \"text-yellow-500 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Champions League Ranking\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 930,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 920,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 686,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 \".concat(className),\n        children: isUsingMockData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 text-yellow-800 p-3 text-sm rounded-lg mb-4 inline-flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-5 w-5 mr-2 text-yellow-600\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 946,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 945,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Exibindo dados de exemplo. As m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 948,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                lineNumber: 944,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 943,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: \"Nenhum dado de ranking dispon\\xedvel para esta campanha.\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 952,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n        lineNumber: 941,\n        columnNumber: 5\n    }, this);\n}\n_s(RankingHighlightFallback, \"WGLfjIgcazs9KbS3S754fmFbURI=\");\n_c = RankingHighlightFallback;\nvar _c;\n$RefreshReg$(_c, \"RankingHighlightFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx\n"));

/***/ })

});