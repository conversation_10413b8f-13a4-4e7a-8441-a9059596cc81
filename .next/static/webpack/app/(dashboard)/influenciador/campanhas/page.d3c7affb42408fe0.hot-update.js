"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx":
/*!*************************************************************!*\
  !*** ./src/components/premium/RankingHighlightFallback.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RankingHighlightFallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowDown,FaArrowUp,FaBookmark,FaChartLine,FaComment,FaHeart,FaMinus,FaTrophy!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _RankingNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RankingNotifications */ \"(app-pages-browser)/./src/components/premium/RankingNotifications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction RankingHighlightFallback(param) {\n    let { campaignId, userId, userRole, className = '' } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rankingData, setRankingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [competitors, setCompetitors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUsingMockData, setIsUsingMockData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RankingHighlightFallback.useEffect\": ()=>{\n            async function fetchRankingData() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('RankingHighlightFallback - Iniciando busca de dados para:', {\n                        campaignId,\n                        userId,\n                        userRole\n                    });\n                    // Buscar dados diretamente das tabelas\n                    if (userRole === 'influencer') {\n                        // Para influenciadores, buscar dados básicos\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              status,\\n              created_at\\n            \").eq('campaign_id', campaignId);\n                        if (ciError) throw ciError;\n                        // Buscar dados do influenciador atual\n                        const userInfluencer = campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        if (!userInfluencer) {\n                            // Se o usuário não está na campanha, retornar dados vazios em vez de erro\n                            console.log('Usuário não encontrado na campanha, retornando dados vazios');\n                            return {\n                                userRank: null,\n                                userPoints: 0,\n                                totalParticipants: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 0,\n                                topInfluencers: [],\n                                userPreviousRank: null,\n                                userRankChange: 0\n                            };\n                        }\n                        // Buscar perfis dos influenciadores para obter nomes e usernames\n                        const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                        if (profilesError) throw profilesError;\n                        // Criar dados para o influenciador atual\n                        const userProfile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        const rankingData = {\n                            influencer_id: userId,\n                            rank: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            previous_rank: null,\n                            total_influencers: (campaignInfluencers === null || campaignInfluencers === void 0 ? void 0 : campaignInfluencers.length) || 1,\n                            total_likes: 0,\n                            total_comments: 0,\n                            total_saves: 0,\n                            total_points: 0,\n                            engagement_rate: \"0.0\"\n                        };\n                        setRankingData(rankingData);\n                        // Criar lista de competidores com dados reais\n                        const competitors = campaignInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                var _profile_profile_data;\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || 'username';\n                                return {\n                                    influencer_id: ci.influencer_id,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                    username: username,\n                                    rank: index + 1,\n                                    total_likes: 0,\n                                    total_comments: 0,\n                                    total_saves: 0,\n                                    total_points: 0,\n                                    engagement_rate: 0.0\n                                };\n                            }\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                        // Ordenar por ranking\n                        competitors.sort({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>a.rank - b.rank\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                        // Limitar a 5 competidores\n                        const topCompetitors = competitors.slice(0, 5);\n                        // Adicionar o influenciador atual se não estiver nos top 5\n                        const userIncluded = topCompetitors.some({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\": (c)=>c.influencer_id === userId\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData.userIncluded\"]);\n                        if (!userIncluded) {\n                            var _userProfile_profile_data;\n                            topCompetitors.push({\n                                influencer_id: userId,\n                                influencer_name: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.full_name) || 'Você',\n                                username: (userProfile === null || userProfile === void 0 ? void 0 : (_userProfile_profile_data = userProfile.profile_data) === null || _userProfile_profile_data === void 0 ? void 0 : _userProfile_profile_data.instagram_username) || 'seu_username',\n                                rank: rankingData.rank,\n                                total_likes: rankingData.total_likes,\n                                total_comments: rankingData.total_comments,\n                                total_saves: rankingData.total_saves,\n                                total_points: rankingData.total_points,\n                                engagement_rate: parseFloat(rankingData.engagement_rate)\n                            });\n                        }\n                        setCompetitors(topCompetitors);\n                    } else {\n                        // Para restaurantes, primeiro buscar todos os influenciadores aceitos na campanha\n                        console.log('Buscando influenciadores aceitos para a campanha:', campaignId);\n                        const { data: acceptedInfluencers, error: aiError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              status\\n            \").eq('campaign_id', campaignId).eq('status', 'accepted');\n                        if (aiError) {\n                            console.error('Erro ao buscar influenciadores aceitos:', aiError);\n                            throw aiError;\n                        }\n                        console.log('Influenciadores aceitos encontrados:', acceptedInfluencers);\n                        // Se não houver influenciadores aceitos, mostrar mensagem apropriada\n                        if (!acceptedInfluencers || acceptedInfluencers.length === 0) {\n                            console.log('Nenhum influenciador aceito encontrado para esta campanha');\n                            // Vamos tentar buscar novamente sem filtrar por status para debug\n                            const { data: allInfluencers, error: allError } = await supabase.from('campaign_influencers').select(\"\\n                id,\\n                influencer_id,\\n                status\\n              \").eq('campaign_id', campaignId);\n                            console.log('Todos os influenciadores da campanha (debug):', allInfluencers);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Se existem influenciadores mas nenhum com status 'accepted', vamos usar todos\n                                console.log('Usando todos os influenciadores disponíveis como fallback');\n                                // Continuar com todos os influenciadores disponíveis\n                                setError(null);\n                            } else {\n                                setError('Nenhum influenciador aceito encontrado para esta campanha');\n                                setLoading(false);\n                                return;\n                            }\n                        }\n                        // Buscar dados de métricas para os influenciadores aceitos\n                        console.log('Buscando métricas para os influenciadores aceitos');\n                        let influencerIds = acceptedInfluencers ? acceptedInfluencers.map({\n                            \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                        }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]) : [];\n                        // Se não temos influenciadores aceitos, buscar todos os influenciadores da campanha\n                        if (influencerIds.length === 0) {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                influencerIds = allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                console.log('Usando todos os influenciadores disponíveis:', influencerIds);\n                            }\n                        }\n                        const { data: campaignInfluencers, error: ciError } = await supabase.from('campaign_influencers').select(\"\\n              id,\\n              influencer_id,\\n              campaign_id,\\n              current_rank,\\n              previous_rank,\\n              total_points,\\n              previous_total_likes,\\n              previous_total_comments,\\n              previous_total_saves,\\n              total_engagement,\\n              status\\n            \").eq('campaign_id', campaignId).in('influencer_id', influencerIds).order('total_points', {\n                            ascending: false\n                        });\n                        if (ciError) {\n                            console.error('Erro ao buscar métricas dos influenciadores:', ciError);\n                            throw ciError;\n                        }\n                        console.log('Dados de métricas dos influenciadores:', campaignInfluencers);\n                        if (!campaignInfluencers || campaignInfluencers.length === 0) {\n                            // Se não houver dados reais, criar dados de exemplo\n                            console.warn('Nenhum dado real de métricas encontrado para os influenciadores. Usando dados de exemplo.');\n                            setIsUsingMockData(true);\n                            // Buscar perfis dos influenciadores para usar nomes reais\n                            // Usar todos os influenciadores disponíveis, não apenas os aceitos\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select('influencer_id').eq('campaign_id', campaignId);\n                            const influencerIds = (allInfluencers === null || allInfluencers === void 0 ? void 0 : allInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"])) || [];\n                            console.log('Usando todos os influenciadores disponíveis para dados de exemplo:', influencerIds);\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', influencerIds);\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis dos influenciadores:', profilesError);\n                            // Continuar mesmo com erro\n                            }\n                            console.log('Perfis dos influenciadores para dados de exemplo:', profiles);\n                            // Criar dados de exemplo com nomes reais, se disponíveis\n                            const mockTopInfluencers = [];\n                            // Usar os IDs de influenciadores já obtidos anteriormente\n                            const maxInfluencers = Math.min(5, influencerIds.length || 1);\n                            for(let i = 0; i < maxInfluencers; i++){\n                                var _profile_profile_data;\n                                const influencerId = influencerIds[i] || 'mock-id';\n                                const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                mockTopInfluencers.push({\n                                    influencer_id: influencerId,\n                                    influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                    username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || \"influencer\".concat(i + 1),\n                                    rank: i + 1,\n                                    total_likes: Math.floor(Math.random() * 1000) + 100,\n                                    total_comments: Math.floor(Math.random() * 200) + 20,\n                                    total_saves: Math.floor(Math.random() * 100) + 10,\n                                    total_points: Math.floor(Math.random() * 2000) + 200,\n                                    engagement_rate: Math.random() * 5 + 1,\n                                    is_mock_data: true\n                                });\n                            }\n                            setCompetitors(mockTopInfluencers);\n                        } else {\n                            // Buscar perfis dos influenciadores para obter nomes e usernames\n                            console.log('Buscando perfis para os influenciadores:', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.influencer_id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (profilesError) {\n                                console.error('Erro ao buscar perfis:', profilesError);\n                                throw profilesError;\n                            }\n                            console.log('Perfis encontrados:', profiles);\n                            // Buscar posts dos influenciadores para obter métricas mais recentes\n                            console.log('Buscando posts dos influenciadores');\n                            const { data: posts, error: postsError } = await supabase.from('posts').select(\"\\n                id,\\n                campaign_influencer_id,\\n                likes_count,\\n                comments_count,\\n                saves_count,\\n                engagement_rate\\n              \").in('campaign_influencer_id', campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (ci)=>ci.id\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                            if (postsError) {\n                                console.error('Erro ao buscar posts:', postsError);\n                            // Não lançar erro, apenas registrar - usaremos os dados do campaign_influencers\n                            }\n                            console.log('Posts encontrados:', posts);\n                            // Criar lista de competidores com dados reais\n                            const competitors = campaignInfluencers.map({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (ci, index)=>{\n                                    var _profile_profile_data, _ci_total_engagement;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.id === ci.influencer_id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                                    console.log(\"Perfil para influenciador \".concat(ci.influencer_id, \":\"), profile);\n                                    // Buscar posts deste influenciador para esta campanha\n                                    const influencerPosts = (posts === null || posts === void 0 ? void 0 : posts.filter({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors\": (p)=>p.campaign_influencer_id === ci.id\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"])) || [];\n                                    console.log(\"Posts para influenciador \".concat(ci.influencer_id, \":\"), influencerPosts);\n                                    // Calcular totais de métricas dos posts\n                                    const totalLikesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\": (sum, post)=>sum + (post.likes_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalLikesFromPosts\"], 0);\n                                    const totalCommentsFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\": (sum, post)=>sum + (post.comments_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalCommentsFromPosts\"], 0);\n                                    const totalSavesFromPosts = influencerPosts.reduce({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\": (sum, post)=>sum + (post.saves_count || 0)\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors.totalSavesFromPosts\"], 0);\n                                    // Usar dados dos posts se disponíveis, caso contrário usar dados do campaign_influencers\n                                    const totalLikes = totalLikesFromPosts > 0 ? totalLikesFromPosts : ci.previous_total_likes || 0;\n                                    const totalComments = totalCommentsFromPosts > 0 ? totalCommentsFromPosts : ci.previous_total_comments || 0;\n                                    const totalSaves = totalSavesFromPosts > 0 ? totalSavesFromPosts : ci.previous_total_saves || 0;\n                                    // Calcular pontos com base nas métricas (fórmula simplificada)\n                                    const calculatedPoints = totalLikes + totalComments * 2 + totalSaves * 3;\n                                    // Usar o ranking atual do banco de dados, se disponível\n                                    const rank = ci.current_rank || index + 1;\n                                    // Obter username do perfil ou gerar um padrão\n                                    const username = (profile === null || profile === void 0 ? void 0 : (_profile_profile_data = profile.profile_data) === null || _profile_profile_data === void 0 ? void 0 : _profile_profile_data.instagram_username) || ((profile === null || profile === void 0 ? void 0 : profile.profile_data) && 'instagram_username' in profile.profile_data ? profile.profile_data.instagram_username : \"influencer\".concat(index + 1));\n                                    return {\n                                        influencer_id: ci.influencer_id,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(index + 1),\n                                        username: username,\n                                        rank: rank,\n                                        total_likes: totalLikes,\n                                        total_comments: totalComments,\n                                        total_saves: totalSaves,\n                                        total_points: ci.total_points || calculatedPoints || 0,\n                                        engagement_rate: parseFloat(((_ci_total_engagement = ci.total_engagement) === null || _ci_total_engagement === void 0 ? void 0 : _ci_total_engagement.toString()) || '0'),\n                                        has_real_data: influencerPosts.length > 0 || Boolean(ci.previous_total_likes)\n                                    };\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData.competitors\"]);\n                            console.log('Competidores criados com dados reais:', competitors);\n                            // Ordenar por pontos (decrescente) e depois por ranking (crescente)\n                            competitors.sort({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (a, b)=>{\n                                    if (b.total_points !== a.total_points) {\n                                        return b.total_points - a.total_points;\n                                    }\n                                    return a.rank - b.rank;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            // Atualizar ranks com base na ordenação\n                            competitors.forEach({\n                                \"RankingHighlightFallback.useEffect.fetchRankingData\": (comp, idx)=>{\n                                    comp.rank = idx + 1;\n                                }\n                            }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                            console.log('Competidores ordenados:', competitors);\n                            // Limitar a 5 competidores\n                            setCompetitors(competitors.slice(0, 5));\n                        }\n                    }\n                } catch (err) {\n                    console.error('Erro ao buscar dados de ranking:', err);\n                    setError('Não foi possível carregar os dados de ranking');\n                    // Em caso de erro, criar dados de exemplo\n                    if (userRole === 'restaurant') {\n                        console.warn('Erro ao buscar dados reais. Usando dados de exemplo como fallback.');\n                        setIsUsingMockData(true);\n                        // Tentar buscar todos os influenciadores para usar nomes reais\n                        try {\n                            const { data: allInfluencers } = await supabase.from('campaign_influencers').select(\"id, influencer_id, status\").eq('campaign_id', campaignId);\n                            if (allInfluencers && allInfluencers.length > 0) {\n                                // Buscar perfis de todos os influenciadores\n                                const { data: profiles } = await supabase.from('profiles').select('id, full_name, profile_data').in('id', allInfluencers.map({\n                                    \"RankingHighlightFallback.useEffect.fetchRankingData\": (ai)=>ai.influencer_id\n                                }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]));\n                                // Criar dados de exemplo com nomes reais\n                                const mockTopInfluencers = [];\n                                for(let i = 0; i < Math.min(5, allInfluencers.length); i++){\n                                    var _profile_profile_data1;\n                                    const influencerId = allInfluencers[i].influencer_id;\n                                    const profile = profiles === null || profiles === void 0 ? void 0 : profiles.find({\n                                        \"RankingHighlightFallback.useEffect.fetchRankingData\": (p)=>p.id === influencerId\n                                    }[\"RankingHighlightFallback.useEffect.fetchRankingData\"]);\n                                    mockTopInfluencers.push({\n                                        influencer_id: influencerId,\n                                        influencer_name: (profile === null || profile === void 0 ? void 0 : profile.full_name) || \"Influencer \".concat(i + 1),\n                                        username: (profile === null || profile === void 0 ? void 0 : (_profile_profile_data1 = profile.profile_data) === null || _profile_profile_data1 === void 0 ? void 0 : _profile_profile_data1.instagram_username) || \"influencer\".concat(i + 1),\n                                        rank: i + 1,\n                                        total_likes: Math.floor(Math.random() * 1000) + 100,\n                                        total_comments: Math.floor(Math.random() * 200) + 20,\n                                        total_saves: Math.floor(Math.random() * 100) + 10,\n                                        total_points: Math.floor(Math.random() * 2000) + 200,\n                                        engagement_rate: Math.random() * 5 + 1,\n                                        is_mock_data: true\n                                    });\n                                }\n                                setCompetitors(mockTopInfluencers);\n                                return;\n                            }\n                        } catch (fallbackErr) {\n                            console.error('Erro ao tentar buscar dados para fallback:', fallbackErr);\n                        }\n                        // Se não conseguir dados reais, usar dados completamente fictícios\n                        const mockTopInfluencers = [];\n                        for(let i = 0; i < 5; i++){\n                            mockTopInfluencers.push({\n                                influencer_id: \"mock-id-\".concat(i),\n                                influencer_name: \"Influencer \".concat(i + 1),\n                                username: \"influencer\".concat(i + 1),\n                                rank: i + 1,\n                                total_likes: Math.floor(Math.random() * 1000) + 100,\n                                total_comments: Math.floor(Math.random() * 200) + 20,\n                                total_saves: Math.floor(Math.random() * 100) + 10,\n                                total_points: Math.floor(Math.random() * 2000) + 200,\n                                engagement_rate: Math.random() * 5 + 1,\n                                is_mock_data: true\n                            });\n                        }\n                        setCompetitors(mockTopInfluencers);\n                    }\n                } finally{\n                    setLoading(false);\n                }\n            }\n            if (campaignId && userId) {\n                fetchRankingData();\n            }\n        }\n    }[\"RankingHighlightFallback.useEffect\"], [\n        campaignId,\n        userId,\n        userRole,\n        supabase\n    ]);\n    // Renderização para estado de carregamento\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg p-6 animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 bg-gray-200 rounded w-1/3\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-24 bg-gray-200 rounded-lg mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-8 bg-gray-200 rounded-full mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 460,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para estado de erro\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg \".concat(className),\n            children: [\n                error,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium\",\n                        children: \"Tentar novamente\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 481,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para influenciadores\n    if (userRole === 'influencer' && rankingData) {\n        const { rank, total_influencers, previous_rank, engagement_rate, total_points, total_saves } = rankingData;\n        // Calcular mudança de posição\n        const rankChange = previous_rank ? previous_rank - rank : 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                        className: \"mr-2 text-yellow-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Seu Ranking na Campanha\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm bg-white/20 px-2 py-1 rounded\",\n                                children: [\n                                    total_influencers,\n                                    \" participantes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 bg-blue-50 border-b border-blue-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl \".concat(rank === 1 ? 'bg-yellow-500' : rank === 2 ? 'bg-gray-400' : rank === 3 ? 'bg-amber-700' : 'bg-blue-600'),\n                                        children: [\n                                            rank,\n                                            \"\\xba\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-700\",\n                                                children: \"Sua posi\\xe7\\xe3o atual\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-blue-900\",\n                                                children: rank === 1 ? '🏆 Primeiro Lugar!' : rank === 2 ? '🥈 Segundo Lugar!' : rank === 3 ? '🥉 Terceiro Lugar!' : \"\".concat(rank, \"\\xba Lugar\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mt-1\",\n                                                children: rankChange > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Subiu \",\n                                                        rankChange,\n                                                        \" \",\n                                                        rankChange === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this) : rankChange < 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Desceu \",\n                                                        Math.abs(rankChange),\n                                                        \" \",\n                                                        Math.abs(rankChange) === 1 ? 'posição' : 'posições'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600 flex items-center text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" Manteve a posi\\xe7\\xe3o\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700\",\n                                        children: \"Taxa de Engajamento\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-900\",\n                                        children: [\n                                            engagement_rate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-end mt-1 space-x-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                                        className: \"mr-1 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_points,\n                                                                    \" pts\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de pontos acumulados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    total_saves\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Total de salvamentos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-3\",\n                            children: \"Seus Competidores Diretos\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: competitors.map((competitor)=>{\n                                const isCurrentUser = competitor.influencer_id === userId;\n                                const isAhead = competitor.rank < rankingData.rank;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 rounded-lg \".concat(isCurrentUser ? 'bg-blue-50 border border-blue-200' : isAhead ? 'bg-red-50' : 'bg-green-50'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center text-white font-bold \".concat(competitor.rank === 1 ? 'bg-yellow-500' : competitor.rank === 2 ? 'bg-gray-400' : competitor.rank === 3 ? 'bg-amber-700' : 'bg-gray-600'),\n                                                    children: competitor.rank\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium \".concat(isCurrentUser ? 'text-blue-700' : 'text-gray-800'),\n                                                            children: [\n                                                                competitor.influencer_name,\n                                                                \" \",\n                                                                isCurrentUser && '(Você)'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"@\",\n                                                                competitor.username\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-800\",\n                                                    children: [\n                                                        competitor.engagement_rate.toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2 text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-red-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_likes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-blue-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_comments\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex items-center text-green-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                    className: \"mr-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                competitor.total_saves\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, competitor.influencer_id, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-yellow-800 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 mr-1\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Dicas para Melhorar seu Ranking\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mt-2 text-xs text-yellow-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Responda aos coment\\xe1rios para aumentar o engajamento geral.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-3 w-3 mr-1 mt-0.5 flex-shrink-0\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Crie conte\\xfado que incentive o compartilhamento e salvamento.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500\",\n                    children: [\n                        \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                        new Date().toLocaleDateString('pt-BR', {\n                            day: '2-digit',\n                            month: '2-digit',\n                            year: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 672,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 503,\n            columnNumber: 7\n        }, this);\n    }\n    // Renderização para restaurantes - Estilo Champions League\n    if (userRole === 'restaurant' && competitors.length > 0) {\n        console.log('Renderizando para restaurante com competidores:', competitors);\n        // Calcular pontuação total para cada influenciador\n        const totalPointsMax = Math.max(...competitors.map((inf)=>inf.total_points));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl overflow-hidden \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-900 to-indigo-900 p-5 relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full opacity-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full bg-[url('https://www.uefa.com/contentassets/c9b1b12d4c074c3ca3f03a7fdb018a2f/ucl-2021-24-starball-on-pitch-min.jpg')] bg-cover bg-center\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold flex items-center text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                className: \"mr-3 text-yellow-300 text-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"block\",\n                                                        children: \"Champions da Campanha\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-normal text-blue-200\",\n                                                        children: \"Ranking de Influenciadores\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold\",\n                                                    children: competitors.length\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-200\",\n                                                    children: \"influenciadores\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 710,\n                                                    columnNumber: 73\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RankingNotifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            campaignId: campaignId,\n                                            userId: userId,\n                                            userRole: userRole\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 694,\n                    columnNumber: 9\n                }, this),\n                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-100 text-yellow-800 p-3 text-sm border-b border-yellow-200 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            className: \"h-5 w-5 mr-2 text-yellow-600\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Exibindo dados de exemplo. Os influenciadores s\\xe3o reais, mas as m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 723,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-hidden rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-gray-100 to-blue-50 p-3 grid grid-cols-12 text-xs font-semibold border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 text-center text-gray-600\",\n                                            children: \"#\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-3 text-gray-600\",\n                                            children: \"Influenciador\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-red-500\",\n                                            children: \"Curtidas\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-blue-500\",\n                                            children: \"Coment\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center text-green-500\",\n                                            children: \"Salvamentos\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-700 text-white py-1.5 px-3 rounded-lg shadow-sm inline-block font-bold\",\n                                                children: \"PONTOS\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y divide-gray-100\",\n                                    children: competitors.map((influencer, index)=>{\n                                        // Determinar cores e estilos com base na posição\n                                        const isTop = index < 3;\n                                        const rankStyles = [\n                                            'bg-gradient-to-r from-yellow-500 to-yellow-400 text-white',\n                                            'bg-gradient-to-r from-gray-400 to-gray-300 text-white',\n                                            'bg-gradient-to-r from-amber-700 to-amber-600 text-white',\n                                            'bg-blue-50 text-blue-800' // Demais posições\n                                        ];\n                                        // Calcular porcentagem para a barra de progresso\n                                        const progressPercent = Math.round(influencer.total_points / totalPointsMax * 100);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative p-3 grid grid-cols-12 items-center text-sm \".concat(index < 3 ? 'bg-blue-50/50' : 'hover:bg-gray-50', \" \").concat(index === 0 ? 'border-l-4 border-yellow-400' : index === 1 ? 'border-l-4 border-gray-400' : index === 2 ? 'border-l-4 border-amber-700' : ''),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-0 top-0 h-full z-0 \".concat(index === 0 ? 'bg-yellow-50' : index === 1 ? 'bg-gray-50' : index === 2 ? 'bg-amber-50' : 'bg-blue-50'),\n                                                    style: {\n                                                        width: \"\".concat(progressPercent, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-1 flex justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full flex items-center justify-center font-bold \".concat(rankStyles[index < 3 ? index : 3]),\n                                                        children: index + 1\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-3 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: influencer.influencer_name || \"Influencer \".concat(index + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"@\",\n                                                                    influencer.username || \"influencer\".concat(index + 1)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-red-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 789,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_likes / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-blue-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 801,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_comments\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 802,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_comments / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-green-500 font-semibold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 814,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: influencer.total_saves\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 815,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 813,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    Math.round(influencer.total_saves / (influencer.total_points || 1) * 100) || 0,\n                                                                    \"% do total\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 col-span-2 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(index < 3 ? 'bg-gradient-to-r from-blue-600 to-blue-700' : 'bg-blue-600', \" rounded-lg py-2 px-4 inline-block shadow-md border \").concat(index === 0 ? 'border-yellow-300' : index === 1 ? 'border-gray-300' : index === 2 ? 'border-amber-300' : 'border-blue-500'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-white text-xl\",\n                                                                children: influencer.total_points\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-100 flex items-center justify-center mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChartLine, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    influencer.engagement_rate.toFixed(1),\n                                                                    \"% taxa\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            influencer.previous_rank && influencer.rank && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 text-xs\",\n                                                                children: influencer.previous_rank > influencer.rank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-green-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowUp, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 837,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"+\",\n                                                                        influencer.previous_rank - influencer.rank\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 31\n                                                                }, this) : influencer.previous_rank < influencer.rank ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaArrowDown, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 842,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        influencer.previous_rank - influencer.rank\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaMinus, {\n                                                                            className: \"mr-1\",\n                                                                            size: 10\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"0\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                                lineNumber: 834,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, influencer.influencer_id, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-5 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold flex items-center mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-5 w-5 mr-2 text-blue-300\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"An\\xe1lise de Desempenho da Campanha\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Engajamento Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        competitors.reduce((sum, inf)=>sum + inf.total_points, 0),\n                                                        \" pts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Salvamentos\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: competitors.reduce((sum, inf)=>sum + inf.total_saves, 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 876,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white/10 backdrop-blur-sm rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-blue-200\",\n                                                    children: \"Taxa M\\xe9dia\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: [\n                                                        (competitors.reduce((sum, inf)=>sum + inf.engagement_rate, 0) / competitors.length).toFixed(1),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-100 mb-4\",\n                                    children: [\n                                        \"Os salvamentos representam \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-white\",\n                                            children: [\n                                                Math.round(competitors.reduce((sum, inf)=>sum + inf.total_saves, 0) / competitors.reduce((sum, inf)=>sum + inf.total_likes + inf.total_comments + inf.total_saves, 0) * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 42\n                                        }, this),\n                                        \" do engajamento total desta campanha, demonstrando alto interesse do p\\xfablico em guardar o conte\\xfado para refer\\xeancia futura.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/5 rounded-lg p-3 border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"text-sm font-semibold mb-2 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"h-4 w-4 mr-1 text-blue-300\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"F\\xf3rmula de C\\xe1lculo de Pontos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-2 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {\n                                                            className: \"text-red-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Curtidas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"1 ponto cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaComment, {\n                                                            className: \"text-blue-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Coment\\xe1rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 907,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"2 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBookmark, {\n                                                            className: \"text-green-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Salvamentos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"3 pontos cada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-500/20 p-2 rounded flex flex-col items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                                            className: \"text-yellow-400 mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Total\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-200\",\n                                                            children: \"Soma ponderada\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                            lineNumber: 918,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 862,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"\\xdaltima atualiza\\xe7\\xe3o: \",\n                                new Date().toLocaleDateString('pt-BR', {\n                                    day: '2-digit',\n                                    month: '2-digit',\n                                    year: 'numeric',\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowDown_FaArrowUp_FaBookmark_FaChartLine_FaComment_FaHeart_FaMinus_FaTrophy_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTrophy, {\n                                    className: \"text-yellow-500 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Champions League Ranking\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                    lineNumber: 926,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 692,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-lg p-6 \".concat(className),\n        children: isUsingMockData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 text-yellow-800 p-3 text-sm rounded-lg mb-4 inline-flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-5 w-5 mr-2 text-yellow-600\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 951,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Exibindo dados de exemplo. As m\\xe9tricas s\\xe3o simuladas para fins de visualiza\\xe7\\xe3o.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                        lineNumber: 954,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n                lineNumber: 950,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 949,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: \"Nenhum dado de ranking dispon\\xedvel para esta campanha.\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n            lineNumber: 958,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/premium/RankingHighlightFallback.tsx\",\n        lineNumber: 947,\n        columnNumber: 5\n    }, this);\n}\n_s(RankingHighlightFallback, \"WGLfjIgcazs9KbS3S754fmFbURI=\");\n_c = RankingHighlightFallback;\nvar _c;\n$RefreshReg$(_c, \"RankingHighlightFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/premium/RankingHighlightFallback.tsx\n"));

/***/ })

});