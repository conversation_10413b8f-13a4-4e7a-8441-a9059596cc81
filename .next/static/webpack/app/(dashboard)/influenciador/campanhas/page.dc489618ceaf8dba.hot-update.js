"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/influenciador/campanhas/page",{

/***/ "(app-pages-browser)/./src/services/campaignService.ts":
/*!*****************************************!*\
  !*** ./src/services/campaignService.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   campaignService: () => (/* binding */ campaignService)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n\n// Real campaign service that queries the Supabase database\nconst campaignService = {\n    // Fetch campaigns for an influencer\n    async getInfluencerCampaigns (influencerId) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            // Query campaign_participants table to get campaigns the influencer is participating in\n            const { data: participantsData, error: participantsError } = await supabase.from('campaign_participants').select(\"\\n          id, \\n          status, \\n          campaigns(\\n            id, \\n            name, \\n            description, \\n            start_date, \\n            end_date, \\n            status, \\n            restaurant_id, \\n            restaurant_profiles(id, business_name)\\n          )\\n        \").eq('profile_id', influencerId);\n            if (participantsError) {\n                console.error('Error fetching campaign participants:', participantsError);\n                return [];\n            }\n            if (!participantsData || participantsData.length === 0) {\n                return [];\n            }\n            // Transform the data to match the expected format\n            const campaignInfluencers = await Promise.all(participantsData.map(async (participant)=>{\n                // Get campaign details\n                const campaignData = participant.campaigns;\n                // Handle case where campaign data might be an array\n                const campaign = Array.isArray(campaignData) ? campaignData[0] : campaignData;\n                if (!campaign) {\n                    return null;\n                }\n                // Get restaurant details\n                const restaurantData = campaign.restaurant_profiles;\n                const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n                // Get total points for this campaign participation\n                const { data: pointsData, error: pointsError } = await supabase.from('points_history').select('points').eq('campaign_participant_id', participant.id);\n                let totalPoints = 0;\n                if (!pointsError && pointsData) {\n                    totalPoints = pointsData.reduce((sum, item)=>sum + (item.points || 0), 0);\n                }\n                // Get video status\n                let videoStatus;\n                const { data: videoData, error: videoError } = await supabase.from('campaign_videos').select('status').eq('campaign_participant_id', participant.id).order('created_at', {\n                    ascending: false\n                }).limit(1).single();\n                if (!videoError && videoData) {\n                    videoStatus = videoData.status;\n                }\n                // Get rank\n                let rank;\n                // This would need to be implemented based on your ranking logic\n                // For example, you might have a separate table for rankings or calculate it on the fly\n                return {\n                    id: participant.id,\n                    campaign_id: campaign.id,\n                    influencer_id: influencerId,\n                    status: participant.status,\n                    total_points: totalPoints,\n                    video_status: videoStatus,\n                    rank,\n                    campaigns: {\n                        id: campaign.id,\n                        name: campaign.name,\n                        description: campaign.description,\n                        start_date: campaign.start_date,\n                        end_date: campaign.end_date,\n                        status: campaign.status,\n                        restaurant_id: campaign.restaurant_id,\n                        restaurants: restaurant ? {\n                            id: restaurant.id,\n                            name: restaurant.business_name\n                        } : null\n                    }\n                };\n            }));\n            // Filter out any null values\n            return campaignInfluencers.filter(Boolean);\n        } catch (error) {\n            console.error('Error in getInfluencerCampaigns:', error);\n            return [];\n        }\n    },\n    // Fetch available campaigns\n    async getAvailableCampaigns (filters) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            console.log('Fetching available campaigns with filters:', filters);\n            let query = supabase.from('campaigns').select(\"\\n          id,\\n          name,\\n          description,\\n          start_date,\\n          end_date,\\n          status,\\n          restaurant_id,\\n          restaurant_profiles(id, business_name, city, state),\\n          requirements\\n        \");\n            // Remove the status filter to see all campaigns\n            // .eq('status', 'active');\n            // Apply filters if provided\n            if (filters === null || filters === void 0 ? void 0 : filters.city) {\n                query = query.eq('city', filters.city);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.state) {\n                query = query.eq('state', filters.state);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error('Error fetching available campaigns:', error);\n                return [];\n            }\n            console.log('Raw campaigns data from database:', data);\n            if (!data || data.length === 0) {\n                console.log('No campaigns found with the current filters');\n                return [];\n            }\n            // Transform the data to match the expected format\n            const campaigns = data.map((campaign)=>{\n                const restaurantData = campaign.restaurant_profiles;\n                const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n                // Extract hashtags and mentions from requirements\n                const requirements = campaign.requirements || {};\n                const hashtags = requirements.hashtags || [];\n                const mentions = requirements.mentions || [];\n                return {\n                    id: campaign.id,\n                    name: campaign.name,\n                    description: campaign.description,\n                    start_date: campaign.start_date,\n                    end_date: campaign.end_date,\n                    status: campaign.status,\n                    restaurant_id: campaign.restaurant_id,\n                    restaurants: restaurant ? {\n                        id: restaurant.id,\n                        name: restaurant.business_name,\n                        city: restaurant.city,\n                        state: restaurant.state\n                    } : null,\n                    hashtags,\n                    mentions,\n                    city: restaurant === null || restaurant === void 0 ? void 0 : restaurant.city,\n                    state: restaurant === null || restaurant === void 0 ? void 0 : restaurant.state\n                };\n            });\n            console.log('Transformed campaigns:', campaigns);\n            return campaigns;\n        } catch (error) {\n            console.error('Error in getAvailableCampaigns:', error);\n            return [];\n        }\n    },\n    // Apply for a campaign\n    async applyForCampaign (influencerId, campaignId) {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        try {\n            console.log(\"Attempting to apply for campaign: \".concat(campaignId, \" by influencer: \").concat(influencerId));\n            // Get the authenticated user's ID\n            const { data: { user } } = await supabase.auth.getUser();\n            if (!user) {\n                return {\n                    success: false,\n                    message: 'Usuário não autenticado. Por favor, faça login e tente novamente.'\n                };\n            }\n            console.log(\"InfluencerId: \".concat(influencerId));\n            console.log(\"Auth.uid(): \".concat(user.id));\n            // Ensure the influencerId matches the authenticated user's ID\n            if (influencerId !== user.id) {\n                return {\n                    success: false,\n                    message: 'Erro de autenticação. Por favor, faça login novamente e tente mais uma vez.'\n                };\n            }\n            // Check if the influencer is already participating in this campaign\n            const { data: existingParticipation, error: checkError } = await supabase.from('campaign_influencers').select('id, status').eq('influencer_id', user.id).eq('campaign_id', campaignId).single();\n            if (checkError && checkError.code !== 'PGRST116') {\n                console.error('Error checking existing participation:', checkError);\n                return {\n                    success: false,\n                    message: 'Erro ao verificar participação existente. Tente novamente.'\n                };\n            }\n            if (existingParticipation) {\n                return {\n                    success: false,\n                    message: 'Você já se candidatou a esta campanha.'\n                };\n            }\n            // Get the campaign details first to ensure it exists\n            const { data: campaignData, error: campaignError } = await supabase.from('campaigns').select(\"\\n          id,\\n          name,\\n          description,\\n          start_date,\\n          end_date,\\n          status,\\n          restaurant_id,\\n          restaurant_profiles(id, business_name)\\n        \").eq('id', campaignId).single();\n            if (campaignError) {\n                console.error('Error fetching campaign details:', campaignError);\n                return {\n                    success: false,\n                    message: 'Erro ao buscar detalhes da campanha. Tente novamente.'\n                };\n            }\n            // Create a new participation record\n            const { data: newParticipation, error: insertError } = await supabase.from('campaign_influencers').insert({\n                influencer_id: user.id,\n                campaign_id: campaignId,\n                status: 'applied',\n                created_at: new Date().toISOString()\n            }).select('id').single();\n            if (insertError) {\n                console.error('Error creating participation:', insertError);\n                return {\n                    success: false,\n                    message: \"Erro ao se candidatar para a campanha: \".concat(insertError.message, \". Tente novamente.\")\n                };\n            }\n            console.log('Successfully applied to campaign:', newParticipation);\n            const restaurantData = campaignData.restaurant_profiles;\n            const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;\n            // Create a campaign influencer object to return\n            const campaignInfluencer = {\n                id: newParticipation.id,\n                campaign_id: campaignId,\n                influencer_id: user.id,\n                status: 'pending',\n                total_points: 0,\n                campaigns: {\n                    id: campaignData.id,\n                    name: campaignData.name,\n                    description: campaignData.description,\n                    start_date: campaignData.start_date,\n                    end_date: campaignData.end_date,\n                    status: campaignData.status,\n                    restaurant_id: campaignData.restaurant_id,\n                    restaurants: restaurant ? {\n                        id: restaurant.id,\n                        name: restaurant.business_name\n                    } : null\n                }\n            };\n            return {\n                success: true,\n                message: 'Candidatura enviada com sucesso! Aguarde a aprovação do restaurante.',\n                campaignInfluencer\n            };\n        } catch (error) {\n            console.error('Error in applyForCampaign:', error);\n            return {\n                success: false,\n                message: 'Erro ao se candidatar para a campanha. Tente novamente.'\n            };\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/campaignService.ts\n"));

/***/ })

});