"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/admin/criadores/new/page",{

/***/ "(app-pages-browser)/./src/app/(admin)/admin/criadores/new/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/(admin)/admin/criadores/new/page.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewInfluencerPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_InfluencerAdminForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/InfluencerAdminForm */ \"(app-pages-browser)/./src/components/admin/InfluencerAdminForm.tsx\");\n/* harmony import */ var _components_layouts_AdminLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layouts/AdminLayout */ \"(app-pages-browser)/./src/components/layouts/AdminLayout.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n // useState removido\n\n\n\n\nfunction NewInfluencerPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // const [loading, setLoading] = useState(false); // Removido estado não utilizado\n    // Dados iniciais para o formulário\n    const initialData = {\n        name: '',\n        username: '',\n        email: '',\n        classification: 'Standard',\n        profile: {\n            bio: '',\n            instagram_username: '',\n            tiktok_username: '',\n            location_city: '',\n            location_state: '',\n            follower_count: 0,\n            avg_engagement_rate: 0,\n            content_niche: [],\n            primary_platform: 'instagram'\n        }\n    };\n    const handleCancel = ()=>{\n        router.push('/admin/influencers');\n    };\n    const handleSuccess = (influencerId)=>{\n        // Redirecionar para a página de detalhes do criador após criação bem-sucedida\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.success('Influenciador criado com sucesso!');\n        router.push(\"/admin/influencers/\".concat(influencerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_AdminLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Novo Influenciador\",\n        backLink: \"/admin/influencers\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-5xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_InfluencerAdminForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                initialData: initialData,\n                onCancel: handleCancel,\n                onSuccess: handleSuccess,\n                isEditing: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/criadores/new/page.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/criadores/new/page.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/criadores/new/page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInfluencerPage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInfluencerPage;\nvar _c;\n$RefreshReg$(_c, \"NewInfluencerPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(admin)/admin/criadores/new/page.tsx\n"));

/***/ })

});