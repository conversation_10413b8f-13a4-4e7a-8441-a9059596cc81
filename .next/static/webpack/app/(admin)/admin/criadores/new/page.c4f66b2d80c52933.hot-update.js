"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/admin/criadores/new/page",{

/***/ "(app-pages-browser)/./src/app/(admin)/admin/criadores/new/page.tsx":
/*!******************************************************!*\
  !*** ./src/app/(admin)/admin/criadores/new/page.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewInfluencerPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_InfluencerAdminForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/InfluencerAdminForm */ \"(app-pages-browser)/./src/components/admin/InfluencerAdminForm.tsx\");\n/* harmony import */ var _components_layouts_AdminLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layouts/AdminLayout */ \"(app-pages-browser)/./src/components/layouts/AdminLayout.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n // useState removido\n\n\n\n\nfunction NewInfluencerPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // const [loading, setLoading] = useState(false); // Removido estado não utilizado\n    // Dados iniciais para o formulário\n    const initialData = {\n        name: '',\n        username: '',\n        email: '',\n        classification: 'Standard',\n        profile: {\n            bio: '',\n            instagram_username: '',\n            tiktok_username: '',\n            location_city: '',\n            location_state: '',\n            follower_count: 0,\n            avg_engagement_rate: 0,\n            content_niche: [],\n            primary_platform: 'instagram'\n        }\n    };\n    const handleCancel = ()=>{\n        router.push('/admin/influencers');\n    };\n    const handleSuccess = (influencerId)=>{\n        // Redirecionar para a página de detalhes do criador após criação bem-sucedida\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.success('Criador criado com sucesso!');\n        router.push(\"/admin/influencers/\".concat(influencerId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layouts_AdminLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Novo Criador\",\n        backLink: \"/admin/influencers\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-5xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_InfluencerAdminForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                initialData: initialData,\n                onCancel: handleCancel,\n                onSuccess: handleSuccess,\n                isEditing: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/criadores/new/page.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/criadores/new/page.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/criadores/new/page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(NewInfluencerPage, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewInfluencerPage;\nvar _c;\n$RefreshReg$(_c, \"NewInfluencerPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(admin)/admin/criadores/new/page.tsx\n"));

/***/ })

});