"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/admin/negocios/page",{

/***/ "(app-pages-browser)/./src/app/(admin)/admin/components/NavigationTabs.tsx":
/*!*************************************************************!*\
  !*** ./src/app/(admin)/admin/components/NavigationTabs.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FaBuilding_FaBullhorn_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaBuilding,FaBullhorn,FaUsers!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NavigationTabs() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = (path)=>{\n        return pathname === path || pathname.startsWith(\"\".concat(path, \"/\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center overflow-x-auto bg-[#f5f5f5] navigation-tabs\",\n        style: {\n            backgroundColor: '#f5f5f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/admin/negocios\",\n                style: {\n                    backgroundColor: '#f5f5f5'\n                },\n                className: \"px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] \".concat(isActive('/admin/negocios') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center bg-[#f5f5f5]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaBullhorn_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBuilding, {\n                            className: \"mr-2 bg-[#f5f5f5]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-[#f5f5f5]\",\n                            children: \"Neg\\xf3cios\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/admin/campaigns\",\n                style: {\n                    backgroundColor: '#f5f5f5'\n                },\n                className: \"px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] \".concat(isActive('/admin/campaigns') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center bg-[#f5f5f5]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaBullhorn_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaBullhorn, {\n                            className: \"mr-2 bg-[#f5f5f5]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-[#f5f5f5]\",\n                            children: \"Campanhas\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/admin/criadores\",\n                style: {\n                    backgroundColor: '#f5f5f5'\n                },\n                className: \"px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] \".concat(isActive('/admin/criadores') ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center bg-[#f5f5f5]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBuilding_FaBullhorn_FaUsers_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaUsers, {\n                            className: \"mr-2 bg-[#f5f5f5]\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-[#f5f5f5]\",\n                            children: \"Criadores\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/app/(admin)/admin/components/NavigationTabs.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationTabs, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = NavigationTabs;\nvar _c;\n$RefreshReg$(_c, \"NavigationTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(admin)/admin/components/NavigationTabs.tsx\n"));

/***/ })

});