"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// import Image from 'next/image'; // Removido\n\nfunction Logo(param) {\n    let { className = '', textClassName = 'text-xl font-semibold', showText: _showText = true, size = 'medium', href = '/' } = param;\n    const sizeMap = {\n        small: 24,\n        medium: 32,\n        large: 48\n    };\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const _logoSize = sizeMap[size]; // logoSize prefixado\n    return href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: href,\n        className: \"flex items-center \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: textClassName,\n            children: \"crIAdores\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: textClassName,\n            children: \"crIAdores\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI_Projects/Criadores/src/components/Logo.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Logo.tsx\n"));

/***/ })

});