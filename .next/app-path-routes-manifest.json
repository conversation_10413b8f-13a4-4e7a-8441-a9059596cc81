{"/api/admin/add-client/route": "/api/admin/add-client", "/api/admin/add-client-public/route": "/api/admin/add-client-public", "/api/admin/campaigns/[campaign_id]/available-dates/route": "/api/admin/campaigns/[campaign_id]/available-dates", "/api/admin/criadores/[id]/route": "/api/admin/criadores/[id]", "/api/admin/criadores/route": "/api/admin/criadores", "/api/admin/influencers/route": "/api/admin/influencers", "/api/admin/influencers/[id]/route": "/api/admin/influencers/[id]", "/api/admin/negocios/[id]/route": "/api/admin/negocios/[id]", "/api/admin/negocios/route": "/api/admin/negocios", "/api/admin/negocios/create-test/route": "/api/admin/negocios/create-test", "/api/auth/instagram/route": "/api/auth/instagram", "/api/auth/tiktok/callback/route": "/api/auth/tiktok/callback", "/api/create-test-data/route": "/api/create-test-data", "/api/digital-presence/sync/route": "/api/digital-presence/sync", "/api/instagram-callback/route": "/api/instagram-callback", "/api/instagram-webhook/route": "/api/instagram-webhook", "/api/instagram/webhook/route": "/api/instagram/webhook", "/api/invite/create/route": "/api/invite/create", "/api/invite/accept/route": "/api/invite/accept", "/api/invite/accept-simple/route": "/api/invite/accept-simple", "/api/placeholder/svg/route": "/api/placeholder/svg", "/api/invite/validate/route": "/api/invite/validate", "/api/setup/create-user/route": "/api/setup/create-user", "/api/setup/create-influencer/route": "/api/setup/create-influencer", "/api/setup/add-gustavucaliani/route": "/api/setup/add-gust<PERSON><PERSON><PERSON>i", "/api/setup/simple-add-client/route": "/api/setup/simple-add-client", "/api/test-supabase/route": "/api/test-supabase", "/api/test/check-instagram-connection/route": "/api/test/check-instagram-connection", "/api/test/create-influencer/route": "/api/test/create-influencer", "/api/test/instagram/connect/route": "/api/test/instagram/connect", "/api/test/instagram/callback/route": "/api/test/instagram/callback", "/api/test/instagram/simulate-connection/route": "/api/test/instagram/simulate-connection", "/api/test/create-simple-user/route": "/api/test/create-simple-user", "/api/test/instagram/simulate-post/route": "/api/test/instagram/simulate-post", "/api/test/route": "/api/test", "/api/test/instagram/simulate-webhook/route": "/api/test/instagram/simulate-webhook", "/api/test/simulate-instagram-connection-v2/route": "/api/test/simulate-instagram-connection-v2", "/api/test/simulate-instagram-connection-v3/route": "/api/test/simulate-instagram-connection-v3", "/api/test/simulate-instagram-post/route": "/api/test/simulate-instagram-post", "/api/test/simulate-instagram-connection/route": "/api/test/simulate-instagram-connection", "/api/test/simulate-instagram-webhook/route": "/api/test/simulate-instagram-webhook", "/api/test/sync-instagram-posts/route": "/api/test/sync-instagram-posts", "/api/test/utils/status/route": "/api/test/utils/status", "/api/v1/campaigns/content/feedback/route": "/api/v1/campaigns/content/feedback", "/api/v1/campaigns/content/revision/route": "/api/v1/campaigns/content/revision", "/api/v1/campaigns/content/review/route": "/api/v1/campaigns/content/review", "/api/v1/campaigns/content/submit/route": "/api/v1/campaigns/content/submit", "/api/test/webhook-test/route": "/api/test/webhook-test", "/api/v1/campaigns/schedule/available-dates/route": "/api/v1/campaigns/schedule/available-dates", "/api/v1/campaigns/leaderboard/route": "/api/v1/campaigns/leaderboard", "/api/v1/cron/process-notifications/route": "/api/v1/cron/process-notifications", "/api/v1/campaigns/schedule/cancel/route": "/api/v1/campaigns/schedule/cancel", "/api/v1/campaigns/schedule/book/route": "/api/v1/campaigns/schedule/book", "/api/v1/cron/weekly-reports/route": "/api/v1/cron/weekly-reports", "/api/v1/health/route": "/api/v1/health", "/api/v1/instagram/connect/route": "/api/v1/instagram/connect", "/api/v1/instagram/callback/route": "/api/v1/instagram/callback", "/api/v1/notifications/settings/route": "/api/v1/notifications/settings", "/api/v1/instagram/sync/route": "/api/v1/instagram/sync", "/api/v1/instagram/webhook/route": "/api/v1/instagram/webhook", "/api/v1/ratings/route": "/api/v1/ratings", "/api/v1/posts/metrics/update/route": "/api/v1/posts/metrics/update", "/api/v1/schedule/cancel/route": "/api/v1/schedule/cancel", "/api/v1/notifications/test/route": "/api/v1/notifications/test", "/api/v1/schedule/criador/route": "/api/v1/schedule/criador", "/api/v1/schedule/complete/route": "/api/v1/schedule/complete", "/api/v1/schedule/campaign/[id]/route": "/api/v1/schedule/campaign/[id]", "/api/v1/schedule/influencer/route": "/api/v1/schedule/influencer", "/api/v1/tasks/[id]/comments/route": "/api/v1/tasks/[id]/comments", "/api/v1/tasks/[id]/route": "/api/v1/tasks/[id]", "/api/v1/tasks/route": "/api/v1/tasks", "/api/v1/whatsapp/mock-verify/route": "/api/v1/whatsapp/mock-verify", "/api/v1/whatsapp/send-template/route": "/api/v1/whatsapp/send-template", "/api/v1/whatsapp/send/route": "/api/v1/whatsapp/send", "/api/v1/tasks/[id]/complete/route": "/api/v1/tasks/[id]/complete", "/api/v1/whatsapp/webhook/route": "/api/v1/whatsapp/webhook", "/api/whatsapp/webhook/route": "/api/whatsapp/webhook", "/api/v1/whatsapp/test/route": "/api/v1/whatsapp/test", "/api/v1/whatsapp/verify/route": "/api/v1/whatsapp/verify", "/instagram-webhook/route": "/instagram-webhook", "/webhook/route": "/webhook", "/favicon.ico/route": "/favicon.ico", "/api/admin/campaigns/create-test/route": "/api/admin/campaigns/create-test", "/api/admin/campaigns/create/route": "/api/admin/campaigns/create", "/api/admin/campaigns/[campaign_id]/[id]/route": "/api/admin/campaigns/[campaign_id]/[id]", "/api/admin/campaigns/[campaign_id]/route": "/api/admin/campaigns/[campaign_id]", "/api/admin/create-tables/route": "/api/admin/create-tables", "/api/admin/campaigns/update/route": "/api/admin/campaigns/update", "/api/admin/campaigns/route": "/api/admin/campaigns", "/api/auth/callback/route": "/api/auth/callback", "/api/auth/instagram/callback/route": "/api/auth/instagram/callback", "/_not-found/page": "/_not-found", "/admin-test/page": "/admin-test", "/admin/whatsapp/page": "/admin/whatsapp", "/admin/test-api/page": "/admin/test-api", "/criador/configuracoes/page": "/criador/configuracoes", "/dev/instagram-test/page": "/dev/instagram-test", "/dev/test-data/page": "/dev/test-data", "/login-simple/page": "/login-simple", "/redirect/page": "/redirect", "/exemplos-popups/page": "/exemplos-popups", "/popups-demo/page": "/popups-demo", "/direct-test/page": "/direct-test", "/simple-test/page": "/simple-test", "/page": "/", "/test/create-simple-user/page": "/test/create-simple-user", "/whatsapp-api-test/page": "/whatsapp-api-test", "/test/create-influencer/page": "/test/create-influencer", "/whatsapp-dashboard/page": "/whatsapp-dashboard", "/whatsapp-messages/page": "/whatsapp-messages", "/whatsapp-mock-test/page": "/whatsapp-mock-test", "/whatsapp-test/page": "/whatsapp-test", "/(auth)/change-password/page": "/change-password", "/(auth)/registro/page": "/registro", "/(dashboard)/exemplos/popups-arrastaveis/page": "/exemplos/popups-arrastaveis", "/(auth)/logout/page": "/logout", "/(dashboard)/exemplos/popups-aninhados/page": "/exemplos/popups-an<PERSON><PERSON><PERSON>", "/(dashboard)/exemplos/popups-otimizados/page": "/exemplos/popups-oti<PERSON><PERSON><PERSON>", "/(auth)/convite/page": "/convite", "/(dashboard)/exemplos/popups-padronizados/page": "/exemplos/popups-padronizados", "/(dashboard)/exemplos/popups/page": "/exemplos/popups", "/(auth)/login/page": "/login", "/(dashboard)/design-options/page": "/design-options", "/(dashboard)/plano-novo/page": "/plano-novo", "/(dashboard)/exemplos/tema/page": "/exemplos/tema", "/(dashboard)/exemplos/popups-dinamicos/page": "/exemplos/popups-dinamicos", "/(dashboard)/restaurante-novo/page": "/restaurante-novo", "/(dashboard)/plano/page": "/plano", "/(public)/privacy-policy/page": "/privacy-policy", "/(public)/politica-de-privacidade/page": "/politica-de-privacidade", "/(test)/setup/create-influencer/page": "/setup/create-influencer", "/(test)/setup/add-user/page": "/setup/add-user", "/(public)/privacy/page": "/privacy", "/(test)/setup/page": "/setup", "/(test)/setup/create-user/page": "/setup/create-user", "/(test)/demo/instagram-integration/page": "/demo/instagram-integration", "/(test)/setup/check-config/page": "/setup/check-config", "/(test)/setup/client-side-add/page": "/setup/client-side-add", "/(test)/setup/simple-add-client/page": "/setup/simple-add-client", "/(test)/webhook-test/page": "/webhook-test", "/(test)/setup/minimal-signup/page": "/setup/minimal-signup", "/(test)/test-supabase/page": "/test-supabase", "/(test)/setup/test-flow/page": "/setup/test-flow", "/(test)/setup/test-accept/page": "/setup/test-accept", "/(test)/setup/simple-test/page": "/setup/simple-test", "/dashboard/page": "/dashboard", "/(test)/setup/test-simple/page": "/setup/test-simple", "/(admin)/admin/add-client-public/page": "/admin/add-client-public", "/(admin)/admin/criadores/[id]/edit/page": "/admin/criadores/[id]/edit", "/(admin)/admin/criadores/new/page": "/admin/criadores/new", "/(admin)/admin/campaigns/[campaign_id]/edit/page": "/admin/campaigns/[campaign_id]/edit", "/(admin)/admin/campaigns/new/page": "/admin/campaigns/new", "/(admin)/admin/add-client-simple/page": "/admin/add-client-simple", "/(admin)/admin/campaigns/page": "/admin/campaigns", "/(admin)/admin/campaigns/[campaign_id]/page": "/admin/campaigns/[campaign_id]", "/(admin)/admin/add-client/page": "/admin/add-client", "/(admin)/admin/negocios/[id]/edit/page": "/admin/negocios/[id]/edit", "/(admin)/admin/criadores/page": "/admin/criadores", "/(admin)/admin/negocios/page": "/admin/negocios", "/(admin)/admin/settings/page": "/admin/settings", "/(admin)/admin/negocios/new/page": "/admin/negocios/new", "/(admin)/admin/invite/page": "/admin/invite", "/(admin)/admin/page": "/admin", "/(admin)/admin/negocios/[id]/page": "/admin/negocios/[id]", "/(admin)/admin/criadores/[id]/page": "/admin/criadores/[id]", "/(dashboard)/criador/campanhas/page": "/criador/campanhas", "/(dashboard)/criador/page": "/criador", "/(dashboard)/restaurante/configuracoes/page": "/restaurante/configuracoes", "/(dashboard)/criador/campanhas/[id]/videos/page": "/criador/campanhas/[id]/videos", "/(dashboard)/restaurante/presenca-digital/page": "/restaurante/presenca-digital", "/(dashboard)/restaurante/presenca-digital-exemplo/page": "/restaurante/presenca-digital-exemplo", "/(dashboard)/restaurante/page": "/restaurante"}