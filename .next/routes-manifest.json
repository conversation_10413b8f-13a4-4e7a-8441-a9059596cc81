{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/admin/campaigns/[campaign_id]", "regex": "^/admin/campaigns/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcampaign_id": "nxtPcampaign_id"}, "namedRegex": "^/admin/campaigns/(?<nxtPcampaign_id>[^/]+?)(?:/)?$"}, {"page": "/admin/campaigns/[campaign_id]/edit", "regex": "^/admin/campaigns/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPcampaign_id": "nxtPcampaign_id"}, "namedRegex": "^/admin/campaigns/(?<nxtPcampaign_id>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/criadores/[id]", "regex": "^/admin/criadores/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/criadores/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/criadores/[id]/edit", "regex": "^/admin/criadores/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/criadores/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/negocios/[id]", "regex": "^/admin/negocios/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/negocios/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/negocios/[id]/edit", "regex": "^/admin/negocios/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/negocios/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/api/admin/campaigns/[campaign_id]", "regex": "^/api/admin/campaigns/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcampaign_id": "nxtPcampaign_id"}, "namedRegex": "^/api/admin/campaigns/(?<nxtPcampaign_id>[^/]+?)(?:/)?$"}, {"page": "/api/admin/campaigns/[campaign_id]/available-dates", "regex": "^/api/admin/campaigns/([^/]+?)/available\\-dates(?:/)?$", "routeKeys": {"nxtPcampaign_id": "nxtPcampaign_id"}, "namedRegex": "^/api/admin/campaigns/(?<nxtPcampaign_id>[^/]+?)/available\\-dates(?:/)?$"}, {"page": "/api/admin/campaigns/[campaign_id]/[id]", "regex": "^/api/admin/campaigns/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcampaign_id": "nxtPcampaign_id", "nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/campaigns/(?<nxtPcampaign_id>[^/]+?)/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/criadores/[id]", "regex": "^/api/admin/criadores/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/criadores/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/influencers/[id]", "regex": "^/api/admin/influencers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/influencers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/negocios/[id]", "regex": "^/api/admin/negocios/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/negocios/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/v1/schedule/campaign/[id]", "regex": "^/api/v1/schedule/campaign/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/v1/schedule/campaign/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/v1/tasks/[id]", "regex": "^/api/v1/tasks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/v1/tasks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/v1/tasks/[id]/comments", "regex": "^/api/v1/tasks/([^/]+?)/comments(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/v1/tasks/(?<nxtPid>[^/]+?)/comments(?:/)?$"}, {"page": "/api/v1/tasks/[id]/complete", "regex": "^/api/v1/tasks/([^/]+?)/complete(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/v1/tasks/(?<nxtPid>[^/]+?)/complete(?:/)?$"}, {"page": "/criador/campanhas/[id]/videos", "regex": "^/criador/campanhas/([^/]+?)/videos(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/criador/campanhas/(?<nxtPid>[^/]+?)/videos(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/add-client", "regex": "^/admin/add\\-client(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/add\\-client(?:/)?$"}, {"page": "/admin/add-client-public", "regex": "^/admin/add\\-client\\-public(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/add\\-client\\-public(?:/)?$"}, {"page": "/admin/add-client-simple", "regex": "^/admin/add\\-client\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/add\\-client\\-simple(?:/)?$"}, {"page": "/admin/campaigns", "regex": "^/admin/campaigns(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/campaigns(?:/)?$"}, {"page": "/admin/campaigns/new", "regex": "^/admin/campaigns/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/campaigns/new(?:/)?$"}, {"page": "/admin/criadores", "regex": "^/admin/criadores(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/criadores(?:/)?$"}, {"page": "/admin/criadores/new", "regex": "^/admin/criadores/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/criadores/new(?:/)?$"}, {"page": "/admin/invite", "regex": "^/admin/invite(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/invite(?:/)?$"}, {"page": "/admin/negocios", "regex": "^/admin/negocios(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/negocios(?:/)?$"}, {"page": "/admin/negocios/new", "regex": "^/admin/negocios/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/negocios/new(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/test-api", "regex": "^/admin/test\\-api(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/test\\-api(?:/)?$"}, {"page": "/admin/whatsapp", "regex": "^/admin/whatsapp(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/whatsapp(?:/)?$"}, {"page": "/admin-test", "regex": "^/admin\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin\\-test(?:/)?$"}, {"page": "/change-password", "regex": "^/change\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/change\\-password(?:/)?$"}, {"page": "/convite", "regex": "^/convite(?:/)?$", "routeKeys": {}, "namedRegex": "^/convite(?:/)?$"}, {"page": "/criador", "regex": "^/criador(?:/)?$", "routeKeys": {}, "namedRegex": "^/criador(?:/)?$"}, {"page": "/criador/campanhas", "regex": "^/criador/campanhas(?:/)?$", "routeKeys": {}, "namedRegex": "^/criador/campanhas(?:/)?$"}, {"page": "/criador/configuracoes", "regex": "^/criador/configuracoes(?:/)?$", "routeKeys": {}, "namedRegex": "^/criador/configuracoes(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/demo/instagram-integration", "regex": "^/demo/instagram\\-integration(?:/)?$", "routeKeys": {}, "namedRegex": "^/demo/instagram\\-integration(?:/)?$"}, {"page": "/design-options", "regex": "^/design\\-options(?:/)?$", "routeKeys": {}, "namedRegex": "^/design\\-options(?:/)?$"}, {"page": "/dev/instagram-test", "regex": "^/dev/instagram\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/dev/instagram\\-test(?:/)?$"}, {"page": "/dev/test-data", "regex": "^/dev/test\\-data(?:/)?$", "routeKeys": {}, "namedRegex": "^/dev/test\\-data(?:/)?$"}, {"page": "/direct-test", "regex": "^/direct\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/direct\\-test(?:/)?$"}, {"page": "/exemplos/popups", "regex": "^/exemplos/popups(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/popups(?:/)?$"}, {"page": "/exemplos/popups-an<PERSON><PERSON><PERSON>", "regex": "^/exemplos/popups\\-an<PERSON><PERSON><PERSON>(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/popups\\-an<PERSON><PERSON><PERSON>(?:/)?$"}, {"page": "/exemplos/popups-arrastaveis", "regex": "^/exemplos/popups\\-arrastaveis(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/popups\\-arrastaveis(?:/)?$"}, {"page": "/exemplos/popups-dinamicos", "regex": "^/exemplos/popups\\-dinamicos(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/popups\\-dinamicos(?:/)?$"}, {"page": "/exemplos/popups-oti<PERSON><PERSON><PERSON>", "regex": "^/exemplos/popups\\-oti<PERSON>zados(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/popups\\-oti<PERSON>zados(?:/)?$"}, {"page": "/exemplos/popups-padronizados", "regex": "^/exemplos/popups\\-padronizados(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/popups\\-padronizados(?:/)?$"}, {"page": "/exemplos/tema", "regex": "^/exemplos/tema(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos/tema(?:/)?$"}, {"page": "/exemplos-popups", "regex": "^/exemplos\\-popups(?:/)?$", "routeKeys": {}, "namedRegex": "^/exemplos\\-popups(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/instagram-webhook", "regex": "^/instagram\\-webhook(?:/)?$", "routeKeys": {}, "namedRegex": "^/instagram\\-webhook(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/login-simple", "regex": "^/login\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/login\\-simple(?:/)?$"}, {"page": "/logout", "regex": "^/logout(?:/)?$", "routeKeys": {}, "namedRegex": "^/logout(?:/)?$"}, {"page": "/plano", "regex": "^/plano(?:/)?$", "routeKeys": {}, "namedRegex": "^/plano(?:/)?$"}, {"page": "/plano-novo", "regex": "^/plano\\-novo(?:/)?$", "routeKeys": {}, "namedRegex": "^/plano\\-novo(?:/)?$"}, {"page": "/politica-de-privacidade", "regex": "^/politica\\-de\\-privacidade(?:/)?$", "routeKeys": {}, "namedRegex": "^/politica\\-de\\-privacidade(?:/)?$"}, {"page": "/popups-demo", "regex": "^/popups\\-demo(?:/)?$", "routeKeys": {}, "namedRegex": "^/popups\\-demo(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/redirect", "regex": "^/redirect(?:/)?$", "routeKeys": {}, "namedRegex": "^/redirect(?:/)?$"}, {"page": "/registro", "regex": "^/registro(?:/)?$", "routeKeys": {}, "namedRegex": "^/registro(?:/)?$"}, {"page": "/restaurante", "regex": "^/restaurante(?:/)?$", "routeKeys": {}, "namedRegex": "^/restaurante(?:/)?$"}, {"page": "/restaurante/configuracoes", "regex": "^/restaurante/configuracoes(?:/)?$", "routeKeys": {}, "namedRegex": "^/restaurante/configuracoes(?:/)?$"}, {"page": "/restaurante/presenca-digital", "regex": "^/restaurante/presenca\\-digital(?:/)?$", "routeKeys": {}, "namedRegex": "^/restaurante/presenca\\-digital(?:/)?$"}, {"page": "/restaurante-novo", "regex": "^/restaurante\\-novo(?:/)?$", "routeKeys": {}, "namedRegex": "^/restaurante\\-novo(?:/)?$"}, {"page": "/setup", "regex": "^/setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup(?:/)?$"}, {"page": "/setup/add-user", "regex": "^/setup/add\\-user(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/add\\-user(?:/)?$"}, {"page": "/setup/check-config", "regex": "^/setup/check\\-config(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/check\\-config(?:/)?$"}, {"page": "/setup/client-side-add", "regex": "^/setup/client\\-side\\-add(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/client\\-side\\-add(?:/)?$"}, {"page": "/setup/create-influencer", "regex": "^/setup/create\\-influencer(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/create\\-influencer(?:/)?$"}, {"page": "/setup/create-user", "regex": "^/setup/create\\-user(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/create\\-user(?:/)?$"}, {"page": "/setup/minimal-signup", "regex": "^/setup/minimal\\-signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/minimal\\-signup(?:/)?$"}, {"page": "/setup/simple-add-client", "regex": "^/setup/simple\\-add\\-client(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/simple\\-add\\-client(?:/)?$"}, {"page": "/setup/simple-test", "regex": "^/setup/simple\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/simple\\-test(?:/)?$"}, {"page": "/setup/test-accept", "regex": "^/setup/test\\-accept(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/test\\-accept(?:/)?$"}, {"page": "/setup/test-flow", "regex": "^/setup/test\\-flow(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/test\\-flow(?:/)?$"}, {"page": "/setup/test-simple", "regex": "^/setup/test\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup/test\\-simple(?:/)?$"}, {"page": "/simple-test", "regex": "^/simple\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/simple\\-test(?:/)?$"}, {"page": "/test/create-influencer", "regex": "^/test/create\\-influencer(?:/)?$", "routeKeys": {}, "namedRegex": "^/test/create\\-influencer(?:/)?$"}, {"page": "/test/create-simple-user", "regex": "^/test/create\\-simple\\-user(?:/)?$", "routeKeys": {}, "namedRegex": "^/test/create\\-simple\\-user(?:/)?$"}, {"page": "/test-supabase", "regex": "^/test\\-supabase(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-supabase(?:/)?$"}, {"page": "/webhook", "regex": "^/webhook(?:/)?$", "routeKeys": {}, "namedRegex": "^/webhook(?:/)?$"}, {"page": "/webhook-test", "regex": "^/webhook\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/webhook\\-test(?:/)?$"}, {"page": "/whatsapp-api-test", "regex": "^/whatsapp\\-api\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/whatsapp\\-api\\-test(?:/)?$"}, {"page": "/whatsapp-dashboard", "regex": "^/whatsapp\\-dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/whatsapp\\-dashboard(?:/)?$"}, {"page": "/whatsapp-messages", "regex": "^/whatsapp\\-messages(?:/)?$", "routeKeys": {}, "namedRegex": "^/whatsapp\\-messages(?:/)?$"}, {"page": "/whatsapp-mock-test", "regex": "^/whatsapp\\-mock\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/whatsapp\\-mock\\-test(?:/)?$"}, {"page": "/whatsapp-test", "regex": "^/whatsapp\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/whatsapp\\-test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}