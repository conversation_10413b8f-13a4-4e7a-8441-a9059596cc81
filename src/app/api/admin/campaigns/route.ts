import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Inicializar o cliente Supabase com a chave de serviço para contornar RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pbehloddlzwandfmpzbo.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxMjc1OTQ3NywiZXhwIjoyMDI4MzM1NDc3fQ.Rl-QQhXG-vVRIELqRJUM0eiZrCTxnNPbqhC4Je_jXpM';

// Configurar CORS para permitir requisições da mesma origem
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Handler para requisições OPTIONS (preflight CORS)
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// GET - Listar todas as campanhas
export async function GET(_request: NextRequest) { // request prefixado
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('Listando todas as campanhas');

    // Buscar todas as campanhas (sem join por enquanto devido a conflitos de FK)
    const { data, error } = await supabase
      .from('campaigns')
      .select(`
        id,
        name,
        description,
        restaurant_id,
        start_date,
        end_date,
        status,
        budget,
        requirements,
        created_at,
        briefing,
        influencer_count_target
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao listar campanhas:', error);
      return NextResponse.json(
        { error: `Erro ao listar campanhas: ${error.message}`, code: error.code },
        { status: 500, headers }
      );
    }

    return NextResponse.json({ data }, { status: 200, headers });
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      {
        error: `Erro ao processar requisição: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      },
      { status: 500, headers: corsHeaders }
    );
  }
}

// POST - Criar uma nova campanha
export async function POST(request: NextRequest) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Obter dados da requisição
    const campaignData = await request.json();

    console.log('Criando nova campanha:', campaignData);

    // Validar dados obrigatórios
    const requiredFields = ['name', 'description', 'restaurant_id', 'status'];
    const missingFields = requiredFields.filter(field => !campaignData[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          error: `Campos obrigatórios ausentes: ${missingFields.join(', ')}`,
          code: 'MISSING_FIELDS'
        },
        { status: 400, headers }
      );
    }

    // Verificar se o restaurante existe
    if (campaignData.restaurant_id) {
      const { data: restaurant, error: restaurantError } = await supabase
        .from('restaurant_profiles')
        .select('id')
        .eq('id', campaignData.restaurant_id)
        .single();

      if (restaurantError || !restaurant) {
        console.error('Restaurante não encontrado:', restaurantError);

        // Se o restaurante não existir, criar um restaurante de teste
        console.log('Criando restaurante de teste');
        const { data: newRestaurant, error: createRestaurantError } = await supabase
          .from('restaurant_profiles')
          .insert({
            business_name: 'Restaurante de Teste',
            description: 'Este é um restaurante de teste',
            instagram_url: 'https://instagram.com/restaurante_teste',
            city: 'São Paulo',
            state: 'SP',
            created_at: new Date().toISOString()
          })
          .select('id')
          .single();

        if (createRestaurantError) {
          console.error('Erro ao criar restaurante de teste:', createRestaurantError);
          return NextResponse.json(
            {
              error: `Erro ao criar restaurante de teste: ${createRestaurantError.message}`,
              code: createRestaurantError.code
            },
            { status: 500, headers }
          );
        }

        // Usar o ID do novo restaurante
        campaignData.restaurant_id = newRestaurant.id;
        console.log('Restaurante de teste criado:', newRestaurant.id);
      }
    }

    // Adicionar data de criação se não existir
    if (!campaignData.created_at) {
      campaignData.created_at = new Date().toISOString();
    }

    // Inserir campanha no banco de dados
    const { data, error } = await supabase
      .from('campaigns')
      .insert(campaignData)
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar campanha:', error);
      return NextResponse.json(
        { error: `Erro ao criar campanha: ${error.message}`, code: error.code },
        { status: 500, headers }
      );
    }

    console.log('Campanha criada com sucesso:', data.id);
    return NextResponse.json(
      {
        message: 'Campanha criada com sucesso',
        data
      },
      { status: 201, headers }
    );
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      {
        error: `Erro ao processar requisição: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
