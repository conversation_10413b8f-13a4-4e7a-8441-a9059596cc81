import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Inicializar o cliente Supabase com a chave de serviço
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://pbehloddlzwandfmpzbo.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBiZWhsb2RkbHp3YW5kZm1wemJvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxMjc1OTQ3NywiZXhwIjoyMDI4MzM1NDc3fQ.Rl-QQhXG-vVRIELqRJUM0eiZrCTxnNPbqhC4Je_jXpM';

// Configurar CORS para permitir requisições da mesma origem
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Handler para requisições OPTIONS (preflight CORS)
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

export async function POST(request: Request) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Obter dados da requisição
    const campaignData = await request.json();

    console.log('Criando nova campanha:', campaignData);

    // Verificar se há um restaurant_id válido
    if (!campaignData.restaurant_id) {
      console.warn('restaurant_id não fornecido, buscando um restaurante existente...');

      try {
        // Tentar criar um restaurante de teste usando a API
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        const response = await fetch(`${baseUrl}/api/admin/negocios/create-test`);

        if (!response.ok) {
          throw new Error(`Erro HTTP: ${response.status}`);
        }

        const responseData = await response.json();
        if (!responseData.success || !responseData.data || !responseData.data.id) {
          throw new Error('Resposta inválida da API de criação de restaurante');
        }

        campaignData.restaurant_id = responseData.data.id;
        console.log('Restaurante de teste criado/encontrado via API:', campaignData.restaurant_id);
      } catch (error: any) {
        console.error('Erro ao criar restaurante de teste via API:', error);

        // Buscar um restaurante existente como fallback
        const { data: restaurants, error: restaurantError } = await supabase
          .from('restaurant_profiles')
          .select('id')
          .limit(1);

        if (restaurantError) {
          console.error('Erro ao buscar restaurantes:', restaurantError);
          return NextResponse.json(
            { success: false, error: `Erro ao buscar restaurantes: ${restaurantError.message}` },
            { status: 500, headers }
          );
        }

        if (!restaurants || restaurants.length === 0) {
          console.log('Nenhum restaurante encontrado, criando um restaurante de teste...');

          // Criar um restaurante de teste
          const { data: newRestaurant, error: createRestaurantError } = await supabase
            .from('restaurant_profiles')
            .insert({
              created_at: new Date().toISOString()
            })
            .select('id')
            .single();

          if (createRestaurantError) {
            console.error('Erro ao criar restaurante de teste:', createRestaurantError);
            return NextResponse.json(
              { success: false, error: `Erro ao criar restaurante de teste: ${createRestaurantError.message}` },
              { status: 500, headers }
            );
          }

          // Usar o ID do novo restaurante
          campaignData.restaurant_id = newRestaurant.id;
          console.log('Restaurante de teste criado:', campaignData.restaurant_id);
        } else {
          // Usar o ID do primeiro restaurante encontrado
          campaignData.restaurant_id = restaurants[0].id;
          console.log('Usando restaurante existente:', campaignData.restaurant_id);
        }
      }
    } else {
      // Verificar se o restaurant_id fornecido existe
      const { data: _restaurantCheck, error: checkError } = await supabase // restaurantCheck prefixado
        .from('restaurant_profiles')
        .select('id')
        .eq('id', campaignData.restaurant_id)
        .single();

      if (checkError) {
        console.warn('Restaurante não encontrado, buscando restaurantes existentes...');

        // Buscar restaurantes existentes
        const { data: restaurants, error: fetchError } = await supabase
          .from('restaurant_profiles')
          .select('id')
          .limit(1);

        if (fetchError || !restaurants || restaurants.length === 0) {
          console.error('Nenhum restaurante encontrado');
          return NextResponse.json(
            { success: false, error: 'Restaurante não encontrado e nenhum restaurante alternativo disponível' },
            { status: 500, headers }
          );
        }

        // Usar o ID do primeiro restaurante encontrado
        campaignData.restaurant_id = restaurants[0].id;
        console.log('Usando restaurante alternativo:', campaignData.restaurant_id);
      }
    }

    // Garantir que a campanha tenha um status válido
    if (!campaignData.status) {
      campaignData.status = 'draft';
    }

    // Garantir que a campanha tenha um briefing
    if (!campaignData.briefing) {
      campaignData.briefing = 'Briefing padrão para a campanha';
    }

    // Garantir que a campanha tenha um influencer_count_target
    if (!campaignData.influencer_count_target) {
      campaignData.influencer_count_target = 5;
    }

    // Garantir que a campanha tenha uma data de criação
    if (!campaignData.created_at) {
      campaignData.created_at = new Date().toISOString();
    }

    console.log('Dados finais da campanha:', campaignData);

    // Criar a campanha
    const { data: campaign, error: insertError } = await supabase
      .from('campaigns')
      .insert(campaignData)
      .select('id, name, restaurant_id, status')
      .single();

    if (insertError) {
      console.error('Erro ao criar campanha:', insertError);
      return NextResponse.json(
        { success: false, error: `Erro ao criar campanha: ${insertError.message}` },
        { status: 500, headers }
      );
    }

    console.log('Campanha criada com sucesso:', campaign);

    return NextResponse.json(
      {
        success: true,
        message: 'Campanha criada com sucesso',
        data: campaign
      },
      { status: 200, headers }
    );
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Erro ao processar requisição: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
