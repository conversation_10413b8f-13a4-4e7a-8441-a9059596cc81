import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Headers CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// OPTIONS - Preflight para CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// GET - Listar todos os negocioes
export async function GET(_request: NextRequest) { // request prefixado
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Buscar todos os negocioes
    const { data, error } = await supabase
      .from('restaurant_profiles')
      .select('*')
      .order('business_name', { ascending: true });

    if (error) {
      console.error('Erro ao buscar negocioes:', error);
      return NextResponse.json({ error: error.message }, { status: 500, headers });
    }

    return NextResponse.json(data, { headers });
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// POST - Criar um novo negocioe
export async function POST(request: NextRequest) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Obter dados da requisição
    const negocioData = await request.json();

    console.log('Criando novo negocioe:', negocioData);

    // Validar dados obrigatórios
    const requiredFields = ['business_name', 'city', 'state'];
    const missingFields = requiredFields.filter(field => !negocioData[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Campos obrigatórios ausentes: ${missingFields.join(', ')}` },
        { status: 400, headers }
      );
    }

    // Verificar se já existe um perfil com o mesmo nome
    const { data: existingNegocio, error: checkError } = await supabase
      .from('restaurant_profiles')
      .select('id')
      .eq('business_name', negocioData.business_name)
      .maybeSingle();

    if (checkError) {
      console.error('Erro ao verificar negocioe existente:', checkError);
    } else if (existingNegocio) {
      return NextResponse.json(
        { error: 'Já existe um negocioe com este nome' },
        { status: 409, headers }
      );
    }

    // Gerar email e senha temporária
    const businessEmail = negocioData.email || `${negocioData.business_name.toLowerCase().replace(/\s+/g, '.')}@example.com`;
    const temporaryPassword = Math.random().toString(36).slice(-8);

    // Criar usuário no Supabase Auth
    const { data: userData, error: createUserError } = await supabase.auth.admin.createUser({
      email: businessEmail,
      password: temporaryPassword,
      email_confirm: true,
    });

    if (createUserError) {
      console.error('Erro ao criar usuário:', createUserError);
      return NextResponse.json(
        { error: `Erro ao criar usuário: ${createUserError.message}` },
        { status: 500, headers }
      );
    }

    const negocioId = userData.user.id;

    // Criar perfil básico
    const { data: _profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: negocioId,
        role: 'restaurant',
        full_name: negocioData.business_name,
        email: businessEmail,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (profileError) {
      console.error('Erro ao criar perfil:', profileError);
      return NextResponse.json(
        { error: 'Erro ao criar perfil para o negócio' },
        { status: 500, headers }
      );
    }

    // Preparar dados para restaurant_profiles (sem email)
    const { email, ...negocioDataWithoutEmail } = negocioData;
    const negocioWithId = {
      ...negocioDataWithoutEmail,
      id: negocioId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Inserir o negocioe na tabela restaurant_profiles
    const { data, error } = await supabase
      .from('restaurant_profiles')
      .insert(negocioWithId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar negocio_profile:', error);

      // Tentar excluir o perfil criado para evitar dados órfãos
      await supabase.from('profiles').delete().eq('id', negocioId);

      return NextResponse.json(
        { error: error.message },
        { status: 500, headers }
      );
    }

    // Também inserir na tabela negocios para manter a consistência
    const { error: negociosError } = await supabase
      .from('negocios')
      .insert({
        id: negocioId,
        owner_id: negocioId,
        name: negocioData.business_name,
        instagram_handle: negocioData.instagram_url ? negocioData.instagram_url.split('/').pop() : null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (negociosError) {
      console.error('Erro ao criar registro na tabela negocios:', negociosError);
      // Não falhar a operação principal se a inserção na tabela negocios falhar
      // Mas registrar o erro para investigação posterior
    }

    // Criar um registro inicial de presença digital
    const { error: digitalPresenceError } = await supabase
      .from('digital_presence')
      .insert({
        negocio_id: negocioId,
        instagram_followers: 0,
        instagram_previous_followers: 0,
        instagram_engagement_rate: 0,
        instagram_recent_posts: 0,
        google_rating: 0,
        google_previous_rating: 0,
        google_total_reviews: 0,
        google_new_reviews: 0,
        tripadvisor_ranking: 0,
        tripadvisor_total_places: 0,
        tripadvisor_rating: 0,
        tripadvisor_reviews: 0,
        snapshot_date: new Date().toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (digitalPresenceError) {
      console.error('Erro ao criar registro de presença digital:', digitalPresenceError);
      // Não falhar a operação principal se a inserção na tabela digital_presence falhar
    }

    // Criar um registro inicial de contagem de conteúdo
    const { error: contentCountError } = await supabase
      .from('content_count')
      .insert({
        negocio_id: negocioId,
        total_content: 0,
        monthly_content: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (contentCountError) {
      console.error('Erro ao criar registro de contagem de conteúdo:', contentCountError);
      // Não falhar a operação principal se a inserção na tabela content_count falhar
    }

    return NextResponse.json({
      message: 'Negócio criado com sucesso',
      id: negocioId,
      email: businessEmail,
      password: temporaryPassword,
      data
    }, { status: 201, headers });
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}
