import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Headers CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// OPTIONS - Preflight para CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// GET - Criar um negocioe de teste
export async function GET(_request: NextRequest) { // request prefixado
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('Criando negocioe de teste...');

    // Verificar se já existe um negocioe de teste
    const { data: existingNegocio, error: checkError } = await supabase
      .from('restaurant_profiles')
      .select('id, business_name')
      .eq('business_name', 'Negocioe de Teste')
      .maybeSingle();

    if (checkError) {
      console.error('Erro ao verificar negocioe existente:', checkError);
    } else if (existingNegocio) {
      console.log('Negocioe de teste já existe:', existingNegocio.id);
      return NextResponse.json(
        {
          message: 'Negocioe de teste já existe',
          id: existingNegocio.id,
          negocio: existingNegocio
        },
        { headers }
      );
    }

    // Gerar um UUID para o novo negocioe
    const { data: newId, error: idError } = await supabase.rpc('generate_uuid');

    if (idError) {
      console.error('Erro ao gerar UUID:', idError);
      return NextResponse.json(
        { error: 'Erro ao gerar ID para o negocioe' },
        { status: 500, headers }
      );
    }

    const negocioId = newId;

    // Criar um perfil básico primeiro
    const { data: _profileData, error: profileError } = await supabase // profileData prefixado
      .from('profiles')
      .insert({
        id: negocioId,
        role: 'negocio',
        full_name: 'Negocioe de Teste',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (profileError) {
      console.error('Erro ao criar perfil:', profileError);
      return NextResponse.json(
        { error: 'Erro ao criar perfil para o negocioe' },
        { status: 500, headers }
      );
    }

    // Dados do negócio de teste
    const testNegocioData = {
      id: negocioId,
      business_name: 'Negócio de Teste',
      description: 'Este é um negócio de teste para desenvolvimento',
      cuisine_type: 'Variada',
      address: 'Rua de Teste, 123',
      city: 'São Paulo',
      state: 'SP',
      postal_code: '01234-567',
      website: 'https://www.negocioteste.com.br',
      instagram_url: 'https://instagram.com/negocio_teste',
      facebook_url: 'https://facebook.com/negocioteste',
      tiktok_url: 'https://tiktok.com/@negocioteste',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Inserir o negocioe
    const { data, error } = await supabase
      .from('restaurant_profiles')
      .insert(testNegocioData)
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar negocioe de teste:', error);

      // Tentar excluir o perfil criado para evitar dados órfãos
      await supabase.from('profiles').delete().eq('id', negocioId);

      return NextResponse.json(
        { error: error.message },
        { status: 500, headers }
      );
    }

    console.log('Negocioe de teste criado com sucesso:', data.id);

    return NextResponse.json(
      {
        message: 'Negocioe de teste criado com sucesso',
        id: data.id,
        negocio: data
      },
      { status: 201, headers }
    );
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}
