import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Headers CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// OPTIONS - Preflight para CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// GET - Buscar um negocioe específico
export async function GET(
  request: NextRequest // Context parameter removed
) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Obter ID do negocioe da URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const negocioId = pathSegments[pathSegments.length - 1]; // Extract from URL

    if (!negocioId) {
      return NextResponse.json(
        { error: 'ID do negocioe não fornecido' },
        { status: 400, headers }
      );
    }

    console.log('Buscando negocioe com ID:', negocioId);

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Buscar o negocioe pelo ID
    const { data, error } = await supabase
      .from('restaurant_profiles')
      .select('*')
      .eq('id', negocioId)
      .single();

    if (error) {
      console.error('Erro ao buscar negocioe:', error);
      return NextResponse.json(
        { error: error.message },
        { status: error.code === 'PGRST116' ? 404 : 500, headers }
      );
    }

    return NextResponse.json(data, { headers });
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// PUT - Atualizar um negocioe específico
export async function PUT(
  request: NextRequest // Context parameter removed
) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Obter ID do negocioe da URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const negocioId = pathSegments[pathSegments.length - 1]; // Extract from URL

    if (!negocioId) {
      return NextResponse.json(
        { error: 'ID do negocioe não fornecido' },
        { status: 400, headers }
      );
    }

    // Obter dados da requisição
    const negocioData = await request.json();

    console.log('Atualizando negocioe:', negocioId, negocioData);

    // Validar dados obrigatórios
    const requiredFields = ['business_name', 'city', 'state'];
    const missingFields = requiredFields.filter(field => !negocioData[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Campos obrigatórios ausentes: ${missingFields.join(', ')}` },
        { status: 400, headers }
      );
    }

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verificar se o negocioe existe
    const { data: existingNegocio, error: checkError } = await supabase
      .from('restaurant_profiles')
      .select('id')
      .eq('id', negocioId)
      .maybeSingle();

    if (checkError) {
      console.error('Erro ao verificar negocioe:', checkError);
      return NextResponse.json(
        { error: checkError.message },
        { status: 500, headers }
      );
    }

    if (!existingNegocio) {
      return NextResponse.json(
        { error: 'Negocioe não encontrado' },
        { status: 404, headers }
      );
    }

    // Adicionar data de atualização
    const updateData = {
      ...negocioData,
      updated_at: new Date().toISOString()
    };

    // Atualizar o negocioe na tabela restaurant_profiles
    const { data, error } = await supabase
      .from('restaurant_profiles')
      .update(updateData)
      .eq('id', negocioId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar negocio_profile:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500, headers }
      );
    }

    // Atualizar também o nome no perfil básico
    if (negocioData.business_name) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          full_name: negocioData.business_name,
          updated_at: new Date().toISOString()
        })
        .eq('id', negocioId);

      if (profileError) {
        console.warn('Erro ao atualizar perfil:', profileError);
        // Não falhar a operação principal se a atualização do perfil falhar
      }

      // Atualizar também na tabela negocios
      const { error: negociosError } = await supabase
        .from('negocios')
        .update({
          name: negocioData.business_name,
          instagram_handle: negocioData.instagram_url ? negocioData.instagram_url.split('/').pop() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', negocioId);

      if (negociosError) {
        console.warn('Erro ao atualizar registro na tabela negocios:', negociosError);
        // Verificar se o registro existe na tabela negocios
        const { data: existingNegocioRecord, error: checkNegocioError } = await supabase
          .from('negocios')
          .select('id')
          .eq('id', negocioId)
          .maybeSingle();

        if (checkNegocioError) {
          console.error('Erro ao verificar registro na tabela negocios:', checkNegocioError);
        } else if (!existingNegocioRecord) {
          // Se o registro não existe, criar um novo
          console.log('Registro não encontrado na tabela negocios, criando um novo...');
          const { error: insertError } = await supabase
            .from('negocios')
            .insert({
              id: negocioId,
              owner_id: negocioId,
              name: negocioData.business_name,
              instagram_handle: negocioData.instagram_url ? negocioData.instagram_url.split('/').pop() : null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (insertError) {
            console.error('Erro ao criar registro na tabela negocios:', insertError);
          }
        }
      }
    }

    return NextResponse.json(data, { headers });
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// DELETE - Excluir um negocioe específico
export async function DELETE(
  request: NextRequest // Context parameter removed
) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Obter ID do negocioe da URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const negocioId = pathSegments[pathSegments.length - 1]; // Extract from URL

    if (!negocioId) {
      return NextResponse.json(
        { error: 'ID do negocioe não fornecido' },
        { status: 400, headers }
      );
    }

    console.log('Excluindo negocioe:', negocioId);

    // Criar cliente Supabase com a chave de serviço para contornar RLS
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verificar se o negocioe existe
    const { data: existingNegocio, error: checkError } = await supabase
      .from('restaurant_profiles')
      .select('id')
      .eq('id', negocioId)
      .maybeSingle();

    if (checkError) {
      console.error('Erro ao verificar negocioe:', checkError);
      return NextResponse.json(
        { error: checkError.message },
        { status: 500, headers }
      );
    }

    if (!existingNegocio) {
      return NextResponse.json(
        { error: 'Negocioe não encontrado' },
        { status: 404, headers }
      );
    }

    // Excluir registros relacionados primeiro

    // 1. Excluir registros de presença digital
    const { error: digitalPresenceError } = await supabase
      .from('digital_presence')
      .delete()
      .eq('negocio_id', negocioId);

    if (digitalPresenceError) {
      console.warn('Erro ao excluir registros de presença digital:', digitalPresenceError);
      // Continuar mesmo com erro
    }

    // 2. Excluir registros de contagem de conteúdo
    const { error: contentCountError } = await supabase
      .from('content_count')
      .delete()
      .eq('negocio_id', negocioId);

    if (contentCountError) {
      console.warn('Erro ao excluir registros de contagem de conteúdo:', contentCountError);
      // Continuar mesmo com erro
    }

    // 3. Excluir registro na tabela negocios
    const { error: negociosError } = await supabase
      .from('negocios')
      .delete()
      .eq('id', negocioId);

    if (negociosError) {
      console.warn('Erro ao excluir registro na tabela negocios:', negociosError);
      // Continuar mesmo com erro
    }

    // 4. Excluir o negocioe na tabela restaurant_profiles
    const { error } = await supabase
      .from('restaurant_profiles')
      .delete()
      .eq('id', negocioId);

    if (error) {
      console.error('Erro ao excluir negocio_profile:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500, headers }
      );
    }

    // 5. Excluir também o perfil básico
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('id', negocioId);

    if (profileError) {
      console.warn('Erro ao excluir perfil:', profileError);
      // Não falhar a operação principal se a exclusão do perfil falhar
    }

    return NextResponse.json(
      { message: 'Negocioe excluído com sucesso' },
      { headers }
    );
  } catch (error: any) {
    console.error('Erro ao processar requisição:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}
