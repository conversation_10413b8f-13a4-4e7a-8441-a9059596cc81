import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generatePassword } from '@/utils/passwordGenerator';

// Inicializar cliente Supabase com a chave de serviço
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Headers CORS para permitir acesso à API
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// OPTIONS - Para lidar com preflight requests do CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// POST - Criar um novo criador
export async function POST(request: NextRequest) {
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Obter dados do corpo da requisição
    const criadorData = await request.json();
    
    // Validar dados obrigatórios
    if (!criadorData.name || !criadorData.username || !criadorData.email) {
      return NextResponse.json(
        { error: 'Nome, nome de usuário e email são obrigatórios' },
        { status: 400, headers }
      );
    }

    // Verificar se o email já existe
    const { data: existingUser, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', criadorData.email)
      .maybeSingle();

    if (userError) {
      console.error('Erro ao verificar usuário existente:', userError);
      return NextResponse.json(
        { error: `Erro ao verificar usuário existente: ${userError.message}` },
        { status: 500, headers }
      );
    }

    let userId;
    let isNewUser = false;
    let temporaryPassword = '';

    if (existingUser) {
      // Usar o ID do usuário existente
      userId = existingUser.id;
      console.log('Usuário existente encontrado:', userId);
    } else {
      // Criar um novo usuário no Auth do Supabase
      isNewUser = true;
      temporaryPassword = generatePassword();
      
      const { data: userData, error: createUserError } = await supabase.auth.admin.createUser({
        email: criadorData.email,
        password: temporaryPassword,
        email_confirm: true,
      });

      if (createUserError) {
        console.error('Erro ao criar usuário:', createUserError);
        return NextResponse.json(
          { error: `Erro ao criar usuário: ${createUserError.message}` },
          { status: 500, headers }
        );
      }

      userId = userData.user.id;
      console.log('Novo usuário criado:', userId);
    }

    try {
      // Criar ou atualizar o perfil do usuário
      if (isNewUser) {
        // Criar novo perfil
        const { error: profileError } = await supabase.from('profiles').insert({
          id: userId,
          role: 'criador',
          full_name: criadorData.name,
          email: criadorData.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (profileError) {
          console.error('Erro ao criar perfil:', profileError);
          return NextResponse.json(
            { error: `Erro ao criar perfil: ${profileError.message}` },
            { status: 500, headers }
          );
        }
      } else {
        // Atualizar perfil existente
        const { error: updateProfileError } = await supabase
          .from('profiles')
          .update({
            full_name: criadorData.name,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateProfileError) {
          console.error('Erro ao atualizar perfil:', updateProfileError);
          return NextResponse.json(
            { error: `Erro ao atualizar perfil: ${updateProfileError.message}` },
            { status: 500, headers }
          );
        }
      }

      // Verificar se o criador já existe
      const { data: existingCriador } = await supabase
        .from('criadors')
        .select('id')
        .eq('id', userId)
        .single();

      if (existingCriador) {
        // Atualizar criador existente
        const { error: updateError } = await supabase
          .from('influencers')
          .update({
            name: criadorData.name,
            username: criadorData.username,
            classification: criadorData.classification || 'Standard',
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Erro ao atualizar registro de influenciador:', updateError);
          return NextResponse.json(
            { error: `Erro ao atualizar criador: ${updateError.message}` },
            { status: 500, headers }
          );
        }
      } else {
        // Criar novo registro de criador
        const { error: criadorError } = await supabase.from('influencers').insert({
          id: userId,
          name: criadorData.name,
          username: criadorData.username,
          classification: criadorData.classification || 'Standard',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (criadorError) {
          console.error('Erro ao criar registro de criador:', criadorError);
          return NextResponse.json(
            { error: `Erro ao criar criador: ${criadorError.message}` },
            { status: 500, headers }
          );
        }
      }

      // Verificar se o perfil de criador já existe
      const { data: existingCriadorProfile } = await supabase
        .from('influencer_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      const profile = criadorData.profile || {};

      if (existingCriadorProfile) {
        // Atualizar perfil de criador existente
        const { error: updateError } = await supabase
          .from('influencer_profiles')
          .update({
            bio: profile.bio || '',
            content_niche: profile.content_niche || [],
            primary_platform: profile.primary_platform || 'instagram',
            instagram_username: profile.instagram_username || '',
            tiktok_username: profile.tiktok_username || '',
            location_city: profile.location_city || '',
            location_state: profile.location_state || '',
            avg_engagement_rate: profile.avg_engagement_rate || 0,
            follower_count: profile.follower_count || 0,
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Erro ao atualizar perfil de criador:', updateError);
          return NextResponse.json(
            { error: `Erro ao atualizar perfil de criador: ${updateError.message}` },
            { status: 500, headers }
          );
        }
      } else {
        // Criar novo perfil de criador
        const { error: criadorProfileError } = await supabase.from('influencer_profiles').insert({
          id: userId,
          bio: profile.bio || '',
          content_niche: profile.content_niche || [],
          primary_platform: profile.primary_platform || 'instagram',
          instagram_username: profile.instagram_username || '',
          tiktok_username: profile.tiktok_username || '',
          location_city: profile.location_city || '',
          location_state: profile.location_state || '',
          avg_engagement_rate: profile.avg_engagement_rate || 0,
          follower_count: profile.follower_count || 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (criadorProfileError) {
          console.error('Erro ao criar perfil de criador:', criadorProfileError);
          return NextResponse.json(
            { error: `Erro ao criar perfil de criador: ${criadorProfileError.message}` },
            { status: 500, headers }
          );
        }
      }

      // Retornar informações do criador criado/atualizado
      return NextResponse.json({
        message: isNewUser ? 'Criador adicionado com sucesso' : 'Criador atualizado com sucesso',
        id: userId,
        email: criadorData.email,
        password: isNewUser ? temporaryPassword : undefined,
        name: criadorData.name,
        username: criadorData.username,
        isNewUser
      }, { headers });
    } catch (profileError: any) {
      console.error('Erro ao criar/atualizar perfil:', profileError);
      return NextResponse.json({
        error: `Erro ao criar/atualizar perfil: ${profileError.message}`,
        details: profileError
      }, { status: 500, headers });
    }
  } catch (err: any) {
    console.error('Erro inesperado:', err);
    return NextResponse.json({
      error: `Erro interno do servidor: ${err.message}`,
      stack: err.stack,
      name: err.name,
      code: err.code
    }, { status: 500, headers: corsHeaders });
  }
}

// GET - Listar todos os criadores
export async function GET(_request: NextRequest) { // request prefixado
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Obter criadores
    const { data: criadores, error } = await supabase
      .from('influencers')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      console.error('Erro ao buscar criadores:', error);
      return NextResponse.json(
        { error: `Erro ao buscar criadores: ${error.message}` },
        { status: 500, headers }
      );
    }

    return NextResponse.json(criadores, { headers });
  } catch (err: any) {
    console.error('Erro inesperado:', err);
    return NextResponse.json({
      error: `Erro interno do servidor: ${err.message}`
    }, { status: 500, headers: corsHeaders });
  }
}
