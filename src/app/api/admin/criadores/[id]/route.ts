import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Inicializar cliente Supabase com a chave de serviço
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Headers CORS para permitir acesso à API
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// OPTIONS - Para lidar com preflight requests do CORS
export async function OPTIONS() {
  return NextResponse.json({}, { headers: corsHeaders });
}

// GET - Obter um influenciador específico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Validar ID
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400, headers }
      );
    }

    // Buscar influenciador
    const { data: criador, error: criadorError } = await supabase
      .from('criadors')
      .select('*')
      .eq('id', id)
      .single();

    if (criadorError) {
      console.error('Erro ao buscar influenciador:', criadorError);
      return NextResponse.json(
        { error: `Erro ao buscar influenciador: ${criadorError.message}` },
        { status: 500, headers }
      );
    }

    if (!criador) {
      return NextResponse.json(
        { error: 'Influenciador não encontrado' },
        { status: 404, headers }
      );
    }

    // Buscar perfil do criador
    const { data: profile, error: profileError } = await supabase
      .from('influencer_profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (profileError && !profileError.message.includes('No rows found')) {
      console.error('Erro ao buscar perfil do influenciador:', profileError);
    }

    // Buscar email do usuário
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', id)
      .single();

    if (userError && !userError.message.includes('No rows found')) {
      console.error('Erro ao buscar email do usuário:', userError);
    }

    // Combinar dados
    const result = {
      ...criador,
      email: user?.email || null,
      profile: profile || null
    };

    return NextResponse.json(result, { headers });
  } catch (err: any) {
    console.error('Erro inesperado:', err);
    return NextResponse.json({
      error: `Erro interno do servidor: ${err.message}`
    }, { status: 500, headers: corsHeaders });
  }
}

// PUT - Atualizar um influenciador específico
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Validar ID
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400, headers }
      );
    }

    // Obter dados do corpo da requisição
    const criadorData = await request.json();

    // Validar dados obrigatórios
    if (!criadorData.name || !criadorData.username) {
      return NextResponse.json(
        { error: 'Nome e nome de usuário são obrigatórios' },
        { status: 400, headers }
      );
    }

    // Verificar se o influenciador existe
    const { data: existingCriador, error: checkError } = await supabase
      .from('criadors')
      .select('id')
      .eq('id', id)
      .single();

    if (checkError) {
      console.error('Erro ao verificar influenciador:', checkError);
      return NextResponse.json(
        { error: `Erro ao verificar influenciador: ${checkError.message}` },
        { status: 500, headers }
      );
    }

    if (!existingCriador) {
      return NextResponse.json(
        { error: 'Influenciador não encontrado' },
        { status: 404, headers }
      );
    }

    try {
      // Atualizar perfil do usuário se o email foi alterado
      if (criadorData.email) {
        const { error: updateProfileError } = await supabase
          .from('profiles')
          .update({
            full_name: criadorData.name,
            email: criadorData.email,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id);

        if (updateProfileError) {
          console.error('Erro ao atualizar perfil:', updateProfileError);
          return NextResponse.json(
            { error: `Erro ao atualizar perfil: ${updateProfileError.message}` },
            { status: 500, headers }
          );
        }
      }

      // Atualizar criador
      const { error: updateError } = await supabase
        .from('influencers')
        .update({
          name: criadorData.name,
          username: criadorData.username,
          classification: criadorData.classification || 'Standard',
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (updateError) {
        console.error('Erro ao atualizar criador:', updateError);
        return NextResponse.json(
          { error: `Erro ao atualizar criador: ${updateError.message}` },
          { status: 500, headers }
        );
      }

      // Verificar se o perfil de criador existe
      const { data: existingProfile } = await supabase
        .from('influencer_profiles')
        .select('id')
        .eq('id', id)
        .single();

      const profile = criadorData.profile || {};

      if (existingProfile) {
        // Atualizar perfil de criador existente
        const { error: updateProfileError } = await supabase
          .from('influencer_profiles')
          .update({
            bio: profile.bio || '',
            content_niche: profile.content_niche || [],
            primary_platform: profile.primary_platform || 'instagram',
            instagram_username: profile.instagram_username || '',
            tiktok_username: profile.tiktok_username || '',
            location_city: profile.location_city || '',
            location_state: profile.location_state || '',
            avg_engagement_rate: profile.avg_engagement_rate || 0,
            follower_count: profile.follower_count || 0,
            updated_at: new Date().toISOString(),
          })
          .eq('id', id);

        if (updateProfileError) {
          console.error('Erro ao atualizar perfil de influenciador:', updateProfileError);
          return NextResponse.json(
            { error: `Erro ao atualizar perfil de influenciador: ${updateProfileError.message}` },
            { status: 500, headers }
          );
        }
      } else {
        // Criar perfil de criador se não existir
        const { error: createProfileError } = await supabase
          .from('influencer_profiles')
          .insert({
            id: id,
            bio: profile.bio || '',
            content_niche: profile.content_niche || [],
            primary_platform: profile.primary_platform || 'instagram',
            instagram_username: profile.instagram_username || '',
            tiktok_username: profile.tiktok_username || '',
            location_city: profile.location_city || '',
            location_state: profile.location_state || '',
            avg_engagement_rate: profile.avg_engagement_rate || 0,
            follower_count: profile.follower_count || 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

        if (createProfileError) {
          console.error('Erro ao criar perfil de influenciador:', createProfileError);
          return NextResponse.json(
            { error: `Erro ao criar perfil de influenciador: ${createProfileError.message}` },
            { status: 500, headers }
          );
        }
      }

      // Retornar sucesso
      return NextResponse.json({
        message: 'Influenciador atualizado com sucesso',
        id: id
      }, { headers });
    } catch (updateError: any) {
      console.error('Erro ao atualizar influenciador:', updateError);
      return NextResponse.json({
        error: `Erro ao atualizar influenciador: ${updateError.message}`,
        details: updateError
      }, { status: 500, headers });
    }
  } catch (err: any) {
    console.error('Erro inesperado:', err);
    return NextResponse.json({
      error: `Erro interno do servidor: ${err.message}`,
      stack: err.stack,
      name: err.name,
      code: err.code
    }, { status: 500, headers: corsHeaders });
  }
}

// DELETE - Excluir um influenciador específico
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  try {
    // Adicionar headers CORS à resposta
    const headers = { ...corsHeaders, 'Content-Type': 'application/json' };

    // Validar ID
    if (!id) {
      return NextResponse.json(
        { error: 'ID do influenciador é obrigatório' },
        { status: 400, headers }
      );
    }

    // Verificar se o influenciador existe
    const { data: existingCriador, error: checkError } = await supabase
      .from('criadors')
      .select('id')
      .eq('id', id)
      .single();

    if (checkError) {
      console.error('Erro ao verificar influenciador:', checkError);
      return NextResponse.json(
        { error: `Erro ao verificar influenciador: ${checkError.message}` },
        { status: 500, headers }
      );
    }

    if (!existingCriador) {
      return NextResponse.json(
        { error: 'Influenciador não encontrado' },
        { status: 404, headers }
      );
    }

    // Excluir perfil de criador (as chaves estrangeiras devem ter ON DELETE CASCADE)
    const { error: profileError } = await supabase
      .from('influencer_profiles')
      .delete()
      .eq('id', id);

    if (profileError && !profileError.message.includes('No rows found')) {
      console.error('Erro ao excluir perfil de influenciador:', profileError);
      return NextResponse.json(
        { error: `Erro ao excluir perfil de influenciador: ${profileError.message}` },
        { status: 500, headers }
      );
    }

    // Excluir influenciador
    const { error: deleteError } = await supabase
      .from('criadors')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Erro ao excluir influenciador:', deleteError);
      return NextResponse.json(
        { error: `Erro ao excluir influenciador: ${deleteError.message}` },
        { status: 500, headers }
      );
    }

    // Retornar sucesso
    return NextResponse.json({
      message: 'Influenciador excluído com sucesso'
    }, { headers });
  } catch (err: any) {
    console.error('Erro inesperado:', err);
    return NextResponse.json({
      error: `Erro interno do servidor: ${err.message}`
    }, { status: 500, headers: corsHeaders });
  }
}
