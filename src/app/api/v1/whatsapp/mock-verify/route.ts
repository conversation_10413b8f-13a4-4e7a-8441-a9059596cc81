import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { mockWhatsappClient } from '@/lib/whatsapp/mock-client';

// POST: Inicia o processo de verificação enviando um código
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }
    
    const { phone } = await request.json();
    
    if (!phone) {
      return NextResponse.json(
        { error: 'Número de telefone é obrigatório' },
        { status: 400 }
      );
    }
    
    // Gerar código de verificação (6 dígitos)
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Armazenar o código e o timestamp no banco de dados
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        phone: phone,
        verification_code: verificationCode,
        verification_sent_at: new Date().toISOString()
      })
      .eq('id', session.user.id);
    
    if (updateError) {
      console.error('Erro ao salvar código de verificação:', updateError);
      return NextResponse.json(
        { error: 'Erro ao processar solicitação' },
        { status: 500 }
      );
    }
    
    // Enviar o código via WhatsApp (mock)
    await mockWhatsappClient.sendTextMessage(
      phone,
      `Seu código de verificação crIAdores é: ${verificationCode}. Este código expira em 10 minutos.`
    );
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'Código de verificação enviado',
        // For testing purposes only, we'll return the code
        code: verificationCode
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Erro ao processar solicitação:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PUT: Verifica o código enviado pelo usuário
export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }
    
    const { code } = await request.json();
    
    if (!code) {
      return NextResponse.json(
        { error: 'Código de verificação é obrigatório' },
        { status: 400 }
      );
    }
    
    // Buscar o código armazenado
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('verification_code, verification_sent_at, phone')
      .eq('id', session.user.id)
      .single();
    
    if (fetchError || !profile) {
      console.error('Erro ao buscar perfil:', fetchError);
      return NextResponse.json(
        { error: 'Erro ao verificar código' },
        { status: 500 }
      );
    }
    
    // Verificar se o código é válido
    if (profile.verification_code !== code) {
      return NextResponse.json(
        { error: 'Código de verificação inválido' },
        { status: 400 }
      );
    }
    
    // Verificar se o código não expirou (10 minutos)
    const sentAt = new Date(profile.verification_sent_at);
    const now = new Date();
    const diffMinutes = (now.getTime() - sentAt.getTime()) / (1000 * 60);
    
    if (diffMinutes > 10) {
      return NextResponse.json(
        { error: 'Código de verificação expirado' },
        { status: 400 }
      );
    }
    
    // Atualizar o perfil como verificado
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        phone_verified: true,
        verification_code: null,
        verification_sent_at: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', session.user.id);
    
    if (updateError) {
      console.error('Erro ao atualizar perfil:', updateError);
      return NextResponse.json(
        { error: 'Erro ao verificar número' },
        { status: 500 }
      );
    }
    
    // Enviar mensagem de boas-vindas
    try {
      const { data: userData } = await supabase
        .from('profiles')
        .select('full_name')
        .eq('id', session.user.id)
        .single();
      
      const userName = userData?.full_name || 'usuário';
      
      await mockWhatsappClient.sendTextMessage(
        profile.phone,
        `Olá ${userName}! Seu número de WhatsApp foi verificado com sucesso. Agora você receberá notificações importantes do crIAdores por aqui.`
      );
    } catch (messageError) {
      console.error('Erro ao enviar mensagem de boas-vindas:', messageError);
      // Não falhar o processo por causa disso
    }
    
    return NextResponse.json(
      { 
        success: true, 
        message: 'Número verificado com sucesso',
        phone: profile.phone
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Erro ao processar solicitação:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
