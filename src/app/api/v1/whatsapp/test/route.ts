import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { whatsappClient } from '@/lib/whatsapp/client';
import { logError } from '@/lib/utils/errors';

// Create Supabase client
const supabase = createServerSupabaseClient();

// Test endpoint for WhatsApp API
export async function GET(req: NextRequest) {
  try {
    // Check if the user is an admin
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .single();
    
    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Permissão negada' },
        { status: 403 }
      );
    }
    
    // Get the phone number from the query string
    const { searchParams } = new URL(req.url);
    const phone = searchParams.get('phone');
    
    if (!phone) {
      return NextResponse.json(
        { error: 'Número de telefone é obrigatório' },
        { status: 400 }
      );
    }
    
    // Send a test message
    const messageId = await whatsappClient.sendTextMessage(
      phone,
      'Esta é uma mensagem de teste do crIAdores. Se você recebeu esta mensagem, a integração com WhatsApp está funcionando corretamente.'
    );
    
    return NextResponse.json({
      success: true,
      message: 'Mensagem de teste enviada com sucesso',
      messageId
    });
  } catch (error) {
    logError('api.whatsapp.test', error);
    return NextResponse.json(
      { error: error.message || 'Erro ao enviar mensagem de teste' },
      { status: 500 }
    );
  }
}
