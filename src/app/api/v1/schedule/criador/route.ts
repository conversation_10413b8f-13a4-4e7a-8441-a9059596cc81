/**
 * API para obter agendamentos de um criador
 */

import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
// import { SchedulingService } from '@/lib/services/scheduling'; // Removido
import { logError } from '@/lib/utils/errors';

// GET /api/v1/schedule/criador - Obter agendamentos do criador atual
export async function GET(_req: NextRequest) { // req prefixado
  try {
    // Verificar autenticação
    const supabase = createServerSupabaseClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Não autorizado' },
        { status: 401 }
      );
    }
    
    // Verificar se o usuário é um criador
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', session.user.id)
      .single();

    if (profile?.role !== 'influencer') {
      return NextResponse.json(
        { error: 'Apenas criadores podem acessar esta API' },
        { status: 403 }
      );
    }
    
    // Obter agendamentos diretamente
    const { data: schedules, error: schedulesError } = await supabase
      .from('campaign_schedule')
      .select(`
        *,
        campaigns (
          name,
          restaurants (name)
        )
      `)
      .eq('influencer_id', session.user.id)
      .order('scheduled_date', { ascending: true });

    if (schedulesError) {
      throw new Error(`Erro ao buscar agendamentos: ${schedulesError.message}`);
    }
    
    return NextResponse.json({ schedules });
  } catch (error) {
    logError('api.schedule.influencer', error);
    
    return NextResponse.json(
      { error: error.message || 'Erro ao obter agendamentos' },
      { status: 500 }
    );
  }
}
