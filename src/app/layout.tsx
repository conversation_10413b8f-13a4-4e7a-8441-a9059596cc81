import type { Metadata } from "next";
import "./globals.css";
import "./dev-hide.css";
import "@/styles/themes.css";
import "./fix-purple-border.css";
import "./remove-space.css";
import "./fix-width.css";
import MainLayoutClient from "@/components/layout/MainLayoutClient"; 
import React from "react";

export const metadata: Metadata = {
  title: "crIAdores - Conectando Restaurantes e Influenciadores",
  description: "Plataforma que conecta restaurantes e influenciadores para impulsionar negócios locais",
  icons: {
    icon: '/images/logo-triangle.svg',
    apple: '/images/logo-triangle.svg',
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR" className="w-full">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <script src="/remove-dev-menu.js" defer></script>
      </head>
      <body className="antialiased w-full overflow-x-hidden">
        <MainLayoutClient>{children}</MainLayoutClient>
      </body>
    </html>
  );
}
