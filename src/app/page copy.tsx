"use client";

import React, { useState, useEffect } from "react";
import { FaWhatsapp } from "react-icons/fa";
import Link from "next/link";
import Logo from "@/components/Logo";

export default function Home() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const section = document.querySelector('section.bg-white');
      if (!section) return;

      const sectionTop = section.getBoundingClientRect().top;

      if (sectionTop <= 60) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="font-[-apple-system,BlinkMacSystemFont,'SF_Pro','Inter',sans-serif] text-[#1d1d1f] bg-[#f5f5f7]">
      {/* Fixed WhatsApp Button */}
      <a
        href="https://wa.me/5543991049779"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-6 right-6 z-50 flex items-center justify-center w-12 h-12 bg-[#34C759] rounded-full shadow-md hover:bg-[#2BB14F] transition-all duration-300 hover:transform hover:scale-105 hover:shadow-lg"
        aria-label="Contato via WhatsApp"
      >
        <FaWhatsapp className="text-white text-xl" />
      </a>

      {/* Navigation - Apple Style */}
      <header
        className={`fixed top-0 w-full z-50 transition-all duration-300 ${isScrolled ? "bg-[rgba(245,245,247,0.8)] backdrop-blur-md" : "bg-transparent"}`}
      >
        <div className="max-w-[980px] mx-auto px-6 py-3">
          <nav className="flex items-center justify-between h-[44px]">
            <div className="flex items-center">
              <Logo size="medium" textClassName={`text-xl font-semibold ${isScrolled ? "text-[#1d1d1f]" : "text-white"}`} />
            </div>
            <div className="hidden md:flex space-x-8 text-sm">
              <a href="#visao-geral" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200`}>Visão Geral</a>
              <a href="#como-funciona" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200`}>Como Funciona</a>
              <a href="#planos" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200`}>Planos</a>
              <a href="#depoimentos" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200`}>Depoimentos</a>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/login"
                className={`text-sm font-medium rounded-full px-4 py-1.5 transition duration-200 ${
                  isScrolled
                    ? "bg-transparent text-[#0071e3] border border-[#0071e3] hover:bg-[#0071e3] hover:text-white"
                    : "bg-white text-[#1d1d1f] hover:bg-gray-200"
                }`}
              >
                Login
              </Link>
              <Link
                href="/login?redirect=/admin/campaigns"
                className={`text-xs font-medium rounded-full px-3 py-1 transition duration-200 ${
                  isScrolled
                    ? "bg-[#407662] text-white hover:bg-[#2c5446]"
                    : "bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm"
                }`}
                title="Acesso Administrativo"
              >
                Admin
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero - Apple Style */}
      <section
        className="relative h-screen px-6 bg-cover bg-center text-white flex flex-col justify-center"
        id="visao-geral"
        style={{
          backgroundImage: "url('/images/connectcitybackground.jpg')"
        }}
      >
        <div className="absolute inset-0 bg-black opacity-40"></div>
        <div className="relative z-10 max-w-[980px] mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold tracking-tight mb-4">crIAdores</h1>
          <h2 className="text-3xl md:text-4xl font-semibold mb-6">
            Marketing de influência simplificado.
          </h2>
          <p className="text-xl max-w-2xl mx-auto mb-8">
            Conecte seu restaurante com influenciadores locais e veja resultados reais.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
            <a
              href="https://wa.me/5547999543437"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-5 py-2.5 bg-[#0071e3] text-white text-sm font-medium rounded-full shadow-sm hover:bg-[#0077ED] transition-all duration-200 hover:shadow-md hover:transform hover:translate-y-[-1px]"
            >
              <FaWhatsapp className="mr-2" /> Falar com um especialista
            </a>
          </div>
        </div>
      </section>

      {/* Depoimentos */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-[1200px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-12">O que dizem nossos clientes</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:scale-105 transform">
              <img src="https://i.pravatar.cc/150?u=restaurante1" alt="Restaurante Boussole" className="w-20 h-20 rounded-full mx-auto mb-4 object-cover" />
              <p className="text-lg mb-4 italic">"A crIAdores nos ajudou a dobrar o movimento no restaurante em apenas 3 meses."</p>
              <p className="font-semibold">Restaurante Boussole</p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:scale-105 transform">
              <img src="https://i.pravatar.cc/150?u=restaurante2" alt="Cantina Italiana" className="w-20 h-20 rounded-full mx-auto mb-4 object-cover" />
              <p className="text-lg mb-4 italic">"Com a crIAdores, conseguimos alcançar um público que nunca imaginávamos."</p>
              <p className="font-semibold">Cantina Italiana</p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:scale-105 transform">
              <img src="https://i.pravatar.cc/150?u=influencer1" alt="Influenciador" className="w-20 h-20 rounded-full mx-auto mb-4 object-cover" />
              <p className="text-lg mb-4 italic">"A plataforma facilitou muito meu trabalho como influenciador e aumentou minhas parcerias."</p>
              <p className="font-semibold">@gourmetanasita</p>
            </div>
          </div>
        </div>
      </section>

      {/* Números que impressionam - Apple Style */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-[980px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-16">Números que impressionam</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <p className="text-5xl font-semibold text-[#0071e3] mb-2">150+</p>
              <p className="text-lg text-[#86868b]">Restaurantes</p>
            </div>
            <div>
              <p className="text-5xl font-semibold text-[#0071e3] mb-2">1200+</p>
              <p className="text-lg text-[#86868b]">Influenciadores</p>
            </div>
            <div>
              <p className="text-5xl font-semibold text-[#0071e3] mb-2">35%</p>
              <p className="text-lg text-[#86868b]">Aumento em vendas</p>
            </div>
            <div>
              <p className="text-5xl font-semibold text-[#0071e3] mb-2">28k+</p>
              <p className="text-lg text-[#86868b]">Novos clientes</p>
            </div>
          </div>
          <div className="mt-20 flex flex-col md:flex-row items-center justify-between gap-12 text-left">
            <div className="md:w-1/2">
              <h3 className="text-3xl font-semibold mb-4">Venda mais com a crIAdores</h3>
              <p className="text-lg text-[#86868b] mb-6">Nossa plataforma conecta seu restaurante aos influenciadores certos, gerando conteúdo autêntico e aumentando suas vendas de forma consistente.</p>
              <a
                href="https://wa.me/5547999543437"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-5 py-2.5 bg-[#0071e3] text-white text-sm font-medium rounded-full shadow-sm hover:bg-[#0077ED] transition-all duration-200 hover:shadow-md hover:transform hover:translate-y-[-1px]"
              >
                <FaWhatsapp className="mr-2" /> Quero vender mais
              </a>
            </div>
            <div className="md:w-1/2 mt-8 md:mt-0">
              <img
                src="/images/restaurant-social.jpg"
                alt="Café com ambiente social"
                className="w-full h-auto rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Recursos poderosos - Apple Style */}
      <section className="py-20 px-6" id="recursos">
        <div className="max-w-[980px] mx-auto">
          <h2 className="text-4xl font-semibold text-center mb-16">Recursos poderosos</h2>

          {/* Feature 1 */}
          <div className="flex flex-col md:flex-row items-center gap-12 mb-24">
            <div className="md:w-1/2 order-2 md:order-1">
              <h3 className="text-3xl font-semibold mb-4">Curadoria inteligente</h3>
              <p className="text-lg text-[#86868b] mb-6">
                Encontramos os influenciadores perfeitos para o seu restaurante, baseados em localização, público-alvo e
                estilo culinário.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Perfis verificados e autênticos</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Análise de engajamento real</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Compatibilidade com sua marca</span>
                </li>
              </ul>
            </div>
            <div className="md:w-1/2 order-1 md:order-2">
              <div className="bg-[rgba(255,255,255,0.8)] backdrop-filter backdrop-blur-lg rounded-xl shadow-sm border border-[rgba(0,0,0,0.05)] overflow-hidden transition-all duration-300 hover:transform hover:translate-y-[-2px] hover:shadow-md">
                <img
                  src="/placeholder.svg?height=400&width=500"
                  alt="Curadoria de influenciadores"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>

          {/* Feature 2 - Métricas em tempo real */}
          <div className="flex flex-col md:flex-row items-center gap-12 mb-24">
            <div className="md:w-1/2">
              <div className="bg-[rgba(255,255,255,0.8)] backdrop-filter backdrop-blur-lg rounded-xl shadow-sm border border-[rgba(0,0,0,0.05)] overflow-hidden transition-all duration-300 hover:transform hover:translate-y-[-2px] hover:shadow-md">
                <img
                  src="/images/metricas-tempo-real.jpg"
                  alt="Dashboard de métricas em tempo real"
                  className="w-full h-auto"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h3 className="text-3xl font-semibold mb-4">Métricas em tempo real</h3>
              <p className="text-lg text-[#86868b] mb-6">
                Acompanhe o desempenho de cada campanha com dados precisos e visualizações claras.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">ROI mensurável e transparente</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Rastreamento de cupons e códigos</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Relatórios detalhados por campanha</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Feature 3 - Gestão simplificada */}
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2 order-2 md:order-1">
              <h3 className="text-3xl font-semibold mb-4">Gestão simplificada</h3>
              <p className="text-lg text-[#86868b] mb-6">
                Gerencie todos os aspectos da sua campanha em um só lugar, sem complicações.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Contratos e pagamentos automatizados</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Comunicação integrada com WhatsApp</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Aprovação de conteúdo em um clique</span>
                </li>
              </ul>
            </div>
            <div className="md:w-1/2 order-1 md:order-2">
              <div className="bg-[rgba(255,255,255,0.8)] backdrop-filter backdrop-blur-lg rounded-xl shadow-sm border border-[rgba(0,0,0,0.05)] overflow-hidden transition-all duration-300 hover:transform hover:translate-y-[-2px] hover:shadow-md">
                <img
                  src="/placeholder.svg?height=400&width=500"
                  alt="Gestão de campanhas"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How it Works Section - Apple Style */}
      <section className="py-20 px-6 bg-white" id="como-funciona">
        <div className="max-w-[980px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-6">Como funciona</h2>
          <p className="text-lg text-[#86868b] max-w-2xl mx-auto mb-16">
            Três passos simples para revolucionar o marketing do seu restaurante
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div className="flex flex-col items-center group transition-all duration-300 hover:transform hover:scale-105">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-2xl font-semibold mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                1
              </div>
              <div className="bg-white rounded-xl p-6 shadow-sm group-hover:shadow-md transition-all duration-300 h-full">
                <h3 className="text-2xl font-semibold mb-3">Escolha seu plano</h3>
                <p className="text-[#86868b]">Selecione o plano que melhor atende às necessidades do seu restaurante.</p>
                <div className="mt-4">
                  <img
                    src="/images/plan-selection.jpg"
                    alt="Seleção de plano"
                    className="w-full h-auto rounded-lg shadow-sm"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg?height=120&width=200";
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center group transition-all duration-300 hover:transform hover:scale-105">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-2xl font-semibold mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                2
              </div>
              <div className="bg-white rounded-xl p-6 shadow-sm group-hover:shadow-md transition-all duration-300 h-full">
                <h3 className="text-2xl font-semibold mb-3">Selecione influenciadores</h3>
                <p className="text-[#86868b]">Escolha entre nossa lista curada de criadores de conteúdo locais.</p>
                <div className="mt-4">
                  <img
                    src="/images/influencer-selection.jpg"
                    alt="Seleção de influenciadores"
                    className="w-full h-auto rounded-lg shadow-sm"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg?height=120&width=200";
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center group transition-all duration-300 hover:transform hover:scale-105">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-2xl font-semibold mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                3
              </div>
              <div className="bg-white rounded-xl p-6 shadow-sm group-hover:shadow-md transition-all duration-300 h-full">
                <h3 className="text-2xl font-semibold mb-3">Acompanhe resultados</h3>
                <p className="text-[#86868b]">Veja em tempo real o impacto das campanhas no seu negócio.</p>
                <div className="mt-4">
                  <img
                    src="/images/results-dashboard.jpg"
                    alt="Dashboard de resultados"
                    className="w-full h-auto rounded-lg shadow-sm"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg?height=120&width=200";
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="mt-20">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
              <div className="p-6 bg-gradient-to-r from-[#0071e3] to-[#00a2ff] text-white">
                <h3 className="text-2xl font-semibold mb-2">Fluxo de trabalho da plataforma crIAdores</h3>
                <p className="opacity-90">Um processo simplificado para maximizar seus resultados</p>
              </div>
              <img
                src="/images/platform-workflow.jpg"
                alt="Fluxo de trabalho da plataforma crIAdores"
                className="w-full h-auto"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/placeholder.svg?height=500&width=980";
                }}
              />
              <div className="p-6 bg-white border-t border-gray-100">
                <div className="flex flex-wrap gap-4 justify-center">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-[#0071e3] mr-2"></div>
                    <span className="text-sm text-gray-600">Seleção de plano</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-[#00a2ff] mr-2"></div>
                    <span className="text-sm text-gray-600">Curadoria de influenciadores</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-[#407662] mr-2"></div>
                    <span className="text-sm text-gray-600">Criação de conteúdo</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-[#34C759] mr-2"></div>
                    <span className="text-sm text-gray-600">Análise de resultados</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section - Apple Style */}
      <section className="py-20 px-6 bg-white" id="planos">
        <div className="max-w-[980px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-6">Escolha o plano ideal</h2>
          <p className="text-lg text-[#86868b] max-w-2xl mx-auto mb-16">
            Planos flexíveis para restaurantes de todos os tamanhos
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-[#f5f5f7] p-8 rounded-2xl">
              <h3 className="text-2xl font-semibold mb-2">Light</h3>
              <p className="text-[#86868b] mb-6">Para quem está começando</p>
              <p className="text-5xl font-semibold mb-1">R$ 2.080</p>
              <p className="text-[#86868b] mb-6">/mês</p>
              <p className="text-sm text-[#1d1d1f] mb-6">4 influenciadores</p>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Curadoria básica</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Relatórios mensais</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Suporte por e-mail</span>
                </li>
              </ul>
              <a
                href="https://wa.me/5547999543437"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full py-3 bg-[#0071e3] text-white rounded-full text-sm font-medium hover:bg-[#0077ED] transition"
              >
                Falar com especialista
              </a>
            </div>

            <div className="bg-[#f5f5f7] p-8 rounded-2xl relative transform scale-105 shadow-lg border-2 border-[#0071e3]">
              <div className="absolute -top-4 left-0 right-0 flex justify-center">
                <span className="bg-[#0071e3] text-white text-xs font-medium py-1 px-3 rounded-full">Mais Popular</span>
              </div>
              <h3 className="text-2xl font-semibold mb-2">Premium</h3>
              <p className="text-[#86868b] mb-6">Valor otimizado</p>
              <p className="text-5xl font-semibold mb-1">R$ 3.560</p>
              <p className="text-[#86868b] mb-6">/mês</p>
              <p className="text-sm text-[#1d1d1f] mb-6">8 influenciadores</p>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Curadoria avançada</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Relatórios semanais</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Suporte por WhatsApp</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Estratégia personalizada</span>
                </li>
              </ul>
              <a
                href="https://wa.me/5547999543437"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full py-3 bg-[#0071e3] text-white rounded-full text-sm font-medium hover:bg-[#0077ED] transition"
              >
                Falar com especialista
              </a>
            </div>

            <div className="bg-[#f5f5f7] p-8 rounded-2xl">
              <h3 className="text-2xl font-semibold mb-2">Super Premium</h3>
              <p className="text-[#86868b] mb-6">Máximo impacto</p>
              <p className="text-5xl font-semibold mb-1">R$ 4.440</p>
              <p className="text-[#86868b] mb-6">/mês</p>
              <p className="text-sm text-[#1d1d1f] mb-6">12 influenciadores</p>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Curadoria VIP</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Relatórios em tempo real</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Gerente dedicado</span>
                </li>
                <li className="flex items-center">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3">
                    ✓
                  </span>
                  <span>Estratégia completa</span>
                </li>
              </ul>
              <a
                href="https://wa.me/5547999543437"
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full py-3 bg-[#0071e3] text-white rounded-full text-sm font-medium hover:bg-[#0077ED] transition"
              >
                Falar com especialista
              </a>
            </div>
          </div>

          <p className="mt-8 text-[#86868b]">Taxa mensal isenta acima de R$ 2.500</p>
        </div>
      </section>

      {/* CTA Section - Apple Style */}
      <section className="py-20 px-6">
        <div className="max-w-[980px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-6">Pronto para transformar seu marketing?</h2>
          <p className="text-lg text-[#86868b] max-w-2xl mx-auto mb-8">
            Junte-se aos 150+ restaurantes que já estão crescendo com a AdLeader.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a
              href="https://wa.me/5547999543437"
              target="_blank"
              rel="noopener noreferrer"
              className="px-8 py-4 bg-[#0071e3] text-white rounded-full text-sm font-medium hover:bg-[#0077ED] transition-all duration-200 hover:shadow-md hover:transform hover:translate-y-[-1px]"
            >
              <FaWhatsapp className="inline mr-2" /> Falar com um especialista
            </a>
            <a
              href="#demo"
              className="px-8 py-4 bg-transparent text-[#0071e3] border border-[#0071e3] rounded-full text-sm font-medium hover:bg-[rgba(0,113,227,0.1)] transition-all duration-200"
            >
              Solicitar demonstração
            </a>
          </div>
        </div>
      </section>

      {/* Footer - Apple Style */}
      <footer className="py-12 px-6 bg-white border-t border-gray-200">
        <div className="max-w-[980px] mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <Logo size="small" textClassName="font-semibold" />
              <p className="text-sm text-[#86868b] mb-4">
                Conectando restaurantes e influenciadores locais para resultados reais.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Links Rápidos</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Início
                  </a>
                </li>
                <li>
                  <a href="#visao-geral" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Visão Geral
                  </a>
                </li>
                <li>
                  <a href="#como-funciona" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Como Funciona
                  </a>
                </li>
                <li>
                  <a href="#planos" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Planos
                  </a>
                </li>
                <li>
                  <a href="#depoimentos" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Depoimentos
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Recursos</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Guia de marketing
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Webinars
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                    Suporte
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                    FAQ
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Contato</h3>
              <ul className="space-y-2 text-sm text-[#86868b]">
                <li className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    ></path>
                  </svg>
                  <span>(47) 99954-3437</span>
                </li>
                <li className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <span><EMAIL></span>
                </li>
                <li className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    ></path>
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    ></path>
                  </svg>
                  <span>Londrina, PR - Brasil</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="pt-6 border-t border-gray-200 text-center">
            <p className="text-sm text-[#86868b]">
              &copy; {new Date().getFullYear()} crIAdores. Todos os direitos reservados.
            </p>
            <div className="mt-2">
              <Link href="/politica-de-privacidade" className="text-sm text-[#0071e3] hover:underline">
                Política de Privacidade
              </Link>
            </div>
            <div className="flex justify-center space-x-6 mt-4">
              <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                <span className="sr-only">Facebook</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </a>
              <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                <span className="sr-only">Instagram</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path
                    fillRule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </a>
              <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                <span className="sr-only">Twitter</span>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
