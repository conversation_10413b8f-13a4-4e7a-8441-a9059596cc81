"use client";

import React from 'react';
import CriadoresCard from '@/components/dashboard/CriadoresCard';
import CriadoresCardAlt from '@/components/dashboard/CriadoresCardAlt';
import CriadoresCardAlt2 from '@/components/dashboard/CriadoresCardAlt2';
import { useRouter } from 'next/navigation';

// Dados de exemplo para os cards
const mockInfluencers = [
  {
    id: '1',
    name: '<PERSON>',
    username: '@gourmetanasita',
    points: 1250,
    avatarUrl: 'https://i.pravatar.cc/150?u=ana_silva'
  },
  {
    id: '2',
    name: '<PERSON>',
    username: '@pedrosantos',
    points: 980,
    avatarUrl: 'https://i.pravatar.cc/150?u=pedro_santos'
  },
  {
    id: '3',
    name: '<PERSON>',
    username: '@carlamendes',
    points: 840,
    avatarUrl: 'https://i.pravatar.cc/150?u=carla_mendes'
  },
  {
    id: '4',
    name: '<PERSON>',
    username: '@marcosoliveira',
    points: 720,
    avatarUrl: 'https://i.pravatar.cc/150?u=marcos_oliveira'
  },
  {
    id: '5',
    name: '<PERSON> Ferreira',
    username: '@juliaferreira',
    points: 650,
    avatarUrl: 'https://i.pravatar.cc/150?u=julia_ferreira'
  }
];

export default function DesignOptionsPage() {
  const router = useRouter();

  const handleViewAllClick = () => {
    router.push('/restaurante/criadores');
  };

  const handleCriadorClick = (id: string) => {
    console.log(`Criador clicado: ${id}`);
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Opções de Design para o Card de Criadores</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <h2 className="text-lg font-semibold mb-3">Design 1 (Atual)</h2>
          <CriadoresCard
            totalInfluencers={30}
            activeInfluencers={8}
            targetInfluencers={12}
            totalContent={30}
            monthlyContent={8}
            topInfluencers={mockInfluencers}
            onViewAllClick={handleViewAllClick}
            onInfluencerClick={handleCriadorClick}
          />
        </div>
        
        <div>
          <h2 className="text-lg font-semibold mb-3">Design 2</h2>
          <CriadoresCardAlt
            totalInfluencers={30}
            activeInfluencers={8}
            targetInfluencers={12}
            totalContent={30}
            monthlyContent={8}
            topInfluencers={mockInfluencers}
            onViewAllClick={handleViewAllClick}
            onInfluencerClick={handleInfluencerClick}
          />
        </div>
        
        <div>
          <h2 className="text-lg font-semibold mb-3">Design 3</h2>
          <CriadoresCardAlt2
            totalInfluencers={30}
            activeInfluencers={8}
            targetInfluencers={12}
            totalContent={30}
            monthlyContent={8}
            topInfluencers={mockInfluencers}
            onViewAllClick={handleViewAllClick}
            onInfluencerClick={handleInfluencerClick}
          />
        </div>
      </div>
      
      <div className="mt-8">
        <button 
          onClick={() => router.push('/restaurante')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Voltar para o Dashboard
        </button>
      </div>
    </div>
  );
}
