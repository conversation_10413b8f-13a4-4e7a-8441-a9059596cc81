"use client";

import { useState, Suspense } from 'react';
import { useDynamicPopup } from '@/hooks/useDynamicPopup';
import DynamicPopup from '@/components/ui/DynamicPopup';
import { FaInfo, FaImage, FaChartBar } from 'react-icons/fa'; // FaTable, FaCode removidos

function PopupsDinamicosContent() {
  // Hook para gerenciar o popup dinâmico
  const {
    isOpen,
    title,
    content,
    size,
    openPopup,
    closePopup,
    updateContent,
    updateTitle,
    updateSize
  } = useDynamicPopup({
    popupId: 'dynamic-popup'
  });
  
  // Estado para o contador
  const [counter, setCounter] = useState(0);
  
  // Função para abrir o popup com conteúdo de texto
  const openTextPopup = () => {
    openPopup({
      title: 'Conteúdo de Texto',
      content: (
        <div className="space-y-4">
          <p>
            Este é um exemplo de popup com conteúdo de texto simples.
            O conteúdo pode ser atualizado dinamicamente sem fechar o popup.
          </p>
          
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-700 mb-2">Contador: {counter}</h4>
            <div className="flex space-x-2">
              <button
                onClick={() => setCounter(prev => prev - 1)}
                className="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Diminuir
              </button>
              <button
                onClick={() => setCounter(prev => prev + 1)}
                className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                Aumentar
              </button>
            </div>
          </div>
          
          <div className="flex space-x-3 mt-4">
            <button
              onClick={() => updateTitle('Título Atualizado')}
              className="px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              Atualizar Título
            </button>
            <button
              onClick={() => updateSize('large')}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Aumentar Tamanho
            </button>
          </div>
        </div>
      ),
      size: 'medium'
    });
  };
  
  // Função para abrir o popup com conteúdo de imagem
  const openImagePopup = () => {
    openPopup({
      title: 'Visualizador de Imagem',
      content: (
        <div className="space-y-4">
          <div className="bg-gray-100 rounded-lg overflow-hidden">
            <img
              src="https://images.unsplash.com/photo-1682687982501-1e58ab814714"
              alt="Imagem de exemplo"
              className="w-full h-auto"
            />
          </div>
          
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Foto por Unsplash
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => updateContent(
                  <div className="space-y-4">
                    <div className="bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src="https://images.unsplash.com/photo-1682687982501-1e58ab814714?q=80&w=1000"
                        alt="Imagem de exemplo em baixa qualidade"
                        className="w-full h-auto"
                      />
                    </div>
                    <div className="text-center text-gray-500">
                      Imagem em baixa qualidade
                    </div>
                    <button
                      onClick={() => openImagePopup()}
                      className="w-full px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Restaurar Qualidade Original
                    </button>
                  </div>
                )}
                className="px-3 py-1 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
              >
                Baixa Qualidade
              </button>
              <button
                onClick={() => updateSize('fullscreen')}
                className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                Tela Cheia
              </button>
            </div>
          </div>
        </div>
      ),
      size: 'large'
    });
  };
  
  // Função para abrir o popup com conteúdo de gráfico
  const openChartPopup = () => {
    openPopup({
      title: 'Visualização de Dados',
      content: (
        <div className="space-y-6">
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium mb-4">Dados de Vendas</h3>
            
            <div className="h-64 flex items-end space-x-4 pb-4 border-b border-gray-200">
              {[65, 40, 85, 30, 55, 60, 75].map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-blue-500 rounded-t"
                    style={{ height: `${value}%` }}
                  ></div>
                  <div className="text-xs mt-1">{`Dia ${index + 1}`}</div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 flex justify-between">
              <div className="text-sm text-gray-500">Última semana</div>
              <div className="text-sm font-medium">Total: R$ 12.450,00</div>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => updateContent(
                <div className="space-y-6">
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 className="text-lg font-medium mb-4">Dados de Vendas (Mensal)</h3>
                    
                    <div className="h-64 flex items-end space-x-4 pb-4 border-b border-gray-200">
                      {[45, 60, 35, 80, 55, 70, 50, 65, 75, 40, 90, 55].map((value, index) => (
                        <div key={index} className="flex-1 flex flex-col items-center">
                          <div
                            className="w-full bg-purple-500 rounded-t"
                            style={{ height: `${value}%` }}
                          ></div>
                          <div className="text-xs mt-1">{`M${index + 1}`}</div>
                        </div>
                      ))}
                    </div>
                    
                    <div className="mt-4 flex justify-between">
                      <div className="text-sm text-gray-500">Último ano</div>
                      <div className="text-sm font-medium">Total: R$ 145.320,00</div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-3">
                    <button
                      onClick={() => openChartPopup()}
                      className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Visualização Semanal
                    </button>
                    <button
                      onClick={() => updateTitle('Relatório Anual de Vendas')}
                      className="px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                    >
                      Atualizar Título
                    </button>
                  </div>
                </div>
              )}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Visualização Mensal
            </button>
            <button
              onClick={() => updateTitle('Relatório Semanal de Vendas')}
              className="px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              Atualizar Título
            </button>
          </div>
        </div>
      ),
      size: 'medium'
    });
  };
  
  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Popups com Conteúdo Dinâmico</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center mb-4">
            <FaInfo className="text-blue-500 mr-2" size={24} />
            <h2 className="text-xl font-semibold">Conteúdo de Texto</h2>
          </div>
          <p className="text-gray-600 mb-4">
            Exemplo de popup com conteúdo de texto que pode ser atualizado dinamicamente.
          </p>
          <button
            onClick={openTextPopup}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Abrir Popup
          </button>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center mb-4">
            <FaImage className="text-green-500 mr-2" size={24} />
            <h2 className="text-xl font-semibold">Visualizador de Imagem</h2>
          </div>
          <p className="text-gray-600 mb-4">
            Exemplo de popup para visualização de imagens com opções de tamanho.
          </p>
          <button
            onClick={openImagePopup}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Abrir Popup
          </button>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center mb-4">
            <FaChartBar className="text-purple-500 mr-2" size={24} />
            <h2 className="text-xl font-semibold">Visualização de Dados</h2>
          </div>
          <p className="text-gray-600 mb-4">
            Exemplo de popup para visualização de dados com gráficos interativos.
          </p>
          <button
            onClick={openChartPopup}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            Abrir Popup
          </button>
        </div>
      </div>
      
      <div className="bg-white rounded-xl p-6 shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-4">Como Funciona</h2>
        <div className="space-y-4">
          <p className="text-gray-600">
            Os popups com conteúdo dinâmico são implementados usando o hook <code>useDynamicPopup</code>, que permite:
          </p>
          
          <ul className="list-disc pl-5 space-y-2 text-gray-600">
            <li>Abrir um popup com conteúdo inicial</li>
            <li>Atualizar o conteúdo do popup sem fechá-lo</li>
            <li>Atualizar o título do popup</li>
            <li>Alterar o tamanho do popup</li>
            <li>Manter o estado do popup entre atualizações</li>
          </ul>
          
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mt-4">
            <h3 className="font-medium text-blue-700 mb-2">Casos de Uso</h3>
            <ul className="list-disc pl-5 space-y-1 text-blue-600">
              <li>Visualizadores de imagem com opções de zoom</li>
              <li>Dashboards interativos com filtros</li>
              <li>Formulários com validação em tempo real</li>
              <li>Visualizadores de dados com diferentes visualizações</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Popup Dinâmico */}
      <DynamicPopup
        id="dynamic-popup"
        isOpen={isOpen}
        onClose={closePopup}
        title={title}
        content={content}
        size={size}
      />
    </div>
  );
}

export default function PopupsDinamicosPage() {
  return (
    <Suspense fallback={<div>Carregando...</div>}>
      <PopupsDinamicosContent />
    </Suspense>
  );
}
