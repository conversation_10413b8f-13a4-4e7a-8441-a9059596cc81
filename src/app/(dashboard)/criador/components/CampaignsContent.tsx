"use client";
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { FaTrophy, FaCalendarAlt, FaHashtag, FaAt, FaMapMarkerAlt, FaAward, FaCheckSquare, FaCalendarCheck } from 'react-icons/fa';
import RankingHighlight from '@/components/premium/RankingHighlight';
import CriadorRankingTable from '@/components/campaign/CriadorRankingTable';
import LeaderboardTable from '@/components/campaign/LeaderboardTable';
import AchievementsCard from '@/components/criador/AchievementsCard';
import PointsHistoryCard from '@/components/criador/PointsHistoryCard';
import TaskList from '@/components/tasks/TaskList';
import ScheduleCalendar from '@/components/schedule/ScheduleCalendar';
import { brazilianCities } from '@/services/mockCampaignService';
import { campaignService } from '@/services/campaignService';
import type { Campaign, CampaignInfluencer } from '@/services/campaignService';

interface CampaignsContentProps {
  influencer: any;
}

export default function CampaignsContent({ influencer }: CampaignsContentProps) {
  const searchParams = useSearchParams();
  const campaignIdFromUrl = searchParams?.get('campaign');

  const [campaigns, setCampaigns] = useState<CampaignInfluencer[]>([]);
  const [availableCampaigns, setAvailableCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [cityFilter, setCityFilter] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [applyingToCampaign, setApplyingToCampaign] = useState<string | null>(null);
  const [applyMessage, setApplyMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    if (influencer?.id) {
      fetchCampaigns(influencer.id);
      fetchAvailableCampaigns();
    }
  }, [influencer]);

  useEffect(() => {
    if (campaignIdFromUrl && campaigns.length > 0) {
      const campaign = campaigns.find(c => c.campaigns.id === campaignIdFromUrl);
      if (campaign) {
        setSelectedCampaign(campaign.campaigns);
      }
    }
  }, [campaignIdFromUrl, campaigns]);

  const fetchCampaigns = async (influencerId: string) => {
    setLoading(true);
    try {
      // Usar o serviço real que consulta o Supabase
      const data = await campaignService.getInfluencerCampaigns(influencerId);
      console.log('Fetched campaigns from database:', data);
      setCampaigns(data);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableCampaigns = async (city?: string) => {
    try {
      // Usar o serviço real que consulta o Supabase
      const filters = city ? { city } : undefined;
      const data = await campaignService.getAvailableCampaigns(filters);
      console.log('Fetched available campaigns from database:', data);

      // Filtrar campanhas que o criador já está participando
      if (campaigns.length > 0) {
        const campaignIds = campaigns.map(c => c.campaigns.id);
        const available = data.filter(c => !campaignIds.includes(c.id));
        setAvailableCampaigns(available);
      } else {
        setAvailableCampaigns(data);
      }
    } catch (error) {
      console.error('Error fetching available campaigns:', error);
    }
  };

  // Aplicar para uma campanha
  const applyForCampaign = async (campaignId: string) => {
    if (!influencer?.id) return;

    setApplyingToCampaign(campaignId);
    setApplyMessage(null);

    try {
      const result = await campaignService.applyForCampaign(influencer.id, campaignId);

      if (result.success) {
        setApplyMessage({ type: 'success', text: result.message });

        // Atualizar a lista de campanhas do criador
        if (result.campaignInfluencer) {
          setCampaigns(prev => [...prev, result.campaignInfluencer!]);
        }

        // Atualizar a lista de campanhas disponíveis
        setAvailableCampaigns(prev => prev.filter(c => c.id !== campaignId));
      } else {
        setApplyMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error('Error applying for campaign:', error);
      setApplyMessage({ type: 'error', text: 'Erro ao se candidatar para a campanha. Tente novamente.' });
    } finally {
      setApplyingToCampaign(null);
    }
  };

  // Filtrar campanhas por cidade
  const handleCityFilterChange = (city: string) => {
    setCityFilter(city);
    fetchAvailableCampaigns(city);
  };

  // Format date to DD/MM/YYYY
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };

  // Calculate days remaining
  const getDaysRemaining = (endDateString: string) => {
    if (!endDateString) return 0;
    const endDate = new Date(endDateString);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Filter campaigns based on status and search term
  const filteredCampaigns = campaigns.filter(item => {
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    const matchesSearch = !searchTerm ||
      item.campaigns.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.campaigns.restaurants?.name || '').toLowerCase().includes(searchTerm.toLowerCase());

    return matchesStatus && matchesSearch;
  });

  // Handle campaign selection
  const handleCampaignSelect = (campaign: Campaign) => {
    setSelectedCampaign(campaign);

    // Update URL with campaign ID
    const url = new URL(window.location.href);
    url.searchParams.set('campaign', campaign.id);
    window.history.pushState({}, '', url.toString());
  };

  // Handle campaign deselection
  const handleBackToCampaigns = () => {
    setSelectedCampaign(null);

    // Remove campaign ID from URL
    const url = new URL(window.location.href);
    url.searchParams.delete('campaign');
    window.history.pushState({}, '', url.toString());
  };

  return (
    <div>
      {selectedCampaign ? (
        // Campaign Details View
        <div>
          {/* Campaign Header */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50 mb-6">
            <div className="flex justify-between items-center">
              <button
                onClick={handleBackToCampaigns}
                className="text-blue-600 hover:text-blue-700 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Voltar para campanhas
              </button>
            </div>

            <div className="mt-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-indigo-50 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{selectedCampaign.name}</h2>
                  <p className="text-gray-600">{selectedCampaign.restaurants?.name}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Campaign Content */}
          <div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Campaign Details */}
              <div className="md:col-span-2 space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <FaCalendarAlt className="text-blue-500 h-4 w-4" />
                      </div>
                      <h4 className="font-medium text-gray-900">Período</h4>
                    </div>
                    <p className="text-gray-700">{formatDate(selectedCampaign.start_date)} a {formatDate(selectedCampaign.end_date)}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      {getDaysRemaining(selectedCampaign.end_date) > 0
                        ? `Faltam ${getDaysRemaining(selectedCampaign.end_date)} dias`
                        : 'Encerrada'}
                    </p>
                  </div>

                  <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center mr-3">
                        <FaMapMarkerAlt className="text-red-500 h-4 w-4" />
                      </div>
                      <h4 className="font-medium text-gray-900">Restaurante</h4>
                    </div>
                    <p className="text-gray-700">{selectedCampaign.restaurants?.name}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-3">
                        <FaHashtag className="text-purple-500 h-4 w-4" />
                      </div>
                      <h4 className="font-medium text-gray-900">Hashtags</h4>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {selectedCampaign.hashtags?.map((tag, index) => (
                        <span key={index} className="bg-gray-50 border border-gray-100 px-2 py-1 rounded-full text-sm text-gray-700">
                          #{tag}
                        </span>
                      )) || <p className="text-gray-500">Nenhuma hashtag definida</p>}
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                        <FaAt className="text-blue-500 h-4 w-4" />
                      </div>
                      <h4 className="font-medium text-gray-900">Menções</h4>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {selectedCampaign.mentions?.map((mention, index) => (
                        <span key={index} className="bg-gray-50 border border-gray-100 px-2 py-1 rounded-full text-sm text-gray-700">
                          @{mention}
                        </span>
                      )) || <p className="text-gray-500">Nenhuma menção definida</p>}
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-gray-50 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900">Descrição</h4>
                  </div>
                  <p className="text-gray-700">
                    {selectedCampaign.description || 'Nenhuma descrição disponível para esta campanha.'}
                  </p>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-yellow-50 flex items-center justify-center mr-3">
                      <FaTrophy className="text-yellow-500 h-4 w-4" />
                    </div>
                    <h4 className="font-medium text-gray-900">Ranking Detalhado</h4>
                  </div>
                  <LeaderboardTable campaignId={selectedCampaign.id} />
                </div>

                {/* Achievements */}
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-amber-50 flex items-center justify-center mr-3">
                      <FaAward className="text-amber-500 h-4 w-4" />
                    </div>
                    <h4 className="font-medium text-gray-900">Suas Conquistas</h4>
                  </div>
                  <AchievementsCard influencerId={influencer?.id || ''} className="border-0 shadow-none" />
                </div>

                {/* Points History */}
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900">Histórico de Pontos</h4>
                  </div>
                  <PointsHistoryCard
                    campaignInfluencerId={
                      campaigns.find(c => c.campaigns.id === selectedCampaign.id)?.id || ''
                    }
                    className="border-0 shadow-none"
                  />
                </div>

                {/* Tasks Section */}
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3">
                      <FaCheckSquare className="text-green-500 h-4 w-4" />
                    </div>
                    <h4 className="font-medium text-gray-900">Tarefas da Campanha</h4>
                  </div>
                  <TaskList
                    campaignId={selectedCampaign.id}
                    userId={influencer?.id}
                    className="border-0 shadow-none"
                  />
                </div>

                {/* Schedule Section */}
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                      <FaCalendarCheck className="text-blue-500 h-4 w-4" />
                    </div>
                    <h4 className="font-medium text-gray-900">Agenda da Campanha</h4>
                  </div>
                  <ScheduleCalendar
                    campaignId={selectedCampaign.id}
                    className="border-0 shadow-none"
                  />
                </div>
              </div>

              {/* Ranking Highlight */}
              <div className="md:col-span-1">
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50 sticky top-24">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-yellow-50 flex items-center justify-center mr-3">
                      <FaTrophy className="text-yellow-500 h-4 w-4" />
                    </div>
                    <h4 className="font-medium text-gray-900">Seu Ranking</h4>
                  </div>

                  <RankingHighlight
                    campaignId={selectedCampaign.id}
                    userId={influencer?.id || ''}
                    userRole="influencer"
                  />
                </div>

                {/* Gamification Stats */}
                <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50 mt-6">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 rounded-full bg-amber-50 flex items-center justify-center mr-3">
                      <FaAward className="text-amber-500 h-4 w-4" />
                    </div>
                    <h4 className="font-medium text-gray-900">Estatísticas de Gamificação</h4>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-500">Total de Pontos</p>
                      <p className="text-xl font-bold text-gray-900">
                        {campaigns.find(c => c.campaigns.id === selectedCampaign.id)?.total_points || 0}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Posição Atual</p>
                      <p className="text-xl font-bold text-gray-900">
                        {campaigns.find(c => c.campaigns.id === selectedCampaign.id)?.rank || '-'}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500">Conquistas Desbloqueadas</p>
                      <p className="text-xl font-bold text-gray-900">
                        {/* This would need to be fetched from the backend */}
                        {Math.floor(Math.random() * 5)} de 12
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Campaigns List View
        <>
          {/* Filters and Search */}
          <div className="mb-8">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Filtrar Campanhas</h3>
              </div>

              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-4 w-full">
                  <select
                    className="px-4 py-2 rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 border border-gray-100"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="all">Todas as campanhas</option>
                    <option value="accepted">Ativas</option>
                    <option value="pending">Pendentes</option>
                    <option value="rejected">Rejeitadas</option>
                    <option value="completed">Concluídas</option>
                  </select>

                  <select
                    className="px-4 py-2 rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 border border-gray-100"
                    value={cityFilter}
                    onChange={(e) => handleCityFilterChange(e.target.value)}
                  >
                    <option value="">Todas as cidades</option>
                    {brazilianCities.map((location, index) => (
                      <option key={index} value={location.city}>
                        {location.city} - {location.state}
                      </option>
                    ))}
                  </select>

                  <div className="relative flex-grow">
                    <input
                      type="text"
                      placeholder="Buscar campanhas..."
                      className="px-4 py-2 w-full rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 border border-gray-100"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Mensagem de sucesso ou erro ao se candidatar */}
            {applyMessage && (
              <div className={`mt-4 p-4 rounded-xl shadow-sm border border-gray-50 ${
                applyMessage.type === 'success'
                  ? 'bg-white text-gray-700'
                  : 'bg-white text-gray-700'
              }`}>
                {applyMessage.text}
              </div>
            )}
          </div>

          {/* Minhas Campanhas Section - Design minimalista e premium */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-indigo-50 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Minhas Campanhas</h3>
              </div>
            </div>

            {/* Campaigns Grid */}
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredCampaigns.length === 0 ? (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500 mb-4">Nenhuma campanha encontrada com os filtros atuais.</p>
                {statusFilter !== 'all' && (
                  <button
                    onClick={() => setStatusFilter('all')}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Ver todas as campanhas
                  </button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCampaigns.map((item) => (
                  <div
                    key={item.id}
                    className="bg-white p-6 rounded-xl shadow-sm border border-gray-50 cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleCampaignSelect(item.campaigns)}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-semibold text-lg">{item.campaigns.name}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        item.status === 'accepted' ? 'bg-green-50 text-green-700' :
                        item.status === 'pending' ? 'bg-yellow-50 text-yellow-700' :
                        item.status === 'rejected' ? 'bg-red-50 text-red-700' :
                        'bg-gray-50 text-gray-700'
                      }`}>
                        {item.status === 'accepted' ? 'Ativa' :
                         item.status === 'pending' ? 'Pendente' :
                         item.status === 'rejected' ? 'Rejeitada' :
                         item.status === 'completed' ? 'Concluída' : 'Desconhecido'}
                      </span>
                    </div>

                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {item.campaigns.description || 'Sem descrição disponível'}
                    </p>

                    <div className="flex justify-between text-sm">
                      <div>
                        <p className="text-gray-500">Restaurante</p>
                        <p className="font-medium">{item.campaigns.restaurants?.name}</p>
                      </div>

                      <div>
                        <p className="text-gray-500">Término</p>
                        <p className="font-medium">{formatDate(item.campaigns.end_date)}</p>
                        <p className="text-xs text-gray-500">
                          {getDaysRemaining(item.campaigns.end_date) > 0
                            ? `Faltam ${getDaysRemaining(item.campaigns.end_date)} dias`
                            : 'Encerrada'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Available Campaigns Section */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-50">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900">Campanhas Disponíveis</h3>
              </div>

              {cityFilter && (
                <span className="text-sm bg-gray-50 px-3 py-1 rounded-full flex items-center border border-gray-100">
                  {cityFilter}
                  <button
                    onClick={() => handleCityFilterChange('')}
                    className="ml-2 text-gray-500 hover:text-gray-900 focus:outline-none"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>

            {availableCampaigns.length === 0 ? (
              <div className="text-center p-8 bg-gray-50 rounded-lg">
                <p className="text-gray-500 mb-2">Nenhuma campanha disponível {cityFilter ? `em ${cityFilter}` : 'no momento'}.</p>
                {cityFilter && (
                  <button
                    onClick={() => handleCityFilterChange('')}
                    className="text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Ver todas as cidades
                  </button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-100">
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campanha</th>
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Restaurante</th>
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Localização</th>
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pagamento</th>
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Término</th>
                      <th className="py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {availableCampaigns.map((campaign) => (
                      <tr key={campaign.id} className="hover:bg-gray-50 transition-colors">
                        <td className="py-4 pr-4">
                          <div className="text-sm font-medium text-gray-900">{campaign.name}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {campaign.current_influencers}/{campaign.max_influencers} criadores
                          </div>
                        </td>
                        <td className="py-4 pr-4">
                          <div className="text-sm text-gray-500">{campaign.restaurants?.name}</div>
                        </td>
                        <td className="py-4 pr-4">
                          <div className="text-sm text-gray-500">{campaign.city}, {campaign.state}</div>
                        </td>
                        <td className="py-4 pr-4">
                          <div className="text-sm font-medium">R$ {campaign.payment_amount}</div>
                        </td>
                        <td className="py-4 pr-4">
                          <div className="text-sm text-gray-500">{formatDate(campaign.end_date)}</div>
                          <div className="text-xs text-gray-400">
                            {getDaysRemaining(campaign.end_date) > 0
                              ? `${getDaysRemaining(campaign.end_date)} dias`
                              : 'Encerrada'}
                          </div>
                        </td>
                        <td className="py-4 text-right text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleCampaignSelect(campaign)}
                              className="px-4 py-1 rounded-lg bg-gray-50 text-gray-700 text-xs hover:bg-gray-100 transition-colors border border-gray-100"
                            >
                              Detalhes
                            </button>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                applyForCampaign(campaign.id);
                              }}
                              disabled={applyingToCampaign === campaign.id}
                              className={`px-4 py-1 rounded-lg bg-blue-600 text-white text-xs hover:bg-blue-700 transition-colors ${
                                applyingToCampaign === campaign.id ? 'opacity-50 cursor-not-allowed' : ''
                              }`}
                            >
                              {applyingToCampaign === campaign.id ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Enviando...
                                </>
                              ) : (
                                'Candidatar-se'
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {availableCampaigns.length > 0 && (
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-500">
                  Mostrando {availableCampaigns.length} campanhas disponíveis
                  {cityFilter ? ` em ${cityFilter}` : ''}
                </p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
