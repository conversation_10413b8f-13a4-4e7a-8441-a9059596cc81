"use client";

import { Fa<PERSON><PERSON><PERSON>ram, <PERSON>aGoogle, FaTripad<PERSON>, FaChartLine, FaHistory } from 'react-icons/fa';
import { usePopupNavigation } from '@/hooks/usePopupNavigation';
import StandardPopup, { PopupTab } from '@/components/ui/StandardPopup';
import PopupLink from '@/components/ui/PopupLink';
import { Suspense } from 'react';

function PresencaDigitalExemploContent() {
  // Usar o hook para gerenciar o popup
  const {
    isOpen,
    activeTabId,
    openPopup,
    closePopup,
    getPopupLink
  } = usePopupNavigation({
    popupId: 'presenca-digital',
    defaultTabId: 'resumo'
  });

  // Definir as abas do popup
  const tabs: PopupTab[] = [
    {
      id: 'resumo',
      label: 'Resumo',
      icon: <FaChartLine className="text-blue-500 mr-1" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Card do Instagram */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <FaInstagram className="text-pink-500 mr-2" />
              Instagram
            </h3>

            <div className="space-y-4">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500">Seguidores</p>
                  <p className="text-2xl font-bold">77.7K</p>
                  <p className="text-xs text-green-500">+3%</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Engajamento médio</p>
                  <p className="text-2xl font-bold">4.8%</p>
                  <p className="text-xs text-green-500">+1.2%</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-500 mb-2">Hashtags mais usadas</p>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-gray-100 px-2 py-1 rounded text-xs">#gastronomia</span>
                  <span className="bg-gray-100 px-2 py-1 rounded text-xs">#rooftop</span>
                  <span className="bg-gray-100 px-2 py-1 rounded text-xs">#jantar</span>
                  <span className="bg-gray-100 px-2 py-1 rounded text-xs">#vistapanoramica</span>
                </div>
              </div>
            </div>
          </div>

          {/* Card do Google Reviews */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <FaGoogle className="text-yellow-500 mr-2" />
              Google Reviews
            </h3>

            <div className="space-y-4">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500">Média de avaliações</p>
                  <p className="text-2xl font-bold">4.7/5</p>
                  <p className="text-xs text-green-500">+0.1</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Volume de comentários</p>
                  <p className="text-2xl font-bold">637</p>
                  <p className="text-xs text-green-500">+15</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-500 mb-2">Tópicos recorrentes</p>

                <div className="space-y-2">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Atendimento</span>
                      <span className="text-sm">78% positivo</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Comida</span>
                      <span className="text-sm">92% positivo</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Tempo de espera</span>
                      <span className="text-sm">65% positivo</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Card do TripAdvisor */}
          <div className="bg-white rounded-lg p-6 shadow-sm md:col-span-2">
            <h3 className="text-lg font-medium mb-4 flex items-center">
              <FaTripadvisor className="text-green-600 mr-2" />
              TripAdvisor
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500">Posição no ranking</p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-bold">#23</p>
                  <p className="text-xs text-green-500 ml-2">Top 10%</p>
                </div>
                <p className="text-xs text-gray-500">de 1201 restaurantes</p>

                <div className="mt-4">
                  <p className="text-sm text-gray-500 mb-2">Posição relativa</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-500">Avaliação média</p>
                <p className="text-2xl font-bold">4.3/5</p>
                <p className="text-xs text-gray-500">264 avaliações</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'comparativo',
      label: 'Comparativo',
      icon: <FaChartLine className="text-purple-500 mr-1" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-medium mb-4">Comparativo Mensal</h3>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <p className="text-gray-500 mb-4">Comparação de métricas mês a mês</p>

            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">Instagram</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Maio</p>
                    <p className="text-lg font-bold">75.2K</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Junho</p>
                    <p className="text-lg font-bold">76.5K</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Julho</p>
                    <p className="text-lg font-bold">77.7K</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Google Reviews</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Maio</p>
                    <p className="text-lg font-bold">4.5/5</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Junho</p>
                    <p className="text-lg font-bold">4.6/5</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Julho</p>
                    <p className="text-lg font-bold">4.7/5</p>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">TripAdvisor</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Maio</p>
                    <p className="text-lg font-bold">#28</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Junho</p>
                    <p className="text-lg font-bold">#25</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <p className="text-sm text-gray-500">Julho</p>
                    <p className="text-lg font-bold">#23</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'historico',
      label: 'Histórico',
      icon: <FaHistory className="text-gray-500 mr-1" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-medium mb-4">Histórico de Dados</h3>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <p className="text-gray-500 mb-4">Histórico dos últimos 12 meses</p>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mês</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Instagram</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Google</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TripAdvisor</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Julho 2023</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">77.7K</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4.7/5</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#23</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Junho 2023</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">76.5K</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4.6/5</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#25</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Maio 2023</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">75.2K</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4.5/5</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#28</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Abril 2023</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">74.1K</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4.5/5</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">#30</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Presença Digital - Exemplo de Navegação</h1>

      <div className="bg-white rounded-xl p-6 shadow-sm mb-6">
        <p className="text-gray-600 mb-4">
          Esta página demonstra como usar o sistema de navegação por URL para popups. Você pode abrir o popup e navegar entre as abas, e a URL será atualizada para refletir o estado atual.
        </p>

        {/* Botão para abrir o popup */}
        <button
          onClick={() => openPopup('resumo')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Ver Detalhes Completos
        </button>
      </div>

      {/* Cards resumidos */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Card resumido do Instagram */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold flex items-center">
              <FaInstagram className="text-pink-500 mr-2" />
              Instagram
            </h3>
            <PopupLink
              popupId="presenca-digital"
              tabId="resumo"
              className="text-xs text-blue-600 hover:underline"
              onClick={() => openPopup('resumo')}
            >
              Ver detalhes
            </PopupLink>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500">Seguidores</p>
              <p className="text-2xl font-bold">77.7K</p>
              <p className="text-xs text-green-500">+3% vs. mês anterior</p>
            </div>
          </div>
        </div>

        {/* Card resumido do Google */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold flex items-center">
              <FaGoogle className="text-yellow-500 mr-2" />
              Google Reviews
            </h3>
            <PopupLink
              popupId="presenca-digital"
              tabId="resumo"
              className="text-xs text-blue-600 hover:underline"
              onClick={() => openPopup('resumo')}
            >
              Ver detalhes
            </PopupLink>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500">Avaliação média</p>
              <p className="text-2xl font-bold">4.7/5</p>
              <p className="text-xs text-green-500">+0.1 vs. mês anterior</p>
            </div>
          </div>
        </div>

        {/* Card resumido do TripAdvisor */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold flex items-center">
              <FaTripadvisor className="text-green-600 mr-2" />
              TripAdvisor
            </h3>
            <PopupLink
              popupId="presenca-digital"
              tabId="resumo"
              className="text-xs text-blue-600 hover:underline"
              onClick={() => openPopup('resumo')}
            >
              Ver detalhes
            </PopupLink>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-500">Ranking</p>
              <p className="text-2xl font-bold">#23</p>
              <p className="text-xs text-green-500">+2 posições vs. mês anterior</p>
            </div>
          </div>
        </div>
      </div>

      {/* Links diretos para as abas */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="font-semibold mb-4">Links Rápidos</h3>
        <div className="flex flex-wrap gap-4">
          <PopupLink
            popupId="presenca-digital"
            tabId="resumo"
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors"
            onClick={() => openPopup('resumo')}
          >
            <FaChartLine className="inline mr-2" />
            Resumo
          </PopupLink>

          <PopupLink
            popupId="presenca-digital"
            tabId="comparativo"
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors"
            onClick={() => openPopup('comparativo')}
          >
            <FaChartLine className="inline mr-2" />
            Comparativo
          </PopupLink>

          <PopupLink
            popupId="presenca-digital"
            tabId="historico"
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors"
            onClick={() => openPopup('historico')}
          >
            <FaHistory className="inline mr-2" />
            Histórico
          </PopupLink>
        </div>
      </div>

      {/* URLs diretas */}
      <div className="bg-white rounded-xl p-6 shadow-sm mt-6">
        <h3 className="font-semibold mb-4">URLs Diretas</h3>
        <div className="space-y-2">
          <div>
            <p className="text-sm font-medium">URL para Resumo:</p>
            <code className="bg-gray-100 p-2 rounded block text-sm overflow-x-auto">
              {getPopupLink('resumo')}
            </code>
          </div>
          <div>
            <p className="text-sm font-medium">URL para Comparativo:</p>
            <code className="bg-gray-100 p-2 rounded block text-sm overflow-x-auto">
              {getPopupLink('comparativo')}
            </code>
          </div>
          <div>
            <p className="text-sm font-medium">URL para Histórico:</p>
            <code className="bg-gray-100 p-2 rounded block text-sm overflow-x-auto">
              {getPopupLink('historico')}
            </code>
          </div>
        </div>
      </div>

      {/* Popup de Presença Digital */}
      <StandardPopup
        id="presenca-digital"
        isOpen={isOpen}
        onClose={closePopup}
        title="Presença Digital"
        tabs={tabs}
        defaultTabId={activeTabId}
        size="large"
        minContentHeight="450px"
      />
    </div>
  );
}

export default function PresencaDigitalExemploPage() {
  return (
    <Suspense fallback={<div>Carregando...</div>}>
      <PresencaDigitalExemploContent />
    </Suspense>
  );
}
