"use client";

import { useState, useEffect, useRef, Suspense } from "react";
import { FaWhatsapp, FaC<PERSON>, FaQuestionCircle, FaSignOutAlt } from "react-icons/fa"; // FaTrophy removido
// import { handleAuthError } from "@/utils/authErrorHandler"; // Removido
import { useAuth } from "@/contexts/AuthContext";
import RouteGuard from "@/components/auth/RouteGuard";
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useRouter, useSearchParams } from 'next/navigation';
// Importar componentes
import Logo from "@/components/Logo";
import DropdownMenu from "@/components/ui/DropdownMenu";
// Importar utilitários para busca de dados de restaurante
import { getRestaurantData, createRestaurantProfile } from "@/lib/restaurant/getRestaurantData";

// Componentes de card
import PlanCard from "@/components/dashboard/PlanCard";
import PlanDetailsPopupNew from "@/components/dashboard/PlanDetailsPopupNew";
import DigitalPresenceCard from "@/components/dashboard/DigitalPresenceCard";
import ImpactoFaturamentoCard from "@/components/dashboard/ImpactoFaturamentoCard";
// import ImpactoFaturamentoPopup from "@/components/dashboard/ImpactoFaturamentoPopup"; // Removido
import ImpactoFaturamentoPopupNew from "@/components/dashboard/ImpactoFaturamentoPopupNew";
import SimpleCriadoresCard from "@/components/dashboard/SimpleCriadoresCard";
import DatabaseCampanhasSection from "@/components/dashboard/DatabaseCampanhasSection";
// import RatingStatsCard from '@/components/dashboard/RatingStatsCard'; // Removido pois as avaliações foram integradas ao PlanCard
import AllRatingsPopup from '@/components/popups/AllRatingsPopup';
// import EngagementComparisonChart from '@/components/dashboard/EngagementComparisonChart'; // Removido
// import EngagementComparisonChartFallback from '@/components/dashboard/EngagementComparisonChartFallback'; // Removido
// import EngagementComparisonChartSimple from '@/components/dashboard/EngagementComparisonChartSimple'; // Removido
import EngagementComparisonChartStatic from '@/components/dashboard/EngagementComparisonChartStatic';
// import ErrorBoundary from '@/components/ErrorBoundary'; // Removido
// Removido o import do PasswordChangeAlert
// Removed duplicate supabase import
// Define interface for type safety
import type { Restaurant, DigitalPresence, RestaurantCriadorJoin } from '@/types/restaurant'; // Criador atualizado

// Definições de interface movidas para @/types/restaurant.ts
// interface Restaurant { ... }
// interface DigitalPresence { ... }
// interface Influencer { ... }
// interface RestaurantInfluencerJoin { ... }

// Interface para os criadores no card - Removida pois não é mais necessária
// interface CardInfluencer {
//   id: string;
//   name: string;
//   username: string;
//   points: number;
//   avatarUrl: string;
// }

// Importar o novo Popup e SettingsModal
import Popup from "@/components/ui/Popup";
// import SimplePopup from "@/components/ui/SimplePopup"; // Removido
import SettingsModal from "@/components/ui/SettingsModal";
// import CampanhasSection from "@/components/dashboard/CampanhasSection"; // Removido
import PresencaDigitalDetailsModal from "@/components/dashboard/PresencaDigitalDetailsModal";
import { useDynamicPopup } from '@/hooks/useDynamicPopup';
import { usePopupNavigation } from '@/hooks/usePopupNavigation';

function RestauranteDashboardContentWrapper() {
  return (
    <RouteGuard requiredRole="restaurant">
      <Suspense fallback={<div className="flex justify-center items-center h-screen">Loading...</div>}>
        <RestaurantDashboardContent />
      </Suspense>
    </RouteGuard>
  );
}

export default function RestauranteDashboard() {
  return <RestauranteDashboardContentWrapper />;
}

function RestaurantDashboardContent() {
  // Obter parâmetros da URL
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient();
  const router = useRouter();
  const { user } = useAuth();

  // Efeito para verificar e processar o parâmetro 'campaign' na URL
  useEffect(() => {
    if (searchParams) {
      const campaignId = searchParams.get('campaign');
      if (campaignId) {
      console.log('Parâmetro campaign encontrado na URL:', campaignId);
    }
  }
  }, [searchParams]);

  // Usar o hook useDynamicPopup para gerenciar popups dinâmicos
  const {
    isOpen: isPopupOpen,
    title: popupTitle,
    content: popupContent,
    openPopup: openDynamicPopup,
    closePopup: closeDynamicPopup
  } = useDynamicPopup({
    popupId: 'restaurant-popup'
  });

  // Usar o hook usePopupNavigation para o modal de configurações
  const {
    isOpen: isSettingsModalOpen,
    openPopup: openSettingsModal,
    closePopup: closeSettingsModal
  } = usePopupNavigation({
    popupId: 'settings-modal',
    defaultTabId: 'info'
  });

  const isInitialFetchDone = useRef(false);
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);

  // Estado para armazenar os dados de presença digital - usado no card de Presença Digital
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [digitalPresence, setDigitalPresence] = useState<DigitalPresence | null>(null);
  // const [influencers, setInfluencers] = useState<Influencer[]>([]); // Removido pois não é mais necessário
  // Removido estado de contentCount, agora gerenciado pelo componente SimpleInfluencersCard
  // Removido estado de campanhas e topInfluencer, agora gerenciados de outra forma
  // Removido estado user não utilizado
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const [isPresencaPopupOpen, setIsPresencaPopupOpen] = useState(false);

  const openPopup = (title: string, content: React.ReactNode) => {
    openDynamicPopup({
      title,
      content,
    });
  };

  // Função para buscar dados adicionais (presença digital, contagem de conteúdo, criadores)
  const fetchAdditionalData = async (restaurantData: Restaurant) => {
    try {
      // Buscar dados de presença digital
      console.log("Fetching digital presence data for restaurant ID:", restaurantData.id);
      const { data: digitalPresenceData, error: digitalPresenceError } = await supabase
        .from("digital_presence")
        .select("*")
        .eq("restaurant_id", restaurantData.id)
        .order("snapshot_date", { ascending: false })
        .limit(1)
        .maybeSingle();

      if (digitalPresenceError) {
        console.error("Error fetching digital presence data:", digitalPresenceError);
      } else if (digitalPresenceData) {
        console.log("Digital presence data found:", digitalPresenceData);

        // Converter os dados para o formato da interface DigitalPresence
        const formattedData: DigitalPresence = {
          instagram: {
            followers: digitalPresenceData.instagram_followers,
            previous_followers: digitalPresenceData.instagram_previous_followers,
            engagement_rate: digitalPresenceData.instagram_engagement_rate
          },
          google_reviews: {
            rating: digitalPresenceData.google_rating,
            previous_rating: digitalPresenceData.google_previous_rating,
            total_reviews: digitalPresenceData.google_total_reviews,
            new_reviews: digitalPresenceData.google_new_reviews
          },
          tripadvisor: {
            ranking: digitalPresenceData.tripadvisor_ranking,
            total_places: digitalPresenceData.tripadvisor_total_places,
            rating: digitalPresenceData.tripadvisor_rating,
            reviews: digitalPresenceData.tripadvisor_reviews
          }
        };

        // Only update state if the digital presence data has actually changed
        setDigitalPresence(prevDigitalPresence => {
          // Perform a deep comparison or compare relevant properties
          if (!prevDigitalPresence ||
              prevDigitalPresence.instagram.followers !== formattedData.instagram.followers ||
              prevDigitalPresence.instagram.previous_followers !== formattedData.instagram.previous_followers ||
              prevDigitalPresence.instagram.engagement_rate !== formattedData.instagram.engagement_rate ||
              prevDigitalPresence.google_reviews.rating !== formattedData.google_reviews.rating ||
              prevDigitalPresence.google_reviews.previous_rating !== formattedData.google_reviews.previous_rating ||
              prevDigitalPresence.google_reviews.total_reviews !== formattedData.google_reviews.total_reviews ||
              prevDigitalPresence.google_reviews.new_reviews !== formattedData.google_reviews.new_reviews ||
              prevDigitalPresence.tripadvisor.ranking !== formattedData.tripadvisor.ranking ||
              prevDigitalPresence.tripadvisor.total_places !== formattedData.tripadvisor.total_places ||
              prevDigitalPresence.tripadvisor.rating !== formattedData.tripadvisor.rating ||
              prevDigitalPresence.tripadvisor.reviews !== formattedData.tripadvisor.reviews
             ) {
            return formattedData;
          }
          return prevDigitalPresence;
        });
      } else {
        console.log("No digital presence data found");
      }

      // Buscar dados de contagem de conteúdos
      console.log("Fetching content count data for restaurant ID:", restaurantData.id);
      try {
        // Verificar se a tabela content_count existe verificando se há algum registro
        const { data: tableCheck, error: tableCheckError } = await supabase
          .from("content_count")
          .select("id")
          .limit(1);

        if (tableCheckError || !tableCheck || tableCheck.length === 0) {
          console.warn("Table content_count might not exist:", tableCheckError);
          console.log("Creating content count entry for restaurant");

          // Inserir ou atualizar um registro padrão para o restaurante usando upsert
          const { error: upsertError } = await supabase
            .from("content_count")
            .upsert({
              restaurant_id: restaurantData.id,
              total_content: 30,
              monthly_content: 8,
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'restaurant_id'
            });

          if (upsertError) {
            console.error("Error creating/updating content count entry:", upsertError);
          } else {
            console.log("Content count entry created/updated successfully");
            // Removido: Definir valores padrão no estado
            // setContentCount({
            //   total: 30,
            //   monthly: 8
            // });
          }
        } else {
          // Buscar dados de contagem de conteúdos
          const { data: contentCountData, error: contentCountError } = await supabase
            .from("content_count")
            .select("*")
            .eq("restaurant_id", restaurantData.id)
            .maybeSingle();

          if (contentCountError) {
            console.error("Error fetching content count data:", contentCountError);
            // Valores padrão já definidos no estado inicial
          } else if (contentCountData) {
            console.log("Content count data found:", contentCountData);
            // Removido: Definir valores no estado
            // setContentCount({
            //   total: contentCountData.total_content || 30,
            //   monthly: contentCountData.monthly_content || 8
            // });
          } else {
            console.log("No content count data found, creating default entry");

            // Inserir um registro padrão para o restaurante
            // Inserir ou atualizar um registro padrão para o restaurante usando upsert
            const { error: upsertError } = await supabase
              .from("content_count")
              .upsert({
                restaurant_id: restaurantData.id,
                total_content: 30,
                monthly_content: 8,
                updated_at: new Date().toISOString()
              }, {
                onConflict: 'restaurant_id'
              });

            if (upsertError) {
              console.error("Error creating/updating content count entry:", upsertError);
            } else {
              console.log("Content count entry created/updated successfully");
              // Removido: Definir valores padrão no estado
              // setContentCount({
              //   total: 30,
              //   monthly: 8
              // });
            }
          }
        }
      } catch (error) {
        console.error("Exception when fetching content count data:", error);
        // Removido: Definir valores padrão no estado em caso de erro
        // setContentCount({
        //   total: 30,
        //   monthly: 8
        // });
      }

      // Buscar criadores associados ao restaurante
      console.log("Fetching criadores for restaurant ID:", restaurantData.id);
      const { data: assocData, error: assocError } = await supabase
        .from("restaurant_influencers")
        .select("influencers(*)")
        .eq("restaurant_id", restaurantData.id) as { data: RestaurantCriadorJoin[] | null; error: any };

      if (assocError) {
        console.error("Error fetching associated influencers:", assocError);
        // Continue execution
      } else if (!assocData || assocData.length === 0) {
        console.warn("No influencers found for restaurant");
      } else {
        console.log("Influencers found:", assocData);
        // Comentado pois não estamos mais usando o estado de influencers
        // const mappedInfluencers: Influencer[] = assocData.map(item => item.influencers);
        // const sortedInfluencers = mappedInfluencers.sort((a, b) => (b.conversions || 0) - (a.conversions || 0));
        // setInfluencers(sortedInfluencers);
      }
    } catch (error) {
      console.error("Error fetching additional data:", error);
    }
  };

  useEffect(() => {
    const getUserAndData = async () => {
      if (isInitialFetchDone.current) {
        return; // Prevent re-fetching if already done
      }
      // Não precisamos mais verificar a sessão aqui, pois o RouteGuard já faz isso

      try {
        setLoading(true);
        setError(null);

        // --- Fetch Restaurant Data using the utility function ---
        console.log("Fetching restaurant data for user ID:", user?.id);

        // Enable debug mode to see detailed logs
        const debug = true;

        // Get restaurant data using our utility function
        let restaurantData = await getRestaurantData(supabase, user?.id || '', debug);

        // If no restaurant data was found, create a new restaurant profile
        if (!restaurantData) {
          console.log("No restaurant data found. Creating new restaurant profile...");
          restaurantData = await createRestaurantProfile(supabase, user?.id || '', undefined, debug);

          if (!restaurantData) {
            console.error("Failed to create restaurant profile");
            setError("Erro ao criar perfil do restaurante.");
          } else {
            console.log("New restaurant profile created:", restaurantData);
          }
        }

        // If we have restaurant data, update the state and fetch additional data
        if (restaurantData) {
          console.log("Restaurant data found:", restaurantData);

          // Convert to the Restaurant interface format
          const restaurant: Restaurant = {
            id: restaurantData.id,
            name: restaurantData.name || restaurantData.business_name || "Restaurante",
            instagram_handle: restaurantData.instagram_handle,
            roi: restaurantData.roi
          };

          // Only update state if the restaurant data has actually changed
          setRestaurant(prevRestaurant => {
            if (!prevRestaurant || prevRestaurant.id !== restaurant.id || prevRestaurant.name !== restaurant.name || prevRestaurant.instagram_handle !== restaurant.instagram_handle || prevRestaurant.roi !== restaurant.roi) {
              return restaurant;
            }
            return prevRestaurant;
          });

          // Fetch additional data (digital presence, content count, influencers)
          await fetchAdditionalData(restaurant);
        } else {
          setError("Não foi possível encontrar ou criar dados do restaurante.");
        }
      } catch (err) {
        console.error("Unexpected error in dashboard:", err);
        // Garantir que err não seja um objeto vazio e logar mensagem mais detalhada
        if (err && typeof err === 'object' && Object.keys(err).length === 0) {
          console.error('Erro desconhecido no dashboard (objeto vazio)');
        } else {
          console.error(`Detalhes do erro: ${err?.message || JSON.stringify(err)}`);
        }

        setError("Ocorreu um erro inesperado. Por favor, tente novamente.");
      } finally {
        setLoading(false);
        isInitialFetchDone.current = true; // Mark initial fetch as done
      }
    };

    getUserAndData();
  }, [supabase, router, user?.id]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !restaurant) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h2 className="text-xl font-bold text-red-600 mb-4">Erro</h2>
          <p className="text-gray-700 mb-6">{error}</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition duration-200"
          >
            Voltar para Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#f5f5f5] flex flex-col font-sans relative overflow-hidden">
      {/* Top Navbar - Fixed on scroll */}
      <header className="fixed top-0 left-0 right-0 flex justify-between items-center px-6 py-2.5 bg-[#f5f5f5] z-50">
        <div className="flex items-center">
          <Logo className="h-8 w-auto" />
        </div>

        <DropdownMenu
          trigger={(
            <div className="flex items-center cursor-pointer">
              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 mr-2">
                {restaurant?.name?.charAt(0) || "R"}
              </div>
              <span className="text-gray-700 font-medium mr-1">{restaurant?.name || "Restaurante"}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </div>)
          }
          items={[
            {
              label: "Configurações",
              icon: <FaCog className="text-gray-500" />,
              onClick: openSettingsModal
            },
            {
              label: "Ajuda",
              icon: <FaQuestionCircle className="text-gray-500" />,
              onClick: () => window.open('https://wa.me/5547999543437', '_blank')
            },
            {
              label: "Logout",
              icon: <FaSignOutAlt className="text-gray-500" />,
              onClick: () => {
                const auth = useAuth();
                auth.signOut();
              }
            }
          ]}
        />
      </header>

      {/* Display error banner if there's an error but we still have restaurant data */}
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 mx-6">
          <p>{error}</p>
        </div>
      )}

      {/* Alerta de senha temporária movido para a página de configurações */}

      {/* Main Content */}
      <main className="p-5 pt-4 rounded-xl bg-white overflow-y-auto flex-1 flex flex-col shadow-md m-1" style={{ minHeight: 'calc(100vh - 5rem)', maxHeight: 'calc(100vh - 5rem)' }}>
        <div className="flex-1">
          {/* Main Grid Container */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 auto-rows-auto">
          {/* Seu Plano Card - Ocupa a primeira coluna */}
          <div className="md:col-span-1 h-[220px] overflow-hidden">
            <PlanCard
              plan="premium"
              totalAvailable={8}
              used={4}
              restaurantId={restaurant?.id}
              onDetailsClick={() => openPopup("Detalhes do Plano",
                <PlanDetailsPopupNew
                  isOpen={true}
                  onClose={closeDynamicPopup}
                  plan="premium"
                  totalAvailable={8}
                  used={4}
                  startDate="2025-03-01"
                  endDate="2025-05-01"
                  restaurantId={restaurant?.id}
                  onUpgradeClick={() => {
                    closeDynamicPopup();
                    window.open('https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20fazer%20upgrade%20do%20meu%20plano.', '_blank');
                  }}
                  onViewAllRatingsClick={() => {
                    closeDynamicPopup();
                    openPopup("Avaliações do Restaurante",
                      <AllRatingsPopup
                        userId={restaurant?.id || ''}
                        userRole="restaurant"
                      />
                    );
                  }}
                >
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <h3 className="font-bold text-lg mb-4">Análise de Engajamento dos Criadores</h3>
                    <EngagementComparisonChartStatic
                      showMessage={true}
                      campaignId={restaurant?.id}
                      limit={5}
                    />
                  </div>
                </PlanDetailsPopupNew>
              )}
              onViewAllRatingsClick={() => {
                openPopup("Avaliações do Restaurante",
                  <AllRatingsPopup
                    userId={restaurant?.id || ''}
                    userRole="restaurant"
                  />
                );
              }}
            />
          </div>

          {/* Presença Digital Card - Ocupa a segunda coluna */}
          <div className="md:col-span-1 h-[220px]">
            <DigitalPresenceCard
              restaurantId={restaurant?.id || ''}
              onCardClick={() => {
                setIsPresencaPopupOpen(true);
              }}
            />
          </div>



          {/* Impacto no Faturamento Card - Ocupa a quarta coluna */}
          <div className="md:col-span-1 h-[220px] overflow-hidden">
            <ImpactoFaturamentoCard
              ticketMedio={52}
              ticketMedioAnterior={45}
              novosClientes={128}
              cac={8}
              cacComparativo={{
                canal: 'Google Ads',
                valor: 22
              }}
              faturamentoAtribuido={12500}
              onCardClick={() => {
                // Usar o novo popup premium
                openPopup(
                  "Impacto no Faturamento",
                  <ImpactoFaturamentoPopupNew
                    isOpen={true}
                    onClose={closeDynamicPopup}
                    restaurantId={restaurant?.id}
                    ticketMedio={52}
                    ticketMedioAnterior={45}
                    novosClientes={128}
                    cac={8}
                    cacComparativos={[
                      { canal: 'Criadores', valor: 8 },
                      { canal: 'Google Ads', valor: 22 },
                      { canal: 'Facebook Ads', valor: 18 },
                      { canal: 'Instagram Ads', valor: 15 }
                    ]}
                    faturamentoAtribuido={12500}
                    onSave={async (data) => {
                      if (!restaurant) return;
                      // Aqui você pode implementar a lógica para salvar os dados
                      // Por exemplo, atualizar uma tabela de métricas financeiras
                      console.log('Dados a serem salvos:', data);

                      // Exemplo de atualização (usando a tabela restaurants como placeholder)
                      const { error } = await supabase
                        .from('restaurants')
                        .update({
                          roi: parseFloat((data.ticketMedio / data.cac).toFixed(1)) // Calculando ROI como placeholder
                        })
                        .eq('id', restaurant.id);

                      if (!error) {
                        // Atualizar o estado local se necessário
                        setRestaurant(prev => prev ? {
                          ...prev,
                          roi: parseFloat((data.ticketMedio / data.cac).toFixed(1))
                        } : null);
                      }

                      closeDynamicPopup();
                    }}
                  />
                );
              }}
            />
          </div>

          {/* Campanhas Card - Ocupa as três primeiras colunas na parte inferior */}
          <div className="md:col-span-3 bg-white rounded-xl overflow-hidden">
            <DatabaseCampanhasSection
              initialCampaignId={searchParams?.get('campaign') || undefined}
              restaurantId={restaurant?.id}
            />
          </div>

          {/* Criadores Card - Ocupa a quarta coluna e estende-se verticalmente */}
          <div className="md:col-span-1 md:row-span-2 h-full md:row-start-1 md:col-start-4">
            <SimpleCriadoresCard
              restaurantId={restaurant?.id || ''}
              onViewAllClick={() => {
                openPopup("Criadores", <p>Detalhes dos criadores.</p>);
              }}
            />
          </div>
          </div>
        </div>
      </main>

      {/* WhatsApp Support Button */}
      <a
        href="https://wa.me/5543991049779"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg flex items-center justify-center"
      >
        <FaWhatsapp size={24} />
      </a>

      {/* Popup Dinâmico */}
      <Popup
        isOpen={isPopupOpen}
        onClose={closeDynamicPopup}
        title={popupTitle}
      >
        {popupContent}
      </Popup>

      <SettingsModal
        isOpen={isSettingsModalOpen}
        onClose={closeSettingsModal}
        onSaved={(updated) => setRestaurant(updated)}
        userType="restaurant"
        defaultTab="info"
      />

      {/* Modal de Presença Digital */}
      <PresencaDigitalDetailsModal
        isOpen={isPresencaPopupOpen}
        onClose={() => setIsPresencaPopupOpen(false)}
        restaurantId={restaurant?.id || ''}
        restaurantName={restaurant?.name}
      />
    </div>
  );
}
