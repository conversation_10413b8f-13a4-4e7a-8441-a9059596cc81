"use client";

import React, { useState, useEffect } from "react";
import { FaWhatsapp } from "react-icons/fa";
import Link from "next/link";
import Image from "next/image";
import Logo from "@/components/Logo";

export default function Home() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    handleScroll();
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="font-[-apple-system,BlinkMacSystemFont,'SF_Pro','Inter',sans-serif] text-[#1d1d1f] bg-[#f5f5f7] w-full">
      {/* Fixed WhatsApp Button */}
      <a
        href="https://wa.me/5543991049779"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-6 right-6 z-50 flex items-center justify-center w-12 h-12 bg-[#34C759] rounded-full shadow-md hover:bg-[#2BB14F] transition-all duration-300 hover:transform hover:scale-105 hover:shadow-lg"
        aria-label="Contato via WhatsApp"
      >
        <FaWhatsapp className="text-white text-xl" />
      </a>

      {/* Navigation - Material Design Style */}
      <header
        className={`fixed top-0 w-full z-[100] transition-all duration-500 ${isScrolled ? "bg-[rgba(245,245,247,0.9)] backdrop-blur-md shadow-sm py-3" : "bg-gradient-to-b from-black/40 to-transparent py-5"}`}
      >
        <div className="w-full max-w-[100%] mx-auto px-6 transition-all duration-500">
          <nav className="flex items-center justify-between h-[44px]">
            <div className="flex items-center">
              <Logo size="medium" textClassName={`text-xl font-semibold ${isScrolled ? "text-[#1d1d1f]" : "text-white"}`} />
            </div>
            <div className="hidden md:flex space-x-8 text-sm">
              <a href="#visao-geral" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200 relative group`}>
                Visão Geral
              </a>
              <a href="#recursos" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200 relative group`}>
                Como Funciona
              </a>
              <a href="#planos" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200 relative group`}>
                Planos
              </a>
              <a href="#depoimentos" className={`${isScrolled ? "text-[#1d1d1f]" : "text-white"} hover:text-[#0071e3] transition-colors duration-200 relative group`}>
                Depoimentos
              </a>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/login"
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  isScrolled
                    ? "bg-[#0071e3] text-white hover:bg-[#0077ED]"
                    : "bg-white/10 text-white backdrop-blur-md hover:bg-white/20"
                }`}
              >
                Login
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero - Full Width Material Design Style */}
      <section
        className="relative h-screen w-full px-6 bg-cover bg-center text-white flex flex-col justify-center overflow-hidden"
        id="visao-geral"
        style={{
          backgroundImage: "url('/images/connectcitybackground.jpg')"
        }}
      >
        {/* Overlay with gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-black/30"></div>

        {/* Subtle particle effect */}
        <div className="absolute inset-0 opacity-20 bg-opacity-10"></div>

        <div className="relative z-10 w-full max-w-[1400px] mx-auto text-center">
          <div>
            <h1 className="text-5xl md:text-7xl font-bold tracking-tight mb-4 text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-300">crIAdores</h1>
            <h2 className="text-3xl md:text-4xl font-semibold mb-6 text-white/90">
              Marketing de influência simplificado.
            </h2>
            <p className="text-xl max-w-2xl mx-auto mb-10 text-white/80">
              Conecte seu restaurante com influenciadores locais e veja resultados reais.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
              <a
                href="https://wa.me/5543991049779"
                target="_blank"
                rel="noopener noreferrer"
                className="px-6 py-3 bg-[#0071e3] text-white rounded-full text-sm font-medium hover:bg-[#0077ED] transition-colors duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
              >
                <FaWhatsapp className="text-lg" />
                Falar com um especialista
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Para quem é a crIAdores */}
      <section className="py-20 px-6 bg-white w-full">
        <div className="w-full max-w-[1400px] mx-auto text-center mb-16">
          <h2 className="text-4xl font-semibold mb-6">Para quem é a crIAdores?</h2>
          <p className="text-lg text-[#86868b] max-w-2xl mx-auto">Nossa plataforma conecta restaurantes e influenciadores locais, criando parcerias que geram resultados reais para ambos.</p>
        </div>

        <div className="w-full max-w-[1400px] mx-auto grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Para Restaurantes */}
          <div className="bg-gradient-to-br from-[#f8f8fa] to-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-full bg-[#0071e3] flex items-center justify-center text-white mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold">Para Restaurantes</h3>
            </div>

            <ul className="space-y-4 mb-6">
              <li className="flex">
                <span className="w-6 h-6 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 mt-0.5">
                  ✓
                </span>
                <div>
                  <p className="font-medium mb-1">Atraia clientes locais autênticos</p>
                  <p className="text-[#86868b] text-sm">Conecte-se com influenciadores que já têm seguidores na sua região, interessados em gastronomia.</p>
                </div>
              </li>
              <li className="flex">
                <span className="w-6 h-6 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 mt-0.5">
                  ✓
                </span>
                <div>
                  <p className="font-medium mb-1">Economize tempo e recursos</p>
                  <p className="text-[#86868b] text-sm">Gerencie todas as suas parcerias com influenciadores em um só lugar, sem precisar lidar com múltiplos contatos.</p>
                </div>
              </li>
              <li className="flex">
                <span className="w-6 h-6 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 mt-0.5">
                  ✓
                </span>
                <div>
                  <p className="font-medium mb-1">Acompanhe resultados reais</p>
                  <p className="text-[#86868b] text-sm">Veja métricas de engajamento e avalie o impacto das campanhas no seu negócio.</p>
                </div>
              </li>
            </ul>

            <a
              href="https://wa.me/5543991049779?text=Olá!%20Sou%20um%20restaurante%20interessado%20na%20crIAdores."
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-5 py-2.5 bg-[#0071e3] text-white text-sm font-medium rounded-full shadow-sm hover:bg-[#0077ED] transition-all duration-200 hover:shadow-md"
            >
              <FaWhatsapp className="mr-2" /> Quero atrair mais clientes
            </a>
          </div>

          {/* Para Influenciadores */}
          <div className="bg-gradient-to-br from-[#f8f8fa] to-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-full bg-[#0071e3] flex items-center justify-center text-white mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold">Para Influenciadores</h3>
            </div>

            <ul className="space-y-4 mb-6">
              <li className="flex">
                <span className="w-6 h-6 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 mt-0.5">
                  ✓
                </span>
                <div>
                  <p className="font-medium mb-1">Encontre parcerias relevantes</p>
                  <p className="text-[#86868b] text-sm">Conecte-se com restaurantes que combinam com seu perfil e estilo de conteúdo.</p>
                </div>
              </li>
              <li className="flex">
                <span className="w-6 h-6 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 mt-0.5">
                  ✓
                </span>
                <div>
                  <p className="font-medium mb-1">Monetize seu conteúdo</p>
                  <p className="text-[#86868b] text-sm">Participe de campanhas remuneradas e construa relacionamentos de longo prazo com restaurantes.</p>
                </div>
              </li>
              <li className="flex">
                <span className="w-6 h-6 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 flex-shrink-0 mt-0.5">
                  ✓
                </span>
                <div>
                  <p className="font-medium mb-1">Acompanhe seu desempenho</p>
                  <p className="text-[#86868b] text-sm">Veja como seu conteúdo está performando e melhore sua estratégia com dados reais.</p>
                </div>
              </li>
            </ul>

            <a
              href="https://wa.me/5543991049779?text=Olá!%20Sou%20um%20influenciador%20interessado%20na%20crIAdores."
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center px-5 py-2.5 bg-[#0071e3] text-white text-sm font-medium rounded-full shadow-sm hover:bg-[#0077ED] transition-all duration-200 hover:shadow-md"
            >
              <FaWhatsapp className="mr-2" /> Quero fazer parcerias
            </a>
          </div>
        </div>
      </section>

      {/* Depoimentos */}
      <section className="py-20 px-6 bg-[#f5f5f7] w-full">
        <div className="w-full max-w-[1400px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-12">O que dizem nossos clientes</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:scale-105 transform">
              <img src="https://i.pravatar.cc/150?u=restaurante1" alt="Restaurante Boussole" className="w-20 h-20 rounded-full mx-auto mb-4 object-cover" />
              <p className="text-lg mb-4 italic">"A ConnectCity nos conectou com influenciadores que realmente entendem nossa proposta. Tivemos um aumento de 22% em novos clientes no primeiro mês."</p>
              <p className="font-semibold">Restaurante Boussole</p>
              <p className="text-sm text-gray-500">Curitiba, PR</p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:scale-105 transform">
              <img src="https://i.pravatar.cc/150?u=restaurante2" alt="Cantina Italiana" className="w-20 h-20 rounded-full mx-auto mb-4 object-cover" />
              <p className="text-lg mb-4 italic">"O sistema de avaliação mútua nos ajudou a encontrar os melhores influenciadores para nossa marca. A comunicação via WhatsApp integrado facilita muito o processo."</p>
              <p className="font-semibold">Cantina Italiana</p>
              <p className="text-sm text-gray-500">São Paulo, SP</p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200 hover:shadow-2xl transition-all duration-500 hover:scale-105 transform">
              <img src="https://i.pravatar.cc/150?u=influencer1" alt="Influenciador" className="w-20 h-20 rounded-full mx-auto mb-4 object-cover" />
              <p className="text-lg mb-4 italic">"Como influenciador, a plataforma me ajudou a organizar minhas parcerias e acompanhar meu desempenho. Já fiz 8 campanhas com restaurantes incríveis."</p>
              <p className="font-semibold">@gourmetanasita</p>
              <p className="text-sm text-gray-500">Influenciadora Gastronômica</p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefícios Reais - Baseados em Dados */}
      <section className="py-20 px-6 bg-white w-full">
        <div className="w-full max-w-[1400px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-6">Benefícios Reais</h2>
          <p className="text-lg text-[#86868b] max-w-2xl mx-auto mb-16">Baseados em dados reais de nossos usuários, veja o que a crIAdores pode fazer pelo seu negócio</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <p className="text-4xl font-semibold text-[#0071e3] mb-2">15-25%</p>
              <p className="text-lg font-medium mb-2">Novos Clientes</p>
              <p className="text-[#86868b]">Aumento médio de novos clientes nos primeiros 3 meses de uso da plataforma</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-4xl font-semibold text-[#0071e3] mb-2">4.8x</p>
              <p className="text-lg font-medium mb-2">ROI Médio</p>
              <p className="text-[#86868b]">Retorno médio sobre o investimento para restaurantes usando nossa plataforma</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-4xl font-semibold text-[#0071e3] mb-2">70%</p>
              <p className="text-lg font-medium mb-2">Economia de Tempo</p>
              <p className="text-[#86868b]">Redução no tempo gasto gerenciando parcerias com influenciadores</p>
            </div>
          </div>

          <div className="mt-20 flex flex-col md:flex-row items-center justify-between gap-12 text-left">
            <div className="md:w-1/2">
              <h3 className="text-3xl font-semibold mb-4">Impacto real no seu negócio</h3>
              <p className="text-lg text-[#86868b] mb-6">Nossa plataforma não apenas conecta seu restaurante aos influenciadores certos, mas também fornece ferramentas para medir e maximizar o impacto dessas parcerias.</p>
              <ul className="space-y-3 mb-6">
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span>Sistema de avaliação mútua para garantir parcerias de qualidade</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span>Integração com WhatsApp para comunicação simplificada</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span>Dashboard intuitivo para acompanhar resultados em tempo real</span>
                </li>
              </ul>
              <a
                href="https://wa.me/5543991049779"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-5 py-2.5 bg-[#0071e3] text-white text-sm font-medium rounded-full shadow-sm hover:bg-[#0077ED] transition-all duration-200 hover:shadow-md"
              >
                <FaWhatsapp className="mr-2" /> Quero saber mais
              </a>
            </div>
            <div className="md:w-1/2 mt-8 md:mt-0">
        <div className="relative w-full h-64 md:h-96">
          <Image
            src="/images/connectcitybackground.jpg"
            alt="crIAdores Background"
            fill
            priority
            className="object-cover"
            quality={80}
          />
        </div>
            </div>
          </div>
        </div>
      </section>

      {/* Funcionalidades da Plataforma */}
      <section className="py-20 px-6 bg-[#f5f5f7] w-full" id="recursos">
        <div className="w-full max-w-[1400px] mx-auto">
          <h2 className="text-4xl font-semibold text-center mb-6">Funcionalidades da Plataforma</h2>
          <p className="text-lg text-[#86868b] text-center max-w-2xl mx-auto mb-16">Conheça as principais ferramentas que tornam a ConnectCity a solução ideal para conectar restaurantes e influenciadores</p>

          {/* Feature 1 - Sistema de Avaliação Mútua */}
          <div className="flex flex-col md:flex-row items-center gap-12 mb-24">
            <div className="md:w-1/2 order-2 md:order-1">
              <h3 className="text-3xl font-semibold mb-4">Sistema de Avaliação Mútua</h3>
              <p className="text-lg text-[#86868b] mb-6">
                Nossa plataforma possui um sistema de avaliação mútua que permite que restaurantes e influenciadores avaliem suas experiências de trabalho conjunto, garantindo parcerias de qualidade.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Avaliações de 1 a 5 estrelas com comentários detalhados</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Visualização de avaliações recebidas e dadas</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Ranking de influenciadores baseado em avaliações</span>
                </li>
              </ul>
            </div>
            <div className="md:w-1/2 order-1 md:order-2">
              <div className="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
                <img
                  src="/images/rating-system.jpg"
                  alt="Sistema de Avaliação Mútua"
                  className="w-full h-auto"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/api/placeholder/svg?height=400&width=500";
                  }}
                />
              </div>
            </div>
          </div>

          {/* Feature 2 - Integração com WhatsApp */}
          <div className="flex flex-col md:flex-row items-center gap-12 mb-24">
            <div className="md:w-1/2">
              <div className="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
                <img
                  src="/images/whatsapp-integration.jpg"
                  alt="Integração com WhatsApp"
                  className="w-full h-auto"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/api/placeholder/svg?height=400&width=500";
                  }}
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h3 className="text-3xl font-semibold mb-4">Integração com WhatsApp</h3>
              <p className="text-lg text-[#86868b] mb-6">
                Comunicação simplificada entre restaurantes e influenciadores através da nossa integração nativa com WhatsApp.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Notificações automáticas sobre campanhas e parcerias</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Histórico de mensagens dentro da plataforma</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Configuração personalizada de preferências de notificação</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Feature 3 - Dashboard de Campanhas */}
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2 order-2 md:order-1">
              <h3 className="text-3xl font-semibold mb-4">Dashboard de Campanhas</h3>
              <p className="text-lg text-[#86868b] mb-6">
                Gerencie todas as suas campanhas em um só lugar, com visualizações intuitivas e dados em tempo real.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Visualização de campanhas ativas e histórico</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Métricas de engajamento e impacto das campanhas</span>
                </li>
                <li className="flex items-start">
                  <span className="w-5 h-5 rounded-full bg-[#0071e3] flex items-center justify-center text-white text-xs mr-3 mt-1">
                    ✓
                  </span>
                  <span className="text-[#1d1d1f]">Ranking de influenciadores por campanha</span>
                </li>
              </ul>
            </div>
            <div className="md:w-1/2 order-1 md:order-2">
              <div className="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
                <img
                  src="/images/metricas-tempo-real.jpg"
                  alt="Dashboard de Campanhas"
                  className="w-full h-auto"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "/api/placeholder/svg?height=400&width=500";
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Como Funciona - Fluxo da Plataforma */}
      <section className="py-20 px-6 bg-gradient-to-b from-white to-[#f8f9ff] w-full" id="como-funciona">
        <div className="w-full max-w-[1400px] mx-auto text-center">
          <div className="flex items-center justify-center gap-3 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <h2 className="text-4xl font-bold">Como Funciona</h2>
          </div>
          <p className="text-lg text-[#4a5568] max-w-2xl mx-auto mb-16">
            Conheça o fluxo completo da crIAdores para restaurantes e influenciadores
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-16 mb-20">
            {/* Para Restaurantes */}
            <div className="relative">
              <div className="absolute top-0 right-0 w-1/2 h-full border-r-2 border-dashed border-[#e2e8f0] z-0 hidden md:block"></div>
              <div className="bg-white rounded-xl shadow-md p-6 mb-10 relative z-10">
                <h3 className="text-2xl font-bold mb-4 flex items-center text-[#2d3748]">
                  <div className="w-12 h-12 rounded-full bg-[#0071e3] flex items-center justify-center text-white mr-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  Para Restaurantes
                </h3>
                <p className="text-[#4a5568] ml-16 mb-2">Atraia clientes reais com influenciadores locais</p>
              </div>

              <div className="space-y-8 relative">
                {/* Linha vertical conectando os passos */}
                <div className="absolute top-0 left-7 w-0.5 h-full bg-[#e2e8f0] z-0"></div>

                {/* Passo 1 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      1
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#0071e3] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Cadastre-se e escolha seu plano</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Crie sua conta, complete seu perfil e selecione o plano que melhor atende às necessidades do seu restaurante.</p>
                  </div>
                </div>

                {/* Passo 2 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      2
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#0071e3] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Crie campanhas</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Configure campanhas com objetivos específicos, defina o briefing e selecione os influenciadores que melhor se alinham com sua marca.</p>
                  </div>
                </div>

                {/* Passo 3 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      3
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#0071e3] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Comunique-se via WhatsApp</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Utilize nossa integração com WhatsApp para se comunicar diretamente com os influenciadores, alinhar expectativas e fornecer orientações.</p>
                  </div>
                </div>

                {/* Passo 4 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#FF6B00] to-[#FF9500] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      4
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FF6B00] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Acompanhe resultados e avalie</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Monitore o desempenho das campanhas em tempo real e avalie os influenciadores após a conclusão das parcerias.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Para Influenciadores */}
            <div className="relative">
              <div className="absolute top-0 left-0 w-1/2 h-full border-l-2 border-dashed border-[#e2e8f0] z-0 hidden md:block"></div>
              <div className="bg-white rounded-xl shadow-md p-6 mb-10 relative z-10">
                <h3 className="text-2xl font-bold mb-4 flex items-center text-[#2d3748]">
                  <div className="w-12 h-12 rounded-full bg-[#0071e3] flex items-center justify-center text-white mr-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  Para Influenciadores
                </h3>
                <p className="text-[#4a5568] ml-16 mb-2">Monetize seu conteúdo e cresça profissionalmente</p>
              </div>

              <div className="space-y-8 relative">
                {/* Linha vertical conectando os passos */}
                <div className="absolute top-0 left-7 w-0.5 h-full bg-[#e2e8f0] z-0"></div>

                {/* Passo 1 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      1
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#0071e3] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Cadastre-se e conecte seu Instagram</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Crie sua conta, complete seu perfil e conecte sua conta do Instagram para sincronizar suas métricas e posts.</p>
                  </div>
                </div>

                {/* Passo 2 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      2
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#0071e3] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Participe de campanhas</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Visualize as campanhas disponíveis, candidate-se às que mais combinam com seu perfil e receba convites diretos de restaurantes.</p>
                  </div>
                </div>

                {/* Passo 3 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#0071e3] to-[#00a2ff] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      3
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#0071e3] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Crie conteúdo e compartilhe</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Produza conteúdo autêntico para os restaurantes parceiros e compartilhe em suas redes sociais conforme o briefing da campanha.</p>
                  </div>
                </div>

                {/* Passo 4 */}
                <div className="flex relative z-10">
                  <div className="flex-shrink-0 mr-6">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#FF6B00] to-[#FF9500] text-white flex items-center justify-center text-xl font-bold shadow-md">
                      4
                    </div>
                  </div>
                  <div className="bg-white rounded-xl p-5 shadow-md border border-[#e2e8f0] text-left flex-grow transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                    <div className="flex items-start mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#FF6B00] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                      </svg>
                      <h4 className="text-lg font-bold text-[#2d3748]">Acompanhe seu desempenho e avalie</h4>
                    </div>
                    <p className="text-[#4a5568] ml-8">Monitore o engajamento do seu conteúdo, acompanhe seu ranking nas campanhas e avalie os restaurantes após as parcerias.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-16 mb-8 text-center">
            <a
              href="/login"
              className="inline-flex items-center justify-center px-6 py-3 bg-[#0071e3] text-white text-base font-medium rounded-full shadow-md hover:bg-[#0077ED] transition-all duration-200 hover:shadow-lg"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Comece agora mesmo
            </a>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
            <div className="p-6 bg-gradient-to-r from-[#0071e3] to-[#00a2ff] text-white">
              <h3 className="text-2xl font-bold mb-2">Fluxo de trabalho da plataforma crIAdores</h3>
              <p className="opacity-90">Um processo simplificado para maximizar seus resultados</p>
            </div>

            <div className="p-8 bg-white">
              <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                <div className="flex-1 text-center p-4 bg-[#f8f9ff] rounded-xl border border-[#e2e8f0] transform transition-all duration-300 hover:shadow-md hover:-translate-y-1">
                  <div className="w-16 h-16 rounded-full bg-[#0071e3] flex items-center justify-center text-white mx-auto mb-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-[#2d3748] mb-2">Cadastro e Configuração</h4>
                  <p className="text-[#4a5568] text-sm">Crie sua conta e configure seu perfil com todas as informações necessárias</p>
                </div>

                <div className="hidden md:block">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </div>

                <div className="flex-1 text-center p-4 bg-[#f8f9ff] rounded-xl border border-[#e2e8f0] transform transition-all duration-300 hover:shadow-md hover:-translate-y-1">
                  <div className="w-16 h-16 rounded-full bg-[#00a2ff] flex items-center justify-center text-white mx-auto mb-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-[#2d3748] mb-2">Criação de Campanhas</h4>
                  <p className="text-[#4a5568] text-sm">Configure campanhas com objetivos específicos e selecione os influenciadores ideais</p>
                </div>

                <div className="hidden md:block">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </div>

                <div className="flex-1 text-center p-4 bg-[#f8f9ff] rounded-xl border border-[#e2e8f0] transform transition-all duration-300 hover:shadow-md hover:-translate-y-1">
                  <div className="w-16 h-16 rounded-full bg-[#407662] flex items-center justify-center text-white mx-auto mb-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-[#2d3748] mb-2">Produção de Conteúdo</h4>
                  <p className="text-[#4a5568] text-sm">Crie conteúdo autêntico e compartilhe nas redes sociais conforme o briefing</p>
                </div>

                <div className="hidden md:block">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </div>

                <div className="flex-1 text-center p-4 bg-[#f8f9ff] rounded-xl border border-[#e2e8f0] transform transition-all duration-300 hover:shadow-md hover:-translate-y-1">
                  <div className="w-16 h-16 rounded-full bg-[#FF6B00] flex items-center justify-center text-white mx-auto mb-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-bold text-[#2d3748] mb-2">Avaliação Mútua</h4>
                  <p className="text-[#4a5568] text-sm">Avalie o desempenho e a qualidade das parcerias para garantir resultados cada vez melhores</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Planos e Preços - Estilo Apple */}
      <section className="py-20 px-6 bg-[#f5f5f7]" id="planos">
        <div className="max-w-[1100px] mx-auto text-center">
          <h2 className="text-4xl font-semibold mb-6">Planos Transparentes</h2>
          <p className="text-lg text-[#86868b] max-w-2xl mx-auto mb-16">
            Escolha o plano ideal para o seu restaurante, com valores transparentes e resultados comprovados
          </p>

          {/* Tabela de comparação de planos estilo Apple - Versão compacta */}
          <div className="hidden md:block"> {/* Versão desktop da tabela */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 mb-12 max-w-5xl mx-auto">
              {/* Cabeçalho da tabela com design premium */}
              <div className="grid grid-cols-4">
                {/* Coluna de recursos */}
                <div className="p-4 text-left bg-[#f8f8fa] border-b border-gray-200">
                  <h3 className="text-xs uppercase tracking-wide font-medium text-[#86868b]">Recursos / Benefícios</h3>
                </div>

                {/* Plano Prata - Design premium */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-b from-[#f1f1f1] to-[#e5e5e5] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative p-4 border-b border-l border-gray-200 bg-gradient-to-b from-white to-[#f9f9f9]">
                    <div className="flex flex-col items-center">
                      <div className="w-10 h-10 rounded-full bg-[#f5f5f7] flex items-center justify-center mb-2 shadow-sm">
                        <span className="text-xl">🥉</span>
                      </div>
                      <h3 className="text-sm font-semibold text-[#1d1d1f] flex items-center justify-center gap-1.5"><span className="text-base"></span>Prata</h3>
                      <p className="text-xs text-[#86868b] text-center">Essencial para começar</p>
                    </div>
                  </div>
                </div>

                {/* Plano Gold - Design premium com destaque */}
                <div className="relative group">
                  {/* Badge "Mais Popular" */}
                  <div className="absolute -top-0 inset-x-0 flex justify-center z-0">
                    <div className="bg-gradient-to-r from-[#0077ED] to-[#00A2FF] text-white text-[10px] font-medium py-0.5 px-2 rounded-full shadow-sm">
                      Mais Popular
                    </div>
                  </div>
                  {/* Efeito de hover */}
                  <div className="absolute inset-0 bg-gradient-to-b from-[#f1f1f1] to-[#e5e5e5] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  {/* Conteúdo */}
                  <div className="relative p-4 border-b border-l border-gray-200 bg-gradient-to-b from-white to-[#f9f9f9] border-t-2 border-t-[#0071e3]">
                    <div className="flex flex-col items-center pt-1">
                      <div className="w-10 h-10 rounded-full bg-[#0071e3] bg-opacity-10 flex items-center justify-center mb-2 shadow-sm">
                        <span className="text-xl">🏆</span>
                      </div>
                      <h3 className="text-sm font-semibold text-[#1d1d1f] flex items-center justify-center gap-1.5"><span className="text-base"></span>Gold</h3>
                      <p className="text-xs text-[#86868b] text-center">Melhor custo-benefício</p>
                    </div>
                  </div>
                </div>

                {/* Plano Diamond - Design premium */}
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-b from-[#f1f1f1] to-[#e5e5e5] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative p-4 border-b border-l border-gray-200 bg-gradient-to-b from-white to-[#f9f9f9]">
                    <div className="flex flex-col items-center">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#C2E7FF] to-[#E0F2FE] flex items-center justify-center mb-2 shadow-sm">
                        <span className="text-xl">💎</span>
                      </div>
                      <h3 className="text-sm font-semibold text-[#1d1d1f] flex items-center justify-center gap-1.5"><span className="text-base"></span>Diamond</h3>
                      <p className="text-xs text-[#86868b] text-center">Para máximo impacto</p>
                    </div>
                  </div>
                </div>
              </div>

            {/* Corpo da tabela - Recursos com design premium e compacto */}
            <div>
              {/* Linha: Micro-influenciadores/mês */}
              <div className="grid grid-cols-4 items-center hover:bg-[#f9f9f9] transition-colors duration-150">
                <div className="py-3 px-4 text-left border-b border-gray-200">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M17 8C17 10.7614 14.7614 13 12 13C9.23858 13 7 10.7614 7 8C7 5.23858 9.23858 3 12 3C14.7614 3 17 5.23858 17 8Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M3 21C3 16.5817 7.02944 13 12 13C16.9706 13 21 16.5817 21 21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="font-medium text-[#1d1d1f] text-xs">Micro-influenciadores/mês</p>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <p className="font-semibold text-[#1d1d1f] text-base">4</p>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <p className="font-semibold text-[#1d1d1f] text-base">8</p>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <p className="font-semibold text-[#1d1d1f] text-base">12</p>
                </div>
              </div>

              {/* Linha: Competição entre influenciadores */}
              <div className="grid grid-cols-4 items-center hover:bg-[#f9f9f9] transition-colors duration-150">
                <div className="py-3 px-4 text-left border-b border-gray-200">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M7 17L17 7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M7 7H17V17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="font-medium text-[#1d1d1f] text-xs">Competição entre influenciadores</p>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Linha: Relatório com métricas finais */}
              <div className="grid grid-cols-4 items-center hover:bg-[#f9f9f9] transition-colors duration-150">
                <div className="py-3 px-4 text-left border-b border-gray-200">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H18C19.1046 21 20 20.1046 20 19V7C20 5.89543 19.1046 5 18 5H16M8 5V3C8 2.44772 8.44772 2 9 2H15C15.5523 2 16 2.44772 16 3V5M8 5H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M8 12H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M8 16H12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="font-medium text-[#1d1d1f] text-xs">Relatório com métricas finais</p>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Linha: Auxílio na criação da campanha */}
              <div className="grid grid-cols-4 items-center hover:bg-[#f9f9f9] transition-colors duration-150">
                <div className="py-3 px-4 text-left border-b border-gray-200">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 5V7M15 11V13M15 17V19M5 5C3.89543 5 3 5.89543 3 7V10C4.10457 10 5 10.8954 5 12C5 13.1046 4.10457 14 3 14V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V14C19.8954 14 19 13.1046 19 12C19 10.8954 19.8954 10 21 10V7C21 5.89543 20.1046 5 19 5H5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="font-medium text-[#1d1d1f] text-xs">Auxílio na criação da campanha</p>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#FF3B30] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#FF3B30]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Linha: Reunião para próxima campanha */}
              <div className="grid grid-cols-4 items-center hover:bg-[#f9f9f9] transition-colors duration-150">
                <div className="py-3 px-4 text-left border-b border-gray-200">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 10H21M7 3V5M17 3V5M6 21H18C19.1046 21 20 20.1046 20 19V7C20 5.89543 19.1046 5 18 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="font-medium text-[#1d1d1f] text-xs">Reunião para próxima campanha</p>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#FF3B30] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#FF3B30]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>

              {/* Linha: Reunião de fechamento mensal */}
              <div className="grid grid-cols-4 items-center hover:bg-[#f9f9f9] transition-colors duration-150">
                <div className="py-3 px-4 text-left border-b border-gray-200">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 12H15M12 9V15M12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="font-medium text-[#1d1d1f] text-xs">Reunião de fechamento mensal</p>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#FF3B30] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#FF3B30]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
                <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                  <div className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-[#34C759] bg-opacity-10">
                    <svg className="w-3 h-3 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Seção de preços com design premium e compacto */}
            <div className="bg-gradient-to-b from-[#f8f8fa] to-[#f5f5f7] border-t border-gray-200">
              <div>
                {/* Cabeçalho da seção de preços */}
                <div className="grid grid-cols-4 border-b border-gray-200">
                  <div className="py-4 px-4 text-left">
                    <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b]">Investimento</h4>
                  </div>
                  <div className="py-4 px-4 text-center border-l border-gray-200"></div>
                  <div className="py-4 px-4 text-center border-l border-gray-200"></div>
                  <div className="py-4 px-4 text-center border-l border-gray-200"></div>
                </div>

                {/* Linha: Fee mensal */}
                <div className="grid grid-cols-4 items-center hover:bg-white/50 transition-colors duration-150">
                  <div className="py-3 px-4 text-left border-b border-gray-200">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 8.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 16.5H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M10.5 16.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M22 12.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 12.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 8.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 12.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 20.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 4.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="font-medium text-[#1d1d1f] text-xs">Fee mensal</p>
                    </div>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="font-semibold text-[#1d1d1f] text-base">R$ <span className="text-lg">500</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="font-semibold text-[#1d1d1f] text-base">R$ <span className="text-lg">750</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="font-semibold text-[#1d1d1f] text-base">R$ <span className="text-lg">1.000</span></p>
                  </div>
                </div>

                {/* Linha: Investimento em permuta */}
                <div className="grid grid-cols-4 items-center hover:bg-white/50 transition-colors duration-150">
                  <div className="py-3 px-4 text-left border-b border-gray-200">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.5 9.4L7.5 4.21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M21 16V8C20.9996 7.64927 20.9071 7.30481 20.7315 7.00116C20.556 6.69751 20.3037 6.44536 20 6.27L13 2.27C12.696 2.09446 12.3511 2.00205 12 2.00205C11.6489 2.00205 11.304 2.09446 11 2.27L4 6.27C3.69626 6.44536 3.44398 6.69751 3.26846 7.00116C3.09294 7.30481 3.00036 7.64927 3 8V16C3.00036 16.3507 3.09294 16.6952 3.26846 16.9988C3.44398 17.3025 3.69626 17.5546 4 17.73L11 21.73C11.304 21.9055 11.6489 21.9979 12 21.9979C12.3511 21.9979 12.696 21.9055 13 21.73L20 17.73C20.3037 17.5546 20.556 17.3025 20.7315 16.9988C20.9071 16.6952 20.9996 16.3507 21 16Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M3.27 6.96L12 12.01L20.73 6.96" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M12 22.08V12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="font-medium text-[#1d1d1f] text-xs">Investimento em permuta <span className="text-[10px] text-[#86868b]">(por influenciador)</span></p>
                    </div>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="text-[#1d1d1f] text-base">R$ <span className="text-lg">100</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="text-[#1d1d1f] text-base">R$ <span className="text-lg">100</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="text-[#1d1d1f] text-base">R$ <span className="text-lg">100</span></p>
                  </div>
                </div>

                {/* Linha: Premiação total */}
                <div className="grid grid-cols-4 items-center hover:bg-white/50 transition-colors duration-150">
                  <div className="py-3 px-4 text-left border-b border-gray-200">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8.21 13.89L7 23L12 20L17 23L15.79 13.88" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="font-medium text-[#1d1d1f] text-xs">Premiação total <span className="text-[10px] text-[#86868b]">(por influenciador)</span></p>
                    </div>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="text-[#1d1d1f] text-base">R$ <span className="text-lg">1.000</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="text-[#1d1d1f] text-base">R$ <span className="text-lg">1.500</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="text-[#1d1d1f] text-base">R$ <span className="text-lg">1.800</span></p>
                  </div>
                </div>

                {/* Linha: Investimento total estimado */}
                <div className="grid grid-cols-4 items-center bg-white/30 hover:bg-white/50 transition-colors duration-150">
                  <div className="py-3 px-4 text-left border-b border-gray-200">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1V23" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6313 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6313 13.6815 18 14.5717 18 15.5C18 16.4283 17.6313 17.3185 16.9749 17.9749C16.3185 18.6313 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="font-medium text-[#1d1d1f] text-xs">Investimento total estimado</p>
                    </div>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="font-semibold text-[#1d1d1f] text-base">R$ <span className="text-xl">1.900</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="font-semibold text-[#1d1d1f] text-base">R$ <span className="text-xl">3.050</span></p>
                  </div>
                  <div className="py-3 px-4 text-center border-l border-b border-gray-200">
                    <p className="font-semibold text-[#1d1d1f] text-base">R$ <span className="text-xl">4.000</span></p>
                  </div>
                </div>

                {/* Linha: Custo por micro-influencer - Destaque */}
                <div className="grid grid-cols-4 items-center bg-gradient-to-r from-[#f5f5f7] to-[#f8f8fa]">
                  <div className="py-4 px-4 text-left">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-[#0071e3] bg-opacity-10 flex items-center justify-center mr-2.5">
                        <svg className="w-3 h-3 text-[#0071e3]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <p className="font-semibold text-[#1d1d1f] text-xs">Custo por micro-influencer</p>
                    </div>
                  </div>
                  <div className="py-4 px-4 text-center border-l border-gray-200">
                    <p className="font-bold text-[#0071e3] text-lg">R$ 475</p>
                  </div>
                  <div className="py-4 px-4 text-center border-l border-gray-200">
                    <p className="font-bold text-[#0071e3] text-lg">R$ 381</p>
                  </div>
                  <div className="py-4 px-4 text-center border-l border-gray-200">
                    <p className="font-bold text-[#0071e3] text-lg">R$ 333</p>
                  </div>
                </div>
              </div>

              {/* Botões de ação com design premium e compacto */}
              <div className="grid grid-cols-4 border-t border-gray-200">
                <div className="p-4 flex items-center">
                  <p className="text-xs text-[#86868b] italic">Escolha o plano ideal para o seu negócio</p>
                </div>
                <div className="p-4 border-l border-gray-200">
                  <a
                    href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20plano%20Prata."
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group relative flex w-full justify-center items-center py-2 px-3 bg-gradient-to-b from-[#f5f5f7] to-[#e5e5e5] text-[#1d1d1f] rounded-lg text-xs font-medium shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#0071e3] to-[#0077ED] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    <span className="relative flex items-center group-hover:text-white transition-colors duration-300">
                      <span className="mr-1.5 text-base">🥉</span>
                      Escolher Prata
                    </span>
                  </a>
                </div>
                <div className="p-4 border-l border-gray-200">
                  <a
                    href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20plano%20Gold."
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group relative flex w-full justify-center items-center py-2 px-3 bg-[#0071e3] text-white rounded-lg text-xs font-medium shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
                  >
                    <span className="absolute inset-0 w-0 bg-black bg-opacity-10 group-hover:w-full transition-all duration-300 ease-out"></span>
                    <span className="relative flex items-center">
                      <span className="mr-1.5 text-base">🏆</span>
                      Escolher Gold
                    </span>
                  </a>
                </div>
                <div className="p-4 border-l border-gray-200">
                  <a
                    href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20plano%20Diamond."
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group relative flex w-full justify-center items-center py-2 px-3 bg-gradient-to-b from-[#f5f5f7] to-[#e5e5e5] text-[#1d1d1f] rounded-lg text-xs font-medium shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
                  >
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-[#0071e3] to-[#0077ED] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                    <span className="relative flex items-center group-hover:text-white transition-colors duration-300">
                      <span className="mr-1.5 text-base">💎</span>
                      Escolher Diamond
                    </span>
                  </a>
                </div>
              </div>
            </div>
            </div>
          </div>

          {/* Versão mobile da tabela de planos - Design premium */}
          <div className="md:hidden mb-12">
            {/* Card Plano Prata - Design premium */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 mb-8 overflow-hidden transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
              <div className="relative">
                {/* Fundo com gradiente e efeito */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#f8f8fa] via-white to-[#f5f5f7] opacity-80"></div>
                <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDIwIDAgTCAwIDAgMCAyMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZjBmMGYwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-5"></div>

                {/* Conteúdo do cabeçalho */}
                <div className="relative p-8 border-b border-gray-100">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mb-4 shadow-sm">
                      <svg className="w-8 h-8 text-[#8e8e93]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8.21 13.89L7 23L12 20L17 23L15.79 13.88" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-[#1d1d1f] mb-2">Plano Prata</h3>
                    <p className="text-[#86868b] text-sm text-center">Essencial para começar</p>
                    <div className="mt-4 bg-[#f5f5f7] rounded-full px-4 py-1.5 inline-flex items-center">
                      <span className="text-sm font-medium text-[#1d1d1f]">4 micro-influenciadores</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Corpo do card com recursos - Design premium */}
              <div className="px-6 py-4">
                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mb-4">Recursos Incluídos</h4>
                <div className="space-y-4">
                  {/* Competição entre influenciadores */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Competição entre influenciadores</p>
                  </div>

                  {/* Relatório com métricas */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Relatório com métricas detalhadas</p>
                  </div>
                </div>

                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mt-6 mb-4">Recursos Não Incluídos</h4>
                <div className="space-y-4">
                  {/* Auxílio na criação da campanha */}
                  <div className="flex items-center opacity-60">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#FF3B30] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#FF3B30]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Auxílio na criação da campanha</p>
                  </div>

                  {/* Reunião para próxima campanha */}
                  <div className="flex items-center opacity-60">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#FF3B30] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#FF3B30]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Reunião para próxima campanha</p>
                  </div>

                  {/* Reunião de fechamento mensal */}
                  <div className="flex items-center opacity-60">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#FF3B30] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#FF3B30]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Reunião de fechamento mensal</p>
                  </div>
                </div>
              </div>

              {/* Seção de preços com design premium */}
              <div className="bg-gradient-to-b from-[#f8f8fa] to-[#f5f5f7] p-6 border-t border-gray-200">
                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mb-5">Investimento</h4>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  {/* Fee mensal */}
                  <div className="bg-white/60 rounded-xl p-4 shadow-sm">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 8.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 16.5H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M10.5 16.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M22 12.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 12.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 8.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 12.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 20.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 4.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="text-xs text-[#86868b]">Fee mensal</p>
                    </div>
                    <p className="font-semibold text-[#1d1d1f] text-xl">R$ <span className="text-2xl">500</span></p>
                  </div>

                  {/* Investimento total */}
                  <div className="bg-white/60 rounded-xl p-4 shadow-sm">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1V23" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6313 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6313 13.6815 18 14.5717 18 15.5C18 16.4283 17.6313 17.3185 16.9749 17.9749C16.3185 18.6313 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="text-xs text-[#86868b]">Investimento total</p>
                    </div>
                    <p className="font-semibold text-[#1d1d1f] text-xl">R$ <span className="text-2xl">1.900</span></p>
                  </div>
                </div>

                {/* Custo por micro-influencer - Destaque */}
                <div className="bg-white rounded-xl p-5 shadow-sm mb-6 border border-[#0071e3]/10">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-[#0071e3] bg-opacity-10 flex items-center justify-center mr-2">
                        <svg className="w-3.5 h-3.5 text-[#0071e3]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-[#1d1d1f]">Custo por micro-influencer</p>
                    </div>
                    <div className="bg-[#0071e3]/5 px-2 py-1 rounded-md">
                      <p className="text-xs text-[#0071e3] font-medium">Economia</p>
                    </div>
                  </div>
                  <div className="flex items-end justify-between">
                    <p className="font-bold text-[#0071e3] text-3xl">R$ 475</p>
                    <p className="text-sm text-[#86868b]">por influenciador</p>
                  </div>
                </div>

                {/* Botão de ação */}
                <a
                  href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20plano%20Prata."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative flex w-full justify-center items-center py-3.5 px-4 bg-gradient-to-b from-[#0071e3] to-[#0062c3] text-white rounded-xl text-sm font-medium shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
                >
                  <span className="absolute inset-0 w-0 bg-black bg-opacity-10 group-hover:w-full transition-all duration-300 ease-out"></span>
                  <span className="relative flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                    Escolher Plano Prata
                  </span>
                </a>
              </div>
            </div>

            {/* Card Plano Gold - Design premium */}
            <div className="bg-white rounded-2xl shadow-xl border border-[#0071e3] mb-8 overflow-hidden transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative">
              {/* Badge "Mais Popular" */}
              <div className="absolute -top-3 inset-x-0 flex justify-center z-10">
                <div className="bg-gradient-to-r from-[#0077ED] to-[#00A2FF] text-white text-xs font-medium py-1 px-4 rounded-full shadow-md">
                  Mais Popular
                </div>
              </div>

              <div className="relative">
                {/* Fundo com gradiente e efeito */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#f8f8fa] via-white to-[#f5f5f7] opacity-80"></div>
                <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDIwIDAgTCAwIDAgMCAyMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZjBmMGYwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-5"></div>

                {/* Conteúdo do cabeçalho */}
                <div className="relative p-8 border-b border-gray-100 pt-10">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 rounded-full bg-[#0071e3] bg-opacity-10 flex items-center justify-center mb-4 shadow-sm">
                      <svg className="w-8 h-8 text-[#0071e3]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 15C15.866 15 19 11.866 19 8C19 4.13401 15.866 1 12 1C8.13401 1 5 4.13401 5 8C5 11.866 8.13401 15 12 15Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8.21 13.89L7 23L12 20L17 23L15.79 13.88" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-[#1d1d1f] mb-2">Plano Gold</h3>
                    <p className="text-[#86868b] text-sm text-center">Melhor custo-benefício</p>
                    <div className="mt-4 bg-[#0071e3] bg-opacity-10 rounded-full px-4 py-1.5 inline-flex items-center">
                      <span className="text-sm font-medium text-[#0071e3]">8 micro-influenciadores</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Corpo do card com recursos - Design premium */}
              <div className="px-6 py-4">
                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mb-4">Recursos Incluídos</h4>
                <div className="space-y-4">
                  {/* Competição entre influenciadores */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Competição entre influenciadores</p>
                  </div>

                  {/* Relatório com métricas */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Relatório com métricas detalhadas</p>
                  </div>

                  {/* Auxílio na criação da campanha */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Auxílio na criação da campanha</p>
                  </div>

                  {/* Reunião para próxima campanha */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Reunião para próxima campanha</p>
                  </div>

                  {/* Reunião de fechamento mensal */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Reunião de fechamento mensal</p>
                  </div>
                </div>
              </div>

              {/* Seção de preços com design premium */}
              <div className="bg-gradient-to-b from-[#f8f8fa] to-[#f5f5f7] p-6 border-t border-gray-200">
                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mb-5">Investimento</h4>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  {/* Fee mensal */}
                  <div className="bg-white/60 rounded-xl p-4 shadow-sm">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 8.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 16.5H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M10.5 16.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M22 12.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 12.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 8.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 12.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 20.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 4.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="text-xs text-[#86868b]">Fee mensal</p>
                    </div>
                    <p className="font-semibold text-[#1d1d1f] text-xl">R$ <span className="text-2xl">750</span></p>
                  </div>

                  {/* Investimento total */}
                  <div className="bg-white/60 rounded-xl p-4 shadow-sm">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1V23" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6313 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6313 13.6815 18 14.5717 18 15.5C18 16.4283 17.6313 17.3185 16.9749 17.9749C16.3185 18.6313 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="text-xs text-[#86868b]">Investimento total</p>
                    </div>
                    <p className="font-semibold text-[#1d1d1f] text-xl">R$ <span className="text-2xl">3.050</span></p>
                  </div>
                </div>

                {/* Custo por micro-influencer - Destaque */}
                <div className="bg-white rounded-xl p-5 shadow-sm mb-6 border border-[#0071e3]/10">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-[#0071e3] bg-opacity-10 flex items-center justify-center mr-2">
                        <svg className="w-3.5 h-3.5 text-[#0071e3]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-[#1d1d1f]">Custo por micro-influencer</p>
                    </div>
                    <div className="bg-[#0071e3]/5 px-2 py-1 rounded-md">
                      <p className="text-xs text-[#0071e3] font-medium">Melhor valor</p>
                    </div>
                  </div>
                  <div className="flex items-end justify-between">
                    <p className="font-bold text-[#0071e3] text-3xl">R$ 381</p>
                    <p className="text-sm text-[#86868b]">por influenciador</p>
                  </div>
                </div>

                {/* Botão de ação */}
                <a
                  href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20plano%20Gold."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative flex w-full justify-center items-center py-3.5 px-4 bg-[#0071e3] text-white rounded-xl text-sm font-medium shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
                >
                  <span className="absolute inset-0 w-0 bg-black bg-opacity-10 group-hover:w-full transition-all duration-300 ease-out"></span>
                  <span className="relative flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                    Escolher Plano Gold
                  </span>
                </a>
              </div>
            </div>

            {/* Card Plano Diamond - Design premium */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 mb-8 overflow-hidden transform transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
              <div className="relative">
                {/* Fundo com gradiente e efeito */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#f8f8fa] via-white to-[#f5f5f7] opacity-80"></div>
                <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDIwIDAgTCAwIDAgMCAyMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZjBmMGYwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-5"></div>

                {/* Conteúdo do cabeçalho */}
                <div className="relative p-8 border-b border-gray-100">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#C2E7FF] to-[#E0F2FE] flex items-center justify-center mb-4 shadow-sm">
                      <svg className="w-8 h-8 text-[#0071e3]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.5 1.5L9 3.75L12 8.25L15 3.75L13.5 1.5H10.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M12 8.25L9 3.75L3.75 10.5L8.25 12L12 8.25Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8.25 12L3.75 10.5L1.5 13.5V16.5L8.25 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8.25 12L1.5 16.5L6 21L12 15.75L8.25 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M12 15.75L6 21L13.5 22.5H16.5L12 15.75Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M12 15.75L16.5 22.5L21 18L15.75 12L12 15.75Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M15.75 12L21 18L22.5 10.5L15.75 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M15.75 12L22.5 10.5V7.5L20.25 5.25L15.75 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M15.75 12L20.25 5.25L15 3.75L12 8.25L15.75 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-[#1d1d1f] mb-2">Plano Diamond</h3>
                    <p className="text-[#86868b] text-sm text-center">Para máximo impacto</p>
                    <div className="mt-4 bg-gradient-to-r from-[#C2E7FF] to-[#E0F2FE] rounded-full px-4 py-1.5 inline-flex items-center">
                      <span className="text-sm font-medium text-[#0071e3]">12 micro-influenciadores</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Corpo do card com recursos - Design premium */}
              <div className="px-6 py-4">
                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mb-4">Recursos Incluídos</h4>
                <div className="space-y-4">
                  {/* Competição entre influenciadores */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Competição entre influenciadores</p>
                  </div>

                  {/* Relatório com métricas */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Relatório com métricas detalhadas</p>
                  </div>

                  {/* Auxílio na criação da campanha */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Auxílio na criação da campanha</p>
                  </div>

                  {/* Reunião para próxima campanha */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Reunião para próxima campanha</p>
                  </div>

                  {/* Reunião de fechamento mensal */}
                  <div className="flex items-center">
                    <div className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#34C759] bg-opacity-10 mr-3">
                      <svg className="w-3.5 h-3.5 text-[#34C759]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p className="text-sm text-[#1d1d1f]">Reunião de fechamento mensal</p>
                  </div>
                </div>
              </div>

              {/* Seção de preços com design premium */}
              <div className="bg-gradient-to-b from-[#f8f8fa] to-[#f5f5f7] p-6 border-t border-gray-200">
                <h4 className="text-xs uppercase tracking-wide font-medium text-[#86868b] mb-5">Investimento</h4>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  {/* Fee mensal */}
                  <div className="bg-white/60 rounded-xl p-4 shadow-sm">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 8.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6 16.5H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M10.5 16.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M22 12.5H16.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 12.5H14.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 8.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M14.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 12.5V20.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M16.5 4.5V8.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 20.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M19.5 4.5H11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="text-xs text-[#86868b]">Fee mensal</p>
                    </div>
                    <p className="font-semibold text-[#1d1d1f] text-xl">R$ <span className="text-2xl">1.000</span></p>
                  </div>

                  {/* Investimento total */}
                  <div className="bg-white/60 rounded-xl p-4 shadow-sm">
                    <div className="flex items-center mb-2">
                      <svg className="w-4 h-4 text-[#8e8e93] mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1V23" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6313 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6313 13.6815 18 14.5717 18 15.5C18 16.4283 17.6313 17.3185 16.9749 17.9749C16.3185 18.6313 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <p className="text-xs text-[#86868b]">Investimento total</p>
                    </div>
                    <p className="font-semibold text-[#1d1d1f] text-xl">R$ <span className="text-2xl">4.000</span></p>
                  </div>
                </div>

                {/* Custo por micro-influencer - Destaque */}
                <div className="bg-white rounded-xl p-5 shadow-sm mb-6 border border-[#0071e3]/10">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-[#0071e3] bg-opacity-10 flex items-center justify-center mr-2">
                        <svg className="w-3.5 h-3.5 text-[#0071e3]" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-[#1d1d1f]">Custo por micro-influencer</p>
                    </div>
                    <div className="bg-[#0071e3]/5 px-2 py-1 rounded-md">
                      <p className="text-xs text-[#0071e3] font-medium">Menor custo</p>
                    </div>
                  </div>
                  <div className="flex items-end justify-between">
                    <p className="font-bold text-[#0071e3] text-3xl">R$ 333</p>
                    <p className="text-sm text-[#86868b]">por influenciador</p>
                  </div>
                </div>

                {/* Botão de ação */}
                <a
                  href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20o%20plano%20Diamond."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative flex w-full justify-center items-center py-3.5 px-4 bg-gradient-to-r from-[#0071e3] to-[#0077ED] text-white rounded-xl text-sm font-medium shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
                >
                  <span className="absolute inset-0 w-0 bg-black bg-opacity-10 group-hover:w-full transition-all duration-300 ease-out"></span>
                  <span className="relative flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                    Escolher Plano Diamond
                  </span>
                </a>
              </div>
            </div>
          </div>

          {/* Seção de destaque de valor */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="bg-gradient-to-br from-[#f8f8fa] to-white rounded-2xl p-6 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">🥈</span>
                <h3 className="text-xl font-semibold text-[#1d1d1f]">Plano Prata</h3>
              </div>
              <p className="text-[#86868b] mb-4">Ideal para restaurantes que estão começando com marketing de influência e querem testar o formato.</p>
              <div className="bg-[#f5f5f7] rounded-lg p-4 mb-4">
                <p className="font-medium text-[#1d1d1f] mb-1">Retorno médio esperado:</p>
                <p className="text-[#0071e3] font-bold text-lg">3.5x o investimento</p>
              </div>
              <p className="text-sm text-[#86868b] italic">"Perfeito para restaurantes que querem iniciar no marketing de influência com investimento controlado."</p>
            </div>

            <div className="bg-gradient-to-br from-[#f8f8fa] to-white rounded-2xl p-6 shadow-md border border-[#0071e3] hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">🥇</span>
                <h3 className="text-xl font-semibold text-[#1d1d1f]">Plano Gold</h3>
              </div>
              <p className="text-[#86868b] mb-4">Ideal para restaurantes que já entendem o valor do marketing de influência e querem escalar resultados.</p>
              <div className="bg-[#f5f5f7] rounded-lg p-4 mb-4">
                <p className="font-medium text-[#1d1d1f] mb-1">Retorno médio esperado:</p>
                <p className="text-[#0071e3] font-bold text-lg">4.2x o investimento</p>
              </div>
              <p className="text-sm text-[#86868b] italic">"Nossa opção mais popular, com o melhor equilíbrio entre investimento e resultados."</p>
            </div>

            <div className="bg-gradient-to-br from-[#f8f8fa] to-white rounded-2xl p-6 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
              <div className="flex items-center mb-4">
                <span className="text-2xl mr-3">💎</span>
                <h3 className="text-xl font-semibold text-[#1d1d1f]">Plano Diamond</h3>
              </div>
              <p className="text-[#86868b] mb-4">Ideal para restaurantes que buscam máximo impacto e resultados consistentes com marketing de influência.</p>
              <div className="bg-[#f5f5f7] rounded-lg p-4 mb-4">
                <p className="font-medium text-[#1d1d1f] mb-1">Retorno médio esperado:</p>
                <p className="text-[#0071e3] font-bold text-lg">5.0x o investimento</p>
              </div>
              <p className="text-sm text-[#86868b] italic">"A solução completa para restaurantes que querem dominar o marketing de influência em sua região."</p>
            </div>
          </div>

          <div className="mt-8 bg-white p-8 rounded-2xl shadow-md border border-gray-100">
            <h3 className="text-2xl font-semibold mb-6 text-center">Perguntas Frequentes</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
              <div className="bg-[#f8f8fa] p-6 rounded-xl hover:shadow-md transition-all duration-300">
                <p className="font-medium text-lg mb-3 text-[#1d1d1f]">O que está incluído no investimento total?</p>
                <p className="text-[#86868b]">O investimento total inclui o fee mensal de gestão, o valor de permuta para cada influenciador e a premiação total distribuída entre os participantes da campanha.</p>
              </div>
              <div className="bg-[#f8f8fa] p-6 rounded-xl hover:shadow-md transition-all duration-300">
                <p className="font-medium text-lg mb-3 text-[#1d1d1f]">Posso mudar de plano?</p>
                <p className="text-[#86868b]">Sim, você pode fazer upgrade ou downgrade do seu plano a qualquer momento, com ajuste proporcional de valores. Recomendamos completar pelo menos um ciclo mensal antes de mudar.</p>
              </div>
              <div className="bg-[#f8f8fa] p-6 rounded-xl hover:shadow-md transition-all duration-300">
                <p className="font-medium text-lg mb-3 text-[#1d1d1f]">Como funciona a competição entre influenciadores?</p>
                <p className="text-[#86868b]">Os influenciadores competem entre si para criar o melhor conteúdo e gerar mais engajamento. A premiação é distribuída com base no desempenho, incentivando resultados superiores.</p>
              </div>
              <div className="bg-[#f8f8fa] p-6 rounded-xl hover:shadow-md transition-all duration-300">
                <p className="font-medium text-lg mb-3 text-[#1d1d1f]">Oferecem planos personalizados?</p>
                <p className="text-[#86868b]">Sim, para restaurantes com necessidades específicas ou redes com múltiplas unidades, podemos criar um plano sob medida. Entre em contato com nossa equipe para uma proposta personalizada.</p>
              </div>
            </div>

            <div className="mt-8 text-center">
              <a
                href="https://wa.me/5547999543437?text=Olá!%20Tenho%20dúvidas%20sobre%20os%20planos%20da%20ConnectCity."
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 bg-[#f5f5f7] text-[#0071e3] rounded-full text-sm font-medium border border-[#0071e3] hover:bg-[#0071e3] hover:text-white transition-all duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Tenho outras dúvidas
              </a>
            </div>
          </div>

          {/* Depoimentos sobre os planos */}
          <div className="mt-16 bg-[#f0f0f3] rounded-2xl p-8 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-[#0071e3] to-[#00a2ff] opacity-5 rounded-full transform translate-x-1/3 -translate-y-1/3"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-br from-[#0071e3] to-[#00a2ff] opacity-5 rounded-full transform -translate-x-1/3 translate-y-1/3"></div>

            <h3 className="text-2xl font-semibold mb-8 text-center relative z-10">O que nossos clientes dizem sobre os planos</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 relative z-10">
              <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-start mb-4">
                  <img src="/images/restaurant-owner-1.jpg" alt="Proprietário de Restaurante" className="w-12 h-12 rounded-full mr-4 object-cover" onError={(e) => { const target = e.target as HTMLImageElement; target.src = "https://i.pravatar.cc/150?u=owner1"; }} />
                  <div>
                    <p className="font-semibold text-[#1d1d1f]">Ricardo Mendes</p>
                    <p className="text-sm text-[#86868b]">Restaurante Sabor & Arte - Plano Gold</p>
                  </div>
                </div>
                <p className="text-[#4a5568] italic">"Estamos no plano Gold há 6 meses e o ROI tem sido incrível. A competição entre os influenciadores gera conteúdos muito autênticos, e as reuniões mensais nos ajudam a ajustar a estratégia constantemente."</p>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-start mb-4">
                  <img src="/images/restaurant-owner-2.jpg" alt="Proprietária de Restaurante" className="w-12 h-12 rounded-full mr-4 object-cover" onError={(e) => { const target = e.target as HTMLImageElement; target.src = "https://i.pravatar.cc/150?u=owner2"; }} />
                  <div>
                    <p className="font-semibold text-[#1d1d1f]">Amanda Oliveira</p>
                    <p className="text-sm text-[#86868b]">Cantina Bella Napoli - Plano Diamond</p>
                  </div>
                </div>
                <p className="text-[#4a5568] italic">"Começamos com o plano Prata, mas logo fizemos upgrade para o Diamond. A diferença foi notável! O suporte dedicado e as 12 parcerias mensais transformaram completamente nossa presença digital e aumentaram nosso faturamento em 30%."</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Chamada para Ação */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-[980px] mx-auto">
          <div className="bg-gradient-to-r from-[#0071e3] to-[#00a2ff] rounded-2xl p-12 text-white text-center shadow-lg">
            <h2 className="text-4xl font-semibold mb-6">Pronto para transformar seu marketing?</h2>
            <p className="text-lg max-w-2xl mx-auto mb-12 opacity-90">
              Junte-se a centenas de restaurantes e influenciadores que já estão criando parcerias de sucesso na ConnectCity
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a
                href="https://wa.me/5547999543437?text=Olá!%20Gostaria%20de%20saber%20mais%20sobre%20a%20ConnectCity."
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-4 bg-white text-[#0071e3] rounded-full text-sm font-medium hover:bg-[#f5f5f7] transition-all duration-200 hover:shadow-md hover:transform hover:translate-y-[-1px]"
              >
                <FaWhatsapp className="inline mr-2" /> Falar com um especialista
              </a>
              <a
                href="/login"
                className="px-8 py-4 bg-transparent text-white border border-white rounded-full text-sm font-medium hover:bg-white hover:text-[#0071e3] transition-all duration-200"
              >
                Acessar plataforma
              </a>
            </div>
          </div>

          <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Segurança</h3>
              <p className="text-[#86868b]">Seus dados estão protegidos com as mais avançadas tecnologias de segurança.</p>
            </div>

            <div>
              <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Facilidade</h3>
              <p className="text-[#86868b]">Interface intuitiva que permite gerenciar suas campanhas sem complicações.</p>
            </div>

            <div>
              <div className="w-16 h-16 rounded-full bg-[#f5f5f7] flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-[#0071e3]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Confiança</h3>
              <p className="text-[#86868b]">Sistema de avaliação mútua que garante parcerias de qualidade.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer - Rodapé */}
      <footer className="py-12 px-6 bg-[#f5f5f7]">
        <div className="max-w-[980px] mx-auto">
          <div className="flex flex-col md:flex-row justify-between mb-12">
            <div className="mb-8 md:mb-0">
              <Logo size="small" textClassName="font-semibold" />
              <p className="text-sm text-[#86868b] max-w-xs mb-6 mt-4">
                Marketing de influência simplificado para restaurantes e influenciadores locais.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 rounded-full bg-[#e5e5e5] flex items-center justify-center text-[#86868b] hover:bg-[#0071e3] hover:text-white transition-all duration-200">
                  <span className="sr-only">Instagram</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd"></path>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-[#e5e5e5] flex items-center justify-center text-[#86868b] hover:bg-[#0071e3] hover:text-white transition-all duration-200">
                  <span className="sr-only">Facebook</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd"></path>
                  </svg>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-[#e5e5e5] flex items-center justify-center text-[#86868b] hover:bg-[#0071e3] hover:text-white transition-all duration-200">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                  </svg>
                </a>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="font-semibold mb-4">Plataforma</h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <a href="#recursos" className="text-[#86868b] hover:text-[#0071e3] transition">
                      Funcionalidades
                    </a>
                  </li>
                  <li>
                    <a href="#como-funciona" className="text-[#86868b] hover:text-[#0071e3] transition">
                      Como funciona
                    </a>
                  </li>
                  <li>
                    <a href="#planos" className="text-[#86868b] hover:text-[#0071e3] transition">
                      Planos e preços
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold mb-4">Empresa</h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                      Sobre nós
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                      Blog
                    </a>
                  </li>
                  <li>
                    <a href="#" className="text-[#86868b] hover:text-[#0071e3] transition">
                      Carreiras
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold mb-4">Contato</h3>
                <ul className="space-y-2 text-sm text-[#86868b]">
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <a href="https://wa.me/5543991049779" className="hover:text-[#0071e3] transition">(43) 99104-9779</a>
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <a href="mailto:<EMAIL>" className="hover:text-[#0071e3] transition"><EMAIL></a>
                  </li>
                  <li className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>Londrina, PR - Brasil</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-[#86868b] mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} ConnectCity. Todos os direitos reservados.
            </p>
            <div className="flex space-x-4">
              <Link href="/politica-de-privacidade" className="text-sm text-[#86868b] hover:text-[#0071e3] transition">
                Política de Privacidade
              </Link>
              <a href="#" className="text-sm text-[#86868b] hover:text-[#0071e3] transition">
                Termos de Serviço
              </a>
              <a href="#" className="text-sm text-[#86868b] hover:text-[#0071e3] transition">
                Cookies
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
