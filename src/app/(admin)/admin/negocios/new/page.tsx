"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NegocioAdminForm from '@/components/admin/NegocioAdminForm';
import AdminLayout from '@/components/layouts/AdminLayout';
import { toast } from 'react-hot-toast';

export default function NewNegocioPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Dados iniciais para o formulário
  const initialData = {
    business_name: '',
    description: '',
    cuisine_type: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    website: '',
    instagram_url: '',
    facebook_url: '',
    tiktok_url: ''
  };

  const handleCancel = () => {
    router.push('/admin/negocios');
  };

  const handleSuccess = (negocioId: string) => {
    // Redirecionar para a página de detalhes do negócio após criação bem-sucedida
    toast.success('Negócio criado com sucesso!');
    router.push(`/admin/negocios/${negocioId}`);
  };

  return (
    <AdminLayout title="Novo Negócio" backLink="/admin/negocios">
      <div className="max-w-5xl mx-auto">
        <NegocioAdminForm
          initialData={initialData}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
          isEditing={false}
        />
      </div>
    </AdminLayout>
  );
}
