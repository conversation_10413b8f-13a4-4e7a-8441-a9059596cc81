"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/layouts/AdminLayout';
import NegocioAdminForm from '@/components/admin/NegocioAdminForm';
import { toast } from 'react-hot-toast';

export default function EditNegocioClient({ negocioId }: { negocioId: string }) {
  const router = useRouter();
  const [negocio, setNegocio] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Buscar dados do negocioe usando a API
  useEffect(() => {
    // Não buscar o negocioe se o ID não estiver disponível
    if (!negocioId) return;

    const fetchNegocio = async () => {
      try {
        setLoading(true);
        
        // Usar a API para buscar o negocioe
        const baseUrl = window.location.origin;
        const response = await fetch(`${baseUrl}/api/admin/negocios/${negocioId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
        }

        const data = await response.json();
        setNegocio(data);
      } catch (error: any) {
        console.error('Erro ao buscar negocioe:', error);
        setError(`Erro ao carregar dados do negocioe: ${error.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchNegocio();
  }, [negocioId]);

  const handleCancel = () => {
    router.push(`/admin/negocios/${negocioId}`);
  };

  const handleSuccess = (id: string) => {
    toast.success('Negocioe atualizado com sucesso!');
    router.push(`/admin/negocios/${id}`);
  };

  return (
    <AdminLayout title="Editar Negocioe" backLink={`/admin/negocios/${negocioId}`}>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p>{error}</p>
          <button 
            onClick={handleCancel}
            className="mt-2 text-red-700 hover:text-red-900 underline"
          >
            Voltar para a lista de negocioes
          </button>
        </div>
      ) : negocio ? (
        <div className="max-w-5xl mx-auto">
          <NegocioAdminForm 
            initialData={negocio}
            onCancel={handleCancel}
            onSuccess={handleSuccess}
            isEditing={true}
          />
        </div>
      ) : (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
          <p>Negocioe não encontrado</p>
          <button 
            onClick={() => router.push('/admin/negocios')}
            className="mt-2 text-yellow-700 hover:text-yellow-900 underline"
          >
            Voltar para a lista de negocioes
          </button>
        </div>
      )}
    </AdminLayout>
  );
}
