"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import AdminLayout from '@/components/layouts/AdminLayout';
import { FaEdit, FaTrash, FaInstagram, FaFacebook, FaTiktok, FaGlobe } from 'react-icons/fa';
import { toast } from 'react-hot-toast';

interface Negocio {
  id: string;
  business_name: string;
  description: string;
  cuisine_type: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  website: string;
  instagram_url: string;
  facebook_url: string;
  tiktok_url: string;
  created_at: string;
  updated_at: string;
}

export default function NegocioDetailsClient({ negocioId }: { negocioId: string }) {
  console.log('[NegocioDetailsClient] Rendering with negocioId:', negocioId); // New top-level log
  const router = useRouter();
  const [negocio, setNegocio] = useState<Negocio | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [campaigns, setCampaigns] = useState<any[]>([]);

  // Buscar dados do negócio
  useEffect(() => {
    console.log('[NegocioDetailsClient] useEffect triggered. negocioId:', negocioId); // New log for useEffect trigger
    const fetchNegocio = async () => {
      try {
        setLoading(true);

        // Buscar negócio pelo ID
        const { data, error } = await supabase
          .from('negocio_profiles')
          .select('*')
          .eq('id', negocioId)
          .single();
        
        if (error) {
          console.error('Erro ao buscar negocioe:', error);
          setError('Erro ao carregar dados do negocioe');
          return;
        }
        
        setNegocio(data);
        
        // Buscar campanhas associadas a este negocioe
        console.log('Fetching campaigns for negocioId:', negocioId); // Added log
        const { data: campaignsData, error: campaignsError } = await supabase
          .from('campaigns')
          .select('*')
          .eq('negocio_id', negocioId)
          .order('created_at', { ascending: false });
        
        if (campaignsError) {
          console.error('[NegocioDetailsClient] Erro ao buscar campanhas:', campaignsError);
          toast.error(`Erro ao buscar campanhas: ${campaignsError.message}`);
          // Optionally set a specific error state for campaigns
          // setCampaignsErrorState(campaignsError.message);
        } else {
          console.log('[NegocioDetailsClient] Campaigns data fetched:', campaignsData);
          setCampaigns(campaignsData || []);
        }
      } catch (e: any) { // Changed error variable name to avoid conflict
        console.error('[NegocioDetailsClient] Erro geral ao buscar dados do negocioe ou campanhas:', e);
        setError(`Erro ao carregar dados: ${e.message}`);
      } finally {
        setLoading(false);
      }
    };
    
    fetchNegocio();
  }, [negocioId]);

  // Função para excluir o negocioe
  const handleDelete = async () => {
    if (window.confirm('Tem certeza que deseja excluir este negocioe? Esta ação não pode ser desfeita.')) {
      try {
        const { error } = await supabase
          .from('negocio_profiles')
          .delete()
          .eq('id', negocioId);
        
        if (error) {
          console.error('Erro ao excluir negocioe:', error);
          toast.error('Erro ao excluir negocioe');
          return;
        }
        
        toast.success('Negocioe excluído com sucesso');
        router.push('/admin/negocios');
      } catch (error) {
        console.error('Erro ao excluir negocioe:', error);
        toast.error('Erro ao excluir negocioe');
      }
    }
  };

  // Formatar data para exibição
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AdminLayout title="Detalhes do Negocioe" backLink="/admin/negocios">
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p>{error}</p>
          <Link href="/admin/negocios">
            <button className="mt-2 text-red-700 hover:text-red-900 underline">
              Voltar para a lista de negocioes
            </button>
          </Link>
        </div>
      ) : negocio ? (
        <div className="max-w-5xl mx-auto">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            {/* Cabeçalho com ações */}
            <div className="p-6 border-b flex justify-between items-center">
              <h2 className="text-2xl font-semibold">{negocio.business_name}</h2>
              <div className="flex space-x-2">
                <Link href={`/admin/negocios/${negocioId}/edit`}>
                  <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <FaEdit className="mr-2" />
                    Editar
                  </button>
                </Link>
                <button
                  onClick={handleDelete}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <FaTrash className="mr-2" />
                  Excluir
                </button>
              </div>
            </div>

            {/* Informações do negocioe */}
            <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-4 border-b pb-2">Informações Básicas</h3>
                
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Nome</p>
                    <p className="text-base">{negocio.business_name}</p>
                  </div>
                  
                  {negocio.description && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Descrição</p>
                      <p className="text-base">{negocio.description}</p>
                    </div>
                  )}
                  
                  {negocio.cuisine_type && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Tipo de Culinária</p>
                      <p className="text-base">{negocio.cuisine_type}</p>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Data de Criação</p>
                    <p className="text-base">{formatDate(negocio.created_at)}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Última Atualização</p>
                    <p className="text-base">{formatDate(negocio.updated_at)}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4 border-b pb-2">Localização</h3>
                
                <div className="space-y-3">
                  {negocio.address && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Endereço</p>
                      <p className="text-base">{negocio.address}</p>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium text-gray-500">Cidade/Estado</p>
                    <p className="text-base">{negocio.city}, {negocio.state}</p>
                  </div>
                  
                  {negocio.postal_code && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">CEP</p>
                      <p className="text-base">{negocio.postal_code}</p>
                    </div>
                  )}
                </div>
                
                <h3 className="text-lg font-medium mt-6 mb-4 border-b pb-2">Redes Sociais</h3>
                
                <div className="space-y-3">
                  {negocio.website && (
                    <div className="flex items-center">
                      <FaGlobe className="text-gray-500 mr-2" />
                      <a 
                        href={negocio.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {negocio.website}
                      </a>
                    </div>
                  )}
                  
                  {negocio.instagram_url && (
                    <div className="flex items-center">
                      <FaInstagram className="text-pink-600 mr-2" />
                      <a 
                        href={negocio.instagram_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {negocio.instagram_url}
                      </a>
                    </div>
                  )}
                  
                  {negocio.facebook_url && (
                    <div className="flex items-center">
                      <FaFacebook className="text-blue-600 mr-2" />
                      <a 
                        href={negocio.facebook_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {negocio.facebook_url}
                      </a>
                    </div>
                  )}
                  
                  {negocio.tiktok_url && (
                    <div className="flex items-center">
                      <FaTiktok className="text-black mr-2" />
                      <a 
                        href={negocio.tiktok_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {negocio.tiktok_url}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Campanhas associadas */}
            <div className="p-6 border-t">
              <h3 className="text-lg font-medium mb-4">Campanhas Associadas</h3>
              
              {campaigns.length === 0 ? (
                <p className="text-gray-500">Nenhuma campanha associada a este negocioe.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Nome
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Período
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ações
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {campaigns.map((campaign) => (
                        <tr key={campaign.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{campaign.name}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">{campaign.description}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {formatDate(campaign.start_date)} a {formatDate(campaign.end_date)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              campaign.status === 'active' ? 'bg-green-100 text-green-800' :
                              campaign.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                              campaign.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {campaign.status === 'active' ? 'Ativa' :
                               campaign.status === 'draft' ? 'Rascunho' :
                               campaign.status === 'completed' ? 'Concluída' :
                               'Cancelada'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link href={`/admin/campaigns/${campaign.id}`}>
                              <button className="text-blue-600 hover:text-blue-900">
                                Ver Detalhes
                              </button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-md">
          <p>Negocioe não encontrado</p>
          <Link href="/admin/negocios">
            <button className="mt-2 text-yellow-700 hover:text-yellow-900 underline">
              Voltar para a lista de negocioes
            </button>
          </Link>
        </div>
      )}
    </AdminLayout>
  );
}
