"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { supabase } from '@/lib/supabase/client';
import AdminPageWrapper from '@/components/admin/AdminPageWrapper';
import { StandardButton } from '@/components/ui/StandardButton';
import { FaPlus, FaSearch, FaEdit, FaEye, FaTrash } from 'react-icons/fa'; // FaFilter removido
import { toast } from 'react-hot-toast';

interface Negocio {
  id: string;
  business_name: string;
  description: string;
  city: string;
  state: string;
  instagram_url: string;
  created_at: string;
}

export default function NegociosPage() {
  const [negocios, setNegocios] = useState<Negocio[]>([]);
  const [filteredNegocios, setFilteredNegocios] = useState<Negocio[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [statusFilter, _setStatusFilter] = useState('all'); // setStatusFilter prefixado e com comentário eslint
  // const [creatingTestData, setCreatingTestData] = useState(false); // Removido

  // Buscar negócios ao carregar a página
  useEffect(() => {
    fetchNegocios();
  }, []);

  // Filtrar negócios quando o termo de busca ou filtro de status mudar
  useEffect(() => {
    if (negocios) {
      const filtered = negocios.filter(negocio => {
        const matchesSearch = negocio.business_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (negocio.description && negocio.description.toLowerCase().includes(searchTerm.toLowerCase()));

        return matchesSearch;
      });

      setFilteredNegocios(filtered);
    }
  }, [negocios, searchTerm, statusFilter]);

  // Função para buscar negócios
  const fetchNegocios = async () => {
    try {
      setLoading(true);

      // Buscar negócios com join na tabela de perfis
      const { data, error } = await supabase
        .from('restaurant_profiles')
        .select('*')
        .order('business_name', { ascending: true });

      if (error) {
        console.error('Erro ao buscar negócios:', error);
        toast.error('Erro ao carregar negócios');
        return;
      }

      setNegocios(data || []);
      setFilteredNegocios(data || []);
    } catch (error) {
      console.error('Erro ao buscar negócios:', error);
      toast.error('Erro ao carregar negócios');
    } finally {
      setLoading(false);
    }
  };

  // Função para excluir um negócio
  const handleDeleteNegocio = async (id: string) => {
    if (window.confirm('Tem certeza que deseja excluir este negócio? Esta ação não pode be desfeita.')) {
      try {
        const { error } = await supabase
          .from('restaurant_profiles')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Erro ao excluir negócio:', error);
          toast.error('Erro ao excluir negócio');
          return;
        }

        toast.success('Negócio excluído com sucesso');
        fetchNegocios();
      } catch (error) {
        console.error('Erro ao excluir negócio:', error);
        toast.error('Erro ao excluir negócio');
      }
    }
  };

  // Formatar data para exibição
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <AdminPageWrapper title="Negócios">
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FaSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar negócios..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="flex space-x-2">
          <Link href="/admin/negocios/new">
            <StandardButton variant="primary" size="md">
              <FaPlus className="mr-2" />
              Novo Negócio
            </StandardButton>
          </Link>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      ) : filteredNegocios.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-500 mb-4">Nenhum negócio encontrado</p>
          <Link href="/admin/negocios/new">
            <StandardButton variant="primary" size="md">
              <FaPlus className="mr-2" />
              Criar Novo Negócio
            </StandardButton>
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Negócio
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Localização
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Instagram
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data de Criação
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredNegocios.map((negocio) => (
                <tr key={negocio.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{negocio.business_name}</div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">{negocio.description}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{negocio.city}, {negocio.state}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {negocio.instagram_url ? (
                        <a
                          href={negocio.instagram_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {negocio.instagram_url.replace('https://instagram.com/', '@')}
                        </a>
                      ) : 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{formatDate(negocio.created_at)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link href={`/admin/negocios/${negocio.id}`}>
                        <StandardButton variant="secondary" size="sm">
                          <FaEye />
                        </StandardButton>
                      </Link>
                      <Link href={`/admin/negocios/${negocio.id}/edit`}>
                        <StandardButton variant="secondary" size="sm">
                          <FaEdit />
                        </StandardButton>
                      </Link>
                      <StandardButton
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteNegocio(negocio.id)}
                      >
                        <FaTrash />
                      </StandardButton>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </AdminPageWrapper>
  );
}
