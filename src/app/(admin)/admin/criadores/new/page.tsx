"use client";

import React from 'react'; // useState removido
import { useRouter } from 'next/navigation';
import InfluencerAdminForm from '@/components/admin/InfluencerAdminForm';
import AdminLayout from '@/components/layouts/AdminLayout';
import { toast } from 'react-hot-toast';

export default function NewInfluencerPage() {
  const router = useRouter();
  // const [loading, setLoading] = useState(false); // Removido estado não utilizado

  // Dados iniciais para o formulário
  const initialData = {
    name: '',
    username: '',
    email: '',
    classification: 'Standard',
    profile: {
      bio: '',
      instagram_username: '',
      tiktok_username: '',
      location_city: '',
      location_state: '',
      follower_count: 0,
      avg_engagement_rate: 0,
      content_niche: [],
      primary_platform: 'instagram',
    }
  };

  const handleCancel = () => {
    router.push('/admin/criadores');
  };

  const handleSuccess = (influencerId: string) => {
    // Redirecionar para a página de detalhes do criador após criação bem-sucedida
    toast.success('Criador criado com sucesso!');
    router.push(`/admin/criadores/${influencerId}`);
  };

  return (
    <AdminLayout title="Novo Criador" backLink="/admin/criadores">
      <div className="max-w-5xl mx-auto">
        <InfluencerAdminForm
          initialData={initialData}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
          isEditing={false}
        />
      </div>
    </AdminLayout>
  );
}
