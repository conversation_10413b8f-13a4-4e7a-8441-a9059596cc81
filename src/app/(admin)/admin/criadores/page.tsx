"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { supabase } from "@/lib/supabase/client";
import AdminPageWrapper from "@/components/admin/AdminPageWrapper";
import { StandardButton } from "@/components/ui/StandardButton";
import { DataTable, DataTableRow, DataTableCell } from "@/components/ui/DataTable";
import { FaPlus, FaEdit, FaEye, FaTrash } from "react-icons/fa";
import { toast } from "react-hot-toast";

interface CriadorProfile {
  id: string;
  instagram_username: string | null;
  tiktok_username: string | null;
  location_city: string | null;
  location_state: string | null;
  follower_count: number | null;
  avg_engagement_rate: string | null;
  is_verified: boolean | null;
  created_at: string | null;
}

interface Criador {
  id: string;
  name: string;
  username: string;
  classification: string;
  email: string | null;
  profile?: CriadorProfile;
}

export default function AdminCriadoresPage() {
  const [criadores, setCriadores] = useState<Criador[]>([]);
  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);

  useEffect(() => {
    fetchCriadores();
  }, []);

  const fetchCriadores = async () => {
    try {
      setLoading(true);
      setFetchError(null);

      // Fetch all criadores
      const { data: criadoresData, error: criadoresError } = await supabase
        .from("criadores")
        .select("*")
        .order("name", { ascending: true });

      // Fetch all influencer_profiles
      const { data: profilesData, error: profilesError } = await supabase
        .from("influencer_profiles")
        .select("*");

      // Fetch all profiles (for email)
      const { data: userProfiles, error: userProfilesError } = await supabase
        .from("profiles")
        .select("id, email");

      if (criadoresError || profilesError || userProfilesError) {
        setFetchError("Erro ao carregar criadores: " + (criadoresError?.message || profilesError?.message || userProfilesError?.message));
        toast.error("Erro ao carregar criadores");
        return;
      }

      // Merge by id
      const profilesMap = new Map<string, InfluencerProfile>();
      (profilesData || []).forEach((profile: any) => {
        profilesMap.set(profile.id, profile);
      });

      const emailMap = new Map<string, string>();
      (userProfiles || []).forEach((profile: any) => {
        emailMap.set(profile.id, profile.email);
      });

      const formatted = (criadoresData || []).map((inf: any) => ({
        id: inf.id,
        name: inf.name,
        username: inf.username,
        classification: inf.classification,
        email: emailMap.get(inf.id) || null,
        profile: profilesMap.get(inf.id) || null,
      }));

      setCriadores(formatted);
    } catch (error: any) {
      setFetchError("Erro ao carregar criadores: " + (error.message || error));
      toast.error("Erro ao carregar criadores");
    } finally {
      setLoading(false);
    }
  };

  // TODO: Implementar exclusão de criador
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleDeleteInfluencer = async (_id: string) => {
    if (window.confirm("Tem certeza que deseja excluir este criador?")) {
      console.warn("Tentativa de excluir criador com ID (não implementado):", _id); 
      toast("Funcionalidade de exclusão em desenvolvimento");
    }
  };

  // Formatar data para exibição
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("pt-BR");
  };

  return (
    <AdminPageWrapper title="Criadores">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Criadores</h1>
        <Link href="/admin/criadores/new">
          <StandardButton variant="primary" size="md">
            <FaPlus className="mr-2" />
            Novo Criador
          </StandardButton>
        </Link>
      </div>

      {fetchError && (
        <div className="bg-red-100 text-red-700 p-4 rounded mb-4">
          {fetchError}
        </div>
      )}

      {loading ? (
        <div>Carregando criadores...</div>
      ) : criadores.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-500 mb-4">Nenhum criador encontrado</p>
          <Link href="/admin/criadores/new">
            <StandardButton variant="primary" size="md">
              <FaPlus className="mr-2" />
              Criar Novo Criador
            </StandardButton>
          </Link>
        </div>
      ) : (
        <DataTable
          headers={['Nome', 'Username', 'Email', 'Classificação', 'Instagram', 'Seguidores', 'Engajamento', 'Verificado', 'Data de Criação', 'Ações']}
          loading={loading}
          emptyMessage="Nenhum criador encontrado"
        >
          {criadores.map((inf) => (
            <DataTableRow key={inf.id}>
              <DataTableCell>{inf.name}</DataTableCell>
              <DataTableCell>{inf.username}</DataTableCell>
              <DataTableCell>{inf.email ?? "N/A"}</DataTableCell>
              <DataTableCell>{inf.classification}</DataTableCell>
              <DataTableCell>
                {inf.profile?.instagram_username ? (
                  <a
                    href={`https://instagram.com/${inf.profile.instagram_username.replace("@", "")}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    @{inf.profile.instagram_username.replace("@", "")}
                  </a>
                ) : "N/A"}
              </DataTableCell>
              <DataTableCell>{inf.profile?.follower_count ?? "N/A"}</DataTableCell>
              <DataTableCell>{inf.profile?.avg_engagement_rate ?? "N/A"}</DataTableCell>
              <DataTableCell>
                {inf.profile?.is_verified === true
                  ? "Sim"
                  : inf.profile?.is_verified === false
                  ? "Não"
                  : "N/A"}
              </DataTableCell>
              <DataTableCell>
                {formatDate(inf.profile?.created_at ?? null)}
              </DataTableCell>
              <DataTableCell className="text-right">
                <div className="flex justify-end space-x-2">
                  <Link href={`/admin/criadores/${inf.id}`}>
                    <StandardButton variant="secondary" size="sm">
                      <FaEye />
                    </StandardButton>
                  </Link>
                  <Link href={`/admin/criadores/${inf.id}/edit`}>
                    <StandardButton variant="secondary" size="sm">
                      <FaEdit />
                    </StandardButton>
                  </Link>
                  <StandardButton
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeleteInfluencer(inf.id)}
                  >
                    <FaTrash />
                  </StandardButton>
                </div>
              </DataTableCell>
            </DataTableRow>
          ))}
        </DataTable>
      )}
    </AdminPageWrapper>
  );
}
