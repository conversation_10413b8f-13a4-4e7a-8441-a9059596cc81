import React from 'react';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import AdminLayout from '@/components/layouts/AdminLayout';
import CriadorEditForm from '@/components/admin/CriadorEditForm';

async function getInfluencerData(id: string) {
  const supabase = createServerComponentClient({ cookies });

  // Buscar dados do criador
  const { data: influencerData, error: influencerError } = await supabase
    .from('criadores')
    .select('*')
    .eq('id', id)
    .single();

  if (influencerError) {
    throw new Error(`Erro ao buscar criador: ${influencerError.message}`);
  }

  if (!influencerData) {
    throw new Error('Criador não encontrado');
  }

  // Buscar perfil do criador
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data: profileData, error: _profileError } = await supabase
    .from('influencer_profiles')
    .select('*')
    .eq('id', id)
    .single();
  if (_profileError) console.warn('Error fetching influencer profile for edit:', _profileError);

  // Buscar email do usuário
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { data: userData, error: _userError } = await supabase
    .from('profiles')
    .select('email')
    .eq('id', id)
    .single();
  if (_userError) console.warn('Error fetching user email for edit:', _userError);

  // Combinar dados
  return {
    ...influencerData,
    email: userData?.email || null,
    profile: profileData || null
  };
}

export default async function EditInfluencerPage({ params }: { params: { id: string } }) {
  try {
    const influencer = await getInfluencerData(params.id);

    return (
      <AdminLayout title="Editar Criador" backLink={`/admin/criadores/${params.id}`}>
        <div className="max-w-5xl mx-auto">
          <CriadorEditForm
            initialData={influencer}
            criadorId={params.id}
          />
        </div>
      </AdminLayout>
    );
  } catch (error: any) {
    return (
      <AdminLayout title="Editar Criador" backLink="/admin/criadores">
        <div className="max-w-5xl mx-auto">
          <div className="bg-red-100 text-red-700 p-4 rounded mb-4">
            {error.message || 'Erro ao carregar dados do criador'}
          </div>
        </div>
      </AdminLayout>
    );
  }


}
