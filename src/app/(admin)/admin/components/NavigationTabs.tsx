"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FaBuilding, FaBullhorn, FaUsers } from 'react-icons/fa';

export default function NavigationTabs() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  return (
    <div className="flex items-center overflow-x-auto bg-[#f5f5f5] navigation-tabs" style={{ backgroundColor: '#f5f5f5' }}>
      <Link
        href="/admin/negocios"
        style={{ backgroundColor: '#f5f5f5' }}
        className={`px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ${
          isActive('/admin/negocios')
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700'
        }`}
      >
        <div className="flex items-center bg-[#f5f5f5]">
          <FaBuilding className="mr-2 bg-[#f5f5f5]" />
          <span className="bg-[#f5f5f5]">Negócios</span>
        </div>
      </Link>
      <Link
        href="/admin/campaigns"
        style={{ backgroundColor: '#f5f5f5' }}
        className={`px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ${
          isActive('/admin/campaigns')
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700'
        }`}
      >
        <div className="flex items-center bg-[#f5f5f5]">
          <FaBullhorn className="mr-2 bg-[#f5f5f5]" />
          <span className="bg-[#f5f5f5]">Campanhas</span>
        </div>
      </Link>
      <Link
        href="/admin/criadores"
        style={{ backgroundColor: '#f5f5f5' }}
        className={`px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap !bg-[#f5f5f5] ${
          isActive('/admin/criadores')
            ? 'border-blue-500 text-blue-600'
            : 'border-transparent text-gray-500 hover:text-gray-700'
        }`}
      >
        <div className="flex items-center bg-[#f5f5f5]">
          <FaUsers className="mr-2 bg-[#f5f5f5]" />
          <span className="bg-[#f5f5f5]">Criadores</span>
        </div>
      </Link>

    </div>
  );
}
