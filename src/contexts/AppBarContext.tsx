"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface AppBarContextType {
  appBarTitle: string;
  setAppBarTitle: (title: string) => void;
  appBarTrailing: ReactNode | null;
  setAppBarTrailing: (trailing: ReactNode | null) => void;
}

const AppBarContext = createContext<AppBarContextType | undefined>(undefined);

export const AppBarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [appBarTitle, setAppBarTitle] = useState("crIAdores"); // Default title
  const [appBarTrailing, setAppBarTrailing] = useState<ReactNode | null>(null); // Default trailing content

  return (
    <AppBarContext.Provider value={{ appBarTitle, setAppBarTitle, appBarTrailing, setAppBarTrailing }}>
      {children}
    </AppBarContext.Provider>
  );
};

export const useAppBar = () => {
  const context = useContext(AppBarContext);
  if (context === undefined) {
    throw new Error('useAppBar must be used within an AppBarProvider');
  }
  return context;
};