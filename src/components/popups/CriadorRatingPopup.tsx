'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import RatingForm from '@/components/ratings/RatingForm';
import RatingDisplay from '@/components/ratings/RatingDisplay';
import RatingsSummary from '@/components/ratings/RatingsSummary';
import { Loader2 } from 'lucide-react';

interface CriadorRatingPopupProps {
  campaignId: string;
  criadorId: string;
  restaurantId: string;
}

export default function CriadorRatingPopup({
  campaignId,
  criadorId,
  restaurantId
}: CriadorRatingPopupProps) {
  const [loading, setLoading] = useState(true);
  const [campaignCriador, setCampaignCriador] = useState<any>(null);
  const [criador, setCriador] = useState<any>(null);
  const [restaurant, setRestaurant] = useState<any>(null);
  const [existingRating, setExistingRating] = useState<any>(null);
  const [ratings, setRatings] = useState<any[]>([]);
  const [ratingStats, setRatingStats] = useState({
    averageRating: 0,
    totalRatings: 0,
    distribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
  });
  
  const supabase = createClientComponentClient();
  
  useEffect(() => {
    async function loadData() {
      try {
        // Obter a relação entre campanha e influenciador
        const { data: ciData, error: ciError } = await supabase
          .from('campaign_criadors')
          .select('id')
          .eq('campaign_id', campaignId)
          .eq('criador_id', criadorId)
          .single();
        
        if (ciError) throw ciError;
        setCampaignCriador(ciData);
        
        // Obter dados do influenciador
        const { data: criadorData, error: criadorError } = await supabase
          .from('profiles')
          .select('id, full_name')
          .eq('id', criadorId)
          .single();
        
        if (criadorError) throw criadorError;
        setCriador(criadorData);
        
        // Obter dados do restaurante
        const { data: restaurantData, error: restaurantError } = await supabase
          .from('profiles')
          .select('id, full_name')
          .eq('id', restaurantId)
          .single();
        
        if (restaurantError) throw restaurantError;
        setRestaurant(restaurantData);
        
        // Obter avaliação existente (se houver)
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          const { data: ratingData, error: ratingError } = await supabase
            .from('ratings')
            .select('*')
            .eq('campaign_criador_id', ciData.id)
            .eq('rater_id', user.id)
            .eq('ratee_id', criadorId)
            .single();
          
          if (!ratingError && ratingData) {
            setExistingRating(ratingData);
          }
        }
        
        // Obter todas as avaliações para este influenciador
        const { data: allRatings, error: ratingsError } = await supabase
          .from('ratings')
          .select(`
            id,
            rating,
            comment,
            created_at,
            updated_at,
            profiles!rater_id(full_name)
          `)
          .eq('ratee_id', criadorId)
          .order('created_at', { ascending: false });
        
        if (!ratingsError && allRatings) {
          setRatings(allRatings);
          
          // Calcular estatísticas
          const total = allRatings.length;
          const avg = total > 0 
            ? allRatings.reduce((sum, r) => sum + r.rating, 0) / total 
            : 0;
          
          const dist = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
          allRatings.forEach(r => {
            dist[r.rating as keyof typeof dist]++;
          });
          
          setRatingStats({
            averageRating: avg,
            totalRatings: total,
            distribution: dist
          });
        }
        
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [campaignId, criadorId, restaurantId, supabase]);
  
  const handleRatingSuccess = () => {
    // Recarregar os dados após uma avaliação bem-sucedida
    window.location.reload();
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }
  
  return (
    <div className="p-4">
      <Tabs defaultValue="avaliar">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="avaliar" className="flex-1">Avaliar</TabsTrigger>
          <TabsTrigger value="avaliacoes" className="flex-1">Avaliações</TabsTrigger>
        </TabsList>
        
        <TabsContent value="avaliar">
          {campaignCriador && criador && (
            <RatingForm
              campaignCriadorId={campaignCriador.id}
              rateeId={criadorId}
              rateeName={criador.full_name}
              existingRating={existingRating}
              onSuccess={handleRatingSuccess}
            />
          )}
        </TabsContent>
        
        <TabsContent value="avaliacoes">
          <div className="space-y-6">
            <RatingsSummary
              averageRating={ratingStats.averageRating}
              totalRatings={ratingStats.totalRatings}
              ratingDistribution={ratingStats.distribution}
              className="mb-6"
            />
            
            {ratings.length > 0 ? (
              <div className="space-y-4">
                {ratings.map((rating) => (
                  <RatingDisplay
                    key={rating.id}
                    rating={rating.rating}
                    comment={rating.comment}
                    raterName={rating.profiles.full_name}
                    date={rating.created_at}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Nenhuma avaliação recebida ainda.
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
