"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import CriadorDetail from './CriadorDetail';
import { FaShare, FaStar, FaUser, FaChartLine } from 'react-icons/fa';
import { popupStyles } from '@/styles/popupStyles';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import CriadorRatingPopup from './CriadorRatingPopup';

interface Criador {
  id: string;
  name: string;
  username: string;
  points: number;
  avatarUrl?: string;
  followers?: number;
  engagement?: number;
  posts?: number;
  classification?: string;
  categories?: string[];
  status?: 'active' | 'inactive';
  bio?: string;
  location?: string;
  instagram_username?: string;
  joinedDate?: string;
  // Campos adicionais do banco de dados
  follower_count?: number;
  avg_engagement_rate?: number;
  content_niche?: string[];
  location_city?: string;
  location_state?: string;
  primary_platform?: string;
  is_verified?: boolean;
  verification_date?: string;
  created_at?: string;
  updated_at?: string;
}

interface CriadorDetailsPopupProps {
  criadorId: string;
  mockCriadors?: Criador[];
  onClose?: () => void;
}

export default function CriadorDetailsPopup({
  criadorId,
  mockCriadors = [],
  onClose
}: CriadorDetailsPopupProps) {
  console.log('CriadorDetailsPopup renderizado com criadorId:', criadorId);

  const [criador, setCriador] = useState<Criador | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('perfil');
  const [userId, setUserId] = useState<string>('');
  const [restaurantId, setRestaurantId] = useState<string>('');
  const supabaseClient = createClientComponentClient();

  // Buscar usuário atual
  useEffect(() => {
    const fetchUser = async () => {
      const { data } = await supabaseClient.auth.getUser();
      if (data?.user) {
        setUserId(data.user.id);

        // Buscar o restaurante associado ao usuário
        const { data: restaurantData } = await supabaseClient
          .from('restaurant_profiles')
          .select('id')
          .eq('user_id', data.user.id)
          .single();

        if (restaurantData) {
          setRestaurantId(restaurantData.id);
        }
      }
    };

    fetchUser();
  }, [supabaseClient]);

  useEffect(() => {
    console.log('useEffect executado com criadorId:', criadorId);

    const fetchData = async () => {
      if (!criadorId) {
        console.log('Nenhum criadorId fornecido');
        setCriador(null);
        return;
      }

      setLoading(true);

      try {
        // Primeiro, verificar se o influenciador está nos dados mockados
        const mockCriador = mockCriadors.find(i => i.id === criadorId);

        if (mockCriador) {
          console.log('Influenciador encontrado nos dados mockados:', mockCriador.name);
          setCriador(mockCriador);
        } else {
          // Se não estiver nos dados mockados, tentar buscar do Supabase
          console.log('Influenciador não encontrado nos dados mockados, buscando do Supabase...');

          // Buscar dados do influenciador
          const { data: criadorData, error: criadorError } = await supabase
            .from('criador_profiles')
            .select(`
              id,
              bio,
              content_niche,
              primary_platform,
              instagram_username,
              location_city,
              location_state,
              avg_engagement_rate,
              follower_count,
              is_verified,
              verification_date,
              created_at,
              updated_at,
              profiles(id, name, username)
            `)
            .eq('id', criadorId)
            .single();

          if (criadorError || !criadorData) {
            console.error('Erro ao buscar influenciador:', criadorError);
            setCriador(null);
          } else {
            console.log('Influenciador encontrado no Supabase:', criadorData);

            // Buscar pontos do influenciador
            const { data: pointsData, error: pointsError } = await supabase
              .from('criador_points')
              .select('total_points, post_count')
              .eq('criador_id', criadorId)
              .single();

            if (pointsError) {
              console.error('Erro ao buscar pontos:', pointsError);
            }

            // Formatar os dados para o formato esperado pelo componente
            const formattedCriador: Criador = {
              id: criadorData.id,
              name: criadorData.profiles?.name || 'Nome não disponível',
              username: criadorData.profiles?.username || '@username',
              points: pointsData?.total_points || 0,
              bio: criadorData.bio,
              instagram_username: criadorData.instagram_username,
              location: criadorData.location_city && criadorData.location_state
                ? `${criadorData.location_city}, ${criadorData.location_state}`
                : undefined,
              followers: criadorData.follower_count,
              engagement: criadorData.avg_engagement_rate,
              posts: pointsData?.post_count,
              categories: criadorData.content_niche,
              joinedDate: criadorData.created_at
                ? new Date(criadorData.created_at).toLocaleDateString('pt-BR')
                : undefined,
              // Manter os campos originais também
              follower_count: criadorData.follower_count,
              avg_engagement_rate: criadorData.avg_engagement_rate,
              content_niche: criadorData.content_niche,
              location_city: criadorData.location_city,
              location_state: criadorData.location_state,
              primary_platform: criadorData.primary_platform,
              is_verified: criadorData.is_verified,
              verification_date: criadorData.verification_date,
              created_at: criadorData.created_at,
              updated_at: criadorData.updated_at
            };

            setCriador(formattedCriador);
          }
        }
      } catch (error) {
        console.error('Erro geral ao buscar dados do influenciador:', error);
        setCriador(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [criadorId, mockCriadors]);

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!criador) {
    return (
      <div className="p-8 text-center">
        <p className="text-lg text-gray-600">Influenciador não encontrado.</p>
      </div>
    );
  }

  // Determinar a cor de fundo com base na classificação do influenciador
  const getBackgroundColor = () => {
    const classification = criador.classification?.toLowerCase() || '';

    if (classification.includes('gold') || classification.includes('ouro')) {
      return 'bg-yellow-100 text-yellow-800';
    } else if (classification.includes('silver') || classification.includes('prata')) {
      return 'bg-gray-200 text-gray-800';
    } else if (classification.includes('bronze')) {
      return 'bg-orange-100 text-orange-800';
    }

    // Cor padrão se não tiver classificação
    return 'bg-blue-100 text-blue-800';
  };

  return (
    <div className="bg-gray-100 rounded-lg overflow-hidden flex flex-col h-full">
      {/* Cabeçalho Fixo */}
      <div className={`sticky top-0 z-20 p-6 ${getBackgroundColor()} shadow-md border-b border-white/20 ${popupStyles.responsive.header}`}>
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">{criador.name}</h2>
            <p className="opacity-80">{criador.username}</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="flex items-center gap-1 text-sm px-3 py-1 rounded bg-white/20 hover:bg-white/30 transition-colors"
              onClick={() => {
                // Criar URL para compartilhamento
                const url = new URL(window.location.href);
                url.searchParams.set('criador', criadorId);
                navigator.clipboard.writeText(url.toString());
                alert('Link copiado para a área de transferência!');
              }}
              title="Copiar link para compartilhar"
            >
              <FaShare className="h-4 w-4" />
              Compartilhar
            </button>
            <button
              onClick={() => {
                // Remover o parâmetro criador da URL
                const url = new URL(window.location.href);
                url.searchParams.delete('criador');
                window.history.pushState({}, '', url.toString());
                // Chamar a função onClose se fornecida
                if (onClose) onClose();
              }}
              className="rounded-full w-8 h-8 flex items-center justify-center bg-white/20 hover:bg-white/30 transition-colors"
              aria-label="Fechar"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Abas de navegação */}
      <div className="flex-1 overflow-hidden bg-white" style={{marginTop: "-1px"}}>
        <Tabs defaultValue="perfil" className="w-full h-full" value={activeTab} onValueChange={setActiveTab}>
          <div className="sticky top-0 z-30 shadow-sm p-0 m-0 bg-white border-b border-gray-200 -mt-px">
            <TabsList className="grid grid-cols-2 w-full h-full border-0 p-0 m-0 rounded-none bg-white">
              <TabsTrigger
                value="perfil"
                className="bg-white data-[state=active]:border-b-2 data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-700 data-[state=active]:font-medium py-3 text-gray-600 transition-all border-0 focus:ring-0 rounded-none"
              >
                <span className="flex items-center justify-center">
                  <FaUser className="h-4 w-4 mr-1.5" />
                  Perfil
                </span>
              </TabsTrigger>
              <TabsTrigger
                value="avaliacoes"
                className="bg-white data-[state=active]:border-b-2 data-[state=active]:border-b-blue-600 data-[state=active]:text-blue-700 data-[state=active]:font-medium py-3 text-gray-600 transition-all border-0 focus:ring-0 rounded-none"
              >
                <span className="flex items-center justify-center">
                  <FaStar className="h-4 w-4 mr-1.5" />
                  Avaliações
                </span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Conteúdo da aba Perfil */}
          <TabsContent value="perfil" className="p-6 overflow-auto h-[calc(100%-48px)] mt-0">
            <CriadorDetail criador={criador} />
          </TabsContent>

          {/* Conteúdo da aba Avaliações */}
          <TabsContent value="avaliacoes" className="p-0 overflow-auto h-[calc(100%-48px)] mt-0">
            {userId && restaurantId && criador && (
              <CriadorRatingPopup
                campaignId="default-campaign" // Idealmente, você teria o ID da campanha aqui
                criadorId={criadorId}
                restaurantId={restaurantId}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
