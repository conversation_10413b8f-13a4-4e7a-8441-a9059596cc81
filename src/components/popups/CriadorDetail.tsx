"use client";

import React from 'react';
import Image from 'next/image';
import { FaInstagram, FaMapMarkerAlt, FaCalendarAlt, FaStar } from 'react-icons/fa';

interface CriadorDetailProps {
  criador: {
    id: string;
    name: string;
    username: string;
    points: number;
    avatarUrl?: string;
    bio?: string;
    location?: string;
    followers?: number;
    engagement?: number;
    posts?: number;
    classification?: string;
    joinedDate?: string;
    categories?: string[];
    instagram_username?: string;
    // Campos adicionais do banco de dados
    follower_count?: number;
    avg_engagement_rate?: number;
    content_niche?: string[];
    location_city?: string;
    location_state?: string;
    primary_platform?: string;
    is_verified?: boolean;
    verification_date?: string;
    created_at?: string;
    updated_at?: string;
    // Dados relacionados
    recentPosts?: {
      id: string;
      imageUrl: string;
      caption: string;
      likes: number;
      comments: number;
      date: string;
    }[];
    campaigns?: {
      id: string;
      name: string;
      date: string;
      status: string;
      performance: number;
    }[];
  };
}

export default function CriadorDetail({ criador }: CriadorDetailProps) {
  // Log para depuração
  console.log('Renderizando CriadorDetail para:', {
    id: criador.id,
    name: criador.name,
    username: criador.username,
    points: criador.points
  });

  // Formatar números grandes (ex: 77.7K)
  const formatNumber = (num: number | undefined): string => {
    if (num === undefined) return 'N/A';

    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho com informações do influenciador */}
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {criador.avatarUrl ? (
            <Image
              src={criador.avatarUrl}
              alt={criador.name}
              width={80}
              height={80}
              className="rounded-full"
            />
          ) : (
            <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-2xl">
              {criador.name.charAt(0)}
            </div>
          )}
        </div>
        <div className="flex-grow">
          <h2 className="text-xl font-bold">{criador.name}</h2>
          <div className="flex items-center text-gray-600 mb-1">
            <FaInstagram className="mr-1" />
            <span>{criador.username}</span>
          </div>
          {criador.location && (
            <div className="flex items-center text-gray-600 text-sm mb-1">
              <FaMapMarkerAlt className="mr-1" />
              <span>{criador.location}</span>
            </div>
          )}
          {criador.joinedDate && (
            <div className="flex items-center text-gray-600 text-sm">
              <FaCalendarAlt className="mr-1" />
              <span>Desde {criador.joinedDate}</span>
            </div>
          )}
          {criador.classification && (
            <div className="mt-2 flex items-center">
              <FaStar className="text-yellow-500 mr-1" />
              <span className="font-medium">{criador.classification}</span>
            </div>
          )}
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-green-600">{criador.points} pts</div>
          <div className="text-sm text-gray-600">Pontuação total</div>
        </div>
      </div>

      {/* Bio do influenciador */}
      {criador.bio && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium mb-2">Bio</h3>
          <p className="text-gray-700">{criador.bio}</p>
        </div>
      )}

      {/* Métricas do influenciador */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">{formatNumber(criador.followers || criador.follower_count)}</div>
          <div className="text-sm text-gray-600">Seguidores</div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">{criador.engagement || criador.avg_engagement_rate ? (criador.engagement || criador.avg_engagement_rate).toFixed(1) + '%' : 'N/A'}</div>
          <div className="text-sm text-gray-600">Engajamento</div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">{criador.posts || 'N/A'}</div>
          <div className="text-sm text-gray-600">Posts</div>
        </div>
      </div>

      {/* Categorias */}
      {(criador.categories || criador.content_niche) && (criador.categories?.length > 0 || criador.content_niche?.length > 0) && (
        <div>
          <h3 className="font-medium mb-2">Categorias</h3>
          <div className="flex flex-wrap gap-2">
            {(criador.categories || criador.content_niche || []).map((category, index) => (
              <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                {category}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Campanhas recentes */}
      {criador.campaigns && criador.campaigns.length > 0 && (
        <div>
          <h3 className="font-medium mb-2">Campanhas Recentes</h3>
          <div className="space-y-2">
            {criador.campaigns.map((campaign) => (
              <div key={campaign.id} className="bg-white border border-gray-200 rounded-lg p-3 flex justify-between items-center">
                <div>
                  <div className="font-medium">{campaign.name}</div>
                  <div className="text-sm text-gray-600">{campaign.date}</div>
                </div>
                <div className="flex items-center">
                  <span className={`px-2 py-1 rounded-full text-xs mr-2 ${
                    campaign.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {campaign.status === 'active' ? 'Ativa' : 'Concluída'}
                  </span>
                  <span className="font-medium">{campaign.performance}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Posts recentes */}
      {criador.recentPosts && criador.recentPosts.length > 0 && (
        <div>
          <h3 className="font-medium mb-2">Posts Recentes</h3>
          <div className="grid grid-cols-3 gap-3">
            {criador.recentPosts.map((post) => (
              <div key={post.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div className="relative h-40">
                  <Image
                    src={post.imageUrl}
                    alt={post.caption}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-2">
                  <p className="text-xs text-gray-600 truncate">{post.caption}</p>
                  <div className="flex justify-between text-xs text-gray-600 mt-1">
                    <span>{post.likes} likes</span>
                    <span>{post.comments} comentários</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
