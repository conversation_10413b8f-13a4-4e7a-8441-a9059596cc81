'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { FaStar } from 'react-icons/fa';
import { Loader2 } from 'lucide-react';
import RatingForm from '@/components/ratings/RatingForm';
import RatingDisplay from '@/components/ratings/RatingDisplay';
import { usePopupNavigation } from '@/hooks/usePopupNavigation';
import RestaurantRatingPopup from '@/components/popups/RestaurantRatingPopup';
import CriadorRatingPopup from '@/components/popups/CriadorRatingPopup';

interface CampaignRatingsTabProps {
  campaignId: string;
  userRole: 'restaurant' | 'criador';
  userId: string;
}

export default function CampaignRatingsTab({
  campaignId,
  userRole,
  userId
}: CampaignRatingsTabProps) {
  const [loading, setLoading] = useState(true);
  const [campaignInfluencers, setCampaignInfluencers] = useState<any[]>([]);
  const [selectedInfluencer, setSelectedInfluencer] = useState<any>(null);
  const [restaurant, setRestaurant] = useState<any>(null);
  const [showRatingForm, setShowRatingForm] = useState(false);
  const [existingRating, setExistingRating] = useState<any>(null);
  const [receivedRatings, setReceivedRatings] = useState<any[]>([]);
  const [givenRatings, setGivenRatings] = useState<any[]>([]);
  
  const supabase = createClientComponentClient();
  const { openPopup } = usePopupNavigation({
    popupId: 'campaign-rating-popup'
  });
  
  useEffect(() => {
    async function loadData() {
      try {
        // Obter dados da campanha
        const { data: campaignData, error: campaignError } = await supabase
          .from('campaigns')
          .select('restaurant_id')
          .eq('id', campaignId)
          .single();
        
        if (campaignError) throw campaignError;
        
        // Obter dados do restaurante
        const { data: restaurantData, error: restaurantError } = await supabase
          .from('profiles')
          .select('id, full_name')
          .eq('id', campaignData.restaurant_id)
          .single();
        
        if (restaurantError) throw restaurantError;
        setRestaurant(restaurantData);
        
        // Obter influenciadores da campanha
        const { data: influencersData, error: influencersError } = await supabase
          .from('campaign_influencers')
          .select(`
            id,
            influencer_id,
            profiles:influencer_id(id, full_name)
          `)
          .eq('campaign_id', campaignId);
        
        if (influencersError) throw influencersError;
        setCampaignInfluencers(influencersData || []);
        
        // Obter avaliações recebidas
        const { data: receivedData, error: receivedError } = await supabase
          .from('ratings')
          .select(`
            id,
            rating,
            comment,
            created_at,
            updated_at,
            campaign_influencer_id,
            profiles!rater_id(id, full_name)
          `)
          .eq('ratee_id', userId)
          .eq('campaign_influencer_id', campaignId)
          .order('created_at', { ascending: false });
        
        if (!receivedError) {
          setReceivedRatings(receivedData || []);
        }
        
        // Obter avaliações dadas
        const { data: givenData, error: givenError } = await supabase
          .from('ratings')
          .select(`
            id,
            rating,
            comment,
            created_at,
            updated_at,
            campaign_influencer_id,
            profiles!ratee_id(id, full_name)
          `)
          .eq('rater_id', userId)
          .eq('campaign_influencer_id', campaignId)
          .order('created_at', { ascending: false });
        
        if (!givenError) {
          setGivenRatings(givenData || []);
        }
        
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [campaignId, userId, supabase]);
  
  const handleSelectInfluencer = async (influencer: any) => {
    setSelectedInfluencer(influencer);
    setShowRatingForm(true);
    
    try {
      // Verificar se já existe uma avaliação
      const { data, error } = await supabase
        .from('ratings')
        .select('*')
        .eq('campaign_influencer_id', influencer.id)
        .eq('rater_id', userId)
        .eq('ratee_id', influencer.influencer_id)
        .single();
      
      if (!error && data) {
        setExistingRating(data);
      } else {
        setExistingRating(null);
      }
    } catch (error) {
      console.error('Erro ao verificar avaliação existente:', error);
    }
  };
  
  const handleRateRestaurant = async () => {
    setShowRatingForm(true);
    
    try {
      // Encontrar o campaign_influencer_id correto
      const myInfluencerRecord = campaignInfluencers.find(
        ci => ci.influencer_id === userId
      );
      
      if (myInfluencerRecord) {
        // Verificar se já existe uma avaliação
        const { data, error } = await supabase
          .from('ratings')
          .select('*')
          .eq('campaign_influencer_id', myInfluencerRecord.id)
          .eq('rater_id', userId)
          .eq('ratee_id', restaurant.id)
          .single();
        
        if (!error && data) {
          setExistingRating(data);
        } else {
          setExistingRating(null);
        }
      }
    } catch (error) {
      console.error('Erro ao verificar avaliação existente:', error);
    }
  };
  
  const handleRatingSuccess = () => {
    setShowRatingForm(false);
    // Recarregar os dados
    window.location.reload();
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }
  
  return (
    <div className="p-4">
      <Tabs defaultValue="recebidas">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="recebidas" className="flex-1">Avaliações Recebidas</TabsTrigger>
          <TabsTrigger value="dadas" className="flex-1">Avaliações Dadas</TabsTrigger>
          {userRole === 'restaurant' && (
            <TabsTrigger value="avaliar" className="flex-1">Avaliar Criadores</TabsTrigger>
          )}
          {userRole === 'criador' && (
            <TabsTrigger value="avaliar" className="flex-1">Avaliar Restaurante</TabsTrigger>
          )}
        </TabsList>
        
        <TabsContent value="recebidas">
          {receivedRatings.length > 0 ? (
            <div className="space-y-4">
              {receivedRatings.map((rating) => (
                <RatingDisplay
                  key={rating.id}
                  rating={rating.rating}
                  comment={rating.comment}
                  raterName={rating.profiles.full_name}
                  date={rating.created_at}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Você ainda não recebeu avaliações nesta campanha.
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="dadas">
          {givenRatings.length > 0 ? (
            <div className="space-y-4">
              {givenRatings.map((rating) => (
                <RatingDisplay
                  key={rating.id}
                  rating={rating.rating}
                  comment={rating.comment}
                  raterName={`Você avaliou ${rating.profiles.full_name}`}
                  date={rating.created_at}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Você ainda não avaliou ninguém nesta campanha.
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="avaliar">
          {userRole === 'restaurant' ? (
            showRatingForm && selectedInfluencer ? (
              <RatingForm
                campaignInfluencerId={selectedInfluencer.id}
                rateeId={selectedInfluencer.influencer_id}
                rateeName={selectedInfluencer.profiles.full_name}
                existingRating={existingRating}
                onSuccess={handleRatingSuccess}
                onCancel={() => setShowRatingForm(false)}
              />
            ) : (
              <div>
                <h3 className="text-lg font-medium mb-4">Selecione um criador para avaliar</h3>
                <div className="space-y-2">
                  {campaignInfluencers.map((ci) => (
                    <div 
                      key={ci.id}
                      className="p-3 border rounded-lg flex justify-between items-center cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSelectInfluencer(ci)}
                    >
                      <span>{ci.profiles.full_name}</span>
                      <Button size="sm" variant="outline">
                        <FaStar className="mr-1 text-yellow-500" />
                        Avaliar
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )
          ) : (
            showRatingForm ? (
              <RatingForm
                campaignInfluencerId={campaignInfluencers.find(ci => ci.influencer_id === userId)?.id}
                rateeId={restaurant.id}
                rateeName={restaurant.full_name}
                existingRating={existingRating}
                onSuccess={handleRatingSuccess}
                onCancel={() => setShowRatingForm(false)}
              />
            ) : (
              <div className="text-center py-8">
                <Button onClick={handleRateRestaurant}>
                  <FaStar className="mr-2" />
                  Avaliar {restaurant.full_name}
                </Button>
              </div>
            )
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
