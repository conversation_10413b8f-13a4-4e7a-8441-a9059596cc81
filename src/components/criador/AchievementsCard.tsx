'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { toast } from '@/components/ui/use-toast'
import { Loader2, Award, Lock } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface AchievementsCardProps {
  criadorId: string
  className?: string
}

interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  criteria: any
  points: number
  earned: boolean
  earned_at?: string
}

export default function AchievementsCard({ criadorId, className }: AchievementsCardProps) {
  const supabase = createClientComponentClient()
  const [loading, setLoading] = useState(true)
  const [achievements, setAchievements] = useState<Achievement[]>([])
  
  useEffect(() => {
    fetchAchievements()
  }, [criadorId])
  
  const fetchAchievements = async () => {
    setLoading(true)
    
    try {
      // Obter todas as conquistas disponíveis
      const { data: allAchievements, error: achievementsError } = await supabase
        .from('achievements')
        .select('*')
        .order('points', { ascending: false });
      
      if (achievementsError) {
        throw new Error(`Erro ao carregar conquistas: ${achievementsError.message}`);
      }
      
      // Obter conquistas do influenciador
      const { data: userAchievements, error: userAchievementsError } = await supabase
        .from('user_achievements')
        .select('*')
        .eq('user_id', criadorId);
      
      if (userAchievementsError) {
        throw new Error(`Erro ao carregar conquistas do usuário: ${userAchievementsError.message}`);
      }
      
      // Combinar os dados
      const combinedAchievements = allAchievements.map(achievement => {
        const userAchievement = userAchievements?.find(ua => ua.achievement_id === achievement.id);
        return {
          ...achievement,
          earned: !!userAchievement,
          earned_at: userAchievement?.earned_at
        };
      });
      
      setAchievements(combinedAchievements);
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível carregar as conquistas',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Renderizar ícone da conquista
  const renderAchievementIcon = (icon: string, earned: boolean) => {
    // Aqui você pode mapear os ícones para componentes específicos
    // Por enquanto, vamos usar o ícone Award como padrão
    return earned ? (
      <Award className="h-8 w-8 text-amber-500" />
    ) : (
      <div className="relative">
        <Award className="h-8 w-8 text-gray-300" />
        <Lock className="h-4 w-4 text-gray-400 absolute -bottom-1 -right-1" />
      </div>
    );
  };
  
  // Calcular progresso total
  const totalAchievements = achievements.length;
  const earnedAchievements = achievements.filter(a => a.earned).length;
  const progressPercentage = totalAchievements > 0 
    ? Math.round((earnedAchievements / totalAchievements) * 100) 
    : 0;
  
  // Calcular pontos totais
  const totalPoints = achievements
    .filter(a => a.earned)
    .reduce((sum, a) => sum + (a.points || 0), 0);

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="bg-[#f5f5f5] pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-medium">
              <Award className="h-5 w-5 inline-block mr-2 text-amber-500" />
              Conquistas
            </CardTitle>
            <CardDescription>
              {earnedAchievements} de {totalAchievements} conquistas desbloqueadas
            </CardDescription>
          </div>
          <Badge variant="outline" className="bg-white">
            {totalPoints} pontos
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="p-4">
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : achievements.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            Nenhuma conquista disponível.
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {achievements.map((achievement) => (
              <TooltipProvider key={achievement.id}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div 
                      className={`flex flex-col items-center p-3 rounded-lg ${
                        achievement.earned 
                          ? 'bg-amber-50 border border-amber-200' 
                          : 'bg-gray-50 border border-gray-200'
                      }`}
                    >
                      {renderAchievementIcon(achievement.icon, achievement.earned)}
                      <span className={`mt-2 text-sm font-medium text-center ${
                        achievement.earned ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {achievement.name}
                      </span>
                      <span className="text-xs text-gray-500 mt-1">
                        {achievement.points} pts
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="max-w-xs">
                      <p className="font-medium">{achievement.name}</p>
                      <p className="text-sm">{achievement.description}</p>
                      {achievement.earned && achievement.earned_at && (
                        <p className="text-xs mt-1 text-green-600">
                          Desbloqueado em {new Date(achievement.earned_at).toLocaleDateString('pt-BR')}
                        </p>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
