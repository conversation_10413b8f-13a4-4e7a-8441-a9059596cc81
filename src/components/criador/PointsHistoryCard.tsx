'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { toast } from '@/components/ui/use-toast'
import { Loader2, TrendingUp, Clock } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface PointsHistoryCardProps {
  campaignCriadorId: string
  className?: string
}

interface PointsHistoryEntry {
  id: string
  campaign_criador_id: string
  points: number
  reason: string
  details: any
  created_at: string
}

export default function PointsHistoryCard({ campaignCriadorId, className }: PointsHistoryCardProps) {
  const supabase = createClientComponentClient()
  const [loading, setLoading] = useState(true)
  const [history, setHistory] = useState<PointsHistoryEntry[]>([])
  const [totalPoints, setTotalPoints] = useState(0)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)
  const pageSize = 10
  
  useEffect(() => {
    fetchPointsHistory()
  }, [campaignCriadorId, page])
  
  const fetchPointsHistory = async () => {
    setLoading(true)
    
    try {
      // Obter histórico de pontos
      const { data, error, count } = await supabase
        .from('points_history')
        .select('*', { count: 'exact' })
        .eq('campaign_criador_id', campaignCriadorId)
        .order('created_at', { ascending: false })
        .range((page - 1) * pageSize, page * pageSize - 1);
      
      if (error) {
        throw new Error(`Erro ao carregar histórico de pontos: ${error.message}`);
      }
      
      setHistory(data || []);
      setHasMore(count ? count > page * pageSize : false);
      
      // Obter total de pontos
      const { data: campaignCriador, error: ciError } = await supabase
        .from('campaign_criadors')
        .select('total_points')
        .eq('id', campaignCriadorId)
        .single();
      
      if (!ciError && campaignCriador) {
        setTotalPoints(campaignCriador.total_points || 0);
      }
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível carregar o histórico de pontos',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Formatar data
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Carregar mais
  const loadMore = () => {
    setPage(page + 1);
  };
  
  // Carregar menos
  const loadLess = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="bg-[#f5f5f5] pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-medium">
              <TrendingUp className="h-5 w-5 inline-block mr-2 text-green-500" />
              Histórico de Pontos
            </CardTitle>
            <CardDescription>
              Registro de pontos ganhos
            </CardDescription>
          </div>
          <Badge variant="outline" className="bg-white">
            {totalPoints} pontos totais
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {loading && page === 1 ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : history.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Nenhum registro de pontos encontrado.
          </div>
        ) : (
          <>
            <div className="divide-y divide-gray-200">
              {history.map((entry) => (
                <div key={entry.id} className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium text-gray-900">
                        {entry.reason}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(entry.created_at)}
                      </div>
                      {entry.details?.post_id && (
                        <div className="text-xs text-gray-500 mt-1">
                          Post ID: {entry.details.post_id}
                        </div>
                      )}
                    </div>
                    <Badge variant={entry.points >= 0 ? "success" : "destructive"} className="ml-2">
                      {entry.points >= 0 ? '+' : ''}{entry.points} pts
                    </Badge>
                  </div>
                  
                  {entry.details?.metrics && (
                    <div className="mt-2 grid grid-cols-5 gap-2 text-xs text-gray-500">
                      {entry.details.metrics.likes_count !== undefined && (
                        <div>
                          <span className="font-medium">Likes:</span> {entry.details.metrics.likes_count}
                        </div>
                      )}
                      {entry.details.metrics.comments_count !== undefined && (
                        <div>
                          <span className="font-medium">Comentários:</span> {entry.details.metrics.comments_count}
                        </div>
                      )}
                      {entry.details.metrics.shares_count !== undefined && (
                        <div>
                          <span className="font-medium">Compartilhamentos:</span> {entry.details.metrics.shares_count}
                        </div>
                      )}
                      {entry.details.metrics.saves_count !== undefined && (
                        <div>
                          <span className="font-medium">Salvos:</span> {entry.details.metrics.saves_count}
                        </div>
                      )}
                      {entry.details.metrics.views_count !== undefined && (
                        <div>
                          <span className="font-medium">Visualizações:</span> {entry.details.metrics.views_count}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="flex justify-between p-4 border-t border-gray-200">
              <Button
                variant="outline"
                size="sm"
                onClick={loadLess}
                disabled={page === 1 || loading}
              >
                {loading && page !== 1 ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                Anterior
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={loadMore}
                disabled={!hasMore || loading}
              >
                {loading && page !== 1 ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                Próximo
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
