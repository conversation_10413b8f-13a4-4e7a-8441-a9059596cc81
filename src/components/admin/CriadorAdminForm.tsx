"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FaSave, FaTimes } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { SecondaryActionButton } from '@/components/ui/SecondaryActionButton';

interface CriadorFormData {
  name: string;
  username: string;
  email: string;
  bio?: string;
  instagram_username?: string;
  tiktok_username?: string;
  location_city: string;
  location_state: string;
  follower_count?: number;
  avg_engagement_rate?: number;
  classification: string;
  content_niche?: string;
}

interface CriadorAdminFormProps {
  onCancel?: () => void;
  onSuccess: (criadorId: string) => void;
  initialData?: any;
  isEditing?: boolean;
}

export default function CriadorAdminForm({
  onCancel,
  onSuccess,
  initialData,
  isEditing = false
}: CriadorAdminFormProps) {
  const [loading, setLoading] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm<CriadorFormData>({
    defaultValues: {
      name: initialData?.name || '',
      username: initialData?.username || '',
      email: initialData?.email || '',
      bio: initialData?.profile?.bio || '',
      instagram_username: initialData?.profile?.instagram_username || '',
      tiktok_username: initialData?.profile?.tiktok_username || '',
      location_city: initialData?.profile?.location_city || '',
      location_state: initialData?.profile?.location_state || '',
      follower_count: initialData?.profile?.follower_count || 0,
      avg_engagement_rate: initialData?.profile?.avg_engagement_rate || 0,
      classification: initialData?.classification || 'Standard',
      content_niche: initialData?.profile?.content_niche ? initialData.profile.content_niche.join(', ') : '',
    }
  });

  const onSubmit = async (data: CriadorFormData) => {
    try {
      setLoading(true);

      // Formatar dados para o formato esperado pelo banco
      const criadorData = {
        name: data.name,
        username: data.username,
        email: data.email,
        classification: data.classification,
        profile: {
          bio: data.bio || '',
          instagram_username: data.instagram_username || '',
          tiktok_username: data.tiktok_username || '',
          location_city: data.location_city,
          location_state: data.location_state,
          follower_count: parseInt(data.follower_count?.toString() || '0'),
          avg_engagement_rate: parseFloat(data.avg_engagement_rate?.toString() || '0'),
          content_niche: data.content_niche ? data.content_niche.split(',').map(niche => niche.trim()) : [],
          primary_platform: data.instagram_username ? 'instagram' : 'tiktok',
        },
        updated_at: new Date().toISOString(),
      };

      let result;

      if (isEditing && initialData?.id) {
        console.log('Atualizando influenciador existente:', initialData.id);
        // Atualizar influenciador existente usando a API
        try {
          // Adicionar ID ao objeto de dados
          const updateData = { ...criadorData, id: initialData.id };

          // Usar a API para atualizar o influenciador
          const baseUrl = window.location.origin;
          const response = await fetch(`${baseUrl}/api/admin/criadors/${initialData.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
          }

          result = await response.json();
          toast.success('Influenciador atualizado com sucesso!');
        } catch (error: any) {
          console.error('Erro ao atualizar influenciador:', error);
          toast.error(`Erro ao atualizar influenciador: ${error.message}`);
          setLoading(false);
          return;
        }
      } else {
        console.log('Criando novo influenciador');
        // Inserir novo influenciador usando a API
        try {
          // Adicionar data de criação para novos influenciadores
          const createData = {
            ...criadorData,
            created_at: new Date().toISOString()
          };

          // Usar a API para criar o influenciador
          const baseUrl = window.location.origin;
          const response = await fetch(`${baseUrl}/api/admin/criadors`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(createData)
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
          }

          result = await response.json();
          toast.success('Influenciador criado com sucesso!');
        } catch (error: any) {
          console.error('Erro ao criar influenciador:', error);
          toast.error(`Erro ao criar influenciador: ${error.message}`);
          setLoading(false);
          return;
        }
      }

      // Chamar callback de sucesso com o ID do influenciador
      if (result && result.id) {
        onSuccess(result.id);
      } else if (initialData?.id) {
        onSuccess(initialData.id);
      }
    } catch (error: any) {
      console.error('Erro ao processar formulário:', error);
      toast.error(`Erro ao processar formulário: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="p-6 space-y-6">
          {/* Informações Básicas */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Informações Básicas</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nome Completo *</label>
              <input
                type="text"
                {...register('name', { required: 'Nome é obrigatório' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: João Silva"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nome de Usuário *</label>
              <input
                type="text"
                {...register('username', { required: 'Nome de usuário é obrigatório' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: joaosilva"
              />
              {errors.username && <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
              <input
                type="email"
                {...register('email', {
                  required: 'Email é obrigatório',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Email inválido'
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: <EMAIL>"
              />
              {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Classificação *</label>
              <select
                {...register('classification', { required: 'Classificação é obrigatória' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Standard">Standard</option>
                <option value="Bronze">Bronze</option>
                <option value="Silver">Silver</option>
                <option value="Gold">Gold</option>
                <option value="Diamond">Diamond</option>
              </select>
              {errors.classification && <p className="mt-1 text-sm text-red-600">{errors.classification.message}</p>}
            </div>
          </div>

          {/* Perfil */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Perfil</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
              <textarea
                {...register('bio')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Descrição do influenciador"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nichos de Conteúdo</label>
              <input
                type="text"
                {...register('content_niche')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: food, lifestyle, moda (separados por vírgula)"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cidade *</label>
                <input
                  type="text"
                  {...register('location_city', { required: 'Cidade é obrigatória' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: São Paulo"
                />
                {errors.location_city && <p className="mt-1 text-sm text-red-600">{errors.location_city.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Estado *</label>
                <input
                  type="text"
                  {...register('location_state', { required: 'Estado é obrigatório' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: SP"
                />
                {errors.location_state && <p className="mt-1 text-sm text-red-600">{errors.location_state.message}</p>}
              </div>
            </div>
          </div>

          {/* Redes Sociais */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Redes Sociais</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Usuário do Instagram</label>
              <input
                type="text"
                {...register('instagram_username')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: joaosilva (sem @)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Usuário do TikTok</label>
              <input
                type="text"
                {...register('tiktok_username')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex: joaosilva (sem @)"
              />
            </div>
          </div>

          {/* Métricas */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Métricas</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Número de Seguidores</label>
                <input
                  type="number"
                  {...register('follower_count')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: 10000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Taxa de Engajamento (%)</label>
                <input
                  type="number"
                  step="0.01"
                  {...register('avg_engagement_rate')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: 3.5"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Botões de ação */}
        <div className="px-6 py-4 bg-gray-50 border-t flex justify-end space-x-3 rounded-b-lg">
          {onCancel && (
            <SecondaryActionButton
              type="button"
              onClick={onCancel}
              disabled={loading}
              icon={<FaTimes />}
            >
              Cancelar
            </SecondaryActionButton>
          )}
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={loading}
          >
            <FaSave className="mr-2" />
            {loading ? 'Salvando...' : isEditing ? 'Atualizar' : 'Criar'}
          </button>
        </div>
      </form>
    </div>
  );
}
