"use client";

import React from 'react';
import Link from 'next/link';
import { FaEdit } from 'react-icons/fa';

interface CriadorProfile {
  id: string;
  bio: string | null;
  content_niche: string[] | null;
  primary_platform: string | null;
  instagram_username: string | null;
  tiktok_username: string | null;
  location_city: string | null;
  location_state: string | null;
  follower_count: number | null;
  avg_engagement_rate: number | null;
  is_verified: boolean | null;
  created_at: string | null;
  updated_at: string | null;
}

interface Criador {
  id: string;
  name: string;
  username: string;
  classification: string;
  conversions: number | null;
  total_earnings: number | null;
  created_at: string | null;
  updated_at: string | null;
  email?: string | null;
  profile?: CriadorProfile;
}

interface CriadorDetailsViewProps {
  criador: Criador;
  criadorId: string;
}

export default function CriadorDetailsView({ criador, criadorId }: CriadorDetailsViewProps) {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Cabeçalho */}
      <div className="bg-gray-50 p-6 border-b flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{criador.name}</h1>
          <p className="text-gray-500">@{criador.username}</p>
        </div>
        <div className="flex space-x-2">
          <Link href={`/admin/criadors/${criadorId}/edit`}>
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
              <FaEdit className="mr-2" />
              Editar
            </button>
          </Link>
        </div>
      </div>

      {/* Conteúdo */}
      <div className="p-6 space-y-8">
        {/* Informações Básicas */}
        <div>
          <h2 className="text-lg font-medium border-b pb-2 mb-4">Informações Básicas</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm text-gray-500">Nome</p>
              <p className="font-medium">{criador.name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Nome de Usuário</p>
              <p className="font-medium">@{criador.username}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Email</p>
              <p className="font-medium">{criador.email || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Classificação</p>
              <p className="font-medium">{criador.classification}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Conversões</p>
              <p className="font-medium">{criador.conversions || 0}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Ganhos Totais</p>
              <p className="font-medium">R$ {criador.total_earnings?.toFixed(2) || '0.00'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Data de Criação</p>
              <p className="font-medium">{formatDate(criador.created_at)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Última Atualização</p>
              <p className="font-medium">{formatDate(criador.updated_at)}</p>
            </div>
          </div>
        </div>

        {/* Perfil */}
        {criador.profile && (
          <div>
            <h2 className="text-lg font-medium border-b pb-2 mb-4">Perfil</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <p className="text-sm text-gray-500">Bio</p>
                <p className="font-medium">{criador.profile.bio || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Plataforma Principal</p>
                <p className="font-medium">{criador.profile.primary_platform || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Nichos de Conteúdo</p>
                <p className="font-medium">
                  {criador.profile.content_niche && criador.profile.content_niche.length > 0
                    ? criador.profile.content_niche.join(', ')
                    : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Localização</p>
                <p className="font-medium">
                  {criador.profile.location_city && criador.profile.location_state
                    ? `${criador.profile.location_city}, ${criador.profile.location_state}`
                    : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Verificado</p>
                <p className="font-medium">{criador.profile.is_verified ? 'Sim' : 'Não'}</p>
              </div>
            </div>
          </div>
        )}

        {/* Redes Sociais */}
        {criador.profile && (
          <div>
            <h2 className="text-lg font-medium border-b pb-2 mb-4">Redes Sociais</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500">Instagram</p>
                <p className="font-medium">
                  {criador.profile.instagram_username
                    ? `@${criador.profile.instagram_username}`
                    : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">TikTok</p>
                <p className="font-medium">
                  {criador.profile.tiktok_username
                    ? `@${criador.profile.tiktok_username}`
                    : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Métricas */}
        {criador.profile && (
          <div>
            <h2 className="text-lg font-medium border-b pb-2 mb-4">Métricas</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500">Seguidores</p>
                <p className="font-medium">
                  {criador.profile.follower_count
                    ? criador.profile.follower_count.toLocaleString()
                    : '0'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Taxa de Engajamento</p>
                <p className="font-medium">
                  {criador.profile.avg_engagement_rate
                    ? `${criador.profile.avg_engagement_rate.toFixed(2)}%`
                    : '0%'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
