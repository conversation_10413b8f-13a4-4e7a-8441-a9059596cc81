"use client";

import React, { useState } from 'react';
// import { supabase } from '@/lib/supabase/client'; // Removido
import { useForm } from 'react-hook-form';
import { FaSave, FaTimes } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { SecondaryActionButton } from '@/components/ui/SecondaryActionButton';

interface NegocioFormData {
  business_name: string;
  description: string;
  cuisine_type?: string;
  address?: string;
  city: string;
  state: string;
  postal_code?: string;
  website?: string;
  instagram_url?: string;
  facebook_url?: string;
  tiktok_url?: string;
}

interface NegocioAdminFormProps {
  onCancel?: () => void;
  onSuccess: (negocioId: string) => void;
  initialData?: any;
  isEditing?: boolean;
}

export default function NegocioAdminForm({
  onCancel,
  onSuccess,
  initialData,
  isEditing = false
}: NegocioAdminFormProps) {
  const [loading, setLoading] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm<NegocioFormData>({
    defaultValues: {
      business_name: initialData?.business_name || '',
      description: initialData?.description || '',
      cuisine_type: initialData?.cuisine_type || '',
      address: initialData?.address || '',
      city: initialData?.city || '',
      state: initialData?.state || '',
      postal_code: initialData?.postal_code || '',
      website: initialData?.website || '',
      instagram_url: initialData?.instagram_url || '',
      facebook_url: initialData?.facebook_url || '',
      tiktok_url: initialData?.tiktok_url || ''
    }
  });

  const onSubmit = async (data: NegocioFormData) => {
    try {
      setLoading(true);

      // Formatar dados para o formato esperado pelo banco
      const negocioData = {
        business_name: data.business_name,
        description: data.description,
        cuisine_type: data.cuisine_type,
        address: data.address,
        city: data.city,
        state: data.state,
        postal_code: data.postal_code,
        website: data.website,
        instagram_url: data.instagram_url,
        facebook_url: data.facebook_url,
        tiktok_url: data.tiktok_url,
        updated_at: new Date().toISOString(),
        // Adicionar flag para indicar que queremos sincronizar com a tabela negocios
        sync_with_negocios: true
      };

      let result;

      if (isEditing && initialData?.id) {
        console.log('Atualizando negocioe existente:', initialData.id);
        // Atualizar negocioe existente usando a API
        try {
          // Adicionar ID ao objeto de dados
          const updateData = { ...negocioData, id: initialData.id };

          // Usar a API para atualizar o negocioe
          const baseUrl = window.location.origin;
          const response = await fetch(`${baseUrl}/api/admin/negocios/${initialData.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
          }

          result = await response.json();
          toast.success('Negocioe atualizado com sucesso!');
        } catch (error: any) {
          console.error('Erro ao atualizar negocioe:', error);
          toast.error(`Erro ao atualizar negocioe: ${error.message}`);
          setLoading(false);
          return;
        }
      } else {
        console.log('Criando novo negocioe');
        // Inserir novo negocioe usando a API
        try {
          // Adicionar data de criação para novos negocioes
          const createData = {
            ...negocioData,
            created_at: new Date().toISOString()
          };

          // Usar a API para criar o negocioe
          const baseUrl = window.location.origin;
          const response = await fetch(`${baseUrl}/api/admin/negocios`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(createData)
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
          }

          result = await response.json();
          toast.success('Negocioe criado com sucesso!');
        } catch (error: any) {
          console.error('Erro ao criar negocioe:', error);
          toast.error(`Erro ao criar negocioe: ${error.message}`);
          setLoading(false);
          return;
        }
      }

      // Processar resultado e redirecionar
      if (result) {
        if (Array.isArray(result) && result.length > 0) {
          // Resultado da API antiga (array)
          onSuccess(result[0].id);
        } else if (result.id) {
          // Resultado da nova API (objeto único)
          onSuccess(result.id);
        } else if (isEditing && initialData?.id) {
          // Se estamos editando e não recebemos um ID de volta, use o ID original
          onSuccess(initialData.id);
        } else {
          console.warn('Operação concluída, mas formato de resultado inesperado:', result);
          // Redirecionar para a lista de negocioes
          window.location.href = '/admin/negocios';
        }
      } else {
        console.warn('Operação concluída, mas nenhum resultado retornado');
        // Redirecionar para a lista de negocioes
        window.location.href = '/admin/negocios';
      }
    } catch (error) {
      console.error('Erro ao salvar negocioe:', error);
      toast.error('Erro ao salvar negocioe');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold">Informações do Negocioe</h2>
        <p className="text-gray-600 mt-1">Preencha todos os campos obrigatórios (*)</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="p-6 space-y-6">
          {/* Informações Básicas */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Informações Básicas</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Nome do Negócio *</label>
              <input
                type="text"
                {...register('business_name', { required: 'Nome é obrigatório' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: Negócio Exemplo"
              />
              {errors.business_name && <p className="mt-1 text-sm text-red-600">{errors.business_name.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Descrição</label>
              <textarea
                {...register('description')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Descreva o negócio"
              />
              {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Culinária</label>
              <input
                type="text"
                {...register('cuisine_type')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: Italiana, Japonesa, Brasileira"
              />
            </div>
          </div>

          {/* Localização */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Localização</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Endereço</label>
              <input
                type="text"
                {...register('address')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: Rua Exemplo, 123"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cidade *</label>
                <input
                  type="text"
                  {...register('city', { required: 'Cidade é obrigatória' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Ex: São Paulo"
                />
                {errors.city && <p className="mt-1 text-sm text-red-600">{errors.city.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Estado *</label>
                <input
                  type="text"
                  {...register('state', { required: 'Estado é obrigatório' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Ex: SP"
                />
                {errors.state && <p className="mt-1 text-sm text-red-600">{errors.state.message}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">CEP</label>
              <input
                type="text"
                {...register('postal_code')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: 01234-567"
              />
            </div>
          </div>

          {/* Redes Sociais e Website */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium border-b pb-2">Redes Sociais e Website</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
              <input
                type="url"
                {...register('website')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: https://www.negocioeexemplo.com.br"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Instagram URL</label>
              <input
                type="url"
                {...register('instagram_url')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: https://instagram.com/negocioeexemplo"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Facebook URL</label>
              <input
                type="url"
                {...register('facebook_url')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: https://facebook.com/negocioeexemplo"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">TikTok URL</label>
              <input
                type="url"
                {...register('tiktok_url')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                placeholder="Ex: https://tiktok.com/@negocioeexemplo"
              />
            </div>
          </div>

          <div className="flex space-x-3">
            <SecondaryActionButton
              type="button"
              onClick={onCancel}
              disabled={loading}
              icon={<FaTimes />}
            >
              Cancelar
            </SecondaryActionButton>

            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              disabled={loading}
            >
              <FaSave className="mr-2" />
              {loading ? 'Salvando...' : 'Salvar Negócio'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
