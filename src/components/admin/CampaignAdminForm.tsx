"use client";

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';
import { useForm, Controller } from 'react-hook-form';
import { FaSave, FaTimes, FaEye } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import CampaignPreview from './CampaignPreview'; // Assuming this component will also be updated later
import { SecondaryActionButton } from '@/components/ui/SecondaryActionButton';

interface Negocio {
  id: string;
  business_name: string;
}

interface CampaignFormData {
  name: string;
  description: string;
  restaurant_id: string;
  start_date: string;
  end_date: string;
  plan_type: string; // Replaces budget
  campaign_goal: string; // New
  available_visit_dates: string; // New - simple text for now
  key_message: string; // New
  content_type_suggestions: string; // New - comma-separated
  tone_of_voice: string; // New
  influencer_post_deadline: string; // New
  restaurant_selection_deadline: string; // New
  hashtags: string;
  mentions: string;
  color: string;
  textColor: string;
  progressColor: string;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  briefing: string; // Add briefing field
  influencer_count_target: number; // Add influencer_count_target field
  // Removed: min_followers, required_posts
}

interface CampaignAdminFormProps {
  onCancel?: () => void;
  onSuccess: (campaignId: string) => void;
  initialData?: any; // Consider a more specific type later
  isEditing?: boolean;
}

const CampaignAdminForm: React.FC<CampaignAdminFormProps> = ({
  onCancel,
  onSuccess,
  initialData,
  isEditing = false
}) => {
  const [negocios, setNegocios] = useState<Negocio[]>([]);
  const [loading, setLoading] = useState(false);

  // Helper function to format date string to YYYY-MM-DD
  const formatDateToYYYYMMDD = (dateInput: string | Date | undefined | null): string => {
    if (!dateInput) return '';
    try {
      const dateStr = typeof dateInput === 'string' ? dateInput : dateInput.toISOString();
      // Ensure we are creating a new Date object from the string to handle various input formats correctly
      // and then extract the YYYY-MM-DD part in UTC.
      return new Date(dateStr).toISOString().split('T')[0];
    } catch (e) {
      console.warn("Could not parse date input for YYYY-MM-DD formatting:", dateInput, e);
      // Fallback for simple YYYY-MM-DD strings that might cause `new Date()` to misinterpret
      if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
        return dateInput;
      }
      return '';
    }
  };

  const { register, handleSubmit, control, setValue, formState: { errors } } = useForm<CampaignFormData>({
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      restaurant_id: initialData?.restaurant_id || '',
      start_date: initialData?.start_date ? formatDateToYYYYMMDD(initialData.start_date) : new Date().toISOString().split('T')[0],
      end_date: initialData?.end_date ? formatDateToYYYYMMDD(initialData.end_date) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      plan_type: initialData?.plan_type || 'prata_mensal',
      campaign_goal: initialData?.campaign_goal || 'awareness',
      available_visit_dates: initialData?.available_visit_dates || '',
      key_message: initialData?.key_message || '',
      content_type_suggestions: initialData?.content_type_suggestions ? (Array.isArray(initialData.content_type_suggestions) ? initialData.content_type_suggestions.join(', ') : initialData.content_type_suggestions) : 'Reels, Stories',
      tone_of_voice: initialData?.tone_of_voice || '',
      influencer_post_deadline: initialData?.influencer_post_deadline ? formatDateToYYYYMMDD(initialData.influencer_post_deadline) : new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      restaurant_selection_deadline: initialData?.restaurant_selection_deadline ? formatDateToYYYYMMDD(initialData.restaurant_selection_deadline) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      hashtags: initialData?.requirements?.hashtags ? initialData.requirements.hashtags.join(', ') : '#gastronomia, #foodie',
      mentions: initialData?.requirements?.mentions ? initialData.requirements.mentions.join(', ') : '@restaurante',
      color: initialData?.color || 'bg-green-100',
      textColor: initialData?.textColor || 'text-green-800',
      progressColor: initialData?.progressColor || 'bg-green-500',
      status: initialData?.status || 'draft',
      briefing: initialData?.briefing || '',
      influencer_count_target: initialData?.influencer_count_target || 1,
    }
  });

  useEffect(() => {
    const fetchNegocios = async () => {
      try {
        const { data, error } = await supabase
          .from('restaurant_profiles')
          .select('id, business_name')
          .order('business_name');

        if (error) throw error;
        setNegocios(data as Negocio[] || []);
        console.log('Negócios fetched:', data);

        // If editing, ensure the current negócio is in the list
        if (isEditing && initialData?.restaurant_id) {
          const currentNegocio = data?.find(r => r.id === initialData.restaurant_id);
          if (currentNegocio) {
            setValue('restaurant_id', currentNegocio.id);
          } else {
            console.warn('Current negócio not found in the list');
          }
        }
      } catch (error: any) {
        console.error('Erro ao buscar restaurantes:', error);
        toast.error('Não foi possível carregar a lista de restaurantes');
      }
    };
    fetchNegocios();
  }, [isEditing, initialData?.restaurant_id, setValue]); // Added initialData.restaurant_id to dependency array

  // Effect to specifically re-set restaurant_id if initialData changes after mount
  useEffect(() => {
    if (isEditing && initialData?.restaurant_id && restaurants.length > 0) {
      const currentRestaurant = restaurants.find(r => r.id === initialData.restaurant_id);
      if (currentRestaurant) {
        setValue('restaurant_id', currentRestaurant.id);
        console.log(`Re-setting restaurant_id in separate effect: ${currentRestaurant.id}`);
      } else if (!restaurants.find(r => r.id === initialData.restaurant_id)) {
         // This case handles if the initial restaurant_id is not in the fetched list
        console.warn(`Initial restaurant_id ${initialData.restaurant_id} not found in fetched restaurants. Clearing selection.`);
        // Optionally, clear the selection or add a placeholder
        // setValue('restaurant_id', ''); // Or handle as an error
      }
    }
  }, [isEditing, initialData?.restaurant_id, restaurants, setValue]);

  const onSubmit = async (formData: CampaignFormData) => {
    try {
      setLoading(true);
      console.log('Dados do formulário:', formData);
      let campaignPayload: any = {};
      
      console.log('Raw form data from react-hook-form:', formData);
      console.log('Form Data - restaurant_id (before payload):', formData.restaurant_id);
      console.log('Form Data - start_date (before payload):', formData.start_date);


      // Validation checks
      if (!formData.start_date) {
        throw new Error('Data de início da campanha é obrigatória');
      }
      if (!formData.end_date) {
        throw new Error('Data de término da campanha é obrigatória');
      }
      if (!formData.restaurant_id) {
        throw new Error('Seleção de restaurante é obrigatória');
      }

      // Transform formData to match the expected database schema / API payload
      campaignPayload = {
        name: formData.name,
        description: formData.description,
        restaurant_id: formData.restaurant_id,
        start_date: formData.start_date, // This will be YYYY-MM-DD, Supabase handles conversion to timestamp
        end_date: formData.end_date,     // This will be YYYY-MM-DD, Supabase handles conversion to timestamp
        status: formData.status,
        plan_type: formData.plan_type,
        campaign_goal: formData.campaign_goal || 'awareness',
        available_visit_dates: formData.available_visit_dates || '',
        key_message: formData.key_message,
        content_type_suggestions: formData.content_type_suggestions.split(',').map(s => s.trim()).filter(s => s),
        tone_of_voice: formData.tone_of_voice,
        influencer_post_deadline: formData.influencer_post_deadline, // This is YYYY-MM-DD, suitable for DB 'date' type
        restaurant_selection_deadline: formData.restaurant_selection_deadline, // This is YYYY-MM-DD, suitable for DB 'date' type
        requirements: {
          hashtags: formData.hashtags.split(',').map(tag => tag.trim()).filter(t => t),
          mentions: formData.mentions.split(',').map(mention => mention.trim()).filter(m => m)
        },
        color: formData.color,
        text_color: formData.textColor,
        progress_color: formData.progressColor,
        briefing: formData.briefing || 'Default briefing',
        influencer_count_target: formData.influencer_count_target,
      };

      if (isEditing && initialData?.id) {
        campaignPayload.updated_at = new Date().toISOString();
      } else {
        campaignPayload.created_at = new Date().toISOString();
      }

      console.log('Payload to be sent to API:', campaignPayload);
      console.log('Payload - restaurant_id:', campaignPayload.restaurant_id);
      console.log('Payload - start_date:', campaignPayload.start_date);


      // Check if the restaurant exists
      const { data: restaurantCheck, error: restaurantError } = await supabase
        .from('restaurant_profiles')
        .select('id, business_name')
        .eq('id', campaignPayload.restaurant_id)
        .single();

      if (restaurantError || !restaurantCheck) {
        throw new Error(`Restaurante com ID ${campaignPayload.restaurant_id} não encontrado`);
      }

      console.log('Restaurant found:', restaurantCheck);

      let result;
      let error;

      if (isEditing && initialData?.id) {
        console.log('Atualizando campanha existente:', initialData.id);
        const { data: updateResult, error: updateError } = await supabase
          .from('campaigns')
          .update(campaignPayload)
          .eq('id', initialData.id)
          .select('*');
        
        result = updateResult;
        error = updateError;
      } else {
        console.log('Criando nova campanha');
        const { data: insertResult, error: insertError } = await supabase
          .from('campaigns')
          .insert(campaignPayload)
          .select('*');
        result = insertResult;
        error = insertError;
      }

      if (error) {
        console.error(isEditing ? 'Erro ao atualizar campanha:' : 'Erro ao criar campanha:', error);
        throw new Error(error.message || 'Erro desconhecido');
      }

      console.log('Operação bem-sucedida:', result);

      if (result && result.length > 0) {
        const campaign = result[0];
        console.log('Dados da campanha após operação:', campaign);
        console.log('Restaurant ID saved:', campaign.restaurant_id);
        toast.success(isEditing ? 'Campanha atualizada com sucesso!' : 'Campanha criada com sucesso!');
        onSuccess(campaign.id);
      } else {
        console.warn('Operação concluída, mas nenhum dado retornado do DB.');
        toast('Operação concluída, mas os dados não foram retornados. Por favor, verifique.'); // Changed from toast.warning
        onSuccess(initialData?.id || "unknown");
      }
    } catch (err: any) {
      console.error('Erro no onSubmit:', err);
      toast.error(`Erro: ${err.message || 'Erro desconhecido'}`);
    } finally {
      setLoading(false);
    }
  };

  const planOptions = [
    { value: 'prata_mensal', label: 'Prata Mensal (4 influenciadores)' },
    { value: 'gold_mensal', label: 'Gold Mensal (6 influenciadores)' },
    { value: 'silver_mensal', label: 'Silver Mensal (8 influenciadores)' }, // Assuming Silver is a typo for Diamond or a higher tier
    { value: 'prata_semestral', label: 'Prata Semestral (4 influenciadores/mês)' },
    { value: 'gold_semestral', label: 'Gold Semestral (6 influenciadores/mês)' },
    { value: 'silver_semestral', label: 'Silver Semestral (8 influenciadores/mês)' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold">Informações da Campanha</h2>
        <p className="text-gray-600 mt-1">
          {isEditing ? 'Atualize os campos da campanha' : 'Preencha todos os campos para criar uma nova campanha'}
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="p-6 space-y-6">
          {/* Seção: Informações Básicas */}
          <section>
            <h3 className="text-lg font-medium border-b pb-2 mb-4">Informações Básicas</h3>
            {/* Nome da Campanha, Restaurante, Descrição, Status (existing fields) */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Nome da Campanha *</label>
              <input id="name" type="text" {...register('name', { required: 'Nome é obrigatório' })} className="w-full input-class" placeholder="Ex: Festival de Inverno" />
              {errors.name && <p className="form-error">{errors.name.message}</p>}
            </div>
            <div>
              <label htmlFor="restaurant_id" className="block text-sm font-medium text-gray-700 mb-1">Negócio *</label>
              <select id="restaurant_id" {...register('restaurant_id', { required: 'Negócio é obrigatório' })} className="w-full input-class">
                <option value="">Selecione um negócio</option>
                {negocios.map(r => <option key={r.id} value={r.id}>{r.business_name}</option>)}
              </select>
              {errors.restaurant_id && <p className="form-error">{errors.restaurant_id.message}</p>}
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Descrição *</label>
              <textarea id="description" {...register('description', { required: 'Descrição é obrigatória' })} rows={3} className="w-full input-class" placeholder="Descreva os objetivos e detalhes da campanha." />
              {errors.description && <p className="form-error">{errors.description.message}</p>}
            </div>
             <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select id="status" {...register('status')} className="w-full input-class">
                <option value="draft">Rascunho</option>
                <option value="active">Ativa</option>
                <option value="completed">Concluída</option>
                <option value="cancelled">Cancelada</option>
              </select>
            </div>
          </section>

          {/* Seção: Objetivos e Planejamento da Campanha */}
          <section>
            <h3 className="text-lg font-medium border-b pb-2 mb-4">Objetivos e Planejamento</h3>
            <div>
              <label htmlFor="campaign_goal" className="block text-sm font-medium text-gray-700 mb-1">Objetivo Principal da Campanha *</label>
              <select
                id="campaign_goal"
                {...register('campaign_goal', {
                  required: 'Objetivo é obrigatório'
                })}
                className="w-full input-class"
              >
                <option value="">Selecione um objetivo</option>
                <option value="awareness">Awareness (Conscientização da marca)</option>
                <option value="engagement">Engagement (Engajamento com o público)</option>
                <option value="conversion">Conversion (Conversão de vendas)</option>
                <option value="content">Content (Geração de conteúdo)</option>
              </select>
              {errors.campaign_goal && <p className="form-error">{errors.campaign_goal.message}</p>}
            </div>
            <div>
              <label htmlFor="plan_type" className="block text-sm font-medium text-gray-700 mb-1">Plano Contratado *</label>
              <select id="plan_type" {...register('plan_type', { required: 'Plano é obrigatório' })} className="w-full input-class">
                {planOptions.map(plan => <option key={plan.value} value={plan.value}>{plan.label}</option>)}
              </select>
              {errors.plan_type && <p className="form-error">{errors.plan_type.message}</p>}
            </div>
          </section>

          {/* Seção: Briefing e Datas */}
          <section>
            <h3 className="text-lg font-medium border-b pb-2 mb-4">Briefing e Datas Importantes</h3>
            <div>
              <label htmlFor="briefing" className="block text-sm font-medium text-gray-700 mb-1">Briefing *</label>
              <textarea id="briefing" {...register('briefing', { required: 'Briefing é obrigatório' })} rows={4} className="w-full input-class" placeholder="Detalhes sobre a campanha, objetivos, público-alvo, etc." />
              {errors.briefing && <p className="form-error">{errors.briefing.message}</p>}
            </div>
            <div>
              <label htmlFor="key_message" className="block text-sm font-medium text-gray-700 mb-1">Mensagem-Chave *</label>
              <textarea id="key_message" {...register('key_message', { required: 'Mensagem-chave é obrigatória' })} rows={2} className="w-full input-class" placeholder="A principal mensagem que os influenciadores devem transmitir." />
              {errors.key_message && <p className="form-error">{errors.key_message.message}</p>}
            </div>
            <div>
              <label htmlFor="content_type_suggestions" className="block text-sm font-medium text-gray-700 mb-1">Tipos de Conteúdo Sugeridos (separados por vírgula)</label>
              <input id="content_type_suggestions" type="text" {...register('content_type_suggestions')} className="w-full input-class" placeholder="Ex: Reels, Stories, Post Carrossel" />
            </div>
            <div>
              <label htmlFor="tone_of_voice" className="block text-sm font-medium text-gray-700 mb-1">Tom de Voz</label>
              <input id="tone_of_voice" type="text" {...register('tone_of_voice')} className="w-full input-class" placeholder="Ex: Divertido e informal, Sofisticado e elegante" />
            </div>
            <div>
              <label htmlFor="influencer_count_target" className="block text-sm font-medium text-gray-700 mb-1">Número Alvo de Influenciadores *</label>
              <input
                id="influencer_count_target"
                type="number"
                {...register('influencer_count_target', {
                  required: 'Número alvo de influenciadores é obrigatório',
                  min: { value: 1, message: 'O número deve ser pelo menos 1' }
                })}
                className="w-full input-class"
                placeholder="Ex: 5"
              />
              {errors.influencer_count_target && <p className="form-error">{errors.influencer_count_target.message}</p>}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">Data de Início da Campanha *</label>
                <input id="start_date" type="date" {...register('start_date', { required: 'Data de início é obrigatória' })} className="w-full input-class" />
                {errors.start_date && <p className="form-error">{errors.start_date.message}</p>}
              </div>
              <div>
                <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-1">Data de Término da Campanha *</label>
                <input id="end_date" type="date" {...register('end_date', { required: 'Data de término é obrigatória' })} className="w-full input-class" />
                {errors.end_date && <p className="form-error">{errors.end_date.message}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="restaurant_selection_deadline" className="block text-sm font-medium text-gray-700 mb-1">Prazo Seleção de Influenciadores *</label>
                <input id="restaurant_selection_deadline" type="date" {...register('restaurant_selection_deadline', { required: 'Prazo é obrigatório' })} className="w-full input-class" />
                {errors.restaurant_selection_deadline && <p className="form-error">{errors.restaurant_selection_deadline.message}</p>}
              </div>
              <div>
                <label htmlFor="influencer_post_deadline" className="block text-sm font-medium text-gray-700 mb-1">Prazo Postagem dos Influenciadores *</label>
                <input id="influencer_post_deadline" type="date" {...register('influencer_post_deadline', { required: 'Prazo é obrigatório' })} className="w-full input-class" />
                {errors.influencer_post_deadline && <p className="form-error">{errors.influencer_post_deadline.message}</p>}
              </div>
            </div>
             <div>
              <label htmlFor="available_visit_dates" className="block text-sm font-medium text-gray-700 mb-1">Datas/Horários Disponíveis para Visita (texto livre)</label>
              <textarea id="available_visit_dates" {...register('available_visit_dates')} rows={3} className="w-full input-class" placeholder="Ex: Terças e Quintas das 18h às 22h, Sábados das 12h às 16h" />
            </div>
          </section>

          {/* Seção: Requisitos de Conteúdo (Hashtags e Menções) */}
          <section>
            <h3 className="text-lg font-medium border-b pb-2 mb-4">Requisitos de Conteúdo</h3>
            <div>
              <label htmlFor="hashtags" className="block text-sm font-medium text-gray-700 mb-1">Hashtags (separadas por vírgula) *</label>
              <input id="hashtags" type="text" {...register('hashtags', { required: 'Hashtags são obrigatórias' })} className="w-full input-class" placeholder="Ex: #gastronomia, #foodie" />
              {errors.hashtags && <p className="form-error">{errors.hashtags.message}</p>}
            </div>
            <div>
              <label htmlFor="mentions" className="block text-sm font-medium text-gray-700 mb-1">Menções (separadas por vírgula) *</label>
              <input id="mentions" type="text" {...register('mentions', { required: 'Menções são obrigatórias' })} className="w-full input-class" placeholder="Ex: @restaurante, @chefparceiro" />
              {errors.mentions && <p className="form-error">{errors.mentions.message}</p>}
            </div>
          </section>

          {/* Estilo da Campanha (existing fields) */}
          <section>
            <h3 className="text-lg font-medium border-b pb-2 mb-4">Estilo da Campanha (Visual)</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-1">Cor de Fundo</label>
                <select id="color" {...register('color')} className="w-full input-class">
                  <option value="bg-green-100">Verde</option>
                  <option value="bg-blue-100">Azul</option>
                  {/* Add other color options */}
                </select>
              </div>
              <div>
                <label htmlFor="textColor" className="block text-sm font-medium text-gray-700 mb-1">Cor do Texto</label>
                <select id="textColor" {...register('textColor')} className="w-full input-class">
                  <option value="text-green-800">Verde</option>
                  <option value="text-blue-800">Azul</option>
                  {/* Add other color options */}
                </select>
              </div>
              <div>
                <label htmlFor="progressColor" className="block text-sm font-medium text-gray-700 mb-1">Cor da Barra de Progresso</label>
                <select id="progressColor" {...register('progressColor')} className="w-full input-class">
                  <option value="bg-green-500">Verde</option>
                  <option value="bg-blue-500">Azul</option>
                  {/* Add other color options */}
                </select>
              </div>
            </div>
          </section>
        </div>

        <div className="px-6 py-4 bg-gray-50 flex items-center justify-end">
          <div className="flex space-x-3">
            {onCancel && (
              <SecondaryActionButton type="button" onClick={onCancel} disabled={loading} icon={<FaTimes />}>
                Cancelar
              </SecondaryActionButton>
            )}
            <button type="submit" className="btn-primary" disabled={loading}>
              <FaSave className="mr-2" />{loading ? 'Salvando...' : (isEditing ? 'Salvar Alterações' : 'Criar Campanha')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CampaignAdminForm;
