"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import CriadorAdminForm from '@/components/admin/CriadorAdminForm';
import { toast } from 'react-hot-toast';

interface CriadorEditFormProps {
  initialData: any;
  criadorId: string;
}

export default function CriadorEditForm({ initialData, criadorId }: CriadorEditFormProps) {
  const router = useRouter();
  // const [loading, setLoading] = useState(false); // Removido

  const handleCancel = () => {
    router.push(`/admin/criadors/${criadorId}`);
  };

  const handleSuccess = (criadorId: string) => {
    toast.success('Influenciador atualizado com sucesso!');
    router.push(`/admin/criadors/${criadorId}`);
  };

  return (
    <CriadorAdminForm
      initialData={initialData}
      onCancel={handleCancel}
      onSuccess={handleSuccess}
      isEditing={true}
    />
  );
}
