"use client";

import { useState } from 'react';

interface HelpContentProps {
  onClose?: () => void;
}

interface HelpArticle {
  id: string;
  title: string;
  category: string;
  content: string;
}

export default function HelpContent({ onClose }: HelpContentProps) {
  const [activeCategory, setActiveCategory] = useState<string>('getting-started');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);

  // Dados de exemplo para artigos de ajuda
  const helpArticles: HelpArticle[] = [
    {
      id: 'article1',
      title: 'Como começar a usar a plataforma',
      category: 'getting-started',
      content: `
        <h2>Como começar a usar a plataforma</h2>

        <p>Bem-vindo à crIAdores! Esta é uma plataforma que conecta restaurantes e influenciadores para criar campanhas de marketing eficazes. Aqui está um guia rápido para começar:</p>

        <h3>1. Complete seu perfil</h3>
        <p>Antes de começar a participar de campanhas, é importante completar seu perfil com todas as informações necessárias. Isso inclui:</p>
        <ul>
          <li>Informações pessoais</li>
          <li>Detalhes profissionais</li>
          <li>Conectar suas redes sociais</li>
        </ul>

        <h3>2. Explore campanhas disponíveis</h3>
        <p>Na seção "Campanhas" do seu dashboard, você encontrará todas as campanhas disponíveis para participação. Você pode filtrar por categoria, data e valor da recompensa.</p>

        <h3>3. Candidate-se a campanhas</h3>
        <p>Quando encontrar uma campanha que corresponda ao seu perfil, clique em "Ver detalhes" e depois em "Candidatar-se". O restaurante irá revisar sua candidatura e entrar em contato se você for selecionado.</p>

        <h3>4. Crie conteúdo e acompanhe resultados</h3>
        <p>Após ser selecionado para uma campanha, crie o conteúdo conforme as diretrizes e publique em suas redes sociais. Você pode acompanhar o desempenho do seu conteúdo diretamente na plataforma.</p>
      `
    },
    {
      id: 'article2',
      title: 'Como conectar sua conta do Instagram',
      category: 'getting-started',
      content: `
        <h2>Como conectar sua conta do Instagram</h2>

        <p>Para participar de campanhas na crIAdores, é necessário conectar sua conta do Instagram. Siga os passos abaixo:</p>

        <h3>1. Acesse as configurações do perfil</h3>
        <p>No seu dashboard, clique em "Configurações" e depois na aba "Redes Sociais".</p>

        <h3>2. Clique em "Conectar Instagram"</h3>
        <p>Você será redirecionado para a página de autorização do Instagram.</p>

        <h3>3. Autorize a crIAdores</h3>
        <p>Faça login na sua conta do Instagram e autorize a crIAdores a acessar suas informações básicas e posts.</p>

        <h3>4. Verifique a conexão</h3>
        <p>Após autorizar, você será redirecionado de volta à plataforma. Verifique se a conexão foi bem-sucedida na seção "Redes Sociais" das configurações.</p>

        <h3>Problemas comuns</h3>
        <ul>
          <li><strong>Erro de autorização:</strong> Certifique-se de que você está logado na conta correta do Instagram.</li>
          <li><strong>Conexão não aparece:</strong> Tente atualizar a página ou desconectar e conectar novamente.</li>
          <li><strong>Permissões insuficientes:</strong> Certifique-se de aceitar todas as permissões solicitadas.</li>
        </ul>
      `
    },
    {
      id: 'article3',
      title: 'Como participar de campanhas',
      category: 'campaigns',
      content: `
        <h2>Como participar de campanhas</h2>

        <p>Participar de campanhas na crIAdores é simples. Siga este guia para maximizar suas chances de ser selecionado:</p>

        <h3>1. Encontre campanhas compatíveis</h3>
        <p>No seu dashboard, acesse a seção "Campanhas" para ver todas as oportunidades disponíveis. Você pode filtrar por:</p>
        <ul>
          <li>Categoria (gastronomia, lifestyle, etc.)</li>
          <li>Localização</li>
          <li>Valor da recompensa</li>
          <li>Data de início e término</li>
        </ul>

        <h3>2. Verifique os requisitos</h3>
        <p>Cada campanha tem requisitos específicos, como:</p>
        <ul>
          <li>Número mínimo de seguidores</li>
          <li>Taxa de engajamento mínima</li>
          <li>Tipo de conteúdo a ser produzido</li>
          <li>Hashtags e menções obrigatórias</li>
        </ul>

        <h3>3. Candidate-se à campanha</h3>
        <p>Se você atender aos requisitos, clique em "Ver detalhes" e depois em "Candidatar-se". Você pode adicionar uma mensagem personalizada explicando por que seria uma boa escolha para a campanha.</p>

        <h3>4. Aguarde a aprovação</h3>
        <p>O restaurante irá revisar sua candidatura e perfil. Você receberá uma notificação se for selecionado.</p>

        <h3>5. Crie e publique o conteúdo</h3>
        <p>Após ser selecionado, siga as diretrizes da campanha para criar o conteúdo. Certifique-se de incluir todas as hashtags e menções obrigatórias.</p>

        <h3>6. Sincronize seus posts</h3>
        <p>Após publicar o conteúdo, sincronize seus posts na plataforma para que o restaurante possa verificar e aprovar.</p>
      `
    },
    {
      id: 'article4',
      title: 'Como receber pagamentos',
      category: 'payments',
      content: `
        <h2>Como receber pagamentos</h2>

        <p>A crIAdores oferece um sistema seguro e transparente para receber pagamentos por suas campanhas. Confira como funciona:</p>

        <h3>1. Configure seu método de pagamento</h3>
        <p>Antes de participar de campanhas, configure seu método de pagamento preferido:</p>
        <ul>
          <li>Transferência bancária</li>
          <li>PIX</li>
          <li>PayPal</li>
        </ul>
        <p>Acesse "Configurações" > "Faturamento" para adicionar suas informações de pagamento.</p>

        <h3>2. Complete as campanhas</h3>
        <p>Para receber o pagamento, você precisa completar todos os requisitos da campanha, incluindo:</p>
        <ul>
          <li>Publicar o conteúdo conforme as diretrizes</li>
          <li>Manter o conteúdo publicado pelo período especificado</li>
          <li>Sincronizar os posts na plataforma</li>
        </ul>

        <h3>3. Aprovação do conteúdo</h3>
        <p>O restaurante irá revisar seu conteúdo para garantir que atende a todos os requisitos. Após a aprovação, o pagamento será liberado.</p>

        <h3>4. Processamento do pagamento</h3>
        <p>Os pagamentos são processados em até 15 dias após a aprovação do conteúdo. Você receberá uma notificação quando o pagamento for enviado.</p>

        <h3>5. Histórico de pagamentos</h3>
        <p>Você pode acompanhar todos os seus pagamentos na seção "Faturamento" > "Histórico de pagamentos".</p>

        <h3>Problemas comuns</h3>
        <ul>
          <li><strong>Atraso no pagamento:</strong> Verifique se todos os requisitos da campanha foram cumpridos.</li>
          <li><strong>Pagamento não recebido:</strong> Confirme se suas informações bancárias estão corretas.</li>
          <li><strong>Valor incorreto:</strong> Entre em contato com o suporte se o valor recebido não corresponder ao acordado.</li>
        </ul>
      `
    },
    {
      id: 'article5',
      title: 'Como analisar seu desempenho',
      category: 'analytics',
      content: `
        <h2>Como analisar seu desempenho</h2>

        <p>A ConnectCity oferece ferramentas avançadas de análise para ajudar você a entender e melhorar seu desempenho nas campanhas.</p>

        <h3>1. Dashboard de desempenho</h3>
        <p>No seu dashboard principal, você encontrará um resumo do seu desempenho, incluindo:</p>
        <ul>
          <li>Número de campanhas concluídas</li>
          <li>Taxa de aprovação</li>
          <li>Ganhos totais</li>
          <li>Engajamento médio</li>
        </ul>

        <h3>2. Análise por campanha</h3>
        <p>Para cada campanha, você pode acessar métricas detalhadas:</p>
        <ul>
          <li>Alcance total</li>
          <li>Engajamento (curtidas, comentários, compartilhamentos)</li>
          <li>Taxa de cliques (se aplicável)</li>
          <li>Comparação com outras campanhas</li>
        </ul>

        <h3>3. Relatórios de crescimento</h3>
        <p>Acompanhe seu crescimento ao longo do tempo com relatórios que mostram:</p>
        <ul>
          <li>Crescimento de seguidores</li>
          <li>Evolução da taxa de engajamento</li>
          <li>Aumento nos ganhos</li>
          <li>Melhoria na qualidade do conteúdo</li>
        </ul>

        <h3>4. Comparação com outros influenciadores</h3>
        <p>Veja como você se compara com outros influenciadores na sua categoria:</p>
        <ul>
          <li>Ranking de desempenho</li>
          <li>Benchmarks de engajamento</li>
          <li>Tendências de conteúdo</li>
        </ul>

        <h3>5. Exportação de dados</h3>
        <p>Você pode exportar seus dados de desempenho em formatos CSV ou PDF para análise externa ou apresentação para potenciais parceiros.</p>
      `
    }
  ];

  // Categorias de ajuda
  const helpCategories = [
    { id: 'getting-started', name: 'Primeiros passos' },
    { id: 'campaigns', name: 'Campanhas' },
    { id: 'payments', name: 'Pagamentos' },
    { id: 'analytics', name: 'Análises' },
    { id: 'account', name: 'Conta' }
  ];

  // Filtrar artigos por categoria e pesquisa
  const filteredArticles = helpArticles.filter(article => {
    const matchesCategory = activeCategory === 'all' || article.category === activeCategory;
    const matchesSearch = searchQuery === '' ||
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.content.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  return (
    <div className="w-[850px] h-[650px] bg-[#f7f7f7] rounded-2xl shadow-2xl flex flex-col overflow-hidden">
      <div className="bg-[#f0f0f0] border-b border-gray-300">
        <div className="px-6 pt-4 pb-2 flex items-center">
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
            <h2 className="text-lg font-bold">Central de Ajuda</h2>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 bg-white border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <div className="relative mb-4">
              <input
                type="text"
                placeholder="Pesquisar..."
                className="w-full px-3 py-2 pl-9 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute left-2 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            <div className="mb-4">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Categorias</h3>
              <div className="space-y-1">
                <button
                  className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                    activeCategory === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setActiveCategory('all')}
                >
                  Todas as categorias
                </button>

                {helpCategories.map((category) => (
                  <button
                    key={category.id}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                      activeCategory === category.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                    onClick={() => setActiveCategory(category.id)}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Artigos populares</h3>
              <div className="space-y-1">
                {helpArticles.slice(0, 3).map((article) => (
                  <button
                    key={article.id}
                    className="w-full text-left px-3 py-2 rounded-md text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => setSelectedArticle(article)}
                  >
                    {article.title}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {selectedArticle ? (
            <div>
              <button
                className="flex items-center text-blue-500 hover:text-blue-700 mb-4"
                onClick={() => setSelectedArticle(null)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                Voltar
              </button>

              <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: selectedArticle.content }} />

              <div className="mt-8 border-t border-gray-200 pt-4">
                <p className="text-sm text-gray-500">Este artigo foi útil?</p>
                <div className="flex space-x-2 mt-2">
                  <button className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors">
                    Sim
                  </button>
                  <button className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors">
                    Não
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div>
              <h3 className="text-lg font-medium mb-4">
                {activeCategory === 'all'
                  ? 'Todos os artigos'
                  : helpCategories.find(cat => cat.id === activeCategory)?.name || 'Artigos'}
              </h3>

              {filteredArticles.length === 0 ? (
                <div className="text-center py-8">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-500">Nenhum artigo encontrado.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Tente uma pesquisa diferente ou selecione outra categoria.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredArticles.map((article) => (
                    <div
                      key={article.id}
                      className="bg-white rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => setSelectedArticle(article)}
                    >
                      <h4 className="font-medium mb-2">{article.title}</h4>
                      <p className="text-sm text-gray-500 mb-3">
                        {article.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                      </p>
                      <span className="text-blue-500 text-sm hover:text-blue-700">Ler mais</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="bg-[#f0f0f0] border-t border-gray-300 p-4">
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-500">Não encontrou o que procurava?</p>
          <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
            Contatar suporte
          </button>
        </div>
      </div>
    </div>
  );
}
