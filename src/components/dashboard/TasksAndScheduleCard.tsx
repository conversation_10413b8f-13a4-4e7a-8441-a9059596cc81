'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Loader2, CheckCircle2, Clock, Calendar, Video, MapPin, Upload, Camera } from 'lucide-react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import EmptyState from '@/components/ui/EmptyState'
import { TaskStatus, TaskPriority, TaskType } from '@/lib/services/tasks'
import { ScheduleStatus } from '@/lib/services/scheduling'
import { format, parseISO, isAfter, startOfToday, addDays } from 'date-fns'
import { ptBR } from 'date-fns/locale'

interface TasksAndScheduleCardProps {
  userId?: string
  className?: string
  compact?: boolean
}

interface Task {
  id: string
  user_id: string
  campaign_id?: string
  title: string
  description?: string
  task_type: TaskType
  status: TaskStatus
  priority: TaskPriority
  due_date?: string
  completed_at?: string
  created_at: string
  updated_at: string
  campaign?: any
}

interface Schedule {
  id: string
  campaign_id: string
  influencer_id: string
  scheduled_date: string
  status: ScheduleStatus
  notes?: string
  created_at: string
  updated_at: string
  completed_at?: string
  cancelled_at?: string
  cancellation_reason?: string
  campaign?: any
  influencer?: any
}

// Tipo combinado para exibição na lista
interface ActionItem {
  id: string
  type: 'task' | 'schedule'
  title: string
  date: string
  restaurant?: string
  actionType: string
  status: string
  originalData: Task | Schedule
}

export default function TasksAndScheduleCard({ userId, className, compact = false }: TasksAndScheduleCardProps) {
  const supabase = createClientComponentClient()
  const [loading, setLoading] = useState(true)
  const [tasks, setTasks] = useState<Task[]>([])
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [actionItems, setActionItems] = useState<ActionItem[]>([])

  useEffect(() => {
    fetchData()
  }, [userId])

  // Função para verificar e criar tarefas com base em campanhas ativas
  const checkAndCreateTasksFromCampaigns = async () => {
    try {
      // Verificar se a tabela 'tasks' existe
      let tasksTableExists = true
      try {
        const { error: tableCheckError } = await supabase
          .from('tasks')
          .select('id')
          .limit(1)

        if (tableCheckError) {
          console.error('Tabela tasks pode não existir:', tableCheckError)
          tasksTableExists = false
          return
        }
      } catch (error) {
        console.error('Erro ao verificar tabela tasks:', error)
        tasksTableExists = false
        return
      }

      if (!tasksTableExists) {
        return
      }

      // Verificar se já existem tarefas para o usuário
      const { data: existingTasks, error: checkError } = await supabase
        .from('tasks')
        .select('id')
        .eq('user_id', userId)
        .limit(1)

      if (checkError) {
        console.error('Erro ao verificar tarefas existentes:', checkError)
        return
      }

      // Buscar campanhas ativas do usuário
      const { data: activeCampaigns, error: campaignsError } = await supabase
        .from('campaign_influencers')
        .select(`
          id,
          campaign_id,
          campaigns(
            id,
            name,
            restaurant_id,
            restaurants(
              id,
              name
            )
          )
        `)
        .eq('influencer_id', userId)
        .eq('status', 'accepted')

      if (campaignsError) {
        console.error('Erro ao buscar campanhas ativas:', campaignsError)
        return
      }

      // Se não houver tarefas mas existirem campanhas ativas, criar tarefas baseadas nas campanhas
      if ((!existingTasks || existingTasks.length === 0) && activeCampaigns && activeCampaigns.length > 0) {
        console.log('Criando tarefas baseadas em campanhas ativas para o usuário:', userId)

        for (const campaign of activeCampaigns) {
          // Verificar se já existe conteúdo para esta campanha
          let existingContent = null
          try {
            const { data, error: contentError } = await supabase
              .from('campaign_content')
              .select('id, status')
              .eq('campaign_influencer_id', campaign.id)
              .order('created_at', { ascending: false })
              .limit(1)

            if (contentError) {
              // Se a tabela não existir, apenas continue sem criar tarefas baseadas em conteúdo
              console.log('Tabela campaign_content não existe ou não está acessível, pulando verificação de conteúdo')
              existingContent = null
            } else {
              existingContent = data
            }
          } catch (error) {
            console.log('Erro ao acessar tabela campaign_content, pulando verificação de conteúdo:', error)
            existingContent = null
          }

          // Criar tarefa com base no status do conteúdo
          if (!existingContent || existingContent.length === 0) {
            // Não há conteúdo, criar tarefa para criar conteúdo
            await supabase
              .from('tasks')
              .insert({
                user_id: userId,
                campaign_id: campaign.campaign_id,
                title: `Criar conteúdo para ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                description: `Gravar vídeo para a campanha ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'} do restaurante ${campaign.campaigns?.[0]?.restaurants?.[0]?.name || 'Restaurante Desconhecido'}`,
                task_type: TaskType.CONTENT_CREATION,
                status: TaskStatus.PENDING,
                priority: TaskPriority.HIGH,
                due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 dias a partir de agora
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
          } else if (existingContent[0].status === 'approved') {
            // Conteúdo aprovado, criar tarefa para publicar
            await supabase
              .from('tasks')
              .insert({
                user_id: userId,
                campaign_id: campaign.campaign_id,
                title: `Publicar conteúdo de ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                description: `Publicar o conteúdo aprovado para a campanha ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                task_type: TaskType.CONTENT_PUBLICATION,
                status: TaskStatus.PENDING,
                priority: TaskPriority.HIGH,
                due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 dias a partir de agora
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
          } else if (existingContent[0].status === 'rejected') {
            // Conteúdo rejeitado, criar tarefa para revisar
            await supabase
              .from('tasks')
              .insert({
                user_id: userId,
                campaign_id: campaign.campaign_id,
                title: `Revisar conteúdo de ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                description: `Revisar e reenviar o conteúdo rejeitado para a campanha ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                task_type: TaskType.CONTENT_CREATION,
                status: TaskStatus.PENDING,
                priority: TaskPriority.URGENT,
                due_date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 dia a partir de agora
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
          }

          // Verificar se há agendamento para esta campanha
          let existingSchedule = null
          try {
            const { data, error: scheduleError } = await supabase
              .from('campaign_schedule')
              .select('id')
              .eq('campaign_id', campaign.campaign_id)
              .eq('influencer_id', userId)
              .limit(1)

            if (scheduleError) {
              // Se a tabela não existir, apenas continue sem criar tarefas baseadas em agendamento
              console.log('Tabela campaign_schedule não existe ou não está acessível, pulando verificação de agendamento')
              existingSchedule = null
            } else {
              existingSchedule = data
            }
          } catch (error) {
            console.log('Erro ao acessar tabela campaign_schedule, pulando verificação de agendamento:', error)
            existingSchedule = null
          }

          // Se não houver agendamento, criar tarefa para agendar visita
          if (!existingSchedule || existingSchedule.length === 0) {
            await supabase
              .from('tasks')
              .insert({
                user_id: userId,
                campaign_id: campaign.campaign_id,
                title: `Agendar visita para ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                description: `Agendar visita ao restaurante ${campaign.campaigns?.[0]?.restaurants?.[0]?.name || 'Restaurante Desconhecido'} para a campanha ${campaign.campaigns?.[0]?.name || 'Campanha Desconhecida'}`,
                task_type: TaskType.SCHEDULE_VISIT,
                status: TaskStatus.PENDING,
                priority: TaskPriority.MEDIUM,
                due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 dias a partir de agora
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
          }
        }

        console.log('Tarefas baseadas em campanhas criadas com sucesso')
      }
    } catch (error) {
      console.error('Erro ao criar tarefas baseadas em campanhas:', error)
    }
  }

  const fetchData = async () => {
    setLoading(true)

    try {
      // Se houver um userId, verificar se há dados baseados em campanhas
      if (userId) {
        await checkAndCreateTasksFromCampaigns()
      }

      await Promise.all([
        fetchTasks(),
        fetchSchedules()
      ])
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchTasks = async () => {
    try {
      // Verificar se a tabela 'tasks' existe
      let tableExists = true
      try {
        const { error: tableCheckError } = await supabase
          .from('tasks')
          .select('id')
          .limit(1)

        if (tableCheckError) {
          console.error('Erro ao verificar tabela tasks:', tableCheckError)
          tableExists = false
        }
      } catch (error) {
        console.error('Erro ao verificar tabela tasks:', error)
        tableExists = false
      }

      if (!tableExists) {
        // Se a tabela não existir, usar um array vazio
        setTasks([])
        updateActionItems([], schedules)
        return
      }

      // Usar o cliente Supabase diretamente
      let query = supabase
        .from('tasks')
        .select(`
          id,
          user_id,
          campaign_id,
          title,
          description,
          task_type,
          status,
          priority,
          due_date,
          completed_at,
          created_at,
          updated_at
        `) // Select specific fields from tasks table only

      // Adicionar filtro de status
      query = query.in('status', [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.OVERDUE])

      // Adicionar filtro de usuário se necessário
      if (userId) {
        query = query.eq('user_id', userId)
      }

      // Executar a consulta
      const { data, error } = await query.order('due_date', { ascending: true })

      if (error) {
        console.error('Erro na consulta de tarefas:', error)
        setTasks([])
        updateActionItems([], schedules)
        return
      }

      console.log('Tarefas carregadas com sucesso:', data?.length || 0)
      setTasks(data || [])

      // Atualizar a lista combinada
      updateActionItems(data || [], schedules)
    } catch (error: any) {
      console.error('Erro ao carregar tarefas:', error)
      // Em caso de erro, usar um array vazio
      setTasks([])
      updateActionItems([], schedules)
    }
  }

  const fetchSchedules = async () => {
    try {
      // Verificar se a tabela 'schedules' existe
      let tableExists = true
      try {
        const { error: tableCheckError } = await supabase
          .from('campaign_schedule')
          .select('id')
          .limit(1)

        if (tableCheckError) {
          console.error('Erro ao verificar tabela schedules:', tableCheckError)
          tableExists = false
        }
      } catch (error) {
        console.error('Erro ao verificar tabela schedules:', error)
        tableExists = false
      }

      if (!tableExists) {
        // Se a tabela não existir, usar um array vazio
        setSchedules([])
        updateActionItems(tasks, [])
        return
      }

      // Usar o cliente Supabase diretamente
      let query = supabase
        .from('campaign_schedule')
        .select(`
          id,
          campaign_id,
          influencer_id,
          scheduled_date,
          status,
          notes,
          created_at,
          updated_at,
          completed_at,
          cancelled_at,
          cancellation_reason
        `) // Select specific fields from campaign_schedule table only

      // Adicionar filtro de usuário se necessário
      if (userId) {
        query = query.eq('influencer_id', userId)
      }

      // Executar a consulta
      const { data, error } = await query.order('scheduled_date', { ascending: true })

      if (error) {
        console.error('Erro na consulta de agendamentos:', error)
        setSchedules([])
        updateActionItems(tasks, [])
        return
      }

      console.log('Agendamentos carregados com sucesso:', data?.length || 0)
      setSchedules(data || [])

      // Atualizar a lista combinada
      updateActionItems(tasks, data || [])
    } catch (error: any) {
      console.error('Erro ao carregar agendamentos:', error)
      // Em caso de erro, usar um array vazio
      setSchedules([])
      updateActionItems(tasks, [])
    }
  }

  // Combinar tarefas e agendamentos em uma única lista
  const updateActionItems = (tasksList: Task[], schedulesList: Schedule[]) => {
    const today = startOfToday()
    const nextMonth = addDays(today, 30)

    // Converter tarefas para o formato de exibição
    const taskItems: ActionItem[] = tasksList.map(task => {
      // Determinar o tipo de ação com base no tipo de tarefa
      let actionType = 'Tarefa'
      let icon = <CheckCircle2 className="h-4 w-4 mr-1" />

      switch (task.task_type) {
        case TaskType.CONTENT_CREATION:
          actionType = 'Criar conteúdo'
          icon = <Camera className="h-4 w-4 mr-1" />
          break
        case TaskType.CONTENT_APPROVAL:
          actionType = 'Aprovar conteúdo'
          icon = <CheckCircle2 className="h-4 w-4 mr-1" />
          break
        case TaskType.CONTENT_PUBLICATION:
          actionType = 'Publicar conteúdo'
          icon = <Upload className="h-4 w-4 mr-1" />
          break
        case TaskType.SCHEDULE_VISIT:
          actionType = 'Visitar restaurante'
          icon = <MapPin className="h-4 w-4 mr-1" />
          break
        case TaskType.ADMINISTRATIVE:
          actionType = 'Tarefa administrativa'
          icon = <CheckCircle2 className="h-4 w-4 mr-1" />
          break
        case TaskType.PAYMENT:
          actionType = 'Pagamento'
          icon = <CheckCircle2 className="h-4 w-4 mr-1" />
          break
        case TaskType.OTHER:
          actionType = 'Outra tarefa'
          icon = <CheckCircle2 className="h-4 w-4 mr-1" />
          break
      }

      return {
        id: task.id,
        type: 'task',
        title: task.title,
        date: task.due_date || '',
        restaurant: task.campaign_id ? `Campanha ID: ${task.campaign_id.substring(0,8)}` : 'Restaurante N/A', // Use campaign_id if available
        actionType,
        status: task.status,
        originalData: task,
        icon
      }
    })

    // Converter agendamentos para o formato de exibição
    const scheduleItems: ActionItem[] = schedulesList
      .filter(schedule => {
        const scheduleDate = parseISO(schedule.scheduled_date)
        return isAfter(scheduleDate, today) &&
               schedule.status !== ScheduleStatus.CANCELLED
      })
      .map(schedule => ({
        id: schedule.id,
        type: 'schedule',
        title: schedule.campaign_id ? `Visita Campanha ID: ${schedule.campaign_id.substring(0,8)}` : `Agendamento ID: ${schedule.id.substring(0,8)}`,
        date: schedule.scheduled_date,
        restaurant: schedule.campaign_id ? `Campanha ID: ${schedule.campaign_id.substring(0,8)}` : 'Restaurante N/A', // Use campaign_id if available
        actionType: 'Visita',
        status: schedule.status,
        originalData: schedule,
        icon: <MapPin className="h-4 w-4 mr-1" />
      }))

    // Combinar e ordenar por data
    const combined = [...taskItems, ...scheduleItems]
      .sort((a, b) => {
        if (!a.date) return 1
        if (!b.date) return -1
        return parseISO(a.date).getTime() - parseISO(b.date).getTime()
      })
      .slice(0, 5) // Mostrar apenas os 5 próximos itens

    setActionItems(combined)
  }

  const handleTaskComplete = async (taskId: string) => {
    try {
      // Usar o cliente Supabase diretamente
      const now = new Date().toISOString()

      const { error } = await supabase
        .from('tasks')
        .update({
          status: TaskStatus.COMPLETED,
          completed_at: now,
          updated_at: now
        })
        .eq('id', taskId)

      if (error) {
        throw error
      }

      // Atualizar lista de tarefas
      fetchData()
    } catch (error) {
      console.error('Erro ao concluir tarefa:', error)
    }
  }

  // Formatar data
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Sem prazo'
    return format(parseISO(dateString), "dd/MM", { locale: ptBR })
  }

  // Formatar data e hora
  const formatDateTime = (dateString: string) => {
    const date = parseISO(dateString)
    return format(date, "dd/MM 'às' HH:mm", { locale: ptBR })
  }

  // Renderizar ícone com base no tipo de ação
  const renderActionIcon = (item: ActionItem) => {
    if (item.type === 'task') {
      switch ((item.originalData as Task).task_type) {
        case TaskType.CONTENT_CREATION:
          return <Camera className="h-4 w-4 mr-1 text-blue-500" />
        case TaskType.CONTENT_APPROVAL:
          return <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
        case TaskType.CONTENT_PUBLICATION:
          return <Upload className="h-4 w-4 mr-1 text-indigo-500" />
        case TaskType.SCHEDULE_VISIT:
          return <MapPin className="h-4 w-4 mr-1 text-purple-500" />
        case TaskType.PAYMENT:
          return <CheckCircle2 className="h-4 w-4 mr-1 text-yellow-500" />
        case TaskType.ADMINISTRATIVE:
          return <CheckCircle2 className="h-4 w-4 mr-1 text-orange-500" />
        default:
          return <CheckCircle2 className="h-4 w-4 mr-1 text-gray-500" />
      }
    } else {
      return <MapPin className="h-4 w-4 mr-1 text-red-500" />
    }
  }

  // Se for modo compacto, renderizar apenas a lista de itens sem o card completo
  if (compact) {
    return (
      <div className={className}>
        {loading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
          </div>
        ) : actionItems.length === 0 ? (
          <div className="text-center py-3 text-sm text-gray-500">
            Sem ações pendentes
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {actionItems.slice(0, 3).map(item => (
              <div
                key={`${item.type}-${item.id}`}
                className="py-2.5 px-5 flex items-center justify-between"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center text-sm">
                    {renderActionIcon(item)}
                    <span className="truncate font-medium text-gray-700">{item.title}</span>
                  </div>
                  <div className="flex items-center mt-1 text-xs text-gray-500">
                    <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="truncate">
                      {item.type === 'task' ? formatDate(item.date) : formatDateTime(item.date)}
                      {item.restaurant && ` • ${item.restaurant}`}
                    </span>
                  </div>
                </div>
                {item.type === 'task' && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-7 px-2 text-xs ml-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    onClick={() => handleTaskComplete(item.id)}
                  >
                    Concluir
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Versão padrão com card completo
  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="bg-[#f5f5f5] pb-2">
        <CardTitle className="text-lg font-medium">
          <Clock className="h-5 w-5 inline-block mr-2 text-blue-500" />
          Próximas Ações
        </CardTitle>
      </CardHeader>

      <CardContent className="p-4">
        {loading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
          </div>
        ) : actionItems.length === 0 ? (
          <EmptyState
            title="Sem ações pendentes"
            description="Você não tem tarefas ou agendamentos próximos."
            icon={<Clock className="h-12 w-12" />}
            actionLabel="Atualizar"
            onAction={fetchData}
          />
        ) : (
          <div className="space-y-3">
            {actionItems.map(item => (
              <div
                key={`${item.type}-${item.id}`}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="font-medium">{item.title}</div>
                  <div className="flex flex-col sm:flex-row sm:items-center mt-1 text-sm text-gray-500">
                    <div className="flex items-center mr-3">
                      {renderActionIcon(item)}
                      {item.actionType}
                    </div>
                    <div className="flex items-center mr-3">
                      <Clock className="h-3 w-3 mr-1" />
                      {item.type === 'task'
                        ? formatDate(item.date)
                        : formatDateTime(item.date)
                      }
                    </div>
                    {item.restaurant && (
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {item.restaurant}
                      </div>
                    )}
                  </div>
                </div>
                {item.type === 'task' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTaskComplete(item.id)}
                  >
                    Concluir
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
