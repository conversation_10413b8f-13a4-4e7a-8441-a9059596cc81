'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'

export default function WhatsAppVerification() {
  const supabase = createClientComponentClient()
  const [phone, setPhone] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [isVerifying, setIsVerifying] = useState(false)
  const [isVerified, setIsVerified] = useState(false)
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  // Carregar dados do usuário
  useEffect(() => {
    async function loadUserData() {
      try {
        const { data: { session } } = await supabase.auth.getSession()

        if (session) {
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('phone, phone_verified')
            .eq('id', session.user.id)
            .single()

          if (error) throw error

          if (profile) {
            setPhone(profile.phone || '')
            setIsVerified(profile.phone_verified || false)
          }
        }
      } catch (error) {
        console.error('Erro ao carregar dados do usuário:', error)
      } finally {
        setInitialLoading(false)
      }
    }

    loadUserData()
  }, [supabase])

  // Iniciar verificação
  async function startVerification() {
    if (!phone) {
      toast({
        title: 'Erro',
        description: 'Por favor, informe seu número de telefone',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/v1/whatsapp/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ phone })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao iniciar verificação')
      }

      setIsVerifying(true)
      toast({
        title: 'Código enviado',
        description: 'Um código de verificação foi enviado para o seu WhatsApp'
      })
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível enviar o código',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Confirmar código
  async function confirmCode() {
    if (!verificationCode) {
      toast({
        title: 'Erro',
        description: 'Por favor, informe o código de verificação',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/v1/whatsapp/verify', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ phone, code: verificationCode })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Código inválido')
      }

      setIsVerified(true)
      setIsVerifying(false)
      toast({
        title: 'Número verificado',
        description: 'Seu número de WhatsApp foi verificado com sucesso'
      })
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error.message || 'Não foi possível verificar o código',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Alterar número
  function changeNumber() {
    setIsVerified(false)
    setPhone('')
    setVerificationCode('')
    setIsVerifying(false)
  }

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <p className="mb-6 text-gray-600 text-sm">
          Conecte seu WhatsApp para receber notificações importantes sobre campanhas,
          atualizações de ranking e pagamentos.
        </p>

        {isVerified ? (
          <div className="p-5 bg-[#f5f5f5] rounded-lg">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-[#f5f5f5] border border-gray-200 flex items-center justify-center mr-3">
                <CheckCircle className="w-5 h-5 text-gray-700" />
              </div>
              <div>
                <p className="text-gray-800 font-medium">WhatsApp Conectado</p>
                <p className="text-gray-600 text-sm">{phone}</p>
              </div>
            </div>
            <Button
              variant="outline"
              className="mt-4 bg-[#f5f5f5] hover:bg-[#f5f5f5] text-gray-700 border border-gray-200 rounded-lg"
              onClick={changeNumber}
            >
              Alterar número
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700">Seu número de WhatsApp</label>
              <div className="flex space-x-2">
                <Input
                  type="tel"
                  placeholder="Ex: +5511999999999"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="flex-1 bg-[#f5f5f5] border-none rounded-lg"
                  disabled={isVerifying || loading}
                />
                <Button
                  onClick={startVerification}
                  disabled={!phone || isVerifying || loading}
                  className="bg-gray-800 hover:bg-gray-700 text-white rounded-lg"
                >
                  {loading && !isVerifying ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  {loading && !isVerifying ? 'Enviando...' : 'Verificar'}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Informe seu número no formato internacional com o código do país
              </p>
            </div>

            {isVerifying && (
              <div className="p-5 bg-[#f5f5f5] rounded-lg">
                <div className="flex items-start mb-4">
                  <div className="w-8 h-8 rounded-full bg-[#f5f5f5] border border-gray-200 flex items-center justify-center mr-3 mt-0.5">
                    <AlertCircle className="w-4 h-4 text-gray-700" />
                  </div>
                  <div>
                    <p className="text-gray-800 font-medium">Código de verificação enviado</p>
                    <p className="text-gray-600 text-sm">
                      Enviamos um código para o seu WhatsApp. Por favor, informe o código abaixo:
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Input
                    type="text"
                    placeholder="Código de verificação"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="flex-1 bg-[#f5f5f5] border-none rounded-lg"
                    disabled={loading}
                  />
                  <Button
                    onClick={confirmCode}
                    disabled={!verificationCode || loading}
                    className="bg-gray-800 hover:bg-gray-700 text-white rounded-lg"
                  >
                    {loading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {loading ? 'Verificando...' : 'Confirmar'}
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
