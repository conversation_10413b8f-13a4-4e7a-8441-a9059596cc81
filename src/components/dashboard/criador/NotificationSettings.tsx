'use client'

import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'

interface NotificationSettingsProps {
  whatsappOnly?: boolean
}

export default function NotificationSettings({ whatsappOnly = false }: NotificationSettingsProps) {
  const supabase = createClientComponentClient()
  const [preferences, setPreferences] = useState({
    enable_notifications: true,
    whatsapp_enabled: true,
    whatsapp_campaign_invite: true,
    whatsapp_campaign_status_change: true,
    whatsapp_ranking_update: true,
    whatsapp_payment_status: true,
    whatsapp_post_approval: true,
    email_enabled: true,
    email_campaign_invite: true,
    email_campaign_status_change: true,
    email_ranking_update: true,
    email_payment_status: true,
    email_post_approval: true
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  
  // Carregar preferências do usuário
  useEffect(() => {
    async function loadPreferences() {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) return
        
        const response = await fetch('/api/v1/notifications/settings')
        
        if (!response.ok) {
          throw new Error('Erro ao carregar preferências')
        }
        
        const data = await response.json()
        
        // Mesclar com valores padrão
        setPreferences(prev => ({
          ...prev,
          ...data
        }))
      } catch (error) {
        console.error('Erro ao carregar preferências:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadPreferences()
  }, [supabase])
  
  // Salvar preferências
  async function savePreferences() {
    setSaving(true)
    
    try {
      const response = await fetch('/api/v1/notifications/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(preferences)
      })
      
      if (!response.ok) {
        throw new Error('Erro ao salvar preferências')
      }
      
      toast({
        title: 'Preferências salvas',
        description: 'Suas preferências de notificação foram atualizadas com sucesso'
      })
    } catch (error) {
      console.error('Erro ao salvar preferências:', error)
      toast({
        title: 'Erro',
        description: 'Não foi possível salvar suas preferências',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }
  
  // Atualizar preferência
  function updatePreference(key: string, value: boolean) {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }))
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      {!whatsappOnly && (
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="enable_notifications" className="text-base font-medium">
              Ativar notificações
            </Label>
            <p className="text-sm text-gray-500">
              Receba notificações sobre campanhas, atualizações e pagamentos
            </p>
          </div>
          <Switch
            id="enable_notifications"
            checked={preferences.enable_notifications}
            onCheckedChange={(checked) => updatePreference('enable_notifications', checked)}
          />
        </div>
      )}
      
      {preferences.enable_notifications && (
        <>
          {!whatsappOnly && (
            <div className="border-t pt-4 mt-4">
              <h4 className="font-medium mb-2">Canais de notificação</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_enabled" className="font-medium">
                      WhatsApp
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações via WhatsApp
                    </p>
                  </div>
                  <Switch
                    id="whatsapp_enabled"
                    checked={preferences.whatsapp_enabled}
                    onCheckedChange={(checked) => updatePreference('whatsapp_enabled', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_enabled" className="font-medium">
                      Email
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações via email
                    </p>
                  </div>
                  <Switch
                    id="email_enabled"
                    checked={preferences.email_enabled}
                    onCheckedChange={(checked) => updatePreference('email_enabled', checked)}
                  />
                </div>
              </div>
            </div>
          )}
          
          {/* Preferências de WhatsApp */}
          {(preferences.whatsapp_enabled || whatsappOnly) && (
            <div className={whatsappOnly ? '' : 'border-t pt-4 mt-4'}>
              <h4 className="font-medium mb-2">Notificações via WhatsApp</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_campaign_invite" className="font-medium">
                      Convites de campanha
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando for convidado para novas campanhas
                    </p>
                  </div>
                  <Switch
                    id="whatsapp_campaign_invite"
                    checked={preferences.whatsapp_campaign_invite}
                    onCheckedChange={(checked) => updatePreference('whatsapp_campaign_invite', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_campaign_status_change" className="font-medium">
                      Atualizações de campanha
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando o status de uma campanha mudar
                    </p>
                  </div>
                  <Switch
                    id="whatsapp_campaign_status_change"
                    checked={preferences.whatsapp_campaign_status_change}
                    onCheckedChange={(checked) => updatePreference('whatsapp_campaign_status_change', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_ranking_update" className="font-medium">
                      Atualizações de ranking
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando sua posição no ranking mudar
                    </p>
                  </div>
                  <Switch
                    id="whatsapp_ranking_update"
                    checked={preferences.whatsapp_ranking_update}
                    onCheckedChange={(checked) => updatePreference('whatsapp_ranking_update', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_payment_status" className="font-medium">
                      Pagamentos
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações sobre pagamentos
                    </p>
                  </div>
                  <Switch
                    id="whatsapp_payment_status"
                    checked={preferences.whatsapp_payment_status}
                    onCheckedChange={(checked) => updatePreference('whatsapp_payment_status', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="whatsapp_post_approval" className="font-medium">
                      Aprovação de posts
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando seus posts forem aprovados ou rejeitados
                    </p>
                  </div>
                  <Switch
                    id="whatsapp_post_approval"
                    checked={preferences.whatsapp_post_approval}
                    onCheckedChange={(checked) => updatePreference('whatsapp_post_approval', checked)}
                  />
                </div>
              </div>
            </div>
          )}
          
          {/* Preferências de Email */}
          {preferences.email_enabled && !whatsappOnly && (
            <div className="border-t pt-4 mt-4">
              <h4 className="font-medium mb-2">Notificações via Email</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_campaign_invite" className="font-medium">
                      Convites de campanha
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando for convidado para novas campanhas
                    </p>
                  </div>
                  <Switch
                    id="email_campaign_invite"
                    checked={preferences.email_campaign_invite}
                    onCheckedChange={(checked) => updatePreference('email_campaign_invite', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_campaign_status_change" className="font-medium">
                      Atualizações de campanha
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando o status de uma campanha mudar
                    </p>
                  </div>
                  <Switch
                    id="email_campaign_status_change"
                    checked={preferences.email_campaign_status_change}
                    onCheckedChange={(checked) => updatePreference('email_campaign_status_change', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_ranking_update" className="font-medium">
                      Atualizações de ranking
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando sua posição no ranking mudar
                    </p>
                  </div>
                  <Switch
                    id="email_ranking_update"
                    checked={preferences.email_ranking_update}
                    onCheckedChange={(checked) => updatePreference('email_ranking_update', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_payment_status" className="font-medium">
                      Pagamentos
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações sobre pagamentos
                    </p>
                  </div>
                  <Switch
                    id="email_payment_status"
                    checked={preferences.email_payment_status}
                    onCheckedChange={(checked) => updatePreference('email_payment_status', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_post_approval" className="font-medium">
                      Aprovação de posts
                    </Label>
                    <p className="text-sm text-gray-500">
                      Receba notificações quando seus posts forem aprovados ou rejeitados
                    </p>
                  </div>
                  <Switch
                    id="email_post_approval"
                    checked={preferences.email_post_approval}
                    onCheckedChange={(checked) => updatePreference('email_post_approval', checked)}
                  />
                </div>
              </div>
            </div>
          )}
        </>
      )}
      
      <div className="pt-4">
        <Button onClick={savePreferences} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Salvando...
            </>
          ) : (
            'Salvar preferências'
          )}
        </Button>
      </div>
    </div>
  )
}
