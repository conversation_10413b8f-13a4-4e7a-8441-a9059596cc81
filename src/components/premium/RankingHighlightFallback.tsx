"use client";

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/supabase';
import { FaTrophy, FaArrowUp, FaArrowDown, FaMinus, FaBookmark, FaHeart, FaComment, FaChartLine, FaUser } from 'react-icons/fa';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';
import RankingNotifications from './RankingNotifications';

interface RankingHighlightFallbackProps {
  campaignId: string;
  userId: string;
  userRole: 'influencer' | 'restaurant';
  className?: string;
}

export default function RankingHighlightFallback({
  campaignId,
  userId,
  userRole,
  className = ''
}: RankingHighlightFallbackProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [rankingData, setRankingData] = useState<any>(null);
  const [competitors, setCompetitors] = useState<any[]>([]);
  const [isUsingMockData, setIsUsingMockData] = useState(false);

  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    async function fetchRankingData() {
      try {
        setLoading(true);
        setError(null);

        console.log('RankingHighlightFallback - Iniciando busca de dados para:', { campaignId, userId, userRole });

        // Buscar dados diretamente das tabelas
        if (userRole === 'influencer') {
          // Para influenciadores, buscar dados básicos
          const { data: campaignInfluencers, error: ciError } = await supabase
            .from('campaign_influencers')
            .select(`
              id,
              influencer_id,
              campaign_id,
              status,
              created_at
            `)
            .eq('campaign_id', campaignId);

          if (ciError) throw ciError;

          // Buscar dados do influenciador atual
          const userInfluencer = campaignInfluencers?.find(ci => ci.influencer_id === userId);

          if (!userInfluencer) {
            // Se o usuário não está na campanha, retornar dados vazios em vez de erro
            console.log('Usuário não encontrado na campanha, retornando dados vazios');
            return {
              userRank: null,
              userPoints: 0,
              totalParticipants: campaignInfluencers?.length || 0,
              topInfluencers: [],
              userPreviousRank: null,
              userRankChange: 0
            };
          }

          // Buscar perfis dos influenciadores para obter nomes e usernames
          const { data: profiles, error: profilesError } = await supabase
            .from('profiles')
            .select('id, full_name, profile_data')
            .in('id', campaignInfluencers.map(ci => ci.influencer_id));

          if (profilesError) throw profilesError;

          // Criar dados para o influenciador atual
          const userProfile = profiles?.find(p => p.id === userId);
          const rankingData = {
            influencer_id: userId,
            rank: campaignInfluencers?.length || 1, // Último lugar por padrão para novos participantes
            previous_rank: null, // Sem ranking anterior para novos participantes
            total_influencers: campaignInfluencers?.length || 1,
            total_likes: 0, // Começar com 0 para novos participantes
            total_comments: 0,
            total_saves: 0,
            total_points: 0,
            engagement_rate: "0.0"
          };

          setRankingData(rankingData);

          // Criar lista de competidores com dados reais
          const competitors = campaignInfluencers.map((ci, index) => {
            const profile = profiles?.find(p => p.id === ci.influencer_id);
            const username = profile?.profile_data?.instagram_username || 'username';

            return {
              influencer_id: ci.influencer_id,
              influencer_name: profile?.full_name || `Influencer ${index + 1}`,
              username: username,
              rank: index + 1, // Usar índice como ranking temporário
              total_likes: 0, // Começar com 0 para novos participantes
              total_comments: 0,
              total_saves: 0,
              total_points: 0,
              engagement_rate: 0.0
            };
          });

          // Ordenar por ranking
          competitors.sort((a, b) => a.rank - b.rank);

          // Limitar a 5 competidores
          const topCompetitors = competitors.slice(0, 5);

          // Adicionar o influenciador atual se não estiver nos top 5
          const userIncluded = topCompetitors.some(c => c.influencer_id === userId);
          if (!userIncluded) {
            topCompetitors.push({
              influencer_id: userId,
              influencer_name: userProfile?.full_name || 'Você',
              username: userProfile?.profile_data?.instagram_username || 'seu_username',
              rank: rankingData.rank,
              total_likes: rankingData.total_likes,
              total_comments: rankingData.total_comments,
              total_saves: rankingData.total_saves,
              total_points: rankingData.total_points,
              engagement_rate: parseFloat(rankingData.engagement_rate)
            });
          }

          setCompetitors(topCompetitors);
        } else {
          // Para restaurantes, primeiro buscar todos os influenciadores aceitos na campanha
          console.log('Buscando influenciadores aceitos para a campanha:', campaignId);
          const { data: acceptedInfluencers, error: aiError } = await supabase
            .from('campaign_influencers')
            .select(`
              id,
              influencer_id,
              status
            `)
            .eq('campaign_id', campaignId)
            .eq('status', 'accepted');

          if (aiError) {
            console.error('Erro ao buscar influenciadores aceitos:', aiError);
            throw aiError;
          }

          console.log('Influenciadores aceitos encontrados:', acceptedInfluencers);

          // Se não houver influenciadores aceitos, mostrar mensagem apropriada
          if (!acceptedInfluencers || acceptedInfluencers.length === 0) {
            console.log('Nenhum influenciador aceito encontrado para esta campanha');
            // Vamos tentar buscar novamente sem filtrar por status para debug
            const { data: allInfluencers, error: allError } = await supabase
              .from('campaign_influencers')
              .select(`
                id,
                influencer_id,
                status
              `)
              .eq('campaign_id', campaignId);

            console.log('Todos os influenciadores da campanha (debug):', allInfluencers);

            if (allInfluencers && allInfluencers.length > 0) {
              // Se existem influenciadores mas nenhum com status 'accepted', vamos usar todos
              console.log('Usando todos os influenciadores disponíveis como fallback');
              // Continuar com todos os influenciadores disponíveis
              setError(null);
            } else {
              setError('Nenhum influenciador aceito encontrado para esta campanha');
              setLoading(false);
              return;
            }
          }

          // Buscar dados de métricas para os influenciadores aceitos
          console.log('Buscando métricas para os influenciadores aceitos');
          let influencerIds = acceptedInfluencers ? acceptedInfluencers.map(ai => ai.influencer_id) : [];

          // Se não temos influenciadores aceitos, buscar todos os influenciadores da campanha
          if (influencerIds.length === 0) {
            const { data: allInfluencers } = await supabase
              .from('campaign_influencers')
              .select('influencer_id')
              .eq('campaign_id', campaignId);

            if (allInfluencers && allInfluencers.length > 0) {
              influencerIds = allInfluencers.map(ai => ai.influencer_id);
              console.log('Usando todos os influenciadores disponíveis:', influencerIds);
            }
          }

          const { data: campaignInfluencers, error: ciError } = await supabase
            .from('campaign_influencers')
            .select(`
              id,
              influencer_id,
              campaign_id,
              status,
              created_at
            `)
            .eq('campaign_id', campaignId)
            .in('influencer_id', influencerIds)
            .order('created_at', { ascending: true });

          if (ciError) {
            console.error('Erro ao buscar métricas dos influenciadores:', ciError);
            throw ciError;
          }

          console.log('Dados de métricas dos influenciadores:', campaignInfluencers);

          if (!campaignInfluencers || campaignInfluencers.length === 0) {
            // Se não houver dados reais, criar dados de exemplo
            console.warn('Nenhum dado real de métricas encontrado para os influenciadores. Usando dados de exemplo.');
            setIsUsingMockData(true);

            // Buscar perfis dos influenciadores para usar nomes reais
            // Usar todos os influenciadores disponíveis, não apenas os aceitos
            const { data: allInfluencers } = await supabase
              .from('campaign_influencers')
              .select('influencer_id')
              .eq('campaign_id', campaignId);

            const influencerIds = allInfluencers?.map(ai => ai.influencer_id) || [];
            console.log('Usando todos os influenciadores disponíveis para dados de exemplo:', influencerIds);

            const { data: profiles, error: profilesError } = await supabase
              .from('profiles')
              .select('id, full_name, profile_data')
              .in('id', influencerIds);

            if (profilesError) {
              console.error('Erro ao buscar perfis dos influenciadores:', profilesError);
              // Continuar mesmo com erro
            }

            console.log('Perfis dos influenciadores para dados de exemplo:', profiles);

            // Criar dados de exemplo com nomes reais, se disponíveis
            const mockTopInfluencers = [];
            // Usar os IDs de influenciadores já obtidos anteriormente
            const maxInfluencers = Math.min(5, influencerIds.length || 1);

            for (let i = 0; i < maxInfluencers; i++) {
              const influencerId = influencerIds[i] || 'mock-id';
              const profile = profiles?.find(p => p.id === influencerId);

              mockTopInfluencers.push({
                influencer_id: influencerId,
                influencer_name: profile?.full_name || `Influencer ${i + 1}`,
                username: profile?.profile_data?.instagram_username || `influencer${i + 1}`,
                rank: i + 1,
                total_likes: Math.floor(Math.random() * 1000) + 100,
                total_comments: Math.floor(Math.random() * 200) + 20,
                total_saves: Math.floor(Math.random() * 100) + 10,
                total_points: Math.floor(Math.random() * 2000) + 200,
                engagement_rate: (Math.random() * 5 + 1),
                is_mock_data: true
              });
            }

            setCompetitors(mockTopInfluencers);
          } else {
            // Buscar perfis dos influenciadores para obter nomes e usernames
            console.log('Buscando perfis para os influenciadores:', campaignInfluencers.map(ci => ci.influencer_id));
            const { data: profiles, error: profilesError } = await supabase
              .from('profiles')
              .select('id, full_name, profile_data')
              .in('id', campaignInfluencers.map(ci => ci.influencer_id));

            if (profilesError) {
              console.error('Erro ao buscar perfis:', profilesError);
              throw profilesError;
            }

            console.log('Perfis encontrados:', profiles);

            // Buscar posts dos influenciadores para obter métricas mais recentes
            console.log('Buscando posts dos influenciadores');
            const { data: posts, error: postsError } = await supabase
              .from('posts')
              .select(`
                id,
                campaign_influencer_id,
                likes_count,
                comments_count,
                saves_count,
                engagement_rate
              `)
              .in('campaign_influencer_id', campaignInfluencers.map(ci => ci.id));

            if (postsError) {
              console.error('Erro ao buscar posts:', postsError);
              // Não lançar erro, apenas registrar - usaremos os dados do campaign_influencers
            }

            console.log('Posts encontrados:', posts);

            // Criar lista de competidores com dados reais
            const competitors = campaignInfluencers.map((ci, index) => {
              const profile = profiles?.find(p => p.id === ci.influencer_id);
              console.log(`Perfil para influenciador ${ci.influencer_id}:`, profile);

              // Buscar posts deste influenciador para esta campanha
              const influencerPosts = posts?.filter(p => p.campaign_influencer_id === ci.id) || [];
              console.log(`Posts para influenciador ${ci.influencer_id}:`, influencerPosts);

              // Calcular totais de métricas dos posts
              const totalLikesFromPosts = influencerPosts.reduce((sum, post) => sum + (post.likes_count || 0), 0);
              const totalCommentsFromPosts = influencerPosts.reduce((sum, post) => sum + (post.comments_count || 0), 0);
              const totalSavesFromPosts = influencerPosts.reduce((sum, post) => sum + (post.saves_count || 0), 0);

              // Usar dados dos posts se disponíveis, caso contrário usar dados do campaign_influencers
              const totalLikes = totalLikesFromPosts > 0 ? totalLikesFromPosts : (ci.previous_total_likes || 0);
              const totalComments = totalCommentsFromPosts > 0 ? totalCommentsFromPosts : (ci.previous_total_comments || 0);
              const totalSaves = totalSavesFromPosts > 0 ? totalSavesFromPosts : (ci.previous_total_saves || 0);

              // Calcular pontos com base nas métricas (fórmula simplificada)
              const calculatedPoints = totalLikes + (totalComments * 2) + (totalSaves * 3);

              // Usar índice como ranking temporário
              const rank = index + 1;

              // Obter username do perfil ou gerar um padrão
              const username = profile?.profile_data?.instagram_username ||
                               (profile?.profile_data && 'instagram_username' in profile.profile_data ?
                                profile.profile_data.instagram_username : `influencer${index + 1}`);

              return {
                influencer_id: ci.influencer_id,
                influencer_name: profile?.full_name || `Influencer ${index + 1}`,
                username: username,
                rank: rank,
                total_likes: totalLikes,
                total_comments: totalComments,
                total_saves: totalSaves,
                total_points: calculatedPoints || 0,
                engagement_rate: parseFloat('0'),
                has_real_data: influencerPosts.length > 0
              };
            });

            console.log('Competidores criados com dados reais:', competitors);

            // Ordenar por pontos (decrescente) e depois por ranking (crescente)
            competitors.sort((a, b) => {
              if (b.total_points !== a.total_points) {
                return b.total_points - a.total_points;
              }
              return a.rank - b.rank;
            });

            // Atualizar ranks com base na ordenação
            competitors.forEach((comp, idx) => {
              comp.rank = idx + 1;
            });

            console.log('Competidores ordenados:', competitors);

            // Limitar a 5 competidores
            setCompetitors(competitors.slice(0, 5));
          }
        }
      } catch (err: any) {
        console.error('Erro ao buscar dados de ranking:', err);
        setError('Não foi possível carregar os dados de ranking');

        // Em caso de erro, criar dados de exemplo
        if (userRole === 'restaurant') {
          console.warn('Erro ao buscar dados reais. Usando dados de exemplo como fallback.');
          setIsUsingMockData(true);

          // Tentar buscar todos os influenciadores para usar nomes reais
          try {
            const { data: allInfluencers } = await supabase
              .from('campaign_influencers')
              .select(`id, influencer_id, status`)
              .eq('campaign_id', campaignId);

            if (allInfluencers && allInfluencers.length > 0) {
              // Buscar perfis de todos os influenciadores
              const { data: profiles } = await supabase
                .from('profiles')
                .select('id, full_name, profile_data')
                .in('id', allInfluencers.map(ai => ai.influencer_id));

              // Criar dados de exemplo com nomes reais
              const mockTopInfluencers = [];
              for (let i = 0; i < Math.min(5, allInfluencers.length); i++) {
                const influencerId = allInfluencers[i].influencer_id;
                const profile = profiles?.find(p => p.id === influencerId);

                mockTopInfluencers.push({
                  influencer_id: influencerId,
                  influencer_name: profile?.full_name || `Influencer ${i + 1}`,
                  username: profile?.profile_data?.instagram_username || `influencer${i + 1}`,
                  rank: i + 1,
                  total_likes: Math.floor(Math.random() * 1000) + 100,
                  total_comments: Math.floor(Math.random() * 200) + 20,
                  total_saves: Math.floor(Math.random() * 100) + 10,
                  total_points: Math.floor(Math.random() * 2000) + 200,
                  engagement_rate: (Math.random() * 5 + 1),
                  is_mock_data: true
                });
              }

              setCompetitors(mockTopInfluencers);
              return;
            }
          } catch (fallbackErr) {
            console.error('Erro ao tentar buscar dados para fallback:', fallbackErr);
          }

          // Se não conseguir dados reais, usar dados completamente fictícios
          const mockTopInfluencers = [];
          for (let i = 0; i < 5; i++) {
            mockTopInfluencers.push({
              influencer_id: `mock-id-${i}`,
              influencer_name: `Influencer ${i + 1}`,
              username: `influencer${i + 1}`,
              rank: i + 1,
              total_likes: Math.floor(Math.random() * 1000) + 100,
              total_comments: Math.floor(Math.random() * 200) + 20,
              total_saves: Math.floor(Math.random() * 100) + 10,
              total_points: Math.floor(Math.random() * 2000) + 200,
              engagement_rate: (Math.random() * 5 + 1),
              is_mock_data: true
            });
          }

          setCompetitors(mockTopInfluencers);
        }
      } finally {
        setLoading(false);
      }
    }

    if (campaignId && userId) {
      fetchRankingData();
    }
  }, [campaignId, userId, userRole, supabase]);

  // Renderização para estado de carregamento
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 animate-pulse ${className}`}>
        <div className="flex justify-between items-center mb-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div className="h-24 bg-gray-200 rounded-lg mb-4"></div>
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center">
              <div className="h-8 w-8 bg-gray-200 rounded-full mr-3"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Renderização para estado de erro
  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg ${className}`}>
        {error}
        <div className="mt-2">
          <button
            onClick={() => window.location.reload()}
            className="bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm font-medium"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Renderização para influenciadores
  if (userRole === 'influencer' && rankingData) {
    const { rank, total_influencers, previous_rank, engagement_rate, total_points, total_saves } = rankingData;

    // Calcular mudança de posição
    const rankChange = previous_rank ? previous_rank - rank : 0;

    return (
      <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
        {/* Cabeçalho */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 p-4 text-white">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-bold flex items-center">
              <FaTrophy className="mr-2 text-yellow-300" />
              Seu Ranking na Campanha
            </h3>
            <span className="text-sm bg-white/20 px-2 py-1 rounded">
              {total_influencers} participantes
            </span>
          </div>
        </div>

        {/* Destaque da posição atual */}
        <div className="p-6 bg-blue-50 border-b border-blue-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-2xl ${
                rank === 1 ? 'bg-yellow-500' :
                rank === 2 ? 'bg-gray-400' :
                rank === 3 ? 'bg-amber-700' :
                'bg-blue-600'
              }`}>
                {rank}º
              </div>
              <div className="ml-4">
                <div className="text-sm text-blue-700">Sua posição atual</div>
                <div className="text-2xl font-bold text-blue-900">
                  {rank === 1 ? '🏆 Primeiro Lugar!' :
                   rank === 2 ? '🥈 Segundo Lugar!' :
                   rank === 3 ? '🥉 Terceiro Lugar!' :
                   `${rank}º Lugar`}
                </div>
                <div className="flex items-center mt-1">
                  {rankChange > 0 ? (
                    <span className="text-green-600 flex items-center text-sm">
                      <FaArrowUp className="mr-1" /> Subiu {rankChange} {rankChange === 1 ? 'posição' : 'posições'}
                    </span>
                  ) : rankChange < 0 ? (
                    <span className="text-red-600 flex items-center text-sm">
                      <FaArrowDown className="mr-1" /> Desceu {Math.abs(rankChange)} {Math.abs(rankChange) === 1 ? 'posição' : 'posições'}
                    </span>
                  ) : (
                    <span className="text-gray-600 flex items-center text-sm">
                      <FaMinus className="mr-1" /> Manteve a posição
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-blue-700">Taxa de Engajamento</div>
              <div className="text-2xl font-bold text-blue-900">{engagement_rate}%</div>
              <div className="flex items-center justify-end mt-1 space-x-3 text-sm">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded flex items-center">
                        <FaTrophy className="mr-1 text-blue-500" /> {total_points} pts
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Total de pontos acumulados</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded flex items-center">
                        <FaBookmark className="mr-1 text-green-500" /> {total_saves}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Total de salvamentos</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>

        {/* Competidores próximos */}
        <div className="p-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-3">Seus Competidores Diretos</h4>

          <div className="space-y-3">
            {competitors.map((competitor) => {
              const isCurrentUser = competitor.influencer_id === userId;
              const isAhead = competitor.rank < rankingData.rank;

              return (
                <div
                  key={competitor.influencer_id}
                  className={`flex items-center justify-between p-3 rounded-lg ${
                    isCurrentUser ? 'bg-blue-50 border border-blue-200' :
                    isAhead ? 'bg-red-50' : 'bg-green-50'
                  }`}
                >
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                      competitor.rank === 1 ? 'bg-yellow-500' :
                      competitor.rank === 2 ? 'bg-gray-400' :
                      competitor.rank === 3 ? 'bg-amber-700' :
                      'bg-gray-600'
                    }`}>
                      {competitor.rank}
                    </div>
                    <div className="ml-3">
                      <div className={`font-medium ${isCurrentUser ? 'text-blue-700' : 'text-gray-800'}`}>
                        {competitor.influencer_name} {isCurrentUser && '(Você)'}
                      </div>
                      <div className="text-xs text-gray-500">@{competitor.username}</div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="font-medium text-gray-800">{competitor.engagement_rate.toFixed(1)}%</div>
                    <div className="flex items-center justify-end space-x-2 text-xs">
                      <span className="flex items-center text-red-600">
                        <FaHeart className="mr-0.5" /> {competitor.total_likes}
                      </span>
                      <span className="flex items-center text-blue-600">
                        <FaComment className="mr-0.5" /> {competitor.total_comments}
                      </span>
                      <span className="flex items-center text-green-600">
                        <FaBookmark className="mr-0.5" /> {competitor.total_saves}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Dicas para melhorar */}
          <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <h4 className="text-sm font-semibold text-yellow-800 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Dicas para Melhorar seu Ranking
            </h4>
            <ul className="mt-2 text-xs text-yellow-700 space-y-1">
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Incentive seus seguidores a salvar suas postagens para aumentar seu engajamento.
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Responda aos comentários para aumentar o engajamento geral.
              </li>
              <li className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Crie conteúdo que incentive o compartilhamento e salvamento.
              </li>
            </ul>
          </div>
        </div>

        {/* Rodapé com atualização */}
        <div className="p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500">
          Última atualização: {new Date().toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>
    );
  }

  // Renderização para restaurantes - Estilo Champions League
  if (userRole === 'restaurant' && competitors.length > 0) {
    console.log('Renderizando para restaurante com competidores:', competitors);
    // Calcular pontuação total para cada influenciador
    const totalPointsMax = Math.max(...competitors.map(inf => inf.total_points));

    return (
      <div className={`bg-white rounded-xl shadow-xl overflow-hidden ${className}`}>
        {/* Cabeçalho estilo Champions League */}
        <div className="bg-gradient-to-r from-blue-900 to-indigo-900 p-5 relative overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-10">
            <div className="absolute top-0 left-0 w-full h-full bg-[url('https://www.uefa.com/contentassets/c9b1b12d4c074c3ca3f03a7fdb018a2f/ucl-2021-24-starball-on-pitch-min.jpg')] bg-cover bg-center"></div>
          </div>
          <div className="relative flex justify-between items-center">
            <div>
              <h3 className="text-xl font-bold flex items-center text-white">
                <FaTrophy className="mr-3 text-yellow-300 text-2xl" />
                <span>
                  <span className="block">Champions da Campanha</span>
                  <span className="text-sm font-normal text-blue-200">Ranking de Influenciadores</span>
                </span>
              </h3>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-white text-sm">
                <span className="font-bold">{competitors.length}</span> <span className="text-blue-200">influenciadores</span>
              </div>
              <RankingNotifications campaignId={campaignId} userId={userId} userRole={userRole} />
            </div>
          </div>
        </div>

        {/* Aviso de dados de exemplo */}
        {isUsingMockData && (
          <div className="bg-yellow-100 text-yellow-800 p-3 text-sm border-b border-yellow-200 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Exibindo dados de exemplo. Os influenciadores são reais, mas as métricas são simuladas para fins de visualização.</span>
          </div>
        )}

        {/* Tabela de classificação estilo Champions League */}
        <div className="p-0">
          <div className="overflow-hidden rounded-lg">
            {/* Cabeçalho da tabela */}
            <div className="bg-gradient-to-r from-gray-100 to-blue-50 p-3 grid grid-cols-12 text-xs font-semibold border-b border-gray-200">
              <div className="col-span-1 text-center text-gray-600">#</div>
              <div className="col-span-3 text-gray-600">Influenciador</div>
              <div className="col-span-2 text-center text-red-500">Curtidas</div>
              <div className="col-span-2 text-center text-blue-500">Comentários</div>
              <div className="col-span-2 text-center text-green-500">Salvamentos</div>
              <div className="col-span-2 text-center">
                <div className="bg-blue-700 text-white py-1.5 px-3 rounded-lg shadow-sm inline-block font-bold">
                  PONTOS
                </div>
              </div>
            </div>

            {/* Linhas da tabela */}
            <div className="divide-y divide-gray-100">
              {competitors.map((influencer, index) => {
                // Determinar cores e estilos com base na posição
                const isTop = index < 3;
                const rankStyles = [
                  'bg-gradient-to-r from-yellow-500 to-yellow-400 text-white', // 1º lugar
                  'bg-gradient-to-r from-gray-400 to-gray-300 text-white',    // 2º lugar
                  'bg-gradient-to-r from-amber-700 to-amber-600 text-white',  // 3º lugar
                  'bg-blue-50 text-blue-800'                                  // Demais posições
                ];

                // Calcular porcentagem para a barra de progresso
                const progressPercent = Math.round((influencer.total_points / totalPointsMax) * 100);

                return (
                  <div
                    key={influencer.influencer_id}
                    className={`relative p-3 grid grid-cols-12 items-center text-sm ${index < 3 ? 'bg-blue-50/50' : 'hover:bg-gray-50'} ${index === 0 ? 'border-l-4 border-yellow-400' : index === 1 ? 'border-l-4 border-gray-400' : index === 2 ? 'border-l-4 border-amber-700' : ''}`}
                  >
                    {/* Barra de progresso de fundo */}
                    <div
                      className={`absolute left-0 top-0 h-full z-0 ${index === 0 ? 'bg-yellow-50' : index === 1 ? 'bg-gray-50' : index === 2 ? 'bg-amber-50' : 'bg-blue-50'}`}
                      style={{ width: `${progressPercent}%` }}
                    ></div>

                    {/* Conteúdo da linha */}
                    <div className="relative z-10 col-span-1 flex justify-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${rankStyles[index < 3 ? index : 3]}`}>
                        {index + 1}
                      </div>
                    </div>

                    <div className="relative z-10 col-span-3 flex items-center">
                      <div className="flex flex-col">
                        <span className="font-semibold text-gray-900">{influencer.influencer_name || `Influencer ${index + 1}`}</span>
                        <span className="text-xs text-gray-500">@{influencer.username || `influencer${index + 1}`}</span>
                      </div>
                    </div>

                    {/* Curtidas */}
                    <div className="relative z-10 col-span-2 text-center">
                      <div className="flex flex-col items-center">
                        <div className="flex items-center text-red-500 font-semibold">
                          <FaHeart className="mr-1" />
                          <span>{influencer.total_likes}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {Math.round((influencer.total_likes / (influencer.total_points || 1)) * 100) || 0}% do total
                        </div>
                      </div>
                    </div>

                    {/* Comentários */}
                    <div className="relative z-10 col-span-2 text-center">
                      <div className="flex flex-col items-center">
                        <div className="flex items-center text-blue-500 font-semibold">
                          <FaComment className="mr-1" />
                          <span>{influencer.total_comments}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {Math.round((influencer.total_comments / (influencer.total_points || 1)) * 100) || 0}% do total
                        </div>
                      </div>
                    </div>

                    {/* Salvamentos */}
                    <div className="relative z-10 col-span-2 text-center">
                      <div className="flex flex-col items-center">
                        <div className="flex items-center text-green-500 font-semibold">
                          <FaBookmark className="mr-1" />
                          <span>{influencer.total_saves}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {Math.round((influencer.total_saves / (influencer.total_points || 1)) * 100) || 0}% do total
                        </div>
                      </div>
                    </div>

                    {/* PONTOS - Destaque à direita */}
                    <div className="relative z-10 col-span-2 text-center">
                      <div className={`${index < 3 ? 'bg-gradient-to-r from-blue-600 to-blue-700' : 'bg-blue-600'} rounded-lg py-2 px-4 inline-block shadow-md border ${index === 0 ? 'border-yellow-300' : index === 1 ? 'border-gray-300' : index === 2 ? 'border-amber-300' : 'border-blue-500'}`}>
                        <div className="font-bold text-white text-xl">{influencer.total_points}</div>
                        <div className="text-xs text-blue-100 flex items-center justify-center mt-1">
                          <FaChartLine className="mr-1" />
                          {influencer.engagement_rate.toFixed(1)}% taxa
                        </div>

                        {/* Status do participante */}
                        <div className="mt-1 text-xs">
                          <span className="bg-blue-500/30 text-white px-1.5 py-0.5 rounded flex items-center justify-center">
                            <FaUser className="mr-1" size={10} />
                            Participante
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Insights para o restaurante - Estilo Champions League */}
          <div className="mt-4 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-5 text-white">
            <h4 className="text-sm font-semibold flex items-center mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Análise de Desempenho da Campanha
            </h4>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                <div className="text-xs text-blue-200">Engajamento Total</div>
                <div className="text-xl font-bold">{competitors.reduce((sum, inf) => sum + inf.total_points, 0)} pts</div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                <div className="text-xs text-blue-200">Salvamentos</div>
                <div className="text-xl font-bold">{competitors.reduce((sum, inf) => sum + inf.total_saves, 0)}</div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
                <div className="text-xs text-blue-200">Taxa Média</div>
                <div className="text-xl font-bold">{(competitors.reduce((sum, inf) => sum + inf.engagement_rate, 0) / competitors.length).toFixed(1)}%</div>
              </div>
            </div>

            <div className="text-sm text-blue-100 mb-4">
              Os salvamentos representam <span className="font-bold text-white">{Math.round((competitors.reduce((sum, inf) => sum + inf.total_saves, 0) / competitors.reduce((sum, inf) => sum + inf.total_likes + inf.total_comments + inf.total_saves, 0)) * 100)}%</span> do engajamento total desta campanha, demonstrando alto interesse do público em guardar o conteúdo para referência futura.
            </div>

            {/* Fórmula de cálculo de pontos */}
            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
              <h5 className="text-sm font-semibold mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Fórmula de Cálculo de Pontos
              </h5>
              <div className="grid grid-cols-4 gap-2 text-xs">
                <div className="bg-red-500/20 p-2 rounded flex flex-col items-center">
                  <FaHeart className="text-red-400 mb-1" />
                  <span className="font-semibold">Curtidas</span>
                  <span className="text-blue-200">1 ponto cada</span>
                </div>
                <div className="bg-blue-500/20 p-2 rounded flex flex-col items-center">
                  <FaComment className="text-blue-400 mb-1" />
                  <span className="font-semibold">Comentários</span>
                  <span className="text-blue-200">2 pontos cada</span>
                </div>
                <div className="bg-green-500/20 p-2 rounded flex flex-col items-center">
                  <FaBookmark className="text-green-400 mb-1" />
                  <span className="font-semibold">Salvamentos</span>
                  <span className="text-blue-200">3 pontos cada</span>
                </div>
                <div className="bg-yellow-500/20 p-2 rounded flex flex-col items-center">
                  <FaTrophy className="text-yellow-400 mb-1" />
                  <span className="font-semibold">Total</span>
                  <span className="text-blue-200">Soma ponderada</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Rodapé com atualização */}
        <div className="p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center">
          <div>
            Última atualização: {new Date().toLocaleDateString('pt-BR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
          <div className="flex items-center">
            <FaTrophy className="text-yellow-500 mr-1" />
            <span>Champions League Ranking</span>
          </div>
        </div>
      </div>
    );
  }

  // Fallback
  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      {isUsingMockData ? (
        <div className="text-center">
          <div className="bg-yellow-100 text-yellow-800 p-3 text-sm rounded-lg mb-4 inline-flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Exibindo dados de exemplo. As métricas são simuladas para fins de visualização.</span>
          </div>
        </div>
      ) : (
        <div className="text-center text-gray-500">
          Nenhum dado de ranking disponível para esta campanha.
        </div>
      )}
    </div>
  );
}
