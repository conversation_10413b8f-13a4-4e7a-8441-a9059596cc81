"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>a<PERSON>ser, FaBell, FaLock, FaShieldAlt, FaInfoCircle, FaInstagram, FaShareAlt, FaWhatsapp } from "react-icons/fa";
import { supabase } from "@/utils/supabaseClient";
import StandardPopup, { PopupTab } from "@/components/ui/StandardPopup";
import { usePopupNavigation } from "@/hooks/usePopupNavigation";
import UserAlerts from "@/components/UserAlerts";
import AlertsSettings from "@/components/AlertsSettings";
import InstagramConnectButton from "@/components/instagram/InstagramConnectButton";
import WhatsAppVerification from "@/components/dashboard/criador/WhatsAppVerification";
import NotificationSettings from "@/components/dashboard/criador/NotificationSettings";
import RestaurantWhatsAppVerification from "@/components/dashboard/restaurant/WhatsAppVerification";
import RestaurantNotificationSettings from "@/components/dashboard/restaurant/NotificationSettings";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSaved?: (data: any) => void;
  userType: 'restaurant' | 'influencer';
  defaultTab?: string;
}

export default function SettingsModal({
  isOpen,
  onClose,
  onSaved,
  userType,
  defaultTab = 'info'
}: SettingsModalProps) {
  // Estado para dados do usuário
  const [userId, setUserId] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Estados para restaurante
  const [restaurantId, setRestaurantId] = useState<string | null>(null);
  const [name, setName] = useState("");
  const [responsible, setResponsible] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [location, setLocation] = useState("");

  // Estados para influenciador
  const [bio, setBio] = useState("");
  const [instagramUsername, setInstagramUsername] = useState("");
  const [notifications, setNotifications] = useState(true);

  // Usar o defaultTab como activeTabId inicial
  const [activeTabId, setActiveTabId] = useState(defaultTab);

  useEffect(() => {
    // Only fetch data when the modal is open
    if (!isOpen) return;

    const fetchData = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      // Salvar o ID do usuário para uso com o componente UserAlerts
      setUserId(session.user.id);

      if (userType === 'restaurant') {
        // Buscar dados do restaurante
        const { data } = await supabase
          .from("restaurants")
          .select("*")
          .eq("owner_id", session.user.id)
          .maybeSingle();

        if (data) {
          setRestaurantId(data.id);
          setName(data.name ?? "");
          setResponsible(data.responsible ?? "");
          setEmail(data.email ?? "");
          setPhone(data.phone ?? "");
          setLocation(data.location ?? "");
        }
      } else {
        // Buscar dados do perfil
        const { data: profileData } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", session.user.id)
          .maybeSingle();

        if (profileData) {
          setName(profileData.full_name ?? "");
          setEmail(profileData.email ?? "");
          setPhone(profileData.phone ?? "");
        }

        // Buscar dados do perfil de influenciador
        const { data: influencerData } = await supabase
          .from("influencer_profiles")
          .select("*")
          .eq("id", session.user.id)
          .maybeSingle();

        if (influencerData) {
          setBio(influencerData.bio ?? "");
          setInstagramUsername(influencerData.instagram_username ?? "");
        }
      }
    };

    fetchData();
  }, [isOpen, userType]);

  const handleSaveRestaurant = async () => {
    if (!restaurantId) return;
    setSaving(true);
    try {
      const { data, error } = await supabase
        .from("restaurants")
        .update({
          name,
          responsible,
          email,
          phone,
          location,
        })
        .eq("id", restaurantId)
        .select();

      if (!error && data && data.length > 0) {
        onSaved?.(data[0]);
        onClose();
      } else if (error) {
        alert("Erro ao salvar: " + error.message);
      }
    } catch (err) {
      console.error("Unexpected error saving restaurant info:", err);
      alert("Erro inesperado ao salvar.");
    } finally {
      setSaving(false);
    }
  };

  const handleSaveInfluencer = async () => {
    try {
      setSaving(true);

      if (!userId) {
        console.error('Usuário não encontrado');
        return;
      }

      // Atualizar perfil no banco de dados
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          full_name: name,
          email: email,
          phone: phone,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (profileError) {
        throw new Error(`Erro ao atualizar perfil: ${profileError.message}`);
      }

      // Atualizar perfil de influenciador
      const { error: influencerError } = await supabase
        .from('influencer_profiles')
        .update({
          bio: bio,
          instagram_username: instagramUsername,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (influencerError) {
        throw new Error(`Erro ao atualizar perfil de influenciador: ${influencerError.message}`);
      }

      // Buscar dados atualizados
      const { data: updatedProfile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      onSaved?.(updatedProfile);
      onClose();
    } catch (err) {
      console.error('Erro ao salvar configurações:', err);
      alert(`Erro ao salvar: ${err.message}`);
    } finally {
      setSaving(false);
    }
  };

  const handleSave = () => {
    if (userType === 'restaurant') {
      handleSaveRestaurant();
    } else {
      handleSaveInfluencer();
    }
  };

  // Definir as abas do popup
  const tabs: PopupTab[] = [
    {
      id: 'info',
      label: 'Perfil',
      icon: <FaInfoCircle className="text-green-500" />,
      color: 'green',
      content: (
        <div className="space-y-6">
          <h2 className="text-lg font-bold mb-3 flex items-center gap-2">
            <FaInfoCircle className="text-green-500" /> Informações Básicas
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="form-group">
              <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                <FaUser className="text-gray-500" /> Nome {userType === 'restaurant' ? 'do Restaurante' : 'Completo'}
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full"
                placeholder={userType === 'restaurant' ? "Nome do estabelecimento" : "Seu nome completo"}
              />
            </div>

            {userType === 'restaurant' && (
              <div className="form-group">
                <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                  <FaUser className="text-gray-500" /> Responsável
                </label>
                <input
                  type="text"
                  value={responsible}
                  onChange={(e) => setResponsible(e.target.value)}
                  className="w-full"
                  placeholder="Nome do responsável"
                />
              </div>
            )}

            <div className="form-group">
              <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-group">
              <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                Telefone
              </label>
              <input
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="w-full"
                placeholder="(00) 00000-0000"
              />
            </div>

            {userType === 'restaurant' && (
              <div className="form-group md:col-span-2">
                <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  Localização
                </label>
                <input
                  type="text"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  className="w-full"
                  placeholder="Endereço completo"
                />
              </div>
            )}

            {userType === 'influencer' && (
              <>
                <div className="form-group md:col-span-2">
                  <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    Bio
                  </label>
                  <textarea
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    className="w-full"
                    rows={3}
                    placeholder="Uma breve descrição sobre você"
                  ></textarea>
                </div>

                <div className="form-group md:col-span-2">
                  <label className="block mb-2 text-sm font-medium flex items-center gap-2">
                    <FaInstagram className="text-gray-500" /> Instagram
                  </label>
                  <div className="flex">
                    <span className="inline-flex items-center px-3 text-gray-500 bg-gray-100 border-none rounded-l-lg">
                      @
                    </span>
                    <input
                      type="text"
                      value={instagramUsername}
                      onChange={(e) => setInstagramUsername(e.target.value)}
                      className="flex-1 min-w-0 block w-full rounded-none rounded-r-lg"
                      placeholder="seu_usuario"
                    />
                  </div>
                </div>
              </>
            )}
          </div>

          <div className="flex justify-end mt-8">
            <button
              onClick={handleSave}
              disabled={saving}
              className="px-6 py-2.5 rounded-lg bg-gray-800 text-white text-sm hover:bg-gray-700 transition-all disabled:opacity-50 font-medium flex items-center gap-2"
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Salvando...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Salvar
                </>
              )}
            </button>
          </div>
        </div>
      )
    },
    {
      id: 'security',
      label: 'Segurança',
      icon: <FaLock className="text-red-500" />,
      color: 'red',
      lazyLoad: true,
      content: (
        <div>
          <h2 className="text-lg font-bold mb-3 flex items-center gap-2">
            <FaLock className="text-red-500" /> Configurações de Segurança
          </h2>
          <p className="text-gray-500 mb-3 text-sm">As configurações de segurança estarão disponíveis em breve.</p>

          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaShieldAlt className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Recomendamos alterar sua senha regularmente e ativar a autenticação de dois fatores quando disponível.
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    // Social tab - Only for influencers
    ...(userType === 'influencer' ? [{
      id: 'social',
      label: 'Redes Sociais',
      icon: <FaShareAlt className="text-purple-500" />,
      color: 'purple',
      lazyLoad: true,
      content: (
        <div>
          <h2 className="text-lg font-bold mb-3 flex items-center gap-2">
            <FaShareAlt className="text-purple-500" /> Conexões com Redes Sociais
          </h2>
          <p className="text-gray-500 mb-3 text-sm">Conecte suas redes sociais para participar de campanhas e monitorar seu desempenho.</p>

          <div className="space-y-6">
            <div>
              <h3 className="text-base font-medium mb-2 flex items-center gap-2">
                <FaInstagram className="text-pink-500" /> Instagram
              </h3>
              <p className="text-xs text-gray-600 mb-3">
                Conecte sua conta do Instagram para participar das competições e ter seus posts contabilizados automaticamente para ganhar pontos e prêmios.
              </p>
              {userId && <InstagramConnectButton userId={userId} />}
            </div>

            <div>
              <h3 className="text-base font-medium mb-2 flex items-center gap-2">
                <FaWhatsapp className="text-green-500" /> WhatsApp
              </h3>
              <p className="text-xs text-gray-600 mb-3">
                Conecte seu WhatsApp para receber notificações importantes sobre campanhas, atualizações de ranking e pagamentos.
              </p>
              <WhatsAppVerification />
            </div>
          </div>
        </div>
      )
    }] : []),

    // WhatsApp tab
    {
      id: 'whatsapp',
      label: 'WhatsApp',
      icon: <FaWhatsapp className="text-green-600" />,
      color: 'green',
      lazyLoad: true,
      content: (
        <div>
          <h2 className="text-lg font-bold mb-3 flex items-center gap-2">
            <FaWhatsapp className="text-green-600" /> Configurações de WhatsApp
          </h2>
          <p className="text-gray-500 mb-3 text-sm">Configure suas preferências de notificação via WhatsApp.</p>

          <div className="space-y-6">
            {userType === 'influencer' ? (
              <>
                <WhatsAppVerification />
                <NotificationSettings />
              </>
            ) : (
              <>
                <RestaurantWhatsAppVerification />
                <RestaurantNotificationSettings />
              </>
            )}
          </div>
        </div>
      )
    },
    {
      id: 'alerts',
      label: 'Avisos',
      icon: <FaBell className="text-yellow-500" />,
      color: 'yellow',
      lazyLoad: true,
      content: (
        <div>
          <h2 className="text-lg font-bold mb-3 flex items-center gap-2">
            <FaBell className="text-yellow-500" /> Avisos do Sistema
          </h2>
          <p className="text-gray-500 mb-3 text-sm">Escolha quais avisos você deseja ver na página de configurações.</p>

          {userId ? (
            <>
              <UserAlerts userId={userId} />
              <AlertsSettings userId={userId} />
            </>
          ) : (
            <div className="bg-gray-100 p-4 rounded">
              <p className="text-gray-500">Carregando dados de usuário...</p>
            </div>
          )}
        </div>
      )
    }
  ];

  // Função para lidar com o fechamento do popup
  const handleClose = () => {
    onClose();
  };

  // Atualizar o activeTabId quando a aba mudar
  const handleTabChange = (tabId: string) => {
    setActiveTabId(tabId);
  };

  // Memoize tabs to prevent unnecessary re-renders
  const memoizedTabs = React.useMemo(() => tabs, []);

  return (
    <StandardPopup
      id="settings-modal"
      isOpen={isOpen}
      onClose={handleClose}
      title="Configurações"
      tabs={memoizedTabs}
      defaultTabId={defaultTab} // Use the prop directly instead of state
      onTabChange={handleTabChange}
      size="small"
      minContentHeight="500px"
      className="settings-modal-premium"
      overlayClassName="settings-modal-premium-overlay"
    />
  );
}
