"use client";

// import Image from 'next/image'; // Removido
import Link from 'next/link';

interface LogoProps {
  className?: string;
  textClassName?: string;
  showText?: boolean;
  size?: 'small' | 'medium' | 'large';
  href?: string;
}

export default function Logo({
  className = '',
  textClassName = 'text-xl font-semibold',
  showText: _showText = true, // showText prefixado
  size = 'medium',
  href = '/'
}: LogoProps) {
  const sizeMap = {
    small: 24,
    medium: 32,
    large: 48
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _logoSize = sizeMap[size]; // logoSize prefixado

  return href ? (
    <Link href={href} className={`flex items-center ${className}`}>
      <span className={textClassName}>crIAdores</span>
    </Link>
  ) : (
    <div className={`flex items-center ${className}`}>
      <span className={textClassName}>crIAdores</span>
    </div>
  );
}
