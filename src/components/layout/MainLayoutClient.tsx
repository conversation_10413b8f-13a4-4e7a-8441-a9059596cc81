"use client";

import { usePathname } from "next/navigation";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/contexts/AuthContext";

export default function MainLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if we're on the home page
  const isHomePage = pathname === "/";

  return (
    <AuthProvider>
      <div className="w-full max-w-full">
        {children}
        <Toaster position="top-right" />
      </div>
    </AuthProvider>
  );
}
