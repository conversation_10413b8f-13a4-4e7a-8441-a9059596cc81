"use client";

import React, { useState, useEffect } from 'react';
import { FaInfoCircle, FaMedal, FaBookmark, FaHeart, FaComment } from 'react-icons/fa';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';
import { rankingService, RankedCriador } from '@/services/rankingService';

interface CriadorRankingTableProps {
  campaignId: string;
  className?: string;
}

/**
 * Componente que exibe uma tabela de ranking de influenciadores para uma campanha específica
 * Inclui informações detalhadas sobre engajamento, incluindo salvamentos
 */
export default function CriadorRankingTable({ campaignId, className = '' }: CriadorRankingTableProps) {
  const [criadors, setCriadors] = useState<RankedCriador[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'rank' | 'engagementRate' | 'postCount' | 'totalPoints'>('rank');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [hoveredCriador, setHoveredCriador] = useState<string | null>(null);

  // Função para buscar os dados do ranking
  useEffect(() => {
    async function fetchRanking() {
      try {
        setLoading(true);
        setError(null);

        // Usar o serviço real para buscar os dados
        const rankedCriadors = await rankingService.getCriadorRanking(campaignId);

        // Ordenar influenciadores
        const sortedCriadors = sortCriadors(rankedCriadors, sortBy, sortDirection);

        setCriadors(sortedCriadors);
      } catch (err: any) {
        console.error('Erro ao buscar ranking de influenciadores:', err);
        setError('Não foi possível carregar o ranking de influenciadores');
      } finally {
        setLoading(false);
      }
    }

    if (campaignId) {
      fetchRanking();
    }
  }, [campaignId, sortBy, sortDirection]);

  // Função para ordenar influenciadores
  const sortCriadors = (
    criadors: RankedCriador[],
    sortBy: 'rank' | 'engagementRate' | 'postCount' | 'totalPoints',
    direction: 'asc' | 'desc'
  ) => {
    return [...criadors].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'rank':
          comparison = a.rank - b.rank;
          break;
        case 'engagementRate':
          comparison = a.engagementRate - b.engagementRate;
          break;
        case 'postCount':
          comparison = a.postCount - b.postCount;
          break;
        case 'totalPoints':
          comparison = a.totalPoints - b.totalPoints;
          break;
      }

      return direction === 'asc' ? comparison : -comparison;
    });
  };

  // Função para alternar a ordenação
  const toggleSort = (column: 'rank' | 'engagementRate' | 'postCount' | 'totalPoints') => {
    if (sortBy === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortDirection('asc');
    }
  };

  // Função para renderizar o indicador de posição
  const renderRankIndicator = (rank: number) => {
    if (rank === 1) {
      return (
        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100">
          <FaMedal className="text-yellow-500" />
        </div>
      );
    } else if (rank === 2) {
      return (
        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100">
          <FaMedal className="text-gray-400" />
        </div>
      );
    } else if (rank === 3) {
      return (
        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-amber-100">
          <FaMedal className="text-amber-700" />
        </div>
      );
    } else {
      return (
        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100">
          <span className="text-gray-500 font-medium">{rank}</span>
        </div>
      );
    }
  };

  // Estados de carregamento e erro
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm animate-pulse ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
        </div>
        <div className="p-4">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className="px-4 py-2"><div className="h-4 bg-gray-200 rounded w-8"></div></th>
                <th className="px-4 py-2"><div className="h-4 bg-gray-200 rounded w-24"></div></th>
                <th className="px-4 py-2"><div className="h-4 bg-gray-200 rounded w-16"></div></th>
                <th className="px-4 py-2"><div className="h-4 bg-gray-200 rounded w-20"></div></th>
              </tr>
            </thead>
            <tbody>
              {[...Array(5)].map((_, index) => (
                <tr key={index} className="border-t border-gray-100">
                  <td className="px-4 py-4"><div className="h-8 bg-gray-200 rounded-full w-8 mx-auto"></div></td>
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 bg-gray-200 rounded-full mr-3"></div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                        <div className="h-3 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4"><div className="h-4 bg-gray-200 rounded w-8 mx-auto"></div></td>
                  <td className="px-4 py-4"><div className="h-4 bg-gray-200 rounded w-12 ml-auto"></div></td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 text-red-700 p-4 rounded-md ${className}`}>
        {error}
      </div>
    );
  }

  if (criadors.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Ranking de Influenciadores</h3>
        </div>
        <div className="p-8 text-center">
          <p className="text-gray-500">Nenhum influenciador encontrado para esta campanha.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-semibold flex items-center">
          Ranking de Influenciadores
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <FaInfoCircle className="ml-2 text-gray-400 text-sm cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Classificação baseada no engajamento total, incluindo curtidas, comentários e salvamentos.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </h3>
        <div className="text-sm text-gray-500">
          {criadors.length} participantes
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Posição
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Influenciador
              </th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => toggleSort('postCount')}>
                <div className="flex items-center justify-center">
                  Posts
                  {sortBy === 'postCount' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                </div>
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => toggleSort('engagementRate')}>
                <div className="flex items-center justify-end">
                  Engajamento
                  {sortBy === 'engagementRate' && (
                    <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                  )}
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <FaInfoCircle className="ml-1 text-gray-400 text-xs cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Taxa de engajamento calculada como (curtidas + comentários + salvamentos) / visualizações</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {criadors.map((criador) => (
              <tr
                key={criador.id}
                className={`transition-colors duration-150 ${
                  hoveredCriador === criador.id ? 'bg-blue-50' : ''
                }`}
                onMouseEnter={() => setHoveredCriador(criador.id)}
                onMouseLeave={() => setHoveredCriador(null)}
              >
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center justify-center">
                    {renderRankIndicator(criador.rank)}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {criador.profileImage ? (
                      <img
                        src={criador.profileImage}
                        alt={criador.name}
                        className="h-10 w-10 rounded-full object-cover mr-3"
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                        <span className="text-gray-500 font-medium">
                          {criador.name.charAt(0)}
                        </span>
                      </div>
                    )}
                    <div>
                      <div className="text-sm font-medium text-gray-900">{criador.name}</div>
                      <div className="text-sm text-gray-500">{criador.username}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-center">
                  <span className="text-sm text-gray-900">{criador.postCount}</span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {criador.engagementRate.toFixed(1)}%
                  </div>

                  {/* Detalhamento de engajamento (visível apenas ao passar o mouse) */}
                  {hoveredCriador === criador.id && (
                    <div className="mt-1 flex items-center justify-end space-x-3 text-xs text-gray-500">
                      <div className="flex items-center">
                        <FaHeart className="text-red-500 mr-1" />
                        {criador.totalLikes}
                      </div>
                      <div className="flex items-center">
                        <FaComment className="text-blue-500 mr-1" />
                        {criador.totalComments}
                      </div>
                      <div className="flex items-center">
                        <FaBookmark className="text-green-500 mr-1" />
                        {criador.totalSaves}
                      </div>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="p-4 border-t border-gray-200 bg-gray-50 text-xs text-gray-500 flex justify-between items-center">
        <div>
          Última atualização: {new Date().toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
        <div className="flex items-center">
          <FaBookmark className="text-green-500 mr-1" />
          <span className="mr-3">Salvamentos incluídos no cálculo de engajamento</span>
        </div>
      </div>
    </div>
  );
}
