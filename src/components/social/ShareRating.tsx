'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaWhatsapp, FaCopy, FaCheck } from 'react-icons/fa';
import StarRating from '@/components/ratings/StarRating';

interface ShareRatingProps {
  rating: number;
  comment?: string;
  rateeName: string;
  campaignName: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function ShareRating({
  rating,
  comment,
  rateeName,
  campaignName,
  open,
  onOpenChange
}: ShareRatingProps) {
  const [customMessage, setCustomMessage] = useState('');
  const [includeRating, setIncludeRating] = useState(true);
  const [includeComment, setIncludeComment] = useState(true);
  const [copied, setCopied] = useState(false);
  
  const getShareMessage = () => {
    let message = customMessage || `Acabei de avaliar ${rateeName} na campanha ${campaignName} no crIAdores!`;
    
    if (includeRating) {
      message += `\n\nClassificação: ${rating} estrela${rating !== 1 ? 's' : ''}`;
    }
    
    if (includeComment && comment) {
      message += `\n\nComentário: "${comment}"`;
    }
    
    message += '\n\nConheça o crIAdores: https://criadores.com.br';
    
    return message;
  };
  
  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(getShareMessage());
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  const handleShare = (platform: string) => {
    const message = encodeURIComponent(getShareMessage());
    const url = encodeURIComponent('https://criadores.com.br');
    
    let shareUrl = '';
    
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${message}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${message}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/shareArticle?mini=true&url=${url}&title=Avaliação no crIAdores&summary=${message}`;
        break;
      case 'whatsapp':
        shareUrl = `https://api.whatsapp.com/send?text=${message}`;
        break;
      default:
        return;
    }
    
    window.open(shareUrl, '_blank');
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Compartilhar Avaliação</DialogTitle>
          <DialogDescription>
            Compartilhe sua avaliação nas redes sociais.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <p className="font-medium mb-2">Sua avaliação para {rateeName}</p>
            <div className="flex items-center mb-2">
              <StarRating value={rating} readOnly size="md" />
            </div>
            {comment && (
              <p className="text-sm text-gray-700 italic">
                "{comment}"
              </p>
            )}
          </div>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="custom-message" className="mb-2 block">
                Mensagem personalizada (opcional)
              </Label>
              <Textarea
                id="custom-message"
                placeholder="Adicione uma mensagem personalizada..."
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="include-rating" className="cursor-pointer">
                Incluir classificação
              </Label>
              <Switch
                id="include-rating"
                checked={includeRating}
                onCheckedChange={setIncludeRating}
              />
            </div>
            
            {comment && (
              <div className="flex items-center justify-between">
                <Label htmlFor="include-comment" className="cursor-pointer">
                  Incluir comentário
                </Label>
                <Switch
                  id="include-comment"
                  checked={includeComment}
                  onCheckedChange={setIncludeComment}
                />
              </div>
            )}
          </div>
        </div>
        
        <Tabs defaultValue="social">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="social" className="flex-1">
              Redes Sociais
            </TabsTrigger>
            <TabsTrigger value="copy" className="flex-1">
              Copiar Texto
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="social">
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="flex items-center justify-center"
                onClick={() => handleShare('facebook')}
              >
                <FaFacebook className="mr-2 text-blue-600" />
                Facebook
              </Button>
              <Button
                variant="outline"
                className="flex items-center justify-center"
                onClick={() => handleShare('twitter')}
              >
                <FaTwitter className="mr-2 text-blue-400" />
                Twitter
              </Button>
              <Button
                variant="outline"
                className="flex items-center justify-center"
                onClick={() => handleShare('linkedin')}
              >
                <FaLinkedin className="mr-2 text-blue-700" />
                LinkedIn
              </Button>
              <Button
                variant="outline"
                className="flex items-center justify-center"
                onClick={() => handleShare('whatsapp')}
              >
                <FaWhatsapp className="mr-2 text-green-600" />
                WhatsApp
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="copy">
            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 text-sm">
                <pre className="whitespace-pre-wrap">{getShareMessage()}</pre>
              </div>
              <Button
                variant="outline"
                className="w-full flex items-center justify-center"
                onClick={handleCopyToClipboard}
              >
                {copied ? (
                  <>
                    <FaCheck className="mr-2 text-green-600" />
                    Copiado!
                  </>
                ) : (
                  <>
                    <FaCopy className="mr-2" />
                    Copiar para a área de transferência
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
