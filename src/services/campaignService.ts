import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { VideoStatus } from './videoService';

// Types for campaigns
export interface Restaurant {
  id: string;
  name: string;
  logo_url?: string;
  city?: string;
  state?: string;
}

export interface Campaign {
  id: string;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  status: string;
  restaurant_id: string;
  restaurants: Restaurant;
  hashtags?: string[];
  mentions?: string[];
  city?: string;
  state?: string;
  max_influencers?: number;
  current_influencers?: number;
  min_followers?: number;
  payment_amount?: number;
}

export interface CampaignInfluencer {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: string;
  total_points: number;
  video_status?: VideoStatus;
  campaigns: Campaign;
  rank?: number;
}

// Real campaign service that queries the Supabase database
export const campaignService = {
  // Fetch campaigns for an influencer
  async getInfluencerCampaigns(influencerId: string): Promise<CampaignInfluencer[]> {
    const supabase = createClientComponentClient();
    
    try {
      // Query campaign_participants table to get campaigns the influencer is participating in
      const { data: participantsData, error: participantsError } = await supabase
        .from('campaign_participants')
        .select(`
          id, 
          status, 
          campaigns(
            id, 
            name, 
            description, 
            start_date, 
            end_date, 
            status, 
            restaurant_id, 
            restaurant_profiles(id, business_name)
          )
        `)
        .eq('profile_id', influencerId);

      if (participantsError) {
        console.error('Error fetching campaign participants:', participantsError);
        return [];
      }

      if (!participantsData || participantsData.length === 0) {
        return [];
      }

      // Transform the data to match the expected format
      const campaignInfluencers: CampaignInfluencer[] = await Promise.all(
        participantsData.map(async (participant) => {
          // Get campaign details
          const campaignData = participant.campaigns;
          
          // Handle case where campaign data might be an array
          const campaign = Array.isArray(campaignData) ? campaignData[0] : campaignData;
          
          if (!campaign) {
            return null;
          }

          // Get restaurant details
          const restaurantData = campaign.restaurant_profiles;
          const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;

          // Get total points for this campaign participation
          const { data: pointsData, error: pointsError } = await supabase
            .from('points_history')
            .select('points')
            .eq('campaign_participant_id', participant.id);

          let totalPoints = 0;
          if (!pointsError && pointsData) {
            totalPoints = pointsData.reduce((sum, item) => sum + (item.points || 0), 0);
          }

          // Get video status
          let videoStatus: VideoStatus | undefined;
          const { data: videoData, error: videoError } = await supabase
            .from('campaign_videos')
            .select('status')
            .eq('campaign_participant_id', participant.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (!videoError && videoData) {
            videoStatus = videoData.status as VideoStatus;
          }

          // Get rank
          let rank: number | undefined;
          // This would need to be implemented based on your ranking logic
          // For example, you might have a separate table for rankings or calculate it on the fly

          return {
            id: participant.id,
            campaign_id: campaign.id,
            influencer_id: influencerId,
            status: participant.status,
            total_points: totalPoints,
            video_status: videoStatus,
            rank,
            campaigns: {
              id: campaign.id,
              name: campaign.name,
              description: campaign.description,
              start_date: campaign.start_date,
              end_date: campaign.end_date,
              status: campaign.status,
              restaurant_id: campaign.restaurant_id,
              restaurants: restaurant ? {
                id: restaurant.id,
                name: restaurant.business_name,
              } : null,
            },
          };
        })
      );

      // Filter out any null values
      return campaignInfluencers.filter(Boolean) as CampaignInfluencer[];
    } catch (error) {
      console.error('Error in getInfluencerCampaigns:', error);
      return [];
    }
  },

  // Fetch available campaigns
  async getAvailableCampaigns(filters?: { city?: string; state?: string; minFollowers?: number }): Promise<Campaign[]> {
    const supabase = createClientComponentClient();
    
    try {
      console.log('Fetching available campaigns with filters:', filters);
      
      let query = supabase
        .from('campaigns')
        .select(`
          id,
          name,
          description,
          start_date,
          end_date,
          status,
          restaurant_id,
          restaurant_profiles(id, business_name, city, state),
          requirements
        `);
      
      // Remove the status filter to see all campaigns
      // .eq('status', 'active');

      // Apply filters if provided
      if (filters?.city) {
        query = query.eq('city', filters.city);
      }

      if (filters?.state) {
        query = query.eq('state', filters.state);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching available campaigns:', error);
        return [];
      }

      console.log('Raw campaigns data from database:', data);

      if (!data || data.length === 0) {
        console.log('No campaigns found with the current filters');
        return [];
      }

      // Transform the data to match the expected format
      const campaigns: Campaign[] = data.map((campaign) => {
        const restaurantData = campaign.restaurant_profiles;
        const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;

        // Extract hashtags and mentions from requirements
        const requirements = campaign.requirements || {};
        const hashtags = requirements.hashtags || [];
        const mentions = requirements.mentions || [];

        return {
          id: campaign.id,
          name: campaign.name,
          description: campaign.description,
          start_date: campaign.start_date,
          end_date: campaign.end_date,
          status: campaign.status,
          restaurant_id: campaign.restaurant_id,
          restaurants: restaurant ? {
            id: restaurant.id,
            name: restaurant.business_name,
            city: restaurant.city,
            state: restaurant.state,
          } : null,
          hashtags,
          mentions,
          city: restaurant?.city,
          state: restaurant?.state,
        };
      });

      console.log('Transformed campaigns:', campaigns);

      return campaigns;
    } catch (error) {
      console.error('Error in getAvailableCampaigns:', error);
      return [];
    }
  },

  // Apply for a campaign
  async applyForCampaign(influencerId: string, campaignId: string): Promise<{ success: boolean; message: string; campaignInfluencer?: CampaignInfluencer }> {
    const supabase = createClientComponentClient();
    
    try {
      console.log(`Attempting to apply for campaign: ${campaignId} by influencer: ${influencerId}`);
      
      // Get the authenticated user's ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return {
          success: false,
          message: 'Usuário não autenticado. Por favor, faça login e tente novamente.'
        };
      }

      console.log(`InfluencerId: ${influencerId}`);
      console.log(`Auth.uid(): ${user.id}`);

      // Ensure the influencerId matches the authenticated user's ID
      if (influencerId !== user.id) {
        return {
          success: false,
          message: 'Erro de autenticação. Por favor, faça login novamente e tente mais uma vez.'
        };
      }

      // Check if the influencer is already participating in this campaign
      const { data: existingParticipation, error: checkError } = await supabase
        .from('campaign_influencers')
        .select('id, status')
        .eq('influencer_id', user.id)
        .eq('campaign_id', campaignId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is expected
        console.error('Error checking existing participation:', checkError);
        return {
          success: false,
          message: 'Erro ao verificar participação existente. Tente novamente.'
        };
      }

      if (existingParticipation) {
        return {
          success: false,
          message: 'Você já se candidatou a esta campanha.'
        };
      }

      // Verificar se o usuário tem um perfil de influenciador
      const { data: influencerProfile, error: influencerError } = await supabase
        .from('influencer_profiles')
        .select('id')
        .eq('id', user.id)
        .single();

      if (influencerError || !influencerProfile) {
        console.error('Influencer profile not found:', influencerError);
        return {
          success: false,
          message: 'Perfil de influenciador não encontrado. Por favor, complete seu perfil antes de se candidatar a campanhas.'
        };
      }

      // Get the campaign details first to ensure it exists
      const { data: campaignData, error: campaignError } = await supabase
        .from('campaigns')
        .select(`
          id,
          name,
          description,
          start_date,
          end_date,
          status,
          restaurant_id,
          restaurant_profiles(id, business_name)
        `)
        .eq('id', campaignId)
        .single();

      if (campaignError) {
        console.error('Error fetching campaign details:', campaignError);
        return {
          success: false,
          message: 'Erro ao buscar detalhes da campanha. Tente novamente.'
        };
      }

      // Create a new participation record
      const { data: newParticipation, error: insertError } = await supabase
        .from('campaign_influencers')
        .insert({
          influencer_id: user.id,
          campaign_id: campaignId,
          status: 'invited'
        })
        .select('id')
        .single();

      if (insertError) {
        console.error('Error creating participation:', insertError);

        // Verificar se é um erro de foreign key
        if (insertError.code === '23503') {
          return {
            success: false,
            message: 'Perfil de influenciador não encontrado. Por favor, complete seu perfil antes de se candidatar a campanhas.'
          };
        }

        return {
          success: false,
          message: `Erro ao se candidatar para a campanha: ${insertError.message}. Tente novamente.`
        };
      }

      console.log('Successfully applied to campaign:', newParticipation);

      const restaurantData = campaignData.restaurant_profiles;
      const restaurant = Array.isArray(restaurantData) ? restaurantData[0] : restaurantData;

      // Create a campaign influencer object to return
      const campaignInfluencer: CampaignInfluencer = {
        id: newParticipation.id,
        campaign_id: campaignId,
        influencer_id: user.id,
        status: 'invited',
        total_points: 0,
        campaigns: {
          id: campaignData.id,
          name: campaignData.name,
          description: campaignData.description,
          start_date: campaignData.start_date,
          end_date: campaignData.end_date,
          status: campaignData.status,
          restaurant_id: campaignData.restaurant_id,
          restaurants: restaurant ? {
            id: restaurant.id,
            name: restaurant.business_name,
          } : null,
        },
      };

      return {
        success: true,
        message: 'Candidatura enviada com sucesso! Aguarde a aprovação do restaurante.',
        campaignInfluencer
      };
    } catch (error) {
      console.error('Error in applyForCampaign:', error);
      return {
        success: false,
        message: 'Erro ao se candidatar para a campanha. Tente novamente.'
      };
    }
  }
};