# Estrutura do Banco de Dados crIAdores (Baseado no Schema Fornecido pelo Usuário)

Este documento descreve a estrutura do banco de dados do projeto crIAdores, baseando-se no schema atual fornecido pelo usuário e identificando áreas para alinhamento com os requisitos completos da plataforma.

**Última Atualização:** 10/05/2025

## Tabelas de Usuários e Perfis (Conforme Schema do Usuário)

### `profiles`
- `id`: UUID, PK, referencia `auth.users(id)`
- `role`: USER-DEFINED (`user_role`), NOT NULL
- `full_name`: TEXT, NOT NULL
- `email`: TEXT, NOT NULL, UNIQUE
- `phone`: TEXT (opcional)
- `profile_image_url`: TEXT (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()
- `password_changed`: BOOLEAN, default false
- `profile_type`: USER-DEFINED (`user_role`), NOT NULL, default 'restaurant'::user_role (*Nota: `role` parece ser o campo primário para tipo de usuário*)
- `profile_data`: JSONB, default '{}'::jsonb (para dados genéricos)
- `phone_verified`: BOOLEAN, default false

### `restaurant_profiles`
- `id`: UUID, PK, FK para `profiles(id)`
- `business_name`: TEXT, NOT NULL
- `description`: TEXT (opcional)
- `cuisine_type`: TEXT (opcional)
- `address`: TEXT (opcional)
- `city`: TEXT, NOT NULL
- `state`: TEXT, NOT NULL
- `postal_code`: TEXT (opcional)
- `latitude`: NUMERIC (opcional)
- `longitude`: NUMERIC (opcional)
- `website`: TEXT (opcional)
- `operating_hours`: JSONB (opcional)
- `instagram_url`: TEXT (opcional)
- `facebook_url`: TEXT (opcional)
- `tiktok_url`: TEXT (opcional)
- `is_verified`: BOOLEAN, default false
- `verification_date`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()
- **Campos Adicionais Propostos (para alinhar com a jornada):**
  - `cnpj`: TEXT (opcional, UNIQUE, INDEXED)
  - `logotipo_url`: TEXT (opcional)
  - `ambiente_fotos_urls`: JSONB (array de URLs, opcional)
  - `pratos_fotos_urls`: JSONB (array de URLs, opcional)
  - `faixa_de_preco`: TEXT (e.g., '$', '$$', '$$$') (opcional)
  - `publico_alvo_descricao`: TEXT (opcional)
  - `diferenciais_descricao`: TEXT (opcional)
  - `restricoes_comunicacao`: TEXT (opcional)
  - `must_haves_comunicacao`: TEXT (opcional)
  - `onboarding_details`: JSONB (para Diagnóstico Inicial, Benchmark, Plano de Sucesso)

### `influencer_profiles`
- `id`: UUID, PK, FK para `profiles(id)`
- `bio`: TEXT (opcional)
- `content_niche`: TEXT[] (ARRAY, opcional)
- `primary_platform`: TEXT, NOT NULL
- `instagram_username`: TEXT (opcional, UNIQUE)
- `tiktok_username`: TEXT (opcional, UNIQUE)
- `location_city`: TEXT, NOT NULL
- `location_state`: TEXT, NOT NULL
- `avg_engagement_rate`: NUMERIC (opcional)
- `follower_count`: INTEGER (opcional)
- `is_verified`: BOOLEAN, default false
- `verification_date`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()
- **Campos Adicionais Propostos:**
  - `portfolio_urls`: JSONB (array de links)
  - (*`bank_details` é coberto pela tabela `bank_accounts`*)

### `bank_accounts`
- `id`: UUID, PK, default uuid_generate_v4()
- `user_id`: UUID, NOT NULL, FK para `influencer_profiles(id)`
- `bank_name`: TEXT, NOT NULL
- `bank_code`: TEXT, NOT NULL
- `branch_number`: TEXT, NOT NULL
- `account_number`: TEXT, NOT NULL
- `account_type`: TEXT, NOT NULL
- `document_type`: TEXT, NOT NULL
- `document_number`: TEXT, NOT NULL
- `pix_key`: TEXT (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()

### `social_connections` (Mapeia para `social_api_tokens` do schema do usuário, com ajustes)
- `id`: UUID, PK, default uuid_generate_v4()
- `user_id`: UUID, NOT NULL, FK para `profiles(id)` (*Schema do usuário tem FK para `influencers(id)` que deve ser atualizado para `profiles(id)` ou `influencer_profiles(id)`*)
- `platform`: TEXT, NOT NULL
- `platform_user_id`: TEXT, NOT NULL
- `username`: TEXT, NOT NULL
- `access_token`: TEXT, NOT NULL (DEVE SER CRIPTOGRAFADO)
- `refresh_token`: TEXT (opcional, DEVE SER CRIPTOGRAFADO)
- `token_expires_at` / `expires_at` (do schema do usuário): TIMESTAMPZ (opcional)
- `token_scopes`: TEXT[] (opcional)
- `is_active`: BOOLEAN, default true
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()
- UNIQUE (`user_id`, `platform`)

## Tabelas de Assinaturas e Pagamentos (Conforme Schema do Usuário)

### `subscription_plans`
- `id`: UUID, PK, default uuid_generate_v4()
- `name`: TEXT, NOT NULL, UNIQUE
- `description`: TEXT, NOT NULL
- `price`: NUMERIC, NOT NULL
- `influencer_limit`: INTEGER, NOT NULL
- `is_active`: BOOLEAN, default true
- `features`: JSONB (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()

### `restaurant_subscriptions`
- `id`: UUID, PK, default uuid_generate_v4()
- `restaurant_id`: UUID, NOT NULL, FK para `restaurant_profiles(id)`
- `plan_id`: UUID, NOT NULL, FK para `subscription_plans(id)`
- `status`: TEXT, NOT NULL
- `start_date`: TIMESTAMPZ, NOT NULL
- `end_date`: TIMESTAMPZ (opcional)
- `influencers_used`: INTEGER, default 0
- `is_auto_renew`: BOOLEAN, default true
- `payment_method_id`: TEXT (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()

### `payments`
- `id`: UUID, PK, default uuid_generate_v4()
- `subscription_id`: UUID (opcional), FK para `restaurant_subscriptions(id)`
- `campaign_id`: UUID (opcional), FK para `campaigns(id)`
- `payout_participant_id`: UUID (opcional), FK para `campaign_participants(id)` (*Proposta para pagamentos a influenciadores*)
- `payment_type`: TEXT, NOT NULL
- `amount`: NUMERIC, NOT NULL
- `currency`: TEXT, default 'BRL'
- `payment_method`: TEXT, NOT NULL
- `payment_status`: TEXT, NOT NULL
- `transaction_id`: TEXT (opcional)
- `receipt_url`: TEXT (opcional)
- `paid_at`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()

## Tabelas de Campanhas, Conteúdo e Participação (Conforme Schema do Usuário, com Propostas)

### `campaigns`
- `id`: UUID, PK, default uuid_generate_v4()
- `restaurant_id`: UUID, NOT NULL, FK para `restaurant_profiles(id)` (*Schema do usuário tem FK para `restaurants(id)` que deve ser atualizado*)
- `name`: TEXT, NOT NULL
- `description`: TEXT, NOT NULL
- `briefing`: TEXT, NOT NULL
- `requirements`: JSONB (opcional, para dados estruturados do briefing)
- `status`: USER-DEFINED (`campaign_status`), NOT NULL, default 'draft'
- `start_date`: TIMESTAMPZ (opcional)
- `end_date`: TIMESTAMPZ (opcional)
- `campaign_goal`: TEXT (opcional)
- `budget`: NUMERIC (opcional)
- `influencer_count_target`: INTEGER, NOT NULL
- `hashtags`: TEXT[] (opcional)
- `mentions`: TEXT[] (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()
- **Campos Adicionais Propostos (para alinhar com a jornada):**
  - `influencer_selection_deadline`: TIMESTAMPZ (opcional)
  - `content_submission_deadline`: TIMESTAMPZ (opcional)
  - `posting_period_start`: TIMESTAMPZ (opcional)
  - `posting_period_end`: TIMESTAMPZ (opcional)

### `campaign_participants`
- `id`: UUID, PK, default uuid_generate_v4()
- `campaign_id`: UUID, NOT NULL, FK para `campaigns(id)`
- `profile_id`: UUID, NOT NULL, FK para `profiles(id)` (do influenciador)
- `status`: TEXT, NOT NULL, default 'invited' (e.g., 'invited', 'applied', 'shortlisted', 'accepted_by_restaurant', 'confirmed_by_influencer', 'active_in_campaign', 'content_submitted', 'content_approved', 'post_published', 'completed', 'declined_by_influencer', 'rejected_by_restaurant', 'dropped_out')
- `role`: TEXT, NOT NULL, default 'influencer'
- `current_rank`: INTEGER (opcional)
- `previous_rank`: INTEGER (opcional)
- `metrics`: JSONB, default '{}'::jsonb (para pontos, etc.)
- `payment_status`: TEXT (opcional)
- `payment_amount`: NUMERIC (opcional)
- `payment_date`: TIMESTAMPZ (opcional)
- `invited_at`: TIMESTAMPZ (opcional)
- `accepted_at`: TIMESTAMPZ (opcional)
- `completed_at`: TIMESTAMPZ (opcional)
- `last_rank_update`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()
- UNIQUE (`campaign_id`, `profile_id`)
- **Campos Adicionais Propostos (para alinhar com a jornada):**
  - `application_date`: TIMESTAMPZ (se aplicável)
  - `influencer_pitch`: TEXT (opcional)
  - `restaurant_feedback_on_application`: TEXT (opcional)

### `campaign_content`
- `id`: UUID, PK, default uuid_generate_v4()
- `campaign_id`: UUID, NOT NULL, FK para `campaigns(id)`
- `influencer_id`: UUID, NOT NULL, FK para `profiles(id)` (*Idealmente FK para `campaign_participants(id)`*)
- `content_url`: TEXT, NOT NULL
- `content_type`: TEXT, NOT NULL (e.g., 'video', 'image')
- `status`: TEXT, NOT NULL, default 'pending' (e.g., 'pending_review', 'revision_requested', 'approved', 'rejected')
- `feedback`: TEXT (opcional)
- `version`: INTEGER, default 1
- `created_at`: TIMESTAMPZ, default now(), NOT NULL
- `updated_at`: TIMESTAMPZ, default now(), NOT NULL
- `approved_at`: TIMESTAMPZ (opcional)
- `scheduled_post_date`: TIMESTAMPZ (opcional)
- `actual_post_date`: TIMESTAMPZ (opcional)
- `post_url`: TEXT (opcional)
- `engagement_metrics`: JSONB (opcional)
- **Coluna Adicional Necessária:**
  - `description`: TEXT (opcional, descrição do conteúdo pelo influenciador)
  - `caption_suggestion`: TEXT (opcional)

### `content_versions`
- `id`: UUID, PK, default uuid_generate_v4()
- `content_id`: UUID, NOT NULL, FK para `campaign_content(id)`
- `version`: INTEGER, NOT NULL
- `content_url`: TEXT, NOT NULL
- `created_at`: TIMESTAMPZ, default now(), NOT NULL
- `created_by`: UUID, NOT NULL, FK para `profiles(id)`
- **Campos Adicionais Propostos:**
  - `description`: TEXT (opcional)
  - `caption_suggestion`: TEXT (opcional)

### `content_feedback`
- `id`: UUID, PK, default uuid_generate_v4()
- `content_id`: UUID, NOT NULL, FK para `campaign_content(id)`
- `user_id`: UUID, NOT NULL, FK para `profiles(id)` (quem deu o feedback)
- `comment`: TEXT, NOT NULL
- `timestamp_seconds`: INTEGER (opcional)
- `position_x`: INTEGER (opcional)
- `position_y`: INTEGER (opcional)
- `created_at`: TIMESTAMPZ, default now(), NOT NULL
- **Campo Adicional Proposto:**
  - `resolved_in_version`: INTEGER (opcional)

### `posts`
- `id`: UUID, PK, default uuid_generate_v4()
- `campaign_influencer_id`: UUID, NOT NULL, FK para `campaign_participants(id)` (*Atualizar FK de `campaign_influencers`*)
- `platform`: TEXT, NOT NULL
- `platform_post_id`: TEXT (opcional)
- `post_url`: TEXT, NOT NULL, UNIQUE
- `post_type`: TEXT (opcional)
- `caption`: TEXT (opcional)
- `media_urls`: TEXT[] (opcional)
- `is_verified`: BOOLEAN, default false
- `verification_notes`: TEXT (opcional)
- `likes_count`: INTEGER, default 0
- `comments_count`: INTEGER, default 0
- `shares_count`: INTEGER, default 0
- `views_count`: INTEGER, default 0
- `saves_count`: INTEGER, default 0
- `engagement_rate`: NUMERIC (opcional)
- `external_post_id`: TEXT (opcional)
- `post_status`: TEXT (opcional)
- `last_fetched_at`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- `updated_at`: TIMESTAMPZ, NOT NULL, default now()

### `post_metrics_history`
- `id`: UUID, PK, default uuid_generate_v4()
- `post_id`: UUID, NOT NULL, FK para `posts(id)`
- `likes_count`: INTEGER, NOT NULL, default 0
- `comments_count`: INTEGER, NOT NULL, default 0
- `shares_count`: INTEGER, NOT NULL, default 0
- `views_count`: INTEGER, NOT NULL, default 0
- `saves_count`: INTEGER, default 0
- `reach`: INTEGER (opcional)
- `impressions`: INTEGER (opcional)
- `engagement_rate`: NUMERIC (opcional)
- `snapshot_at`: TIMESTAMPZ, NOT NULL, default now()
- `raw_data`: JSONB (opcional)

## Tabelas de Agendamento e Lembretes (Conforme Schema do Usuário)

### `campaign_available_dates`
- `id`: UUID, PK, default uuid_generate_v4()
- `campaign_id`: UUID, NOT NULL, FK para `campaigns(id)`
- `date`: DATE, NOT NULL
- `start_time`: TIME, NOT NULL
- `end_time`: TIME, NOT NULL
- `booked`: BOOLEAN, default false
- `booked_by`: UUID (opcional), FK para `profiles(id)` (*Idealmente `campaign_participants(id)`*)
- `created_at`: TIMESTAMPZ, default now()
- `updated_at`: TIMESTAMPZ, default now()

### `campaign_schedule`
- `id`: UUID, PK, default uuid_generate_v4()
- `campaign_id`: UUID, NOT NULL, FK para `campaigns(id)`
- `influencer_id`: UUID, NOT NULL, FK para `profiles(id)` (*Idealmente `participant_id` FK para `campaign_participants(id)`*)
- `scheduled_date`: TIMESTAMPZ, NOT NULL
- `status`: TEXT, NOT NULL, default 'pending'
- `notes`: TEXT (opcional)
- `created_at`: TIMESTAMPZ, default now()
- `updated_at`: TIMESTAMPZ, default now()
- `completed_at`: TIMESTAMPZ (opcional)
- `cancelled_at`: TIMESTAMPZ (opcional)
- `cancellation_reason`: TEXT (opcional)

### `schedule_reminders`
- `id`: UUID, PK, default uuid_generate_v4()
- `schedule_id`: UUID, NOT NULL, FK para `campaign_schedule(id)`
- `reminder_type`: TEXT, NOT NULL
- `sent`: BOOLEAN, default false
- `scheduled_for`: TIMESTAMPZ, NOT NULL
- `sent_at`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, default now()

## Tabelas de Notificações e WhatsApp (Conforme Schema do Usuário)

### `user_notification_preferences`
- `id`: UUID, PK, default uuid_generate_v4()
- `user_id`: UUID, NOT NULL, FK para `profiles(id)`, UNIQUE
- `whatsapp_enabled`: BOOLEAN, default true
- `whatsapp_campaign_invite`: BOOLEAN, default true
- `whatsapp_campaign_status_change`: BOOLEAN, default true
- `whatsapp_ranking_update`: BOOLEAN, default true
- `whatsapp_payment_status`: BOOLEAN, default true
- `whatsapp_post_approval`: BOOLEAN, default true
- **Campo Adicional Proposto:**
  - `whatsapp_content_reviewed`: BOOLEAN, default true (para influenciador)

### `whatsapp_templates`
- `id`: UUID, PK, default uuid_generate_v4()
- `name`: TEXT, NOT NULL, UNIQUE
- `description`: TEXT (opcional)
- `template_id`: TEXT, NOT NULL (ID do template no provedor)
- `language`: TEXT, NOT NULL, default 'pt_BR'
- `components`: JSONB (opcional, estrutura do template)
- `status`: TEXT, NOT NULL, default 'active'
- `created_at`: TIMESTAMPZ, default now()
- `updated_at`: TIMESTAMPZ, default now()

### `whatsapp_messages`
- `id`: UUID, PK, default uuid_generate_v4() (*Schema do usuário tem `id` como UUID, mas `whatsapp_verification` tem SERIAL. Padronizar para UUID.*)
- `user_id`: UUID, NOT NULL, FK para `profiles(id)`
- `direction`: TEXT, NOT NULL
- `message`: TEXT, NOT NULL (*Renomear para `content_body` ou similar para clareza*)
- `whatsapp_message_id`: TEXT (opcional)
- `status`: TEXT (opcional)
- `sent_at`: TIMESTAMPZ, default now()
- `delivered_at`: TIMESTAMPZ (opcional)
- `read_at`: TIMESTAMPZ (opcional)
- `created_at`: TIMESTAMPZ, NOT NULL, default now()
- **Campos Adicionais Propostos (para alinhar com minha versão anterior):**
  - `phone_number`: TEXT, NOT NULL, INDEXED (em vez de depender apenas de `user_id`)
  - `message_type`: TEXT, NOT NULL (e.g., 'template', 'text')
  - `template_name_used`: TEXT (opcional)
  - `metadata`: JSONB

### `whatsapp_verification`
- `id`: UUID, PK, default uuid_generate_v4() (*Schema do usuário tem SERIAL*)
- `user_id`: UUID, NOT NULL, FK para `profiles(id)`
- `phone`: TEXT, NOT NULL
- `verification_code`: TEXT, NOT NULL
- `verified`: BOOLEAN, default false
- `created_at`: TIMESTAMPZ, default now()
- `verified_at`: TIMESTAMPZ (opcional)
- `expires_at`: TIMESTAMPZ, NOT NULL

## Outras Tabelas (Conforme Schema do Usuário, com notas)

### `digital_presence`
- `restaurant_id`: UUID, NOT NULL, FK para `restaurants(id)` (*DEVE SER ATUALIZADO para `restaurant_profiles(id)`*)

### `campaign_rankings` & `campaign_ranking_entries`
- `campaign_ranking_entries.influencer_id`: UUID, FK para `influencers(id)` (*DEVE SER ATUALIZADO para `profiles(id)` ou `campaign_participants(profile_id)`*)

### `influencer_points` & `points_history`
- FKs para `influencer_profiles(id)`.

### `notifications` & `notification_types`
- `notifications.user_id` FK para `profiles(id)`.

### `audit_logs`
- `audit_logs.user_id` FK para `profiles(id)`.

### `restaurants` (LEGACY - Presumivelmente)
- Tabela simplificada. A fonte primária deve ser `restaurant_profiles`.

### `influencers` (LEGACY - Presumivelmente)
- Tabela simplificada. A fonte primária deve ser `influencer_profiles`.

### `restaurant_influencers` (LEGACY - Presumivelmente)
- Tabela de associação. Funcionalidade coberta por `campaign_participants` ou uma nova tabela de "conexões diretas" se necessário fora de campanhas.

### `content_count` (LEGACY - Presumivelmente)
- Pode ser calculado dinamicamente ou integrado em `restaurant_profiles`.

### `influencer_coupons` & `coupon_redemptions`
- Tabelas para funcionalidade de cupons.

## Observações e Recomendações Chave (Baseado no Schema Atual)

1.  **Fonte da Verdade para Perfis**: `profiles`, `restaurant_profiles`, e `influencer_profiles` são as tabelas corretas. As tabelas `restaurants` e `influencers` devem ser consideradas legadas e seus dados migrados. **Ação Crítica**: Atualizar todas as Foreign Keys que apontam para `restaurants(id)` ou `influencers(id)` para que apontem para `restaurant_profiles(id)` ou `influencer_profiles(id)` (ou `profiles(id)` se mais genérico) respectivamente. Isso afeta `digital_presence`, `campaigns` (já parece usar `restaurant_profiles` no schema atual, mas a FK para `restaurants` também existe), `campaign_ranking_entries`, `social_connections` (ou `social_api_tokens`).
2.  **`campaign_participants` como Hub**: Esta tabela é central para o fluxo de influenciadores em campanhas. Tabelas como `campaign_content`, `campaign_schedule`, `posts` devem ter FKs para `campaign_participants(id)` para contextualizar a ação à participação específica.
3.  **`campaign_content.description`**: Esta coluna, necessária para a funcionalidade, está faltando no schema atual e precisa ser adicionada.
4.  **WhatsApp Templates (`whatsapp_templates` vs. `config.ts`):** O `WhatsAppService` deve ser refatorado para usar a tabela `whatsapp_templates` para buscar `template_id` e `components` em vez de depender apenas dos nomes em `WHATSAPP_API_CONFIG.templates`. O config pode manter os nomes como chaves para buscar no DB.
5.  **Preferências de Notificação (`user_notification_preferences`):** Todos os serviços que enviam notificações (especialmente `ContentApprovalService` e `WhatsAppService`) devem consultar esta tabela para verificar se o usuário deseja receber a notificação específica.
6.  **Segurança de Tokens**: `social_connections.access_token` e `refresh_token` devem ser armazenados criptografados.
7.  **Consistência de PKs**: Padronizar para UUID onde apropriado (e.g., `whatsapp_messages`, `whatsapp_verification` usam SERIAL no schema do usuário, mas outras tabelas usam UUID).
8.  **Revisão `campaign_content` vs. `posts`**: Clarificar se `campaign_content` armazena metadados do post publicado (`actual_post_date`, `post_url`, `engagement_metrics`) ou se `posts` é a única fonte para posts publicados. Evitar duplicação.
9.  **Campos Propostos**: Revisar e implementar os "Campos Adicionais Propostos" nas tabelas de perfil e campanha para cobrir todos os dados da jornada do cliente.
10. **JSONB Schemas**: Definir e documentar os schemas esperados para todos os campos JSONB.
