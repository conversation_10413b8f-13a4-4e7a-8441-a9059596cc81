-- Adicionar templates de notificação para WhatsApp

-- Verificar se já existem templates
DO $$
DECLARE
    template_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO template_count FROM notification_types;
    
    -- Só inserir se não houver templates
    IF template_count = 0 THEN
        -- Template para convite de campanha
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'campaign_invite',
            'Enviado quando um influenciador é convidado para uma campanha',
            'Convite para campanha: {{campaign_name}}',
            'Olá! Você foi convidado para participar da campanha "{{campaign_name}}" do restaurante {{restaurant_name}}. Acesse a plataforma crIAdores para aceitar o convite.',
            TRUE
        );
        
        -- Template para mudança de status de campanha
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'campaign_status_change',
            'Enviado quando o status de participação em uma campanha muda',
            'Atualização na campanha: {{campaign_name}}',
            'O status da sua participação na campanha "{{campaign_name}}" do restaurante {{restaurant_name}} foi atualizado para: {{status}}.',
            TRUE
        );
        
        -- Template para atualização de ranking
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'ranking_update',
            'Enviado quando há uma atualização no ranking do influenciador',
            'Sua posição no ranking foi atualizada!',
            'Parabéns! Você subiu para a posição {{position}} no ranking da campanha "{{campaign_name}}". Continue assim!',
            TRUE
        );
        
        -- Template para status de pagamento
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'payment_status',
            'Enviado quando há uma atualização no status de pagamento',
            'Atualização de pagamento',
            'Seu pagamento de R$ {{amount}} referente à campanha "{{campaign_name}}" foi {{status}}.',
            TRUE
        );
        
        -- Template para aprovação de post
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'post_approval',
            'Enviado quando um post é aprovado ou rejeitado',
            'Atualização sobre seu post',
            'Seu post para a campanha "{{campaign_name}}" foi {{status}}. {{notes}}',
            TRUE
        );
        
        -- Template para expiração de assinatura
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'subscription_expiry',
            'Enviado quando a assinatura está próxima de expirar',
            'Sua assinatura está prestes a expirar',
            'Atenção! Sua assinatura expira em {{days}} dias. Renove agora para continuar aproveitando todos os benefícios.',
            TRUE
        );
        
        -- Template para novo influenciador na campanha (para restaurantes)
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'new_influencer_joined',
            'Enviado ao restaurante quando um influenciador aceita participar da campanha',
            'Novo influenciador na campanha: {{campaign_name}}',
            'O influenciador {{influencer_name}} ({{influencer_followers}} seguidores) aceitou participar da sua campanha "{{campaign_name}}".',
            TRUE
        );
        
        -- Template para novo post criado (para restaurantes)
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'new_post_created',
            'Enviado ao restaurante quando um influenciador cria um novo post',
            'Novo post na campanha: {{campaign_name}}',
            'O influenciador {{influencer_name}} criou um novo post para a campanha "{{campaign_name}}". Acesse a plataforma para revisar.',
            TRUE
        );
        
        -- Template para relatório semanal (para restaurantes)
        INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
        VALUES (
            'weekly_report',
            'Enviado semanalmente com um resumo das campanhas ativas',
            'Relatório semanal de campanhas',
            'Seu relatório semanal está disponível. Campanha "{{campaign_name}}": {{influencer_count}} influenciadores, {{post_count}} posts, {{engagement}} engajamento total.',
            TRUE
        );
    END IF;
END $$;
