-- Criar tabela para armazenar mensagens de WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    phone_number TEXT,
    direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
    message TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'read', 'failed', 'received')),
    sent_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- <PERSON><PERSON><PERSON> índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_user_id ON whatsapp_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_phone_number ON whatsapp_messages(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_sent_at ON whatsapp_messages(sent_at);

-- Criar tabela para armazenar configurações de notificação
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Configurações gerais
    enable_notifications BOOLEAN DEFAULT TRUE,
    
    -- Configurações de WhatsApp
    whatsapp_enabled BOOLEAN DEFAULT TRUE,
    whatsapp_new_influencer_joined BOOLEAN DEFAULT TRUE,
    whatsapp_new_post_created BOOLEAN DEFAULT TRUE,
    whatsapp_weekly_report BOOLEAN DEFAULT TRUE,
    
    -- Configurações de Email
    email_enabled BOOLEAN DEFAULT TRUE,
    email_new_influencer_joined BOOLEAN DEFAULT TRUE,
    email_new_post_created BOOLEAN DEFAULT TRUE,
    email_weekly_report BOOLEAN DEFAULT TRUE,
    
    -- Metadados
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    -- Restrição de unicidade
    UNIQUE(user_id)
);

-- Criar índice para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);

-- Adicionar colunas para verificação de WhatsApp na tabela de perfis
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_code TEXT,
ADD COLUMN IF NOT EXISTS verification_sent_at TIMESTAMPTZ;

-- Criar tabela para armazenar templates de notificação
CREATE TABLE IF NOT EXISTS notification_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    template_subject TEXT,
    template_body TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Inserir templates padrão
INSERT INTO notification_types (name, description, template_subject, template_body, is_active)
VALUES 
('welcome', 'Mensagem de boas-vindas', 'Bem-vindo à crIAdores', 'Olá {{name}}, bem-vindo à crIAdores! Estamos felizes em tê-lo conosco.', TRUE),
('new_influencer_joined', 'Novo influenciador entrou na campanha', 'Novo influenciador na campanha', 'Olá {{name}}, o influenciador {{influencer_name}} aceitou participar da sua campanha {{campaign_name}}.', TRUE),
('new_post_created', 'Novo post criado por um influenciador', 'Novo post criado', 'Olá {{name}}, o influenciador {{influencer_name}} criou um novo post para a campanha {{campaign_name}}.', TRUE),
('weekly_report', 'Relatório semanal de campanha', 'Relatório semanal', 'Olá {{name}}, aqui está o relatório semanal da sua campanha {{campaign_name}}. Visualizações: {{views}}, Engajamento: {{engagement}}%.', TRUE)
ON CONFLICT (name) DO NOTHING;
