-- Insert fake data for WhatsApp integration testing

-- 1. Update some profiles with phone numbers and verification status
UPDATE profiles
SET 
  phone = '+5511999999999',
  phone_verified = true
WHERE email = '<EMAIL>';

-- 2. Insert fake WhatsApp messages
INSERT INTO whatsapp_messages (
  id,
  user_id,
  phone_number,
  direction,
  message,
  status,
  sent_at,
  delivered_at,
  read_at
)
SELECT
  gen_random_uuid(),
  (SELECT id FROM profiles WHERE email = '<EMAIL>'),
  '+5511999999999',
  CASE WHEN random() > 0.5 THEN 'inbound' ELSE 'outbound' END,
  CASE 
    WHEN i % 5 = 0 THEN '<PERSON><PERSON><PERSON>, gostaria de saber mais sobre as campanhas disponíveis'
    WHEN i % 5 = 1 THEN 'Como faço para participar da campanha do restaurante Boussole?'
    WHEN i % 5 = 2 THEN 'Quando será o próximo pagamento?'
    WHEN i % 5 = 3 THEN 'Obrigado pela informação!'
    ELSE 'Preciso de ajuda com minha conta'
  END,
  CASE 
    WHEN random() > 0.7 THEN 'sent'
    WHEN random() > 0.4 THEN 'delivered'
    ELSE 'read'
  END,
  now() - (i || ' hours')::interval,
  CASE WHEN random() > 0.3 THEN now() - (i || ' hours')::interval + '5 minutes'::interval ELSE NULL END,
  CASE WHEN random() > 0.6 THEN now() - (i || ' hours')::interval + '10 minutes'::interval ELSE NULL END
FROM generate_series(1, 20) i;

-- 3. Insert fake notification settings
INSERT INTO notification_settings (
  user_id,
  enable_notifications,
  whatsapp_enabled,
  whatsapp_new_influencer_joined,
  whatsapp_new_post_created,
  whatsapp_weekly_report,
  email_enabled,
  email_new_influencer_joined,
  email_new_post_created,
  email_weekly_report
)
VALUES (
  (SELECT id FROM profiles WHERE email = '<EMAIL>'),
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true
)
ON CONFLICT (user_id) DO UPDATE SET
  enable_notifications = true,
  whatsapp_enabled = true,
  whatsapp_new_influencer_joined = true,
  whatsapp_new_post_created = true,
  whatsapp_weekly_report = true,
  email_enabled = true,
  email_new_influencer_joined = true,
  email_new_post_created = true,
  email_weekly_report = true;

-- 4. Insert fake notification templates
INSERT INTO notification_types (
  name,
  description,
  template_subject,
  template_body,
  is_active
)
VALUES
  ('welcome', 'Mensagem de boas-vindas', 'Bem-vindo à crIAdores', 'Olá {{name}}, bem-vindo à crIAdores! Estamos felizes em tê-lo conosco.', true),
  ('verification_code', 'Código de verificação', 'Seu código de verificação', 'Seu código de verificação do crIAdores é: {{code}}. Este código expira em 10 minutos.', true),
  ('new_influencer_joined', 'Novo influenciador entrou na campanha', 'Novo influenciador na campanha', 'Olá {{name}}, o influenciador {{influencer_name}} aceitou participar da sua campanha {{campaign_name}}.', true),
  ('new_post_created', 'Novo post criado por um influenciador', 'Novo post criado', 'Olá {{name}}, o influenciador {{influencer_name}} criou um novo post para a campanha {{campaign_name}}.', true),
  ('weekly_report', 'Relatório semanal de campanha', 'Relatório semanal', 'Olá {{name}}, aqui está o relatório semanal da sua campanha {{campaign_name}}. Visualizações: {{views}}, Engajamento: {{engagement}}%.', true)
ON CONFLICT (name) DO UPDATE SET
  description = EXCLUDED.description,
  template_subject = EXCLUDED.template_subject,
  template_body = EXCLUDED.template_body,
  is_active = EXCLUDED.is_active;

-- 5. Insert fake campaign settings for existing campaigns
INSERT INTO campaign_settings (
  campaign_id,
  enable_whatsapp_notifications,
  notify_on_influencer_join,
  notify_on_post_created,
  notify_on_ranking_change,
  send_weekly_reports
)
SELECT
  id,
  true,
  true,
  true,
  random() > 0.5,
  true
FROM campaigns
ON CONFLICT (campaign_id) DO UPDATE SET
  enable_whatsapp_notifications = true,
  notify_on_influencer_join = true,
  notify_on_post_created = true,
  notify_on_ranking_change = EXCLUDED.notify_on_ranking_change,
  send_weekly_reports = true;

-- 6. Insert fake campaign reports
INSERT INTO campaign_reports (
  campaign_id,
  report_type,
  report_data,
  sent_at,
  sent_to,
  delivery_method
)
SELECT
  id,
  'weekly',
  json_build_object(
    'period', json_build_object(
      'start_date', now() - '7 days'::interval,
      'end_date', now()
    ),
    'campaign_id', id,
    'total_posts', floor(random() * 20)::int,
    'active_influencers', floor(random() * 10)::int,
    'total_likes', floor(random() * 1000)::int,
    'total_comments', floor(random() * 200)::int,
    'total_shares', floor(random() * 50)::int,
    'total_views', floor(random() * 5000)::int,
    'total_engagement', floor(random() * 1200)::int
  ),
  now() - (floor(random() * 7) || ' days')::interval,
  '+5511999999999',
  'whatsapp'
FROM campaigns
LIMIT 5;
