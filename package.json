{"name": "criadores_openrouter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NEXT_TELEMETRY_DISABLED=1 next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:security": "node scripts/run_security_tests.js"}, "engines": {"node": ">=18.0.0"}, "eslintConfig": {"extends": ["next"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "@next/next/no-html-link-for-pages": "off", "react-hooks/exhaustive-deps": "off", "prefer-const": "off"}}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "5.0.1", "@radix-ui/react-alert-dialog": "1.1.13", "@radix-ui/react-avatar": "1.1.9", "@radix-ui/react-checkbox": "1.3.1", "@radix-ui/react-dialog": "1.1.7", "@radix-ui/react-label": "2.1.3", "@radix-ui/react-popover": "1.1.13", "@radix-ui/react-progress": "1.1.6", "@radix-ui/react-radio-group": "1.3.6", "@radix-ui/react-select": "2.2.4", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-switch": "1.1.4", "@radix-ui/react-tabs": "1.1.4", "@radix-ui/react-tooltip": "1.2.0", "@supabase/auth-helpers-nextjs": "0.10.0", "@supabase/supabase-js": "2.49.4", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "chart.js": "4.4.8", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "dotenv": "16.4.7", "framer-motion": "12.7.2", "lucide-react": "0.487.0", "next": "15.2.4", "next-auth": "4.24.11", "node-fetch": "3.3.2", "react": "^18.3.1", "react-chartjs-2": "5.3.0", "react-day-picker": "9.6.7", "react-dom": "^18.3.1", "react-hook-form": "7.55.0", "react-hot-toast": "2.5.2", "react-icons": "^5.5.0", "tailwind-merge": "3.2.0", "uuid": "11.1.0", "zod": "3.24.4"}, "devDependencies": {"@angular/cli": "19.2.11", "@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0", "@types/jest": "^29.5.12", "@types/node": "20.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/supertest": "^6.0.2", "autoprefixer": "^10.4.19", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "eslint": "^9", "eslint-config-next": "15.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.2.3", "nock": "^13.5.4", "null-loader": "^4.0.1", "postcss": "^8.4.38", "supertest": "^6.3.4", "tailwindcss": "^3.3.0", "ts-jest": "^29.1.2", "typescript": "5.8.3"}}