{"version": 3, "sources": ["../src/index.ts", "../src/collection-legacy.tsx", "../src/collection.tsx", "../src/ordered-dictionary.ts"], "sourcesContent": ["'use client';\nexport { createCollection } from './collection-legacy';\nexport type { CollectionProps } from './collection-legacy';\n\nexport { createCollection as unstable_createCollection } from './collection';\nexport type { CollectionProps as unstable_CollectionProps } from './collection-legacy';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,mCAAAA;AAAA;AAAA;;;ACAA,mBAAkB;AAClB,2BAAmC;AACnC,gCAAgC;AAChC,wBAAsC;AAuChC;AA1BN,SAAS,iBAAiE,MAAc;AAKtF,QAAM,gBAAgB,OAAO;AAC7B,QAAM,CAAC,yBAAyB,qBAAqB,QAAI,yCAAmB,aAAa;AAUzF,QAAM,CAAC,wBAAwB,oBAAoB,IAAI;AAAA,IACrD;AAAA,IACA,EAAE,eAAe,EAAE,SAAS,KAAK,GAAG,SAAS,oBAAI,IAAI,EAAE;AAAA,EACzD;AAEA,QAAM,qBAA2E,CAAC,UAAU;AAC1F,UAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,UAAM,MAAM,aAAAC,QAAM,OAA0B,IAAI;AAChD,UAAM,UAAU,aAAAA,QAAM,OAAgC,oBAAI,IAAI,CAAC,EAAE;AACjE,WACE,4CAAC,0BAAuB,OAAc,SAAkB,eAAe,KACpE,UACH;AAAA,EAEJ;AAEA,qBAAmB,cAAc;AAMjC,QAAM,uBAAuB,OAAO;AAEpC,QAAM,yBAAqB,8BAAW,oBAAoB;AAC1D,QAAM,iBAAiB,aAAAA,QAAM;AAAA,IAC3B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,UAAU,qBAAqB,sBAAsB,KAAK;AAChE,YAAM,mBAAe,2CAAgB,cAAc,QAAQ,aAAa;AACxE,aAAO,4CAAC,sBAAmB,KAAK,cAAe,UAAS;AAAA,IAC1D;AAAA,EACF;AAEA,iBAAe,cAAc;AAM7B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,iBAAiB;AAOvB,QAAM,6BAAyB,8BAAW,cAAc;AACxD,QAAM,qBAAqB,aAAAA,QAAM;AAAA,IAC/B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,UAAU,GAAG,SAAS,IAAI;AACzC,YAAM,MAAM,aAAAA,QAAM,OAAoB,IAAI;AAC1C,YAAM,mBAAe,2CAAgB,cAAc,GAAG;AACtD,YAAM,UAAU,qBAAqB,gBAAgB,KAAK;AAE1D,mBAAAA,QAAM,UAAU,MAAM;AACpB,gBAAQ,QAAQ,IAAI,KAAK,EAAE,KAAK,GAAI,SAAiC,CAAC;AACtE,eAAO,MAAM,KAAK,QAAQ,QAAQ,OAAO,GAAG;AAAA,MAC9C,CAAC;AAED,aACE,4CAAC,0BAAwB,GAAG,EAAE,CAAC,cAAc,GAAG,GAAG,GAAG,KAAK,cACxD,UACH;AAAA,IAEJ;AAAA,EACF;AAEA,qBAAmB,cAAc;AAMjC,WAAS,cAAc,OAAY;AACjC,UAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;AAEvE,UAAM,WAAW,aAAAA,QAAM,YAAY,MAAM;AACvC,YAAM,iBAAiB,QAAQ,cAAc;AAC7C,UAAI,CAAC,eAAgB,QAAO,CAAC;AAC7B,YAAM,eAAe,MAAM,KAAK,eAAe,iBAAiB,IAAI,cAAc,GAAG,CAAC;AACtF,YAAM,QAAQ,MAAM,KAAK,QAAQ,QAAQ,OAAO,CAAC;AACjD,YAAM,eAAe,MAAM;AAAA,QACzB,CAAC,GAAG,MAAM,aAAa,QAAQ,EAAE,IAAI,OAAQ,IAAI,aAAa,QAAQ,EAAE,IAAI,OAAQ;AAAA,MACtF;AACA,aAAO;AAAA,IACT,GAAG,CAAC,QAAQ,eAAe,QAAQ,OAAO,CAAC;AAE3C,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,EAAE,UAAU,oBAAoB,MAAM,gBAAgB,UAAU,mBAAmB;AAAA,IACnF;AAAA,IACA;AAAA,EACF;AACF;;;ACjIA,IAAAC,gBAAkB;AAClB,IAAAC,wBAAmC;AACnC,IAAAC,6BAAgC;AAChC,IAAAC,qBAAsC;;;ACAtC,IAAM,iBAAiB,oBAAI,QAAwC;AAC5D,IAAM,cAAN,MAAM,qBAA0B,IAAU;AAAA,EAC/C;AAAA,EAGA,YAAY,SAA+C;AACzD,UAAM,OAAO;AACb,SAAK,QAAQ,CAAC,GAAG,MAAM,KAAK,CAAC;AAC7B,mBAAe,IAAI,MAAM,IAAI;AAAA,EAC/B;AAAA,EAEA,IAAI,KAAQ,OAAU;AACpB,QAAI,eAAe,IAAI,IAAI,GAAG;AAC5B,UAAI,KAAK,IAAI,GAAG,GAAG;AACjB,aAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,CAAC,IAAI;AAAA,MACxC,OAAO;AACL,aAAK,MAAM,KAAK,GAAG;AAAA,MACrB;AAAA,IACF;AACA,UAAM,IAAI,KAAK,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAe,KAAQ,OAAU;AACtC,UAAM,MAAM,KAAK,IAAI,GAAG;AACxB,UAAM,SAAS,KAAK,MAAM;AAC1B,UAAM,gBAAgB,cAAc,KAAK;AACzC,QAAI,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;AAChE,UAAM,YAAY,cAAc,KAAK,eAAe,SAAS,KAAK;AAElE,QAAI,cAAc,KAAK,QAAS,OAAO,cAAc,KAAK,OAAO,KAAM,cAAc,IAAI;AACvF,WAAK,IAAI,KAAK,KAAK;AACnB,aAAO;AAAA,IACT;AAEA,UAAM,OAAO,KAAK,QAAQ,MAAM,IAAI;AAMpC,QAAI,gBAAgB,GAAG;AACrB;AAAA,IACF;AAEA,UAAM,OAAO,CAAC,GAAG,KAAK,KAAK;AAC3B,QAAI;AACJ,QAAI,aAAa;AACjB,aAAS,IAAI,aAAa,IAAI,MAAM,KAAK;AACvC,UAAI,gBAAgB,GAAG;AACrB,YAAI,UAAU,KAAK,CAAC;AACpB,YAAI,KAAK,CAAC,MAAM,KAAK;AACnB,oBAAU,KAAK,IAAI,CAAC;AAAA,QACtB;AACA,YAAI,KAAK;AAEP,eAAK,OAAO,GAAG;AAAA,QACjB;AACA,oBAAY,KAAK,IAAI,OAAO;AAC5B,aAAK,IAAI,KAAK,KAAK;AAAA,MACrB,OAAO;AACL,YAAI,CAAC,cAAc,KAAK,IAAI,CAAC,MAAM,KAAK;AACtC,uBAAa;AAAA,QACf;AACA,cAAM,aAAa,KAAK,aAAa,IAAI,IAAI,CAAC;AAC9C,cAAM,eAAe;AACrB,oBAAY,KAAK,IAAI,UAAU;AAC/B,aAAK,OAAO,UAAU;AACtB,aAAK,IAAI,YAAY,YAAY;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAAe,KAAQ,OAAU;AACpC,UAAM,OAAO,IAAI,aAAY,IAAI;AACjC,SAAK,OAAO,OAAO,KAAK,KAAK;AAC7B,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,KAAQ;AACb,UAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,IAAI;AACxC,QAAI,QAAQ,GAAG;AACb,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,KAAQ,QAAW,OAAU;AACrC,UAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG;AACpC,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,OAAO,OAAO,QAAQ,KAAK;AAAA,EACzC;AAAA,EAEA,MAAM,KAAQ;AACZ,QAAI,QAAQ,KAAK,MAAM,QAAQ,GAAG;AAClC,YAAQ,UAAU,MAAM,UAAU,KAAK,OAAO,IAAI,KAAK,QAAQ;AAC/D,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,KAAQ,QAAW,OAAU;AACpC,UAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG;AACpC,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,OAAO,QAAQ,GAAG,QAAQ,KAAK;AAAA,EAC7C;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,QAAQ,CAAC;AAAA,EACvB;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,QAAQ,EAAE;AAAA,EACxB;AAAA,EAEA,QAAQ;AACN,SAAK,QAAQ,CAAC;AACd,WAAO,MAAM,MAAM;AAAA,EACrB;AAAA,EAEA,OAAO,KAAQ;AACb,UAAM,UAAU,MAAM,OAAO,GAAG;AAChC,QAAI,SAAS;AACX,WAAK,MAAM,OAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,CAAC;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,OAAe;AACtB,UAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,QAAI,QAAQ,QAAW;AACrB,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,GAAG,OAAe;AAChB,UAAM,MAAM,GAAG,KAAK,OAAO,KAAK;AAChC,QAAI,QAAQ,QAAW;AACrB,aAAO,KAAK,IAAI,GAAG;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,QAAQ,OAAmC;AACzC,UAAM,MAAM,GAAG,KAAK,OAAO,KAAK;AAChC,QAAI,QAAQ,QAAW;AACrB,aAAO,CAAC,KAAK,KAAK,IAAI,GAAG,CAAE;AAAA,IAC7B;AAAA,EACF;AAAA,EAEA,QAAQ,KAAQ;AACd,WAAO,KAAK,MAAM,QAAQ,GAAG;AAAA,EAC/B;AAAA,EAEA,MAAM,OAAe;AACnB,WAAO,GAAG,KAAK,OAAO,KAAK;AAAA,EAC7B;AAAA,EAEA,KAAK,KAAQ,QAAgB;AAC3B,UAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,QAAQ;AACnB,QAAI,OAAO,EAAG,QAAO;AACrB,QAAI,QAAQ,KAAK,KAAM,QAAO,KAAK,OAAO;AAC1C,WAAO,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA,EAEA,QAAQ,KAAQ,QAAgB;AAC9B,UAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,QAAQ;AACnB,QAAI,OAAO,EAAG,QAAO;AACrB,QAAI,QAAQ,KAAK,KAAM,QAAO,KAAK,OAAO;AAC1C,WAAO,KAAK,MAAM,IAAI;AAAA,EACxB;AAAA,EAEA,KACE,WACA,SACA;AACA,QAAI,QAAQ;AACZ,eAAW,SAAS,MAAM;AACxB,UAAI,QAAQ,MAAM,WAAW,SAAS,CAAC,OAAO,OAAO,IAAI,CAAC,GAAG;AAC3D,eAAO;AAAA,MACT;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,UACE,WACA,SACA;AACA,QAAI,QAAQ;AACZ,eAAW,SAAS,MAAM;AACxB,UAAI,QAAQ,MAAM,WAAW,SAAS,CAAC,OAAO,OAAO,IAAI,CAAC,GAAG;AAC3D,eAAO;AAAA,MACT;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAYA,OACE,WACA,SACA;AACA,UAAM,UAAyB,CAAC;AAChC,QAAI,QAAQ;AACZ,eAAW,SAAS,MAAM;AACxB,UAAI,QAAQ,MAAM,WAAW,SAAS,CAAC,OAAO,OAAO,IAAI,CAAC,GAAG;AAC3D,gBAAQ,KAAK,KAAK;AAAA,MACpB;AACA;AAAA,IACF;AACA,WAAO,IAAI,aAAY,OAAO;AAAA,EAChC;AAAA,EAEA,IACE,YACA,SACmB;AACnB,UAAM,UAAoB,CAAC;AAC3B,QAAI,QAAQ;AACZ,eAAW,SAAS,MAAM;AACxB,cAAQ,KAAK,CAAC,MAAM,CAAC,GAAG,QAAQ,MAAM,YAAY,SAAS,CAAC,OAAO,OAAO,IAAI,CAAC,CAAC,CAAC;AACjF;AAAA,IACF;AACA,WAAO,IAAI,aAAY,OAAO;AAAA,EAChC;AAAA,EA6BA,UACK,MASH;AACA,UAAM,CAAC,YAAY,YAAY,IAAI;AACnC,QAAI,QAAQ;AACZ,QAAI,cAAc,gBAAgB,KAAK,GAAG,CAAC;AAC3C,eAAW,SAAS,MAAM;AACxB,UAAI,UAAU,KAAK,KAAK,WAAW,GAAG;AACpC,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc,QAAQ,MAAM,YAAY,MAAM,CAAC,aAAa,OAAO,OAAO,IAAI,CAAC;AAAA,MACjF;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EA6BA,eACK,MASH;AACA,UAAM,CAAC,YAAY,YAAY,IAAI;AACnC,QAAI,cAAc,gBAAgB,KAAK,GAAG,EAAE;AAC5C,aAAS,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;AACnD,YAAM,QAAQ,KAAK,GAAG,KAAK;AAC3B,UAAI,UAAU,KAAK,OAAO,KAAK,KAAK,WAAW,GAAG;AAChD,sBAAc;AAAA,MAChB,OAAO;AACL,sBAAc,QAAQ,MAAM,YAAY,MAAM,CAAC,aAAa,OAAO,OAAO,IAAI,CAAC;AAAA,MACjF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,WAAiE;AACxE,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC,EAAE,KAAK,SAAS;AAClD,WAAO,IAAI,aAAY,OAAO;AAAA,EAChC;AAAA,EAEA,aAAgC;AAC9B,UAAM,WAAW,IAAI,aAAkB;AACvC,aAAS,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;AACnD,YAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,YAAM,UAAU,KAAK,IAAI,GAAG;AAC5B,eAAS,IAAI,KAAK,OAAO;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA,EAKA,aAAa,MAAgE;AAC3E,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,YAAQ,OAAO,GAAG,IAAI;AACtB,WAAO,IAAI,aAAY,OAAO;AAAA,EAChC;AAAA,EAEA,MAAM,OAAgB,KAAc;AAClC,UAAM,SAAS,IAAI,aAAkB;AACrC,QAAI,OAAO,KAAK,OAAO;AAEvB,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,GAAG;AACb,cAAQ,QAAQ,KAAK;AAAA,IACvB;AAEA,QAAI,QAAQ,UAAa,MAAM,GAAG;AAChC,aAAO,MAAM;AAAA,IACf;AAEA,aAAS,QAAQ,OAAO,SAAS,MAAM,SAAS;AAC9C,YAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,YAAM,UAAU,KAAK,IAAI,GAAG;AAC5B,aAAO,IAAI,KAAK,OAAO;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MACE,WACA,SACA;AACA,QAAI,QAAQ;AACZ,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,QAAQ,MAAM,WAAW,SAAS,CAAC,OAAO,OAAO,IAAI,CAAC,GAAG;AAC5D,eAAO;AAAA,MACT;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,KACE,WACA,SACA;AACA,QAAI,QAAQ;AACZ,eAAW,SAAS,MAAM;AACxB,UAAI,QAAQ,MAAM,WAAW,SAAS,CAAC,OAAO,OAAO,IAAI,CAAC,GAAG;AAC3D,eAAO;AAAA,MACT;AACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAUA,SAAS,GAAM,OAAqB,OAA8B;AAChE,MAAI,QAAQ,MAAM,WAAW;AAC3B,WAAO,MAAM,UAAU,GAAG,KAAK,OAAO,KAAK;AAAA,EAC7C;AACA,QAAM,cAAc,YAAY,OAAO,KAAK;AAC5C,SAAO,gBAAgB,KAAK,SAAY,MAAM,WAAW;AAC3D;AAEA,SAAS,YAAY,OAAuB,OAAe;AACzD,QAAM,SAAS,MAAM;AACrB,QAAM,gBAAgB,cAAc,KAAK;AACzC,QAAM,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;AAClE,SAAO,cAAc,KAAK,eAAe,SAAS,KAAK;AACzD;AAEA,SAAS,cAAc,QAAgB;AAErC,SAAO,WAAW,UAAU,WAAW,IAAI,IAAI,KAAK,MAAM,MAAM;AAClE;;;AD7YM,IAAAC,sBAAA;AAzCN,SAASC,kBAGP,MAAc;AAKd,QAAM,gBAAgB,OAAO;AAC7B,QAAM,CAAC,yBAAyB,qBAAqB,QAAI,0CAAmB,aAAa;AAUzF,QAAM,CAAC,2BAA2B,oBAAoB,IAAI;AAAA,IACxD;AAAA,IACA;AAAA,MACE,mBAAmB;AAAA,MACnB,eAAe,EAAE,SAAS,KAAK;AAAA,MAC/B,qBAAqB,EAAE,SAAS,KAAK;AAAA,MACrC,SAAS,IAAI,YAAY;AAAA,MACzB,YAAY,MAAM;AAAA,IACpB;AAAA,EACF;AAOA,QAAM,qBAID,CAAC,EAAE,OAAO,GAAG,MAAM,MAAM;AAC5B,WAAO,QACL,6CAAC,0BAAwB,GAAG,OAAO,OAAc,IAEjD,6CAAC,kBAAgB,GAAG,OAAO;AAAA,EAE/B;AACA,qBAAmB,cAAc;AAEjC,QAAM,iBAGD,CAAC,UAAU;AACd,UAAM,QAAQ,kBAAkB;AAChC,WAAO,6CAAC,0BAAwB,GAAG,OAAO,OAAc;AAAA,EAC1D;AACA,iBAAe,cAAc,gBAAgB;AAE7C,QAAM,yBAID,CAAC,UAAU;AACd,UAAM,EAAE,OAAO,UAAU,MAAM,IAAI;AACnC,UAAM,MAAM,cAAAC,QAAM,OAA0B,IAAI;AAChD,UAAM,CAAC,mBAAmB,oBAAoB,IAAI,cAAAA,QAAM;AAAA,MACtD;AAAA,IACF;AACA,UAAM,kBAAc,4CAAgB,KAAK,oBAAoB;AAC7D,UAAM,CAAC,SAAS,UAAU,IAAI;AAE9B,kBAAAA,QAAM,UAAU,MAAM;AACpB,UAAI,CAAC,kBAAmB;AAExB,YAAM,WAAW,qBAAqB,MAAM;AAAA,MAkB5C,CAAC;AACD,eAAS,QAAQ,mBAAmB;AAAA,QAClC,WAAW;AAAA,QACX,SAAS;AAAA,MACX,CAAC;AACD,aAAO,MAAM;AACX,iBAAS,WAAW;AAAA,MACtB;AAAA,IACF,GAAG,CAAC,iBAAiB,CAAC;AAEtB,WACE;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB;AAAA,QAEC;AAAA;AAAA,IACH;AAAA,EAEJ;AAEA,yBAAuB,cAAc,gBAAgB;AAMrD,QAAM,uBAAuB,OAAO;AAEpC,QAAM,yBAAqB,+BAAW,oBAAoB;AAC1D,QAAM,iBAAiB,cAAAA,QAAM;AAAA,IAC3B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,YAAM,UAAU,qBAAqB,sBAAsB,KAAK;AAChE,YAAM,mBAAe,4CAAgB,cAAc,QAAQ,aAAa;AACxE,aAAO,6CAAC,sBAAmB,KAAK,cAAe,UAAS;AAAA,IAC1D;AAAA,EACF;AAEA,iBAAe,cAAc;AAM7B,QAAM,iBAAiB,OAAO;AAC9B,QAAM,iBAAiB;AAOvB,QAAM,6BAAyB,+BAAW,cAAc;AACxD,QAAM,qBAAqB,cAAAA,QAAM;AAAA,IAC/B,CAAC,OAAO,iBAAiB;AACvB,YAAM,EAAE,OAAO,UAAU,GAAG,SAAS,IAAI;AACzC,YAAM,MAAM,cAAAA,QAAM,OAAoB,IAAI;AAC1C,YAAM,CAAC,SAAS,UAAU,IAAI,cAAAA,QAAM,SAA6B,IAAI;AACrE,YAAM,mBAAe,4CAAgB,cAAc,KAAK,UAAU;AAClE,YAAM,UAAU,qBAAqB,gBAAgB,KAAK;AAE1D,YAAM,EAAE,WAAW,IAAI;AAEvB,YAAM,cAAc,cAAAA,QAAM,OAAO,QAAQ;AACzC,UAAI,CAAC,aAAa,YAAY,SAAS,QAAQ,GAAG;AAChD,oBAAY,UAAU;AAAA,MACxB;AACA,YAAM,mBAAmB,YAAY;AAErC,oBAAAA,QAAM,UAAU,MAAM;AACpB,cAAMC,YAAW;AACjB,mBAAW,CAAC,QAAQ;AAClB,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,UACT;AAEA,cAAI,CAAC,IAAI,IAAI,OAAO,GAAG;AACrB,gBAAI,IAAI,SAAS,EAAE,GAAIA,WAAkC,QAAQ,CAAC;AAClE,mBAAO,IAAI,SAAS,sBAAsB;AAAA,UAC5C;AAEA,iBAAO,IACJ,IAAI,SAAS,EAAE,GAAIA,WAAkC,QAAQ,CAAC,EAC9D,SAAS,sBAAsB;AAAA,QACpC,CAAC;AAED,eAAO,MAAM;AACX,qBAAW,CAAC,QAAQ;AAClB,gBAAI,CAAC,WAAW,CAAC,IAAI,IAAI,OAAO,GAAG;AACjC,qBAAO;AAAA,YACT;AACA,gBAAI,OAAO,OAAO;AAClB,mBAAO,IAAI,YAAY,GAAG;AAAA,UAC5B,CAAC;AAAA,QACH;AAAA,MACF,GAAG,CAAC,SAAS,kBAAkB,UAAU,CAAC;AAE1C,aACE,6CAAC,0BAAwB,GAAG,EAAE,CAAC,cAAc,GAAG,GAAG,GAAG,KAAK,cACxD,UACH;AAAA,IAEJ;AAAA,EACF;AAEA,qBAAmB,cAAc;AAMjC,WAAS,oBAAoB;AAC3B,WAAO,cAAAD,QAAM,SAAyC,IAAI,YAAY,CAAC;AAAA,EACzE;AAMA,WAAS,cAAc,OAAY;AACjC,UAAM,EAAE,QAAQ,IAAI,qBAAqB,OAAO,sBAAsB,KAAK;AAE3E,WAAO;AAAA,EACT;AAEA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AAAA,IACL,EAAE,UAAU,oBAAoB,MAAM,gBAAgB,UAAU,mBAAmB;AAAA,IACnF;AAAA,EACF;AACF;AAKA,SAAS,aAAa,GAAQ,GAAQ;AACpC,MAAI,MAAM,EAAG,QAAO;AACpB,MAAI,OAAO,MAAM,YAAY,OAAO,MAAM,SAAU,QAAO;AAC3D,MAAI,KAAK,QAAQ,KAAK,KAAM,QAAO;AACnC,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,QAAM,QAAQ,OAAO,KAAK,CAAC;AAC3B,MAAI,MAAM,WAAW,MAAM,OAAQ,QAAO;AAC1C,aAAW,OAAO,OAAO;AACvB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,GAAG,EAAG,QAAO;AAC1D,QAAI,EAAE,GAAG,MAAM,EAAE,GAAG,EAAG,QAAO;AAAA,EAChC;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAY,GAAY;AAClD,SAAO,CAAC,EAAE,EAAE,wBAAwB,CAAC,IAAI,KAAK;AAChD;AAEA,SAAS,uBACP,GACA,GACA;AACA,SAAO,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE,UAC1B,IACA,mBAAmB,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,IAC3C,KACA;AACR;AAEA,SAAS,qBAAqB,UAAsB;AAClD,QAAM,WAAW,IAAI,iBAAiB,CAAC,kBAAkB;AACvD,eAAW,YAAY,eAAe;AACpC,UAAI,SAAS,SAAS,aAAa;AACjC,iBAAS;AACT;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;", "names": ["createCollection", "React", "import_react", "import_react_context", "import_react_compose_refs", "import_react_slot", "import_jsx_runtime", "createCollection", "React", "itemData"]}