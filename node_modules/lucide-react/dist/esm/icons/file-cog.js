/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "m3.2 12.9-.9-.4", key: "1i3dj5" }],
  ["path", { d: "m3.2 15.1-.9.4", key: "1fvgj0" }],
  [
    "path",
    {
      d: "M4.677 21.5a2 2 0 0 0 1.313.5H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2.5",
      key: "1yo3oz"
    }
  ],
  ["path", { d: "m4.9 11.2-.4-.9", key: "otmhb9" }],
  ["path", { d: "m4.9 16.8-.4.9", key: "1b8z07" }],
  ["path", { d: "m7.5 10.3-.4.9", key: "11k65u" }],
  ["path", { d: "m7.5 17.7-.4-.9", key: "431x55" }],
  ["path", { d: "m9.7 12.5-.9.4", key: "87sjan" }],
  ["path", { d: "m9.7 15.5-.9-.4", key: "khqm91" }],
  ["circle", { cx: "6", cy: "14", r: "3", key: "a1xfv6" }]
];
const FileCog = createLucideIcon("file-cog", __iconNode);

export { __iconNode, FileCog as default };
//# sourceMappingURL=file-cog.js.map
