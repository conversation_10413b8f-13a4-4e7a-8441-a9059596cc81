# Análise Completa do Frontend do Dashboard do Influenciador

## 1. Estrutura Geral do Dashboard

O dashboard do influenciador está implementado principalmente no arquivo `src/app/(dashboard)/influenciador/page.tsx`. A estrutura geral consiste em:

- **Header**: Contém o logo, menu de navegação e botões de ações
- **<PERSON><PERSON><PERSON><PERSON> Principal**: Grid de cards com informações e métricas
- **Botão de Suporte WhatsApp**: Fixado no canto inferior direito

### Arquivos Principais

- **Layout Base**: `src/app/(dashboard)/layout.tsx` - Define o layout base para todas as páginas do dashboard
- **Página Principal**: `src/app/(dashboard)/influenciador/page.tsx` - Implementação principal do dashboard
- **Página de Popup**: `src/app/(dashboard)/influenciador/[popup].tsx` - Gerencia popups específicos via URL

## 2. Componentes do Header

O header do dashboard do influenciador contém:

- **Logo**: Componente `Logo` que exibe o logo da crIAdores
- **Menu de Conta**: Implementado com o componente `DropdownMenu` que contém opções como:
  - Configurações (abre o modal de configurações)
  - Ajuda
  - Sair (logout)
- **Botões de Ação**: Ícones para acessar funcionalidades específicas

## 3. Cards do Dashboard

O dashboard do influenciador contém os seguintes cards principais:

### 3.1. Card de Classificação

**Arquivo**: Implementado diretamente em `page.tsx` usando o componente `InfluencerRanking`
**Localização**: Primeiro card no grid (col-span-1)
**Funcionalidades**:
- Exibe a posição do influenciador no ranking geral
- Mostra o total de pontos acumulados
- Indica o nível atual do influenciador
- Possui uma barra de progresso para o próximo nível
- Ao clicar, abre um popup com detalhes mais completos sobre a classificação

### 3.2. Card de Conversão/Procurar Campanhas

**Arquivo**: Implementado diretamente em `page.tsx`
**Localização**: Segundo card no grid (col-span-1)
**Funcionalidades**:
- Permite ao influenciador procurar por novas campanhas
- Ao clicar, abre o popup `CampaignsPopupContent` que lista campanhas disponíveis
- Exibe informações sobre oportunidades disponíveis

### 3.3. Card de Ganhos Totais

**Arquivo**: Implementado diretamente em `page.tsx`
**Localização**: Terceiro card no grid (col-span-1)
**Funcionalidades**:
- Exibe o total de ganhos do influenciador
- Mostra a variação percentual em relação ao mês anterior
- Detalha os ganhos do mês atual e pagamentos pendentes
- Ao clicar, abre um popup com detalhes mais completos sobre os ganhos

### 3.4. Card de Restaurantes

**Arquivo**: Implementado diretamente em `page.tsx`
**Localização**: Quarto card no grid (col-span-1)
**Funcionalidades**:
- Lista os restaurantes com os quais o influenciador trabalha
- Exibe a quantidade de conteúdos criados para cada restaurante
- Mostra os ganhos por restaurante
- Ao clicar em "Ver Todos", abre um popup com a lista completa de restaurantes

### 3.5. Card de Campanhas

**Arquivo**: `src/components/dashboard/CampanhasCorrigido.tsx`
**Localização**: Card maior na parte inferior do grid (col-span-3)
**Funcionalidades**:
- Lista as campanhas em que o influenciador está participando
- Permite filtrar campanhas por status (Todas, Ativas, Inativas)
- Oferece duas visualizações: cards e tabela
- Exibe informações como nome da campanha, restaurante, prêmios, ranking atual
- Ao clicar em uma campanha, abre o popup de detalhes da campanha

## 4. Popups e Modais

O dashboard utiliza vários popups e modais para exibir informações detalhadas:

### 4.1. Sistema de Popups

**Arquivos**:
- `src/components/Popup.tsx` - Componente legado para compatibilidade
- `src/components/ui/BasicPopup.tsx` - Implementação intermediária
- `src/components/ui/StandardPopup.tsx` - Implementação principal com suporte a abas

**Características**:
- Sistema padronizado de popups com diferentes tamanhos
- Suporte a abas para organizar conteúdo
- Animações de entrada e saída
- Suporte a gestos em dispositivos móveis (deslizar para fechar)
- Integração com a URL para manter o estado do popup

### 4.2. Popup de Detalhes da Campanha

**Arquivo**: `src/components/dashboard/CampaignDetailsPopup.tsx`
**Acionado por**: Clique em uma campanha no card de Campanhas
**Conteúdo**:
- Cabeçalho colorido com nome da campanha e restaurante
- Informações detalhadas sobre a campanha (hashtag, período, status)
- Descrição da campanha
- Ranking de influenciadores participantes
- Posição atual do influenciador na campanha

### 4.3. Popup de Procurar Campanhas

**Arquivo**: `src/components/dashboard/CampaignsPopupContent.tsx`
**Acionado por**: Clique no card de Conversão/Procurar Campanhas
**Conteúdo**:
- Lista de campanhas disponíveis em formato de grid
- Informações básicas sobre cada campanha
- Botão para ver detalhes de uma campanha específica
- Modal interno para detalhes da campanha e aplicação

### 4.4. Modal de Detalhes da Campanha

**Arquivo**: `src/components/dashboard/CampaignDetailsModal.tsx`
**Acionado por**: Diversos pontos da aplicação
**Conteúdo**:
- Interface mais completa para visualizar detalhes da campanha
- Sistema de abas: Detalhes, Influenciadores, Métricas
- Informações detalhadas sobre a campanha
- Lista de influenciadores participantes com status
- Métricas de desempenho da campanha

### 4.5. Modal de Configurações

**Arquivo**: `src/components/ui/SettingsModal.tsx` (referenciado no código)
**Acionado por**: Clique no ícone de configurações no header
**Conteúdo**: Configurações do perfil e da conta do influenciador

## 5. Componentes de Perfil do Instagram

**Arquivo**: `src/components/influencer/InstagramProfile.tsx`
**Funcionalidades**:
- Exibe informações do perfil do Instagram conectado
- Permite conectar uma conta do Instagram
- Mostra métricas como seguidores, posts e engajamento
- Oferece funcionalidade de sincronização de dados

## 6. Sistema de Hooks Personalizados

O dashboard utiliza vários hooks personalizados para gerenciar estado e comportamento:

- **useDataCache**: Gerencia o cache de dados para evitar requisições desnecessárias
- **useDynamicPopup**: Gerencia popups dinâmicos em toda a aplicação
- **usePopupNavigation**: Gerencia a navegação entre abas em popups
- **useThrottledCallback**: Limita a frequência de chamadas de funções

## 7. Estilos e Design

O dashboard segue um design consistente com:

- **Cores**: Predominância de branco, cinza claro para o fundo e azul para elementos de destaque
- **Cards**: Cantos arredondados, sombras suaves, espaçamento interno consistente
- **Tipografia**: Hierarquia clara com diferentes tamanhos e pesos de fonte
- **Responsividade**: Layout adaptável para diferentes tamanhos de tela
- **Animações**: Transições suaves para melhorar a experiência do usuário

## 8. Integração com Backend

O dashboard se integra com o backend Supabase para:

- **Autenticação**: Gerenciamento de sessão do usuário
- **Dados do Perfil**: Informações do influenciador
- **Campanhas**: Lista de campanhas disponíveis e participações
- **Métricas**: Dados de desempenho e estatísticas
- **Instagram**: Conexão e sincronização de dados do Instagram

## 9. Fluxos de Usuário Principais

### 9.1. Visualização de Métricas
- Usuário acessa o dashboard
- Visualiza cards com métricas principais
- Pode clicar em cards para ver detalhes em popups

### 9.2. Participação em Campanhas
- Usuário clica no card de Conversão/Procurar Campanhas
- Visualiza campanhas disponíveis
- Seleciona uma campanha para ver detalhes
- Aplica para participar da campanha

### 9.3. Acompanhamento de Campanhas Ativas
- Usuário visualiza campanhas ativas no card de Campanhas
- Clica em uma campanha para ver detalhes
- Acompanha seu ranking e desempenho na campanha

## 10. Conclusão

O frontend do dashboard do influenciador é construído de forma modular, com componentes reutilizáveis e um sistema consistente de UI. A arquitetura permite fácil expansão e manutenção, com separação clara de responsabilidades entre os componentes.

A experiência do usuário é priorizada com:
- Carregamento eficiente de dados
- Feedback visual para ações
- Navegação intuitiva
- Apresentação clara de informações importantes

O sistema de popups e modais permite exibir informações detalhadas sem sobrecarregar a interface principal, mantendo o dashboard limpo e focado nas métricas mais importantes para o influenciador.
