# Guia de Contribuição

Obrigado pelo interesse em contribuir com o projeto crIAdores! Este documento fornece diretrizes para contribuir com o projeto.

## Código de Conduta

Ao participar deste projeto, você concorda em seguir nosso [Código de Conduta](CODE_OF_CONDUCT.md).

## Como Contribuir

### Reportando Bugs

Se você encontrar um bug, por favor, crie uma issue no GitHub com as seguintes informações:

1. Um título claro e descritivo
2. Passos detalhados para reproduzir o bug
3. Comportamento esperado vs. comportamento atual
4. Screenshots, se aplicável
5. Informações do ambiente (navegador, sistema operacional, etc.)

### Sugerindo Melhorias

Para sugerir melhorias, crie uma issue no GitHub com:

1. Um título claro e descritivo
2. Uma descrição detalhada da melhoria proposta
3. Explicação de por que essa melhoria seria útil
4. Exemplos de como a melhoria funcionaria, se aplicável

### Pull Requests

1. Faça um fork do repositório
2. Crie uma branch para sua feature (`git checkout -b feature/amazing-feature`)
3. Faça commit das suas mudanças (`git commit -m 'Add some amazing feature'`)
4. Faça push para a branch (`git push origin feature/amazing-feature`)
5. Abra um Pull Request

### Diretrizes de Código

- Siga o estilo de código existente
- Escreva testes para novas funcionalidades
- Mantenha a documentação atualizada
- Use mensagens de commit claras e descritivas

## Processo de Desenvolvimento

1. As issues são triadas e priorizadas pela equipe
2. As issues são atribuídas a colaboradores ou ficam disponíveis para quem quiser trabalhar nelas
3. O trabalho é feito em branches separadas
4. Pull Requests são revisados pela equipe
5. Após aprovação, o código é mesclado à branch principal

## Configuração do Ambiente de Desenvolvimento

Siga as instruções no [README.md](README.md) para configurar seu ambiente de desenvolvimento.

## Testes

Execute os testes antes de enviar um Pull Request:

```bash
npm run test
# ou
yarn test
```

## Documentação

Atualize a documentação conforme necessário. Isso inclui:

- README.md
- Comentários no código
- Documentação da API
- Documentação de componentes

## Dúvidas?

Se você tiver dúvidas sobre como contribuir, entre em contato com a <NAME_EMAIL>.

Agradecemos sua contribuição!
