# crIAdores - Plataforma de Marketing de Influência para Restaurantes

crIAdores é uma plataforma inovadora que conecta restaurantes a influenciadores locais, facilitando campanhas de marketing de influência e aumentando a visibilidade dos estabelecimentos.

## Visão Geral

A plataforma crIAdores permite que restaurantes criem campanhas de marketing e conectem-se com influenciadores locais relevantes. Os influenciadores podem se candidatar a campanhas, agendar visitas, enviar conteúdo para aprovação e publicar em suas redes sociais. O sistema inclui recursos de gamificação para aumentar o engajamento dos influenciadores.

## Principais Funcionalidades

### Para Restaurantes

- Criação e gerenciamento de campanhas
- Seleção de influenciadores com base em métricas e perfil
- Aprovação de conteúdo antes da publicação
- Agendamento de visitas de influenciadores
- Análise de desempenho de campanhas
- Notificações via WhatsApp em momentos-chave

### Para Influenciadores

- Descoberta de campanhas relevantes
- Candidatura a campanhas de interesse
- Agendamento de visitas a restaurantes
- Envio de conteúdo para aprovação
- Publicação de conteúdo aprovado
- Sistema de gamificação com pontos, conquistas e ranking
- Notificações via WhatsApp em momentos-chave

## Tecnologias Utilizadas

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Banco de Dados**: Supabase (PostgreSQL)
- **Autenticação**: Supabase Auth
- **Armazenamento**: Supabase Storage
- **Notificações**: WhatsApp Business API
- **Integração**: Instagram Graph API

## Estrutura do Projeto

```
connectcity/
├── public/            # Arquivos estáticos
├── src/
│   ├── app/           # Rotas e páginas (Next.js App Router)
│   ├── components/    # Componentes React reutilizáveis
│   ├── contexts/      # Contextos React
│   ├── hooks/         # Hooks personalizados
│   ├── lib/           # Bibliotecas e utilitários
│   │   ├── services/  # Serviços de negócio
│   │   ├── supabase/  # Configuração e clientes Supabase
│   │   ├── utils/     # Funções utilitárias
│   │   └── whatsapp/  # Integração com WhatsApp
│   ├── types/         # Definições de tipos TypeScript
│   └── styles/        # Estilos globais
├── scripts/           # Scripts de utilidade e migração
├── migrations/        # Migrações de banco de dados
└── docs/              # Documentação
```

## Instalação e Configuração

### Pré-requisitos

- Node.js 18.x ou superior
- npm ou yarn
- Conta no Supabase
- Conta no WhatsApp Business API

### Configuração do Ambiente

1. Clone o repositório:
   ```bash
   git clone https://github.com/seu-usuario/connectcity.git
   cd connectcity
   ```

2. Instale as dependências:
   ```bash
   npm install
   # ou
   yarn install
   ```

3. Configure as variáveis de ambiente:
   - Crie um arquivo `.env.local` baseado no `.env.example`
   - Preencha com suas credenciais do Supabase e WhatsApp Business API

4. Execute as migrações do banco de dados:
   ```bash
   npm run migrations
   # ou
   yarn migrations
   ```

5. Inicie o servidor de desenvolvimento:
   ```bash
   npm run dev
   # ou
   yarn dev
   ```

6. Acesse a aplicação em `http://localhost:3000`

## Sistema de Notificações WhatsApp

O sistema de notificações WhatsApp é uma parte fundamental da plataforma, permitindo comunicação em tempo real com restaurantes e influenciadores em momentos-chave de suas jornadas.

### Documentação Completa
- [Guia de Integração WhatsApp](docs/WhatsApp_API_Integration.md)
- [Configuração Técnica](docs/whatsapp_config_analysis.md)
- [Exemplos de Código](docs/whatsapp_system_analysis.md)

### Principais Notificações
- Boas-vindas e onboarding
- Criação e lançamento de campanhas
- Seleção de influenciadores
- Agendamento de visitas
- Aprovação de conteúdo
- Publicação de posts
- Atualizações de ranking e gamificação

### Testes em Produção
```bash
npm run test:whatsapp
```

## Contribuição

Contribuições são bem-vindas! Por favor, leia o [guia de contribuição](CONTRIBUTING.md) para mais detalhes.

## Licença

Este projeto está licenciado sob a [Licença MIT](LICENSE).

## Contato

Para mais informações, entre em contato com a equipe <NAME_EMAIL>.
